# File Management and Organization
- Update A0_word_list.md with detailed status progression instead of just 'complete' vs 'to be created', and add concept file links.
- User prefers vocabulary/ as canonical location for word-related files and wants documentation to reference this location consistently.
- Apply DRY principle to documentation: avoid duplication, create modular structure with links to single source of truth.
- Create temporary files in temp_scripts folder and delete after use to maintain project cleanliness.
- Follow project structure in PROJECT_STRUCTURE.md when creating files/folders.
- User prefers individual concept files per word rather than grouped concept files by level.
- User is concerned that when LLM follows instructions with links to external files (like language classification), the LLM might not actually follow those links and the process could break, questioning whether some duplication might be necessary despite SSOT principle.

# Documentation Preferences
- User prefers the filename '_WORDS_CREATION_GUIDE_WORKFLOW.md' for the main word creation and testing guide.
- User prefers the main instruction file to have a name that clearly indicates it's the primary documentation for sentence creation, testing, and validation workflow, not just database addition.
- User prefers naming convention with underscores and descriptive suffixes like '_WORKFLOW.md' for main instruction files.
- User prefers concise documentation (max 3 lines per section) with critical information placed contextually.
- User prefers creating separate MD files for complex documentation restructuring tasks.
- When restructuring documentation files, never delete any information that was learned through real errors - only reorganize, simplify structure, or move content to other files, but preserve all hard-earned knowledge and examples.
- When structuring step-by-step algorithms, separate actual steps from general principles/rules.
- Add cross-references between algorithm steps and detailed procedures.
- Add motivational principle: frame challenges as system improvement opportunities.
- Add 1-2 line summary at top of each language guide file and reference in instructions.
- User prefers creating alternative answers as a separate workflow step (step 6) rather than integrating into testing steps, and wants alternative answers references removed from step4_testing_draft.md completely.
- Document clear separation rules in each file header: create_words_rules.md for universal sentence creation principles, _language_summary.md for language-specific issues, and add guidance in main workflow about where to update rules when encountering problems.

# Word Testing and Validation
- Follow complete word file creation process from add_words_to_db.md with full testing of all 37 languages and all criteria.
- Use table format with checkboxes for validating all 37 languages and testing parameters in word files.
- Add code phrase 'данное слово было протестировано по всем 37 языкам по всем параметрам таблицы' only when fully tested.
- User prefers multi-stage testing: 1) Basic testing on problematic + core languages, 2) Test remaining problematic languages, 3) Full 37-language testing.
- When testing words, if all 37 languages pass, immediately update MD file and move to next file.
- Add systematic improvement suggestions after each word testing and speed optimization recommendations to batch summaries.
- Prioritize testing: first test 4 super-critical languages and languages with actual problems, then proceed to all 18 problematic languages.

# Language Considerations
- Consider language family characteristics when creating translations (Romance, Germanic, Slavic, Asian).
- Allow semantic equivalence when identical meaning exists and no other natural options available.
- Balance nominative case requirements with natural language constructions.
- Add validation criterion to check if target word corresponds correctly across all 37 languages.
- Allow base forms of target words if they represent same concept and make sentences more natural.
- Глагол 'быть/be' не подходит для уровня A0 и должен быть убран из A0 списка слов.

# JSON File Creation
- Use single A0 template instead of separate templates for ultra_core, core, and extended priorities.
- JSON files should include word_info field in examples (like 'Наречие, вопросительное').
- Template should use placeholders for ALL languages, not just Russian.
- User prefers JSON files contain clean data while MD files serve as testing logs with results tables.
- Implement acceptable answers (synonyms) in JSON under correct_answers after successful testing.
- Alternative answers (alternative_answers field) should be contextual to the specific sentence, not just word synonyms - only words that work in that exact sentence context and translate the same way.
- Alternative answers rules: only exact translations/synonyms that work in specific sentence context, examples like есть/кушать for 'Я хочу ___', извини/извините/прости/простите for '___!', 'мать' is acceptable for '___ работает', keep examples in single language (not mix Russian with English), strict rules against similar-but-different meanings, maximum 3-5 variants per language.
- For alternative answers: avoid words with article conflicts for now, document the issue and programmatic articles solution for future implementation, and always note when alternatives were skipped due to grammatical problems.
- Alternative answers create article conflicts (a mistake vs an error) - target words with different articles cannot be simple substitutions in the same sentence input field.

# MCP Server Usage
- Determine if MCP server is needed based on request context and requirements.
- Use Context7 for studying language_guides before creating new words.
- Apply Sequential Thinking for complex multilingual concepts with grammatical conflicts.
- Use Google Search MCP Server for finding examples of word usage in different languages.
- Workflow: 1) Context7 to study guides, 2) Sequential Thinking for analysis, 3) Context7 for detailed references, 4) Update guides with solutions.

# User-Specific Instructions
- For user Rona, activate virtual environment before running Python files or working with MongoDB: cd backend && source venv_rona/bin/activate.

# Performance Optimization
- Optimize for large volumes (5000+ words): efficient language guide loading, caching frequently used rules, batch processing, parallel validation.
- User prefers LLM-generated sentences over template-based approaches for word creation.