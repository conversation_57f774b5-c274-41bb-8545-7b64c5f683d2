


## 🧠 Backend Development

- Эндпоинт `/api/spaced/{cardId}/response` возвращает `interval_level` — фронтенду нужно извлекать это значение из ответа.
- Перед запуском Python-скриптов или работы с MongoDB активируй виртуальное окружение командой:  
  `source venv/bin/activate` (в директории backend).
- Поле `target_language` — это язык текущей сессии, **не** постоянные настройки пользователя.
- Допустимы одинаковые слова в **разных языках**. Ошибкой считаются дубликаты **внутри одного языка**.
- Чтобы узнать локальный IP сервера, используй команду:  
  `ifconfig | grep 'inet ' | grep -v 127.0.0.1`
- Коллекция `user_progress` хранится в базе данных `word_master`, рядом с коллекцией `words`.

---

## 📘 A0 Vocabulary System

- Уровень A0 включает **155 слов** — базовый "набор для выживания", охватывающий важнейшие темы.
- A0 прогрессия:
  - **ULTRA-CORE (15 слов)** → **CORE** → **EXTENDED**
  - Переход на следующий уровень происходит, когда текущий заканчивается.
- Структура ULTRA-CORE исправлена и включает 15 базовых слов (например: *я, ты, да, нет, хочу, помощь* и т.д.).
- Примеры предложений для A0 должны быть **одинаковыми** во всех 22 языках и иметь **простую грамматику**.
- Избегай притяжательных конструкций и сложных форм — предложения должны быть легко переводимыми.
- Предпочтение — **смысловое соответствие** в переводе, а не строгая грамматическая идентичность.
- В зоне отладки карточек отображается приоритет A0-слов: CORE / EXTENDED.
- При создании слов нужно анализировать ошибки, понимать причины и улучшать документацию.
- Все примеры должны звучать **естественно на всех языках** при проверке.
- При тестировании необходимо показывать статус перевода для **14 проблемных языков**.
- Только артикли в немецком языке (der/die/das) считаются сложными — остальные допустимы, если звучат естественно.
- Примеры с пропущенными словами (___) должны быть грамматически корректными при подстановке правильных ответов.
- После создания каждого JSON-файла:
  - протестируй проблемные языки
  - обнови инструкции и структуру папок `A0`
- JSON-файлы должны группироваться по **3 концепциям** в одном файле.
- Для азиатских языков (ja, ko, zh, th) короткие фразы (2–3 слова) — **норма**, если они грамматически верны.
- Тестирование A0 должно учитывать:
  - корректность формы
  - грамматические правила
  - структуру CORE / ULTRA-CORE / EXTENDED
- **EXAMPLE.md** — главный источник правил по A0. Все другие файлы только ссылаются на него.
- В алгоритме тестирования LLM нужно **явно** указывать ссылку на `EXAMPLE.md`.
- Пользователь хочет проверить масштабируемость подхода A0 на уровни B2/C1 — когда тестируется одно слово, а контекст может варьироваться.
- Предпочтение — автоматизированные методы исправления заглавных букв.
- Список слов A0 должен быть перенесён в `README.md`, незавершённые — отмечены как "to be created".
- После успешного тестирования JSON-файла:
  - обнови `/backend/data/words/A0/README.md`
  - добавь слова, UUID, статус
- Процедура обновления `README.md` должна быть описана в `add_new_words.md` или `example.md`.

---

## 🧾 JSON: Структура и Валидация

- ID концепций в JSON-файлах должны быть в формате UUID.
- Все новые файлы создаются в папке `new`, согласно инструкции.
- Для A0:
  - структура по концепциям
  - отдельная папка A0
  - индексный файл со списком всех файлов
- Предпочтение — **небольшие JSON-файлы** по 3 концепции, а не большие.
- Валидатор должен проверять:
  - наличие поля `priority` у слов A0
  - его **отсутствие** у других уровней
- Если слово в предложении стоит первым — оно должно быть с заглавной буквы.
- В `add_new_words.md` должно быть чётко указано:
  - каждый JSON-файл нужно тестировать через LLM
  - тестировать **все языки**
  - проверять, являются ли найденные проблемы **реальными** по стандартам

---

## 🎨 Интерфейс

- Фон на всех экранах — 4 цветных сферы (разные цвета, случайные).
- Карточки с эффектом **glassmorphism**, высота — адаптивная.
- Формы и кнопки входа/регистрации должны быть **на экране**, а не внутри карточек.
- Языковой дропдаун — кастомный, с флагами.
- Заголовки секций — градиент от **небесно-голубого** к **пастельно-фиолетовому**.
- Текст заголовков — с настоящим **градиентным эффектом**.
- При загрузке новой карточки курсор автоматически фокусируется на поле ввода.
- Кнопки — с радиальными градиентами в стиле фоновых сфер.

---

## 🧭 Навигация и UX

- После регистрации — 3 шага:
  1. Родной язык
  2. Целевой язык
  3. Уровень владения
- Уведомления об успехе — **только в настройках**, не в онбординге.
- Профиль:
  - Родной язык — один флаг
  - Изучаемые — строка флагов
- Если все слова выучены — отображается поздравление.
- В отладке карточек отображается просто `New` или номер интервала, **без префикса**.
- Предпочтение — тестировать с фронтендом, прежде чем загружать большие наборы слов.

---

## 📋 Рабочий процесс и планирование

- После каждого шага (например, ответа ИИ) нужно кратко предлагать:
  - ✅ Текущую задачу
  - 🔜 Следующую задачу 
  - 🔁 Параллельные задачи того же уровня  (2-3)
  - 🔼 2-3 вышестоящие задачу (чтобы понимать в рамках чего мы делаем это)
  Это помогает лучше видеть общий контекст и планировать работу.