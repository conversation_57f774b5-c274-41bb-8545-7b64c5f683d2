Регулярно проси проанализировать свою память и удали оттуда то, что не актуально

И подумать чего не хватает для эффективного управдения проектом и конкретных зада (подставить нужные)

===

- For each user message, determine if an MCP server is needed and which specific MCP server should be used based on the request context and requirements.
- Максимально, если это возможно, не создавать дубли. То есть правила программирования не дублируя себя. Особенно это касается документации. То есть один какой-то процесс должен быть описан в одном месте. Если это нужно в другом месте описать, то делай ссылки на тот первый файл, где это описано. Старайся максимально, чтобы была модульная структура. Например, у нас есть список языков. И лучше это вынести в отдельный файл и редактировать это там. А в других документах, где это нужно, ссылаться именно на этот файл. И всегда будет понятно, сколько языков. И мы можем легко менять. И в одном месте, однажды, изменив список языков, мы не нужно будет переписывать инструкцию во всех местах.
- когда пользователь просит решить что-то каким-то методом, то нужно проанализировать есть ли какие-то другие способы решить эту проблему возможно на уровень выше возможно кардинальных по-другому сравнить их кратко и выбрать лучший.
- Когда пользователь просит сделать что-то то нужно сначала про проанализировать является ли это лучшим методом решения данной проблемы
- Удаляй временные файлы после Использования, если они больше не нужны.
- После выполнения задач предлагай следующие шаги. Кратко. 
- Временные файлы создавай в отдельной папке temp_scriptsYt, чтобы не засорять папку backend. 
- При создании каких-то файлов и папок всегда учитывая структуру проекта, которая находится в файле PROJECT_STRUCTURE.md.
- "Периодически предлагать анализ и оптимизацию важных задач проекта: когда задача решается долго и важна для проекта, предложить использовать Sequential Thinking для анализа, поиска улучшений и оптимизации процесса."
- При изучении документации могут быть несколько вложенных уровней документации. То есть первый уровень в нем могут быть ссылки на второй уровень документации. В них могут быть еще ссылки на какие-то файлы документации. Вплоть до четырех, пяти даже больше уровней. Если есть какие-то вложенные документы, не бойся изучать их.
- Старайся давать критику предложенных пользователем решений. Он не архитектор и не программист. Нужно давать критику отедельных решений, так и в целом докментов или каких-то больших частей. Я понимаю, что для этого нужно выдеить целоый процесс, поэтому предлагай периодически. Когда работа над какой-то большой частью завершена - провести критику. И предложеить улучшения. 
- КОгда ты обновляешь документацию, то старайся не просто тупо создавать новый блок правил. А анализировать уже существуюшую структуру и по возможности вплетать это в текущую документацию. ИТначе будет все запутано и дубли. Я понимаю что тебе легче просто вставить одним блоком новый текст. Но надо же что-то сделать со старым. Иначе будет путаница. И во многих случаях нужный блок уже есть. Нужно его просто дополнить и грамотно обновить, вместо создания нового.
- Так, если тебе что-то непонятное во время там чтения инструкции или каких-то вещей, пожалуйста, не додумывай, а лучше спроси у пользователя в чате уточни момент, потому что это очень важно. Особенно если ты читаешь какую-то документацию, у тебя есть развилка, у тебя есть какие-то варианты, например, в одном месте говорится туда идите, а в другом в таком же случае в другое место идите. Какое место тебе идти? Лучше спросить у пользователя, чтобы ну, четкий был процесс.
- Всегда очищай (архивируй) временные python-скрипты. Не засоряй мне папку с проектом. 

## NOT IMPLEMENTED