# 🏗️ PROJECT STRUCTURE - uMemo

Обновленная структура проекта uMemo после реорганизации данных.

## 📁 КОРНЕВАЯ СТРУКТУРА

```
uMemo/
├── backend/             # 🔧 BACKEND API
│   ├── app/            # FastAPI приложение
│   ├── scripts/        # Важные скрипты
│   ├── tests/          # Unit тесты
│   ├── temp_scripts/   # Временные скрипты (57 файлов)
│   ├── tools/          # Утилиты разработки
│   ├── config/         # Конфигурация
│   ├── legacy/         # Старые файлы
│   └── docs/           # Документация backend
├── frontend/            # 📱 REACT NATIVE APP
│   ├── components/     # React компоненты
│   ├── screens/        # Экраны приложения
│   ├── services/       # API сервисы
│   ├── config/         # Конфигурация (api.ts)
│   └── docs/           # Документация frontend
├── vocabulary/          # 🆕 СЛОВАРНЫЕ ДАННЫЕ
│   ├── concepts/A0/     # Концепты слов (148 файлов)
│   ├── md/A0/           # MD файлы тестирования
│   ├── json/            # JSON файлы переводов
│   │   ├── A0/          # JSON файлы A0
│   │   ├── templates/   # Шаблоны
│   │   └── EXAMPLE.md   # Техническое описание JSON
│   ├── word_lists/      # Списки слов A0_word_list.md
│   ├── language_guides/ # Справочники по 37 языкам
│   ├── _WORDS_CREATION_GUIDE_WORKFLOW.md # 📋 ГЛАВНАЯ ИНСТРУКЦИЯ
│   └── README.md        # Описание структуры данных
├── docs/                # 📚 ДОКУМЕНТАЦИЯ ПРОЕКТА
│   ├── api/            # API документация
│   ├── deployment/     # Развертывание
│   ├── development/    # Разработка
│   ├── future/         # Планы развития
│   ├── unsorted/       # Несортированные документы
│   └── affiliate/      # Партнерская программа
├── scripts/             # 🔧 СКРИПТЫ ПРОЕКТА
│   ├── setup/          # Установка и настройка
│   ├── testing/        # Тестирование (run_tests.py)
│   └── deployment/     # Развертывание
├── design/              # 🎨 ДИЗАЙН И UI ПРИМЕРЫ
├── legacy/              # 📦 СТАРЫЕ/НЕИСПОЛЬЗУЕМЫЕ ФАЙЛЫ
├── memories/            # 💭 ЗАМЕТКИ И ВОСПОМИНАНИЯ
├── logs/                # 📝 ЛОГИ ПРОЕКТА
├── README.md            # 📋 Главное описание проекта
├── PROJECT.md           # 📄 Описание проекта
└── PROJECT_STRUCTURE.md # 🏗️ Структура проекта
```

## 🎯 КЛЮЧЕВЫЕ ИЗМЕНЕНИЯ

### **БЫЛО:**
```
backend/data/words/A0/           # JSON файлы
backend/data/words/temp_md/      # MD файлы
backend/data/concepts/A0/        # Концепты
backend/data/words/EXAMPLE.md    # Техническое описание
md/A0_word_list.md              # Список слов
md/_WORDS_CREATION_GUIDE_WORKFLOW.md           # Инструкции
```

### **СТАЛО:**
```
vocabulary/json/A0/             # JSON файлы
vocabulary/json/EXAMPLE.md      # Техническое описание JSON
vocabulary/md/A0/               # MD файлы
vocabulary/concepts/A0/         # Концепты
vocabulary/word_lists/A0_word_list.md # Список слов
vocabulary/_WORDS_CREATION_GUIDE_WORKFLOW.md   # 📋 ГЛАВНАЯ ИНСТРУКЦИЯ
vocabulary/README.md            # Описание структуры
```

## ✅ ПРЕИМУЩЕСТВА НОВОЙ СТРУКТУРЫ

1. **🎯 Логичность** - данные отделены от кода
2. **🔄 Переиспользование** - доступ из любой части проекта
3. **📁 Организация** - группировка по типам файлов
4. **📈 Масштабируемость** - легко добавлять A1, B1, C1...
5. **🧹 Чистота** - backend содержит только код

## 🔗 СВЯЗЬ ФАЙЛОВ

Для каждого слова создается **3 связанных файла**:

**Пример для слова 10 (помощь/help):**
- `vocabulary/md/A0/0010_A0_ultra_core_10.md` - тестирование
- `vocabulary/json/A0/0010_A0_ultra_core_10.json` - переводы на 37 языков
- `vocabulary/concepts/A0/0010_A0_ultra_core_10_concept.json` - концепт

## 📊 СТАТИСТИКА

- **Концепты A0**: 148 файлов
- **MD файлы**: 13 файлов
- **JSON файлы**: 8 файлов
- **Справочники языков**: 37 файлов
- **Шаблоны**: 7 файлов

## 🚀 WORKFLOW

### **Создание нового слова:**
1. Создать MD файл в `vocabulary/md/A0/`
2. Протестировать по 37 языкам
3. Создать JSON файл в `vocabulary/json/A0/`
4. Концепт уже создан в `vocabulary/concepts/A0/`

### **Импорт в базу данных:**
1. Готовые JSON файлы из `vocabulary/json/A0/`
2. После импорта → перемещение в `vocabulary/json/imported/`

## 📝 ОБНОВЛЕНИЯ ПУТЕЙ

### **В Python скриптах:**
```python
# БЫЛО:
data_path = "backend/data/words/A0/"

# СТАЛО:
data_path = "vocabulary/json/A0/"
```

### **В инструкциях:**
- Все пути обновлены на `vocabulary/`
- Документация приведена в соответствие

## 🎉 РЕЗУЛЬТАТ

Структура проекта стала:
- ✅ Логичнее и понятнее
- ✅ Готова к масштабированию
- ✅ Удобнее для разработки
- ✅ Соответствует best practices

**Данные теперь независимы от backend и доступны всему проекту!**
