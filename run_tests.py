#!/usr/bin/env python3
"""
Скрипт для запуска всех тестов ZAK

Запускает backend и frontend тесты, анализирует результаты
и предоставляет отчет о состоянии системы.
"""

import subprocess
import sys
import os
import json
from datetime import datetime
from pathlib import Path


class TestRunner:
    def __init__(self):
        self.results = {
            "timestamp": datetime.now().isoformat(),
            "backend": {"status": "not_run", "details": {}},
            "frontend": {"status": "not_run", "details": {}},
            "summary": {"total": 0, "passed": 0, "failed": 0}
        }
    
    def run_backend_tests(self):
        """Запускает backend тесты с pytest"""
        print("🧪 Запуск backend тестов...")
        
        try:
            # Переходим в директорию backend
            os.chdir("backend")
            
            # Запускаем pytest с подробным выводом
            result = subprocess.run([
                "python", "-m", "pytest", 
                "tests/test_spaced_repetition_critical.py",
                "-v", "--tb=short", "--json-report", "--json-report-file=test_results.json"
            ], capture_output=True, text=True, timeout=300)
            
            self.results["backend"]["status"] = "completed"
            self.results["backend"]["return_code"] = result.returncode
            self.results["backend"]["stdout"] = result.stdout
            self.results["backend"]["stderr"] = result.stderr
            
            # Пытаемся прочитать JSON отчет
            try:
                with open("test_results.json", "r") as f:
                    test_data = json.load(f)
                    self.results["backend"]["details"] = {
                        "total": test_data.get("summary", {}).get("total", 0),
                        "passed": test_data.get("summary", {}).get("passed", 0),
                        "failed": test_data.get("summary", {}).get("failed", 0),
                        "duration": test_data.get("duration", 0)
                    }
            except (FileNotFoundError, json.JSONDecodeError):
                # Парсим вывод pytest вручную
                lines = result.stdout.split('\n')
                for line in lines:
                    if "passed" in line and "failed" in line:
                        # Пример: "5 passed, 2 failed in 10.5s"
                        parts = line.split()
                        for i, part in enumerate(parts):
                            if part == "passed,":
                                self.results["backend"]["details"]["passed"] = int(parts[i-1])
                            elif part == "failed":
                                self.results["backend"]["details"]["failed"] = int(parts[i-1])
            
            os.chdir("..")
            
            if result.returncode == 0:
                print("✅ Backend тесты прошли успешно")
            else:
                print("❌ Backend тесты завершились с ошибками")
                print(f"Вывод: {result.stdout}")
                print(f"Ошибки: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print("⏰ Backend тесты превысили таймаут (5 минут)")
            self.results["backend"]["status"] = "timeout"
        except Exception as e:
            print(f"💥 Ошибка при запуске backend тестов: {e}")
            self.results["backend"]["status"] = "error"
            self.results["backend"]["error"] = str(e)
    
    def run_frontend_tests(self):
        """Запускает frontend тесты с Jest"""
        print("🧪 Запуск frontend тестов...")
        
        try:
            # Переходим в директорию frontend
            os.chdir("frontend")
            
            # Проверяем, установлены ли зависимости
            if not os.path.exists("node_modules"):
                print("📦 Устанавливаем зависимости...")
                subprocess.run(["npm", "install"], check=True)
            
            # Запускаем Jest тесты
            result = subprocess.run([
                "npm", "test", "--", 
                "tests/integration/cardProgressFlow.test.tsx",
                "--watchAll=false", "--coverage=false", "--verbose"
            ], capture_output=True, text=True, timeout=300)
            
            self.results["frontend"]["status"] = "completed"
            self.results["frontend"]["return_code"] = result.returncode
            self.results["frontend"]["stdout"] = result.stdout
            self.results["frontend"]["stderr"] = result.stderr
            
            # Парсим результаты Jest
            lines = result.stdout.split('\n')
            for line in lines:
                if "Tests:" in line:
                    # Пример: "Tests: 4 passed, 1 failed, 5 total"
                    parts = line.split()
                    for i, part in enumerate(parts):
                        if part == "passed,":
                            self.results["frontend"]["details"]["passed"] = int(parts[i-1])
                        elif part == "failed,":
                            self.results["frontend"]["details"]["failed"] = int(parts[i-1])
                        elif part == "total":
                            self.results["frontend"]["details"]["total"] = int(parts[i-1])
            
            os.chdir("..")
            
            if result.returncode == 0:
                print("✅ Frontend тесты прошли успешно")
            else:
                print("❌ Frontend тесты завершились с ошибками")
                print(f"Вывод: {result.stdout}")
                print(f"Ошибки: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print("⏰ Frontend тесты превысили таймаут (5 минут)")
            self.results["frontend"]["status"] = "timeout"
        except Exception as e:
            print(f"💥 Ошибка при запуске frontend тестов: {e}")
            self.results["frontend"]["status"] = "error"
            self.results["frontend"]["error"] = str(e)
    
    def generate_report(self):
        """Генерирует итоговый отчет"""
        print("\n" + "="*60)
        print("📊 ОТЧЕТ О ТЕСТИРОВАНИИ")
        print("="*60)
        
        # Backend результаты
        print(f"\n🔧 Backend тесты: {self.results['backend']['status']}")
        if self.results["backend"]["status"] == "completed":
            details = self.results["backend"]["details"]
            passed = details.get("passed", 0)
            failed = details.get("failed", 0)
            total = passed + failed
            print(f"   Всего: {total}, Прошли: {passed}, Провалились: {failed}")
            
            if failed > 0:
                print("   ❌ Критические проблемы в backend логике!")
            else:
                print("   ✅ Backend логика работает корректно")
        
        # Frontend результаты  
        print(f"\n🎨 Frontend тесты: {self.results['frontend']['status']}")
        if self.results["frontend"]["status"] == "completed":
            details = self.results["frontend"]["details"]
            passed = details.get("passed", 0)
            failed = details.get("failed", 0)
            total = details.get("total", passed + failed)
            print(f"   Всего: {total}, Прошли: {passed}, Провалились: {failed}")
            
            if failed > 0:
                print("   ❌ Проблемы в frontend компонентах!")
            else:
                print("   ✅ Frontend компоненты работают корректно")
        
        # Общий итог
        total_passed = (self.results["backend"]["details"].get("passed", 0) + 
                       self.results["frontend"]["details"].get("passed", 0))
        total_failed = (self.results["backend"]["details"].get("failed", 0) + 
                       self.results["frontend"]["details"].get("failed", 0))
        
        print(f"\n🎯 ИТОГО: {total_passed + total_failed} тестов")
        print(f"   ✅ Прошли: {total_passed}")
        print(f"   ❌ Провалились: {total_failed}")
        
        if total_failed == 0:
            print("\n🎉 ВСЕ ТЕСТЫ ПРОШЛИ! Система работает корректно.")
        else:
            print(f"\n⚠️  НАЙДЕНО {total_failed} ПРОБЛЕМ! Требуется исправление.")
        
        # Сохраняем отчет
        with open("test_report.json", "w") as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n📄 Подробный отчет сохранен в test_report.json")
    
    def run_all(self):
        """Запускает все тесты"""
        print("🚀 Запуск комплексного тестирования ZAK")
        print(f"⏰ Время начала: {datetime.now().strftime('%H:%M:%S')}")
        
        self.run_backend_tests()
        self.run_frontend_tests()
        self.generate_report()


if __name__ == "__main__":
    runner = TestRunner()
    runner.run_all()
