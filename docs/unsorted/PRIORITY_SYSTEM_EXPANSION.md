# 🔧 СИСТЕМА ПРИОРИТЕТОВ - РАСШИРЕНИЕ НА НОВЫЕ УРОВНИ

## 🚨 КРИТИЧЕСКАЯ ИНФОРМАЦИЯ ДЛЯ РАЗРАБОТЧИКОВ

**Этот документ ОБЯЗАТЕЛЕН к прочтению перед добавлением новых уровней (A1, A2, B1, B2, C1, C2)!**

---

## 📋 ПРОБЛЕМА, КОТОРАЯ БЫЛА РЕШЕНА

### 🚨 Проблема с приоритетами A0 (РЕШЕНА в 2025-06-19)

**Симптомы:**
- 404 ошибка "No cards available" при предзагрузке после изучения 7-го ultra_core слова
- Предзагрузка НЕ переключалась с ultra_core на core приоритет
- Основной запрос переключался корректно

**Корневая причина:**
- Предзагрузка и основной запрос использовали РАЗНЫЕ данные для определения приоритетов
- Предзагрузка: данные БЕЗ кэша
- Основной запрос: кэшированные данные
- Результат: разные приоритеты в одинаковых условиях

**Решение:**
- Создана функция `_get_a0_priority_filter_with_learned_words()` для предзагрузки
- Синхронизация данных между предзагрузкой и основным запросом
- Универсальные функции для будущих уровней

---

## 🔧 ЧТО НУЖНО СДЕЛАТЬ ПРИ ДОБАВЛЕНИИ НОВЫХ УРОВНЕЙ

### 📋 ОБЯЗАТЕЛЬНЫЕ ШАГИ:

#### 1. **Добавить приоритеты в базу данных**
```javascript
// Пример для A1 уровня
{
  "word": "example_word",
  "level": "A1",
  "priority": "ultra_core" | "core" | "extended",
  "language": "cb"
}
```

#### 2. **Обновить универсальные функции**
Файл: `backend/app/services/spaced_repetition.py`

Функции для обновления:
- `_get_universal_priority_filter()`
- `_get_universal_priority_filter_with_learned_words()`

#### 3. **Создать регрессионные тесты**
Скопировать и адаптировать: `frontend/tests/e2e/vocabularyExhaustionTest.js`

#### 4. **Обновить документацию**
- Добавить новый уровень в этот файл
- Обновить `problems.md` с потенциальными проблемами

---

## 🧪 РЕГРЕССИОННЫЕ ТЕСТЫ

### 📋 Существующие тесты:
- **Тест №3:** A0 приоритеты (ultra_core → core → extended)

### 📋 Тесты для создания:
- **Тест №4:** A1 приоритеты (ultra_core → core → extended)
- **Тест №5:** A2 приоритеты (ultra_core → core → extended)
- **Тест №6:** B1 приоритеты (ultra_core → core → extended)
- И так далее...

---

## 🚨 ПРЕДУПРЕЖДЕНИЯ

### ❌ НЕ ДЕЛАЙТЕ:
1. **НЕ копируйте** логику A0 для новых уровней - используйте универсальные функции
2. **НЕ забывайте** создавать регрессионные тесты
3. **НЕ игнорируйте** синхронизацию предзагрузки и основного запроса

### ✅ ОБЯЗАТЕЛЬНО ДЕЛАЙТЕ:
1. **Тестируйте** переходы между приоритетами
2. **Проверяйте** предзагрузку после исчерпания каждого приоритета
3. **Запускайте** все существующие регрессионные тесты

---

## 📊 СТРУКТУРА ПРИОРИТЕТОВ

### 🔄 Порядок приоритетов (для всех уровней):
1. **ultra_core** - самые важные слова
2. **core** - основные слова
3. **extended** - расширенные слова
4. **все слова уровня** - если все приоритеты исчерпаны

### 📋 Переходы:
- ultra_core исчерпан → переключение на core
- core исчерпан → переключение на extended
- extended исчерпан → показ всех слов уровня для повторения

---

## 🔗 СВЯЗАННЫЕ ФАЙЛЫ

### 📁 Код:
- `backend/app/services/spaced_repetition.py` - основная логика
- `frontend/tests/e2e/vocabularyExhaustionTest.js` - регрессионный тест

### 📁 Документация:
- `docs/problems.md` - описание проблем
- `docs/TESTING_STRATEGY.md` - стратегия тестирования

---

## 📞 КОНТАКТЫ

**При возникновении проблем с приоритетами:**
1. Проверьте этот документ
2. Запустите регрессионные тесты
3. Изучите логи backend с детальной отладкой
4. Проверьте синхронизацию предзагрузки и основного запроса

**Дата создания:** 2025-06-19  
**Последнее обновление:** 2025-06-19  
**Статус:** АКТИВНЫЙ - ОБЯЗАТЕЛЕН К ИСПОЛЬЗОВАНИЮ
