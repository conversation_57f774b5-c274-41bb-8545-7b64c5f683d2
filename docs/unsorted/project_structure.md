# Структура проекта Word Master

## Общее описание

Проект состоит из двух основных частей:
1. **Бэкенд** (FastAPI) - API для работы с данными
2. **Фронтенд** (React Native) - мобильное приложение

## Детальная структура

### Бэкенд (`/backend`)

```
backend/
├── app/
│   ├── __init__.py
│   ├── main.py             # Точка входа в приложение
│   ├── config.py           # Конфигурация
│   ├── database.py         # Настройка БД
│   ├── models/             # Модели данных
│   ├── services/           # Бизнес-логика
│   ├── routers/            # API эндпоинты
│   └── utils/              # Вспомогательные функции
├── scripts/                # Скрипты для администрирования
│   ├── words/              # Утилиты для работы со словами
│   │   ├── __init__.py     # Пакет Python
│   │   ├── __main__.py     # Основной интерфейс командной строки
│   │   ├── check_db.py     # Проверка подключения к БД
│   │   ├── check_recent.py # Просмотр недавно добавленных слов
│   │   ├── clear_words.py  # Очистка коллекции слов
│   │   └── validate_word.py # Валидация JSON-файлов
│   └── ...                 # Другие скрипты
├── tests/                  # Тесты
├── requirements.txt        # Зависимости Python
├── data/                  # Данные приложения
│   └── words/             # Словари и примеры
│       ├── new/           # Новые файлы для импорта
│       ├── imported/      # Успешно импортированные файлы
│       ├── invalid/       # Файлы с ошибками
│       └── EXAMPLE.md     # Пример формата файла
└── .env                   # Переменные окружения
```

### Фронтенд (`/frontend`)

```
frontend/
├── src/
│   ├── assets/            # Статические файлы (шрифты, изображения)
│   │
│   ├── components/        # Переиспользуемые UI компоненты
│   │   ├── common/        # Базовые компоненты (Button, Input, Text и т.д.)
│   │   ├── training/      # Компоненты для экрана тренировки
│   │   └── ui/            # Сложные составные компоненты
│   │
│   ├── screens/         # Экранные компоненты
│   │   ├── TrainingScreen/
│   │   ├── AuthScreen/
│   │   └── ...
│   │
│   ├── navigation/       # Навигация
│   │   ├── AppNavigator.tsx
│   │   └── routes.ts
│   │
│   ├── services/        # API клиенты и сервисы
│   │   ├── api/
│   │   ├── auth.ts
│   │   └── words.ts
│   │
│   ├── store/          # Управление состоянием
│   │   ├── auth/
│   │   └── training/
│   │
│   ├── theme/          # Стили и темы
│   │   ├── colors.ts
│   │   ├── spacing.ts
│   │   └── typography.ts
│   │
│   └── utils/         # Вспомогательные функции
│       ├── helpers.ts
│       └── constants.ts
│
├── App.tsx            # Точка входа
├── app.json           
└── package.json       
```

## Связи между компонентами

1. **Фронтенд** обращается к **бэкенду** через REST API
2. **Бэкенд** использует **MongoDB** как основное хранилище данных
3. **Общие ресурсы** (слова, предложения) вынесены в `/shared`

## Документация

- [Общее описание проекта](../project.markdown)
- [Структура бэкенда](backend_structure.md)
- [Структура базы данных](database_schema.md)

## Рекомендации по разработке

1. **Коммиты**: Используйте [Conventional Commits](https://www.conventionalcommits.org/)
2. **Ветки**: 
   - `main` - стабильная версия
   - `develop` - основная ветка разработки
   - `feature/*` - новая функциональность
3. **Кодстайл**:
   - Python: PEP 8, Black
   - TypeScript: ESLint + Prettier
