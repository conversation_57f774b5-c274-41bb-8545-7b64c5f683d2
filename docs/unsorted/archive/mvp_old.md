# План разработки MVP

## Обзор
MVP создается за 4 недели и включает базовые функции для изучения 1000 английских слов с интервальным повторением. Пользователь тренируется с карточками, вводя пропущенные слова, без регистрации и оплаты.

## Ключевые функции
- **Тренировка**: Ввод пропущенного слова в английском предложении с русским переводом.
- **Интервальное повторение**:
  - Первый правильный ввод → список "изученные".
  - Первый неправильный ввод → повтор до правильного, затем первый интервал (1 минута).
  - Последующие показы:
    - Правильный ввод → следующий интервал.
    - Неправильный ввод → снижение интервала на один уровень назад (например, с 30 дней до 14 дней), повтор до правильного.
- **Очереди**:
  - Очередь 1: новые слова или слова с наступившим интервалом.
  - Очередь 2: слова, ожидающие интервала.
  - Список "изученные": слова, введенные правильно с первого раза.
- **Ежедневные цели**: 20–50 карточек (выбор пользователя), прогресс-бар.
- **Уровни**: 1000 слов на 10 уровней (по 100 слов).
- **Данные**: Предложения из Tatoeba, слова из EF Top 1000 (хранятся в `/project/shared/data/words.json`).

## План разработки (4 недели)

### Неделя 1: Настройка и данные
- **Дни 1-2: Инициализация**
  - Установить [Node.js](https://nodejs.org/), [Python](https://www.python.org/), [MongoDB Compass](https://www.mongodb.com/products/compass).
  - Frontend: `npx create-expo-app frontend`.
  - Backend: `pip install fastapi pymongo python-dotenv uvicorn`.
  - Создать кластер в [MongoDB Atlas](https://www.mongodb.com/cloud/atlas).
- **Дни 3-4: База данных**
  - Коллекции:
    ```json
    // cards
    {
      "word": "cat",
      "level": 1,
      "sentence_english": "The ___ is sleeping.",
      "correct_word": "cat",
      "sentence_russian": "Кот спит.",
      "language_pair": "rus-eng"
    }
    // users
    {
      "user_id": "uuid",
      "learned_words": ["dog"],
      "queue_1": [{"word": "cat", "next_review": "2025-04-30T10:00:00Z"}],
      "queue_2": [{"word": "house", "next_review": "2025-05-06T10:00:00Z"}]
    }
    ```
  - Скачать [Tatoeba](https://tatoeba.org/eng/downloads) (`sentences.csv`, `links.csv`) и сохранить в `/project/shared/data/sentences/`.
  - Взять 1000 слов из [EF Top 1000](https://www.ef.edu/english-resources/english-vocabulary/top-1000-words/) и сохранить в `/project/shared/data/words.json`.
  - Разделить на 10 уровней.
  - Скрипт для создания карточек:
    ```python
    import pandas as pd
    import json
    from pymongo import MongoClient

    client = MongoClient("mongodb+srv://<username>:<password>@cluster0.mongodb.net/")
    db = client["language_app"]
    collection = db["cards"]

    sentences = pd.read_csv("../shared/data/sentences/sentences.csv", sep="\t", names=["id", "lang", "text"])
    links = pd.read_csv("../shared/data/sentences/links.csv", sep="\t", names=["source_id", "target_id"])
    words = json.load(open("../shared/data/words.json"))  # Список 1000 слов

    for word_data in words:
        word = word_data["word"]
        eng_sentences = sentences[(sentences["lang"] == "eng") & (sentences["text"].str.contains(word, case=False))]
        for _, eng_row in eng_sentences.head(2).iterrows():
            eng_id = eng_row["id"]
            eng_text = eng_row["text"]
            link = links[links["source_id"] == eng_id]
            if not link.empty:
                rus_id = link.iloc[0]["target_id"]
                rus_row = sentences[sentences["id"] == rus_id]
                if not rus_row.empty and rus_row.iloc[0]["lang"] == "rus":
                    rus_text = rus_row.iloc[0]["text"]
                    card = {
                        "word": word,
                        "level": word_data["level"],
                        "sentence_english": eng_text.replace(word, "___", 1),
                        "correct_word": word,
                        "sentence_russian": rus_text,
                        "language_pair": "rus-eng"
                    }
                    collection.insert_one(card)
    ```
- **Дни 5-7: Backend API**
  - Эндпоинты:
    - `GET /api/cards/daily?language=rus-eng`: Карточки на день.
    - `POST /api/cards/submit`: Проверить ответ, обработать ошибку (снижение интервала).
    - `PUT /api/cards/learned`: Добавить в "изученные".
    - `PUT /api/cards/queue`: Обновить очереди (включая снижение интервала).
  - Настроить `.env` для MongoDB.

### Неделя 2: Frontend
- **Дни 8-10: Сцены**
  - HomeScene: Кнопка "Тренировка", выбор цели (20–50).
  - TrainingScene: Предложение, поле ввода, кнопка "Отправить".
  - ProgressScene: Прогресс-бар, статистика.
- **Дни 11-13: Логика тренировки**
  - Запрос карточек (`GET /api/cards/daily?language=rus-eng`).
  - Проверка ответа:
    - Первый показ: Правильно → `PUT /api/cards/learned`, Неправильно → повтор.
    - Интервал: Правильно → `PUT /api/cards/queue` (следующий интервал), Неправильно → `PUT /api/cards/queue` (снижение интервала).
  - Локальное хранение сессии (AsyncStorage).
- **День 14: Очереди**
  - Отображать очередь 1.
  - Добавлять новые слова с уровня, если очередь 1 пуста.

### Неделя 3: Улучшения
- **Дни 15-17: Ежедневные цели**
  - Настройка цели (20–50).
  - Прогресс-бар (+1 за карточку).
  - Сохранять прогресс в `users`.
- **Дни 18-20: Уровни**
  - Выбор уровня (по умолчанию уровень 1).
  - Добавление слов с текущего уровня.
- **День 21: Тестирование**
  - Проверить на iOS/Android через Expo Go.
  - Исправить баги.

### Неделя 4: Полировка
- **Дни 22-24: Стилизация**
  - Светлая/темная темы (`/styles/theme.ts`).
  - Пурпурный акцент (#B49FCC).
  - Адаптивный дизайн.
- **Дни 25-26: Документация**
  - Обновить `project.md`, `mvp.md`.
  - Добавить `README.md`.
- **Дни 27-28: Деплой**
  - Backend на [Render](https://render.com/).
  - Подключить frontend к API.
  - Проверить с MongoDB Atlas.
- **Дни 29-30: Финальное тестирование**
  - Проверить тренировку, очереди, прогресс.
  - Исправить ошибки.

## Инструкции для начинающих
1. **Установка**:
   - [Node.js](https://nodejs.org/).
   - [Python](https://www.python.org/).
   - [MongoDB Compass](https://www.mongodb.com/products/compass).
   - [MongoDB Atlas](https://www.mongodb.com/cloud/atlas).
2. **Запуск**:
   - Frontend: `cd frontend && npm install && npx expo start`.
   - Backend: `cd backend && pip install -r requirements.txt && uvicorn app:app --reload`.
3. **Данные**:
   - Скачать Tatoeba (`sentences.csv`, `links.csv`) и сохранить в `/project/shared/data/sentences/`.
   - Скачать EF Top 1000 и сохранить в `/project/shared/data/words.json`.