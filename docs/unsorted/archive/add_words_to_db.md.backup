# 📚 Руководство по добавлению слов в базу данных

> **📋 ИСТОЧНИК ИСТИНЫ**: Это основная документация по добавлению слов. Если вы видите расхождения с другими частями документации, приоритет имеет информация из этого файла.

## 🤖 **БЫСТРЫЙ СТАРТ ДЛЯ LLM**

### **📋 ОБЯЗАТЕЛЬНОЕ ЧТЕНИЕ:**
1. **`backend/data/words/EXAMPLE.md`** - ГЛАВНЫЙ файл с техническими правилами
2. **`md/A0_word_list.md`** - список всех 151 A0 слова с приоритетами
3. **Этот файл** - процедура тестирования и создания

### **🚀 АЛГОРИТМ ДЕЙСТВИЙ (КОНЦЕПТ-ПЕРВЫЙ):**
1. **Выбор слова** → Найти следующее слово со статусом ⏳ TO_BE_CREATED в md/A0_word_list.md
2. **Анализ концепта** → Понять семантику и изучить справочники по языкам
3. **Тестирование в чате** → Создать предложения и проверить по ВСЕМ 37 языкам
4. **Создание JSON** → Файл с 1 концептом на 37 языках (если тест прошел)
5. **Валидация и импорт** → Проверка формата, загрузка в БД и обновление статуса

### **⚠️ КРИТИЧЕСКИ ВАЖНО:**
- **Анализ проблем**: НЕ сразу исправлять, СНАЧАЛА понять - действительно ли проблема?
- **Обновление правил**: ВСЕ новые правила ТОЛЬКО в `backend/data/words/EXAMPLE.md`
- **Системное мышление**: анализировать смежные языки и случаи

---

## 🧠 **Работа с концептами (НОВОЕ!)**

### **Что такое концепт?**
**Концепт** - это семантическое описание идеи, которую выражает слово. Каждый концепт имеет уникальный `concept_id` и связывает переводы одного слова на всех языках.

### **Структура концепта:**
```json
{
  "concept_id": "550e8400-e29b-41d4-a716-************",
  "concept_name": "first_person_pronoun",
  "description": {
    "en": "First person singular pronoun - refers to the speaker",
    "ru": "Местоимение первого лица единственного числа"
  },
  "category": "pronouns",
  "semantic_field": "personal_identity"
}
```

### **Workflow "Концепт-первый":**

#### **Шаг 1: Анализ концепта**
```bash
# Проверить, есть ли концепт для слова "мама"
python -m scripts.concepts list A0 | grep mother

# Если нет - создать новый концепт
python -m scripts.concepts create --word "мама" --en "mother" --category "family"
```

#### **Шаг 2: Создание слов**
После создания концепта создаем JSON файл со словами на всех языках, используя `concept_id` из концепта.

#### **Шаг 3: Связь в базе данных**
- **Коллекция `concepts`**: описания концептов
- **Коллекция `words`**: слова связаны через `concept_id`

### **Команды для работы с концептами:**
```bash
# Список всех концептов A0
python -m scripts.concepts list A0

# Просмотр концепта и его слов
python -m scripts.words concept 550e8400-e29b-41d4-a716-************

# Импорт новых концептов
python -m scripts.concepts import data/concepts/new_concepts.json
```

### **🆕 Новые инструменты (2025-06-23):**

#### **1. 📚 Языковые справочники (37 языков)**
```bash
# Создать справочники для всех языков
python scripts/generate_language_guides.py

# Справочники находятся в:
ls data/words/language_guides/
```

#### **2. 📝 Шаблоны JSON файлов**
```bash
# Создать шаблоны для всех приоритетов
python scripts/generate_word_templates.py

# Использовать шаблон:
cp data/words/templates/ultra_core_template.json data/words/A0/concept_007.json
```

#### **3. 🧪 Расширенное тестирование**
```bash
# Тест по всем 37 проблемным языкам
python test_a0_word_creation.py

# Проверка готовности к импорту
python scripts/check_import_ready.py
```

---

## Настройка подключения к MongoDB Atlas

Перед началом работы убедитесь, что у вас настроено подключение к MongoDB Atlas:

1. Получите строку подключения из панели управления MongoDB Atlas
2. Создайте файл `.env` в папке `backend/` со следующим содержимым:
   ```
   MONGODB_URL=ваша_строка_подключения
   DATABASE_NAME=word_master
   ```
3. Убедитесь, что ваш IP-адрес добавлен в белый список в настройках безопасности MongoDB Atlas


## Процесс добавления слов

> **ВАЖНО!** Все команды должны выполняться из корневой директории проекта (`/путь/к/project/`).
>
> Также перед выполнением любых команд необходимо активировать виртуальное окружение:
> ```bash
> # Перейдите в директорию backend
> cd /путь/к/project/backend
>
> # Активируйте виртуальное окружение
> source venv/bin/activate  # для Linux/Mac
> # ИЛИ
> .\venv\Scripts\activate  # для Windows
>
> # Вернитесь в корень проекта
> cd ..
> ```

### 🚀 Переход на новые уровни (A1, B1, B2, C1, C2)

> **⚠️ КРИТИЧЕСКИ ВАЖНО**: Перед созданием слов нового уровня ОБЯЗАТЕЛЬНО создать и утвердить список слов!

**Процедура для новых уровней:**

1. **Проверить наличие списка слов**:
   - Для A0: используем `md/A0_word_list.md` (готов)
   - Для A1: проверить `md/A1_word_list.md` - если нет, создать
   - Для B1: проверить `md/B1_word_list.md` - если нет, создать

2. **Если файла списка нет - создать и утвердить**:
   ```markdown
   # [УРОВЕНЬ] Word List

   > **📊 СТАТУС УРОВНЯ [УРОВЕНЬ]**: ⏳ **СОЗДАНИЕ СПИСКА** | Список не готов

   | № | Русское | Английское | Статус | Приоритет | UUID | Файл |
   |---|---------|------------|--------|-----------|------|------|
   | 1 | [слово] | [перевод] | ⏳ TO_BE_CREATED | [приоритет] | [uuid] | [файл].json |
   ```

3. **Утвердить список** (проанализировать полноту, проверить дубликаты)

4. **Обновить статус** на "✅ **СПИСОК УТВЕРЖДЕН** | ⏳ **В ПРОЦЕССЕ СОЗДАНИЯ JSON**"

5. **Продолжить стандартный процесс** создания слов

---

1. **Подготовка файла**
   - Создайте JSON-файл в соответствии с форматом, описанным в `backend/data/words/EXAMPLE.md`
   - Сохраните файл в папку `backend/data/words/new/`
   - Убедитесь, что у вас есть права на запись в эту папку

2. **Валидация файла**
   ```bash
   # Перейдите в директорию backend
   cd /Users/<USER>/Library/CloudStorage/Dropbox/EVO/2.EnApp/backend
   
   # Активируйте виртуальное окружение
   source venv/bin/activate  # для Linux/Mac
   # ИЛИ
   .\venv\Scripts\activate  # для Windows
   
   # Выполните валидацию
   python -m scripts.words validate data/words/new/ваш_файл.json
   
   # Если возникает ошибка, убедитесь, что вы находитесь в директории backend
   # и что скрипты доступны в PYTHONPATH
   ```

3. **Активация виртуального окружения**
   ```bash
   # Перейдите в директорию backend
   cd backend
   
   # Активируйте виртуальное окружение
   source venv/bin/activate  # для Linux/Mac
   # ИЛИ
   .\venv\Scripts\activate  # для Windows
   
   # Вернитесь в корень проекта
   cd ..
   ```

4. **Импорт слов**
   ```bash
   # Из корня проекта
   python -m scripts.words import
   ```

5. **Проверка результатов**
   - Успешно обработанные файлы будут перемещены в `backend/data/words/imported/`
   - Рекомендуется проверить импорт в БД с помощью скрипта `python -m scripts.words check_recent`
   - Файлы с ошибками - в `backend/data/words/invalid/`
   - Подробные логи доступны в `backend/logs/import_words.log`


## Рекомендации

### Формат файлов

1. **Основная документация (ОБЯЗАТЕЛЬНО К ИЗУЧЕНИЮ):**
   - `backend/data/words/EXAMPLE.md` - полная документация по формату
   - Содержит все требования, рекомендации и правила валидации
   - ВСЕГДА изучайте этот файл перед созданием новых файлов

2. **Пример файла (только для справки):**
   - `backend/data/words/EXAMPLE.json` - пример файла, соответствующий документации
   - Используйте ТОЛЬКО после полного изучения `EXAMPLE.md`
   - Не содержит всех возможных вариантов и объяснений

### Советы по созданию файлов

- Всегда проверяйте свой файл с помощью команды валидации перед импортом
- Используйте `concept_id` для связывания переводов одного слова
- Сохраняйте файлы в кодировке UTF-8
- Имя файла должно соответствовать уровню (например, `A1.json`, `B2.json`)

- Для очистки коллекции слов используйте команду:
  ```bash
  python -m scripts.words clear
  ```
  
- Для проверки структуры файла перед импортом:
  ```bash
  python -m scripts.words validate путь/к/файлу.json
  ```



## Возможные проблемы

1. **Файл не найден**
   - Убедитесь, что файл находится в папке `backend/data/words/new/`
   - Проверьте правильность пути к файлу

2. **Ошибки валидации**
   - Проверьте сообщения об ошибках в логах `backend/logs/import_words.log`
   - Убедитесь, что все обязательные поля заполнены правильно
   - Проверьте формат UUID в поле `concept_id`

3. **Дублирование слов**
   - Скрипт автоматически проверяет наличие слов по комбинации `word` и `language`
   - Существующие слова не будут добавлены повторно



## Создание A0 слов (базовый уровень)

### Принципы создания универсальных A0 слов для всех 37 языков

> **📋 ИСТОЧНИК ИСТИНЫ**: `frontend/constants/languages.ts` - ALL_LANGUAGES (37 языков)

A0 уровень - это 100 самых базовых слов для каждого языка. При создании таких слов необходимо соблюдать строгие правила для совместимости со ВСЕМИ языками.

#### 1. Анализ сложности языков (ОБНОВЛЕНО для 37 языков)

> **🎯 КРИТЕРИЙ ПРОБЛЕМНОСТИ**: язык **МЕНЯЕТ ФОРМУ ЦЕЛЕВОГО СЛОВА** в предложениях
> - ❌ "Я вижу собак**у**" (не "собака") - **ПРОБЛЕМА**
> - ✅ "Собака лает" - **НЕ ПРОБЛЕМА**

**🔴 КРИТИЧЕСКИ ПРОБЛЕМНЫЕ (см. test_a0_word_creation.py) - МЕНЯЮТ ФОРМУ СЛОВА:**

**Падежные языки (6):**
- **Финский (fi)** - 15 падежей + агглютинация: talo→talossa, äiti→äitini
- **Русский (ru)** - 6 падежей: собака→собаку, дом→дома
- **Польский (pl)** - 7 падежей: dom→domu, mama→mamę
- **Немецкий (de)** - 4 падежа + артикли: der Hund→den Hund
- **Турецкий (tr)** - агглютинация: ev→evimiz, anne→annem
- **Украинский (uk)** - 7 падежей: мама→маму, дім→дому

**Сложные конструкции (4):**
- **Японский (ja)** - частицы は/を/に меняют смысл
- **Корейский (ko)** - агглютинация + вежливость
- **Арабский (ar)** - корневая система + падежи
- **Хинди (hi)** - падежи + постпозиции: लड़का→लड़के को

**Романские языки (5) - артикли + род:**
- **Итальянский (it)** - il/la + род (но "casa" остается "casa")
- **Испанский (es)** - el/la + род (но "casa" остается "casa")
- **Французский (fr)** - le/la + род (но "maison" остается "maison")
- **Португальский (pt)** - o/a + род (но "casa" остается "casa")
- **Нидерландский (nl)** - de/het + род (но слово не меняется)

**✅ НЕ ПРОБЛЕМНЫЕ ЯЗЫКИ (22 языка) - слово НЕ меняет форму:**
- **Простые (3)**: en, id, ms - минимальная морфология
- **Средние (19)**: zh, th, vi, my, sv, da, no, kk, uz, pa, mr, bn, ur, ne, fa, tl, cb, ka, ro

#### 2. Целевое слово ТОЛЬКО в базовой форме

**Базовые формы по языкам:**
- **Русский**: именительный падеж, ед.число (дом, мама, красивый)
- **Английский**: словарная форма (house, mom, beautiful)
- **Немецкий**: именительный падеж (Haus, Mutter, schön)
- **Финский**: номинатив без суффиксов (talo, äiti, kaunis)
- **Турецкий**: основа без суффиксов (ev, anne, güzel)
- **Японский**: словарная форма (家, 母, 美しい)

#### 3. ОБЯЗАТЕЛЬНЫЕ запреты для A0

**❌ НИКОГДА не используйте (целевое слово изменит форму):**

**3.1 Падежные конструкции для целевого слова:**
- ❌ "Я вижу ___" → винительный падеж (собаку, а не собака)
- ❌ "Я иду к ___" → дательный падеж (врачу, а не врач)
- ❌ "Я думаю о ___" → предложный падеж (доме, а не дом)
- ❌ "Я живу в ___" → предложный падеж (городе, а не город)

**3.2 Притяжательные суффиксы к целевому слову:**
- ❌ "Моя ___" → финский: "äitini" (äiti + суффикс -ni)
- ❌ "Наш ___" → турецкий: "evimiz" (ev + суффикс -imiz)

**3.3 Сложные грамматические формы целевого слова:**
- ❌ Сравнительная степень (больше, лучше)
- ❌ Прошедшее/будущее время глаголов
- ❌ Условное наклонение
- ❌ Пассивный залог

#### 4. РЕКОМЕНДУЕМЫЕ ограничения для A0

**⚠️ ЖЕЛАТЕЛЬНО избегать (но можно использовать, если полезно):**

**4.1 Сложные конструкции в остальных словах предложения:**
- ⚠️ "У меня один дом" (притяжательность в "у меня", но "дом" остается в базовой форме)
- ⚠️ "Der große Hund bellt" (склонение артикля, но "Hund" в именительном падеже)
- ⚠️ "Al niño le gusta" (сложная конструкция, но целевое слово в базовой форме)

**4.2 Сложная лексика в остальных словах:**
- ⚠️ Слова выше A0 уровня в предложении (но не критично)
- ⚠️ Идиоматические выражения
- ⚠️ Культурно-специфичные конструкции

**Принцип**: Главное - целевое слово в базовой форме. Остальные слова могут быть сложнее, если это делает пример полезнее.

#### 5. РАЗРЕШЕННЫЕ безопасные конструкции

**✅ ВСЕГДА безопасно:**

**5.1 Простое подлежащее + сказуемое:**
- ✅ "___ лает" (собака лает)
- ✅ "___ светит" (солнце светит)
- ✅ "___ работает" (мама работает)

**5.2 Подлежащее + прилагательное:**
- ✅ "___ большой" (дом большой)
- ✅ "___ красивая" (мама красивая)
- ✅ "___ холодная" (вода холодная)

**5.3 Простые утверждения:**
- ✅ "Это ___" (это дом)
- ✅ "___ здесь" (мама здесь)

**5.4 Допустимые сложные конструкции:**
- ✅ "У меня ___ дом" (притяжательность не в целевом слове)
- ✅ "Большая ___ лает" (прилагательное не к целевому слову)

#### 6. Проверочный алгоритм для каждого примера

Перед добавлением примера проверьте по ВСЕМ проблемным языкам (см. test_a0_word_creation.py):

**Падежные языки (6):**
- **Русский (ru)** - остается ли слово в именительном падеже?
- **Немецкий (de)** - остается ли слово в именительном падеже?
- **Финский (fi)** - не добавляются ли суффиксы к слову?
- **Турецкий (tr)** - не добавляются ли суффиксы к слову?
- **Польский (pl)** - остается ли слово в именительном падеже?
- **Украинский (uk)** - остается ли слово в именительном падеже?

**Сложные конструкции (4):**
- **Японский (ja)** - не усложняются ли частицы?
- **Корейский (ko)** - не усложняется ли вежливость?
- **Арабский (ar)** - не меняется ли корневая система?
- **Хинди (hi)** - не добавляются ли постпозиции?

**Романские языки (5):**
- **Итальянский (it)** - простые ли артикли?
- **Испанский (es)** - простые ли артикли?
- **Французский (fr)** - простые ли артикли?
- **Португальский (pt)** - простые ли артикли?
- **Нидерландский (nl)** - простые ли артикли?

**Если ВСЕ 15 проверок ✅ - пример безопасен!**

#### 7. Принцип исправления проблемных предложений

**Если обнаружена проблема в любом языке:**

1. **НЕ исправляйте только один язык** - это нарушит единообразие
2. **Измените предложение для ВСЕГО concept_id** - для всех 25 языков сразу
3. **Найдите новую безопасную конструкцию** для всех языков

**Пример исправления:**
```
❌ Проблемное предложение:
- concept_id: "uuid-123" (слово "какой")
- Русский: "___ это цвет?" → проблема со склонением по родам
- Немецкий: "welche Farbe ist das?" → проблема со склонением

✅ Исправленное предложение:
- concept_id: "uuid-123" (слово "какой")
- Русский: "___ вопрос?" → "какой" ✅
- Немецкий: "welche Frage?" → "welche" ✅
- ВСЕ остальные языки тоже обновляются!
```

**Принцип**: Одно предложение на concept_id для всех языков!

#### 8. Процедура создания и проверки A0 слов

**ОПТИМИЗИРОВАННАЯ ПАКЕТНАЯ ПРОЦЕДУРА (10 слов за 2 сообщения):**

**🚨 ВАЖНЫЕ ПРИНЦИПЫ:**
- **БЕЗ Context7** - пока что не показал ценности для нашей задачи
- **БЕЗ ограничения паттернами** - у нас только 8 слов, рано ограничивать творчество
- **Индивидуальный подход** - каждое слово анализируется по его семантике
- **Полная прозрачность** - все таблицы тестирования и детальная сводка

**СООБЩЕНИЕ 1: Массовая обработка пакета из 10 слов**

1. **Выбрать 10 слов** со статусом ⏳ TO_BE_CREATED из md/A0_word_list.md
2. **Для каждого слова (1-10) выполнить:**
   - Проанализировать концепт индивидуально (БЕЗ Context7, БЕЗ ограничения паттернами)
   - Создать предложение-концепт творчески для всех 37 языков
   - Показать ПОЛНУЮ таблицу тестирования (37 языков × все критерии)
   - Проанализировать проблемы и предложить решения (НЕ спрашивать пользователя сразу)
   - Отметить статус: ✅ ГОТОВ / ❌ ТРЕБУЕТ ИСПРАВЛЕНИЯ
3. **Итоговая сводка пакета:**
   - Количество готовых слов
   - Список проблемных слов с описанием проблем
   - Предлагаемые решения

**СООБЩЕНИЕ 2: Исправления и финализация**

1. **Получить указания пользователя** по проблемным словам
2. **Исправить проблемные концепты** согласно указаниям
3. **Повторно протестировать** ТОЛЬКО исправленные слова
4. **Создать JSON файлы** для всех 10 слов
5. **Массовая валидация и импорт**
6. **Обновление статусов** в md/A0_word_list.md

**РЕЗУЛЬТАТ:** 10 качественных слов за 2 сообщения вместо 70 сообщений

**Список ВСЕХ проблемных языков для проверки (ОБНОВЛЕНО):**

🔴 **КРИТИЧЕСКИ ПРОБЛЕМНЫЕ (см. test_a0_word_creation.py):**

**Падежные языки (6):**
- **Финский (fi)** - 15 падежей + агглютинация
- **Русский (ru)** - 6 падежей: собака→собаку
- **Польский (pl)** - 7 падежей: dom→domu
- **Немецкий (de)** - 4 падежа + артикли: der Hund→den Hund
- **Турецкий (tr)** - агглютинация: ev→evimiz
- **Украинский (uk)** - 7 падежей: мама→маму

**Сложные конструкции (4):**
- **Японский (ja)** - частицы は/を/に
- **Корейский (ko)** - агглютинация + вежливость
- **Арабский (ar)** - корневая система + падежи
- **Хинди (hi)** - падежи + постпозиции

**Романские языки (5):**
- **Итальянский (it)** - il/la + род
- **Испанский (es)** - el/la + род
- **Французский (fr)** - le/la + род
- **Португальский (pt)** - o/a + род
- **Нидерландский (nl)** - de/het + род

**ПОЛНАЯ ПРОЦЕДУРА LLM ТЕСТИРОВАНИЯ В ЧАТЕ:**

**Шаг 1: Выбор слова по статусу**
1. **ОБЯЗАТЕЛЬНО прочитать** `backend/data/words/EXAMPLE.md` - ГЛАВНЫЙ файл с форматом и правилами
2. **Прочитать эту инструкцию** полностью
3. **Открыть** `md/A0_word_list.md` и найти первое слово со статусом `⏳ TO_BE_CREATED` (или первое слово после последнего созданного слова `✅ COMPLETED`)
4. **Взять concept_id** этого слова (сейчас это концепт 7 "хочу" - `550e8400-e29b-41d4-a716-************`)

**Шаг 2: Анализ концепта и справочников**
1. **Проанализировать концепт**:
   ```bash
   # Посмотреть описание концепта
   python -m scripts.words concept 550e8400-e29b-41d4-a716-************
   ```
2. **Понять семантику**: что означает концепт, в каких контекстах используется
3. **Изучить справочники** (если необходимо) по всем языкам: `data/words/language_guides/[LANG].md`
4. **Найти список проблемных языков** (см. test_a0_word_creation.py)

**Шаг 3: Массовая обработка каждого слова (повторить для всех 10 слов)**

**ДЛЯ КАЖДОГО СЛОВА ИЗ ПАКЕТА ВЫПОЛНИТЬ:**

1. **ОБЯЗАТЕЛЬНО**: Все критерии взять из `backend/data/words/EXAMPLE.md` раздел "БАЗОВЫЕ ТРЕБОВАНИЯ"
2. **Создать ОДНО предложение-концепт** для выбранного слова:
   - **Одинаковый смысл** на всех 37 языках
   - **Переводится друг на друга** (ru ↔ en ↔ de ↔ ja)
   - **Одна грамматическая конструкция** для всех языков
   - **Полные предложения** (не "Я хочу", а "Я хочу воду")
3. **ОБЯЗАТЕЛЬНО показать ПОЛНУЮ таблицу тестирования** для всех 37 языков в чате
4. **Проанализировать проблемы** и предложить решения (НЕ спрашивать пользователя сразу)
5. **Отметить статус**: ✅ ГОТОВ / ❌ ТРЕБУЕТ ИСПРАВЛЕНИЯ

**ФОРМАТ ВЫВОДА ДЛЯ КАЖДОГО СЛОВА:**
```
=== СЛОВО X: [русское]/[английское] ===
Концепт: [описание]
Паттерн: [тип паттерна]
Предложение: "[русское предложение]" / "[английское предложение]"

📊 ПОЛНАЯ ТАБЛИЦА ТЕСТИРОВАНИЯ (37 ЯЗЫКОВ):
[полная таблица со всеми языками и критериями]

АНАЛИЗ ПРОБЛЕМ:
✅ Проблем не найдено
ИЛИ
❌ НАЙДЕНЫ ПРОБЛЕМЫ:
- [описание проблем и предлагаемые решения]

СТАТУС: ✅ ГОТОВ / ❌ ТРЕБУЕТ ИСПРАВЛЕНИЯ
```

**Шаг 4: Расширенная итоговая сводка пакета**

После обработки всех 10 слов показать **ПОЛНУЮ СВОДКУ** с четкими указаниями:

```
📈 ИТОГИ ПАКЕТА (слова X-Y):
✅ Готовы к импорту: [количество] слов
❌ Требуют исправления: [количество] слов

🔧 ПРОБЛЕМНЫЕ СЛОВА - ЧТО НУЖНО РЕШИТЬ:
  - Слово X "[русское]/[английское]": [описание проблемы]
    → ВАРИАНТЫ: 1) "[вариант 1]" 2) "[вариант 2]" 3) "[вариант 3]"
    → РЕКОМЕНДАЦИЯ: вариант [номер]

  - Слово Y "[русское]/[английское]": [описание проблемы]
    → ВАРИАНТЫ: 1) "[вариант 1]" 2) "[вариант 2]" 3) "[вариант 3]"
    → РЕКОМЕНДАЦИЯ: вариант [номер]

📋 ОБНОВЛЕНИЯ ПРАВИЛ - ЧТО НУЖНО ДОБАВИТЬ:
  ✅ В EXAMPLE.md раздел "ДОПОЛНИТЕЛЬНЫЕ ПРАВИЛА":
    - "[конкретное правило 1]"
    - "[конкретное правило 2]"

  ❌ КРИТЕРИИ ТЕСТИРОВАНИЯ: обновления НЕ требуются
  ИЛИ
  ⚠️ КРИТЕРИИ ТЕСТИРОВАНИЯ: предлагаю добавить проверку на [что именно]

🎯 ЧТО ПОЛЬЗОВАТЕЛЮ НУЖНО РЕШИТЬ:
1. По слову X: выбрать вариант (1, 2 или 3) или предложить свой
2. По слову Y: выбрать вариант (1, 2 или 3) или предложить свой
3. Подтвердить обновления правил: да/нет/изменить
4. Подтвердить изменения критериев: да/нет (если нужны)

🎯 СЛЕДУЮЩИЙ ШАГ: Ожидание указаний пользователя по проблемным словам
```

**ПРИНЦИП ПОЛНОЙ ПРОЗРАЧНОСТИ**: Даже если пользователь не будет детально смотреть все таблицы тестирования, сводка должна дать полную картину того, что требует решения.

**📊 ОБЯЗАТЕЛЬНАЯ ТАБЛИЦА ТЕСТИРОВАНИЯ (ВСЕ 37 ЯЗЫКОВ):**

| Язык | Слово | Итоговое предложение | Одинаковый смысл | Именительный падеж | Простая грамматика | Естественность | Артикли | Заглавные | Пропуски | Статус |
|------|-------|---------------------|------------------|-------------------|-------------------|----------------|---------|-----------|----------|--------|
| RU   | хочу  | "Я хочу есть"       | ✅               | ✅                | ✅                | ✅             | N/A     | ✅        | ✅       | ✅     |
| EN   | want  | "I want to eat"     | ✅               | ✅                | ✅                | ✅             | N/A     | ✅        | ✅       | ✅     |
| DE   | will  | "Ich will essen"    | ✅               | ✅                | ✅                | ✅             | ✅      | ✅        | ✅       | ✅     |
| ... | ...   | ...                 | ...              | ...               | ...               | ...            | ...     | ...       | ...      | ...    |

**Критерии проверки:**
- **Одинаковый смысл**: предложение переводится с одинаковым смыслом
- **Именительный падеж**: ТОЛЬКО целевое слово (изучаемое) в именительном падеже, остальные слова могут быть в любых падежах
- **Простая грамматика**: подлежащее + сказуемое + объект (максимум 4-5 слов)
- **Естественность**: звучит как реальный пример, понятно ребенку, ПОЛНОЕ предложение (не "Я хочу", а "Я хочу воду")
- **Артикли**: соблюдение правил по артиклям из EXAMPLE.md
- **Заглавные**: правильные заглавные буквы
- **Пропуски**: корректная подстановка correct_answers вместо ___
4. **Показать результаты** в чате с детальными проблемами по языкам
5. **При обнаружении "проблемы"** → ОБЯЗАТЕЛЬНАЯ ПРОЦЕДУРА АНАЛИЗА:

   **5.1. Проверить: действительно ли это проблема?**
   - Сравнить с текущими правилами в инструкции
   - Подумать: создает ли это реальную сложность для A0?
   - Естественно ли звучит на этом языке?

   **5.2. Если НЕ проблема:**
   - Объяснить пользователю почему это НЕ проблема
   - **КОМПЛЕКСНЫЙ АНАЛИЗ**: где еще может проявиться похожая "проблема"?
     * Смежные языки (французские артикли → испанские/итальянские)
     * Похожие грамматические конструкции
     * Другие части речи с похожими особенностями
     * Устойчивые выражения в других языках
   - **Предложить КОНКРЕТНОЕ обновление `backend/data/words/EXAMPLE.md`:**
     * "Я понял что французские артикли (le/la) не проблема. Давайте добавим в раздел 'ДОПОЛНИТЕЛЬНЫЕ ПРАВИЛА': '- **Французские артикли**: le/la допустимы - простая система (добавлено: [дата], тест выявил ложное срабатывание)'?"
     * "Я понял что арабский артикль ال не проблема. Давайте добавим: '- **Арабский артикль**: ال допустим - единый простой артикль (добавлено: [дата], тест выявил ложное срабатывание)'?"
     * "Я понял что устойчивые выражения ('à la maison') не проблема. Давайте добавим: '- **Устойчивые выражения**: с артиклями допустимы если естественны (добавлено: [дата], тест выявил ложное срабатывание)'?"
   - Спросить подтверждение на обновление правил
   - Продолжить тест

   **5.3. Если ДЕЙСТВИТЕЛЬНО проблема:**
   - Объяснить почему это РЕАЛЬНАЯ проблема для A0
   - Предложить 2-3 варианта исправления **ВСЕГО концепта**
   - **КОМПЛЕКСНЫЙ АНАЛИЗ**: как предотвратить похожие проблемы?
     * В каких других языках может быть та же проблема?
     * Какие правила добавить чтобы избежать в будущем?
   - **Предложить КОНКРЕТНЫЕ новые правила для `backend/data/words/EXAMPLE.md`:**
     * "Найдена проблема с немецкими артиклями. Давайте добавим в 'ДОПОЛНИТЕЛЬНЫЕ ПРАВИЛА': '- **Немецкие артикли**: ИЗБЕГАТЬ der/die/das - сложная система + падежи (добавлено: [дата], реальная проблема в тесте)'?"
     * "Найдена проблема с падежными окончаниями. Давайте добавим: '- **Падежные окончания**: проверять именительный падеж в ru/pl/de/fi (добавлено: [дата], реальная проблема в тесте)'?"
     * "Найдена проблема с длинными предложениями. Давайте добавим: '- **Длина предложений**: максимум 5 слов для A0 (добавлено: [дата], реальная проблема в тесте)'?"

6. **Спросить подтверждение** у пользователя какой вариант выбрать
7. **Повторить проверку** выбранного варианта



**СООБЩЕНИЕ 2: Исправления и финализация**

**Шаг 5: Получение указаний пользователя**
1. **Пользователь дает указания** по проблемным словам:
   ```
   По слову X: используй конструкцию Y
   По слову Z: замени на паттерн W
   ```

**Шаг 6: Исправления и повторное тестирование**
1. **Исправить проблемные концепты** согласно указаниям пользователя
2. **Повторно протестировать ТОЛЬКО исправленные слова** по полной таблице
3. **Показать результаты исправлений** в чате

**Шаг 7: Создание JSON файлов и импорт**
1. **Создать JSON файлы** для всех 10 слов:
   ```bash
   cp data/words/templates/ultra_core_template.json data/words/A0/ultra_core_XX.json
   ```
2. **Заполнить переводы** для всех 37 языков
3. **Массовая валидация**:
   ```bash
   python -m scripts.words validate data/words/new/ultra_core_XX.json
   ```
4. **Массовый импорт**:
   ```bash
   python -m scripts.words import
   ```
5. **Обновление статусов** в md/A0_word_list.md: `⏳ TO_BE_CREATED` → `✅ COMPLETED`

**📊 РЕЗУЛЬТАТ ОПТИМИЗАЦИИ:**
- **Было**: 10 слов × 7 сообщений = 70 сообщений
- **Стало**: 2 сообщения на 10 слов
- **Ускорение**: в 35 раз при сохранении качества
- **Полная прозрачность**: все таблицы тестирования видны в чате
- **Контроль качества**: каждое слово проверяется по всем критериям

**Шаг 5: Создание JSON файла (УСТАРЕЛО - заменено на пакетную обработку)**


**Шаг 5: Валидация и импорт**
1. **Копирование для валидации**:
   ```bash
   cp data/words/A0/ultra_core_07.json data/words/new/ultra_core_07.json
   ```
2. **Валидация формата**:
   ```bash
   python -m scripts.words validate data/words/new/ultra_core_07.json
   ```
3. **Импорт в базу данных**:
   ```bash
   python -m scripts.words import
   ```
4. **Обновление статуса** (ТОЛЬКО после успешного импорта):
   - Открыть `md/A0_word_list.md`
   - Изменить статус: `⏳ TO_BE_CREATED` → `✅ COMPLETED`
   - Добавить количество языков: `37/37`

**ВАЖНО: Все новые правила добавляются ТОЛЬКО в `backend/data/words/EXAMPLE.md` раздел "ДОПОЛНИТЕЛЬНЫЕ ПРАВИЛА"**

**🔄 ПРИНЦИП НЕПРЕРЫВНОГО УЛУЧШЕНИЯ:**

**КАЖДАЯ "ОШИБКА" → УЛУЧШЕНИЕ СИСТЕМЫ:**
1. **Анализировать корень проблемы**: почему возникла "ошибка"?
2. **Предлагать улучшение правил**: как предотвратить в будущем?
3. **Думать системно**: какие смежные случаи могут быть?
4. **Спрашивать подтверждение**: "Давайте обновим инструкцию?"
5. **Документировать**: добавлять новые правила в инструкцию

**Шаблоны предложений для обновления инструкции:**

**Если НЕ проблема:**
- "Я понял что [конкретная особенность] не является проблемой, потому что [объяснение]. Давайте добавим в правила: '[новое правило]'?"
- "Это естественно звучит на [язык] и не создает сложности для A0. Предлагаю добавить исключение: '[правило]'?"

**Если РЕАЛЬНАЯ проблема:**
- "Найдена проблема: [описание] создает сложность для A0 потому что [объяснение]. Давайте добавим правило: '[новое ограничение]'?"
- "Это может вызвать путаницу у начинающих. Предлагаю добавить: '[правило избегания]'?"

**ПРИМЕР ПРОВЕРКИ JSON СТРУКТУРЫ:**
```
JSON: {"sentence": "___ работаю", "correct_answers": ["я"]}
Итоговое предложение: "Я работаю"
Проверка на fi: "Minä työskentelen" ✅
Проверка на de: "Ich arbeite" ✅
Результат: естественно на всех языках
```

**Примеры комплексного анализа:**
- **Артикли**: французские OK → испанские/итальянские/португальские тоже OK
- **Устойчивые выражения**: "à la maison" → "en casa", "a casa" тоже OK
- **Падежи**: проблема в русском → проверить польский/финский/немецкий
- **Агглютинация**: проблема в турецком → проверить финский/корейский
- **Тональность**: проблема в китайском → проверить вьетнамский/тайский
- **Письменность**: проблема в арабском → проверить хинди/тайский

**КРИТЕРИИ КАЧЕСТВА ПРЕДЛОЖЕНИЙ:**

✅ **ВСЕ ТЕХНИЧЕСКИЕ ТРЕБОВАНИЯ И КРИТЕРИИ КАЧЕСТВА:**
📋 **См. `backend/data/words/EXAMPLE.md` раздел "СПЕЦИАЛЬНЫЕ ПРАВИЛА ДЛЯ A0 УРОВНЯ"**

**Этот файл содержит только процедуру тестирования. Все правила создания предложений - в EXAMPLE.md.**

**Примеры предложений см. в `backend/data/words/EXAMPLE.md`**

#### 6. Примеры ПРАВИЛЬНЫХ A0 конструкций

**✅ Безопасные примеры (проверены по всем 37 языкам):**

```json
{
  "concept_id": "uuid-1",
  "word": "мама",
  "language": "ru",
  "examples": [{"sentence": "___ работает", "correct_answers": ["мама"]}]
}
```

**Проверка по сложным языкам:**
- 🇷🇺 Русский: "мама" ✅ (им.п.)
- 🇩🇪 Немецкий: "Mutter arbeitet" → "Mutter" ✅ (им.п.)
- 🇫🇮 Финский: "äiti työskentelee" → "äiti" ✅ (без суффиксов)
- 🇹🇷 Турецкий: "anne çalışıyor" → "anne" ✅ (без суффиксов)
- 🇵🇱 Польский: "mama pracuje" → "mama" ✅ (им.п.)

#### 7. Связывание через concept_id
Все переводы одного концепта должны иметь одинаковый `concept_id`:

```json
[
  {
    "concept_id": "550e8400-e29b-41d4-a716-************",
    "word": "мама",
    "language": "ru",
    "examples": [{"sentence": "___ работает", "correct_answers": ["мама"]}]
  },
  {
    "concept_id": "550e8400-e29b-41d4-a716-************",
    "word": "mom",
    "language": "en",
    "examples": [{"sentence": "___ works", "correct_answers": ["mom"]}]
  },
  {
    "concept_id": "550e8400-e29b-41d4-a716-************",
    "word": "Mutter",
    "language": "de",
    "examples": [{"sentence": "___ arbeitet", "correct_answers": ["Mutter"]}]
  }
]
```

#### 8. Утвержденный список A0 слов с приоритетами
> **📋 ПОЛНЫЙ СПИСОК**: См. [md/A0_word_list.md](A0_word_list.md) - единственный источник истины для A0 слов

**🚨 ВАЖНО: ИСПРАВЛЕННЫЙ СПИСОК A0 СЛОВ**

**📋 ПРАВИЛЬНАЯ СТРУКТУРА A0 (155 слов):**

## 📋 Полный список A0 слов

> **🎯 ЕДИНСТВЕННЫЙ ИСТОЧНИК ИСТИНЫ**: [md/A0_word_list.md](A0_word_list.md)

**Все 146 слов A0 уровня с UUID, статусами и приоритетами находятся в файле [A0_word_list.md](A0_word_list.md)**

### Структура приоритетов:
- **🔥 ULTRA-CORE (15 слов)** - "первые слова ребенка", абсолютный минимум
- **🟡 CORE (35 слов)** - базовый набор для выживания
- **📚 EXTENDED (96 слов)** - расширенный набор для полноценного общения

**Поле priority в JSON:**
```json
{
  "concept_id": "uuid-123",
  "word": "я",
  "level": "A0",
  "priority": "ultra_core",    // "ultra_core", "core" или "extended"
  "examples": [...]
}
```

**Статистика:**
- Слов в списке: 146 (15 ULTRA-CORE + 35 CORE + 96 EXTENDED) - добавлены критически важные слова
- Записей в БД: 146 × 37 языков = 5,402 записи
- Категорий: 22 основные группы

#### 9. Разнообразие примеров для A0
Стремитесь к разнообразию конструкций, но в рамках безопасных правил:

**✅ Хорошее разнообразие:**
- "___ лает" (действие)
- "___ большой" (описание)
- "___ здесь" (местоположение)
- "Это ___" (указание)
- "___ работает" (профессиональная деятельность)
- "___ светит" (природные явления)
- "___ холодная" (физические свойства)

**❌ Плохое разнообразие (нарушает правила):**
- "Моя ___" (притяжательность)
- "Я вижу ___" (падежи)
- "Иду к ___" (падежи)
- "Думаю о ___" (падежи)

## Идеи на будущее
- Сделать валидацию имени файла со словами
- Автоматическая проверка базовых форм для сложных языков

## Реализованные улучшения

- [x] Проверка на существование слова перед импортом (на основе полей `word` и `language`)
- [x] Импорт только новых файлов из директории `backend/data/words/new/`
- [x] Автоматическое перемещение обработанных файлов в `imported/` или `invalid/`
- [x] Подробное логирование в `backend/logs/import_words.log`
- [x] Удобный интерфейс командной строки с подкомандами

## Дополнительные команды

Запускаются из корневой директории бэкенда

```bash
# Проверка подключения к БД
python -m scripts.words check_db

# Просмотр недавно добавленных слов
python -m scripts.words check_recent --minutes 5

# Очистка коллекции (с подтверждением)
python -m scripts.words clear_words

# Принудительная очистка (без подтверждения)
python -m scripts.words clear_words --force
```

> **Примечание:** Перед выполнением команд убедитесь, что вы активировали виртуальное окружение (см. шаг 3 в разделе 'Процесс добавления слов').

## Структура папок

### 📁 Новая организованная структура (обновлено 2025-06-14)

```
backend/data/words/
├── A0/                          # Папка для A0 слов (разработка)
│   ├── README.md               # Документация по файлам A0
│   ├── ultra_core_01-03.json  # Концепты 1-3 (я, ты, да)
│   ├── ultra_core_04-06.json  # Концепты 4-6 (нет, что, где)
│   ├── ultra_core_07-09.json  # Концепты 7-9 (когда, как, почему)
│   ├── ultra_core_10-12.json  # Концепты 10-12 (хорошо, плохо, большой)
│   ├── ultra_core_13-15.json  # Концепты 13-15 (маленький, есть, пить)
│   ├── core_01-03.json        # CORE слова 1-3
│   └── extended_01-03.json    # EXTENDED слова 1-3
├── A1/                         # Папка для A1 слов (в будущем)
├── new/                        # Готовые к импорту файлы
├── imported/                   # Успешно импортированные файлы
├── invalid/                    # Файлы с ошибками
└── logs/import_words.log       # Логи импорта
```

### 🎯 Принципы организации:

1. **По уровням**: отдельная папка для каждого уровня (A0, A1, B1, etc.)
2. **По концептам**: 1 концепт на файл (1 слово × 37 языков = 37 записей)
3. **Документация**: README.md в каждой папке уровня
4. **Workflow**: разработка в папке уровня → копирование в `new/` → импорт → архив в `imported/`

### 📋 Обязательное тестирование после создания каждого файла:

**КРИТИЧЕСКИ ВАЖНО**: После создания каждого JSON файла ОБЯЗАТЕЛЬНО проводить тестирование по ВСЕМ 37 языкам согласно инструкциям в этом файле (раздел "Этап 1: Тестирование концептов").

#### 🤖 ПРОЦЕДУРА ТЕСТИРОВАНИЯ JSON ФАЙЛА В ЧАТЕ LLM:

**🚨 ИНСТРУКЦИЯ ДЛЯ LLM АССИСТЕНТА:**
**ПЕРЕД НАЧАЛОМ ТЕСТИРОВАНИЯ ОБЯЗАТЕЛЬНО ПРОЧИТАТЬ ФАЙЛ [EXAMPLE.md](../backend/data/words/EXAMPLE.md#специальные-правила-для-a0-уровня) И ВЗЯТЬ ВСЕ КРИТЕРИИ ТЕСТИРОВАНИЯ ОТТУДА. НЕ ПРИДУМЫВАТЬ КРИТЕРИИ САМОСТОЯТЕЛЬНО!**

**ОБЯЗАТЕЛЬНО выполнять КАЖДЫЙ РАЗ после создания JSON файла:**

1. **📂 Открыть JSON файл** и изучить структуру
2. **🧠 ПОЛНЫЙ АНАЛИЗ ПО ВСЕМ КРИТЕРИЯМ A0** для каждого концепта в режиме чата:
   - Взять `sentence` и `correct_answers[0]` для каждого языка
   - Мысленно подставить `correct_answers[0]` вместо `___`
   - **🚨 КРИТИЧЕСКИ ВАЖНО ДЛЯ LLM**: ОБЯЗАТЕЛЬНО взять критерии тестирования из файла **[EXAMPLE.md](../backend/data/words/EXAMPLE.md#специальные-правила-для-a0-уровня)** раздел "СПЕЦИАЛЬНЫЕ ПРАВИЛА ДЛЯ A0 УРОВНЯ"
   - **НЕ ПРИДУМЫВАТЬ критерии самостоятельно** - использовать ТОЛЬКО из EXAMPLE.md
   - **НЕ ИСПОЛЬЗОВАТЬ Python скрипты** - только анализ в чате LLM

3. **📊 ОБЯЗАТЕЛЬНАЯ ТАБЛИЦА ТЕСТИРОВАНИЯ:**

| Язык | Слово | Итоговое предложение | Смысл | Падеж | Грамматика | Естественность | Артикли | Заглавные | Пропуски | Статус |
|------|-------|---------------------|-------|-------|------------|----------------|---------|-----------|----------|--------|
| 🇷🇺 RU | где | "Где дом?" | ✅ | ✅ | ✅ | ✅ | N/A | ✅ | ✅ | ✅ |
| 🇬🇧 EN | where | "Where is home?" | ✅ | ✅ | ✅ | ✅ | N/A | ✅ | ✅ | ✅ |
| 🇩🇪 DE | wo | "wo ist Haus?" | ✅ | ✅ | ✅ | ✅ | ✅ | ❌ | ✅ | ⚠️ |

**📋 КРИТЕРИИ ТЕСТИРОВАНИЯ:**
- **Все критерии подробно описаны в [EXAMPLE.md](../backend/data/words/EXAMPLE.md#специальные-правила-для-a0-уровня)**
- **Базовые требования**: одинаковый смысл, именительный падеж, простая грамматика, естественность
- **Правила по артиклям**: избегать немецких der/die/das, допустимы романские и арабские
- **Формат предложений**: правильные заглавные буквы, корректная подстановка пропусков
- **Логика проверки**: подставить correct_answers[0] вместо ___ и проверить итоговое предложение

4. **🔍 АНАЛИЗ КАЖДОЙ ПРОБЛЕМЫ** согласно процедуре:
   - **Проверить**: действительно ли это проблема?
   - **Объяснить**: почему это проблема или НЕ проблема
   - **Предложить**: обновление правил если нужно

5. **📝 ОБЯЗАТЕЛЬНЫЕ ВЫВОДЫ**:
   - ✅ "Все концепты соответствуют всем критериям A0 из EXAMPLE.md"
   - ⚠️ "Найдены проблемы: [список] - требуют исправления"
   - 🔄 "Предлагаю обновить правила в EXAMPLE.md: [конкретные правила]"

6. **📋 ОБЯЗАТЕЛЬНОЕ ОБНОВЛЕНИЕ ДОКУМЕНТАЦИИ ПОСЛЕ УСПЕШНОГО ТЕСТИРОВАНИЯ**:

   **🚨 КРИТИЧЕСКИ ВАЖНО**: После успешного тестирования JSON файла ОБЯЗАТЕЛЬНО обновить файл `/backend/data/words/A0/README.md`:

   **Что обновить:**
   - ✅ Изменить статус с "⏳ TO BE CREATED" на "✅ COMPLETED"
   - ✅ Добавить таблицу с концептами (UUID, русские слова, английские слова, описания)
   - ✅ Обновить прогресс (X/15 ULTRA-CORE, X/37 CORE, X/93 EXTENDED)

   **Пример обновления:**
   ```markdown
   ### `ultra_core_07-09.json` - Basic Needs & Actions
   | Concept ID | Russian | English | Description |
   |------------|---------|---------|-------------|
   | 550e8400-e29b-41d4-a716-************ | хочу | want | Basic desire |
   | 550e8400-e29b-41d4-a716-************ | есть (кушать) | eat | Basic action |
   | 550e8400-e29b-41d4-a716-************ | пить | drink | Basic action |
   ```

   **В разделе статуса изменить:**
   ```markdown
   - ✅ **ultra_core_07-09.json** - COMPLETED (хочу, есть, пить) - 9/15 ULTRA-CORE
   ```

**ШАБЛОН ТЕСТИРОВАНИЯ:**
```
🔍 ПОЛНОЕ ТЕСТИРОВАНИЕ JSON ФАЙЛА: [filename]
================================================
📊 Концептов: X | Записей: Y | Языков: 22

[Таблица результатов по всем языкам]

📋 АНАЛИЗ ПРОБЛЕМ:
- Проблема 1: [анализ] → [вердикт: проблема/не проблема]
- Проблема 2: [анализ] → [вердикт: проблема/не проблема]

✅ ИТОГОВЫЙ ВЕРДИКТ: [готов к импорту / требует исправлений]
🔄 ОБНОВЛЕНИЯ ПРАВИЛ: [предложения или "не требуются"]
```

### 🔄 Рабочий процесс:

> **🎯 ЧЕТКИЙ WORKFLOW**: A0/ (создание) → тестирование в чате → валидация → new/ (импорт)

#### **Этап 1: Создание в папке A0/**
1. **Разработка**: создание JSON файлов в папке уровня `A0/`
   - Файлы остаются в `A0/` как **ОРИГИНАЛЫ/МАСТЕР-КОПИИ**
   - НЕ копируем в `new/` до полной готовности

#### **Этап 2: Тестирование в чате**
2. **🤖 ТЕСТИРОВАНИЕ В ЧАТЕ**: ОБЯЗАТЕЛЬНАЯ проверка по всем 37 языкам
   - Проверка по критериям A0 из этого файла
   - Анализ естественности, грамматики, падежей
   - **НЕЛЬЗЯ пропускать!** Критически важно для качества

#### **Этап 3: Исправления**
3. **Исправления**: если найдены проблемы
   - Исправить файлы в `A0/`
   - Повторить тестирование в чате
   - Продолжать до полного соответствия критериям

#### **Этап 4: Валидация**
4. **Копирование для валидации**: `cp A0/ultra_core_07-09.json new/ultra_core_07-09.json`
5. **Валидация**: `python scripts/words/validate_word.py new/ultra_core_07-09.json`
   - Проверка технического формата JSON
   - Проверка обязательных полей
   - Проверка UUID и кодов языков

#### **Этап 5: Импорт**
6. **Импорт**: `python scripts/words/import_words.py new/ultra_core_07-09.json`
7. **Архив**: файл автоматически перемещается в `imported/`

**⚠️ КРИТИЧЕСКИ ВАЖНО**:
- Файлы в `A0/` - это **ОРИГИНАЛЫ**, не трогаем после создания
- В `new/` копируем **ТОЛЬКО ГОТОВЫЕ** файлы для импорта
- Тестирование в чате **ОБЯЗАТЕЛЬНО** для каждого файла
