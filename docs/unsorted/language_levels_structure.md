# Структура уровней языков (Language Levels Structure)

## Обзор

Система поддерживает изучение множественных языков с индивидуальными уровнями для каждого языка.

## Структура данных пользователя

```json
{
  "email": "<EMAIL>",
  "native_language": "ru",           // Родной язык пользователя
  "learning_languages": ["en", "es", "de"],  // Все изучаемые языки
  "settings": {
    "target_language": "en",         // Язык текущей сессии изучения (TODO: переименовать в session_language)
    "daily_goal": 20,
    "language_levels": {             // Уровни по каждому изучаемому языку
      "en": "A0",                    // Английский - уровень A0
      "es": "A1",                    // Испанский - уровень A1
      "de": "B1"                     // Немецкий - уровень B1
    }
  }
}
```

## Ключевые концепции

### 1. Родной язык (native_language)
- Язык, на котором говорит пользователь
- Используется для переводов и интерфейса
- Один язык на пользователя

### 2. Изучаемые языки (learning_languages)
- Массив всех языков, которые изучает пользователь
- Может содержать множество языков
- Каждый язык имеет свой уровень в `language_levels`

### 3. Язык текущей сессии (target_language)
- **ВАЖНО**: Это язык для ТЕКУЩЕЙ сессии изучения
- Определяет, какие карточки показывать пользователю
- Пользователь может переключаться между языками
- **TODO**: Переименовать в `session_language` для ясности

### 4. Уровни языков (language_levels)
- Объект, где ключ = код языка, значение = уровень
- Каждый изучаемый язык имеет свой независимый уровень
- Поддерживаемые уровни: A0, A1, A2, B1, B2, C1, C2

## Примеры использования

### Получение текущего уровня
```javascript
const currentLanguage = user.settings.target_language; // "en"
const currentLevel = user.settings.language_levels[currentLanguage]; // "A0"
```

### Обновление уровня текущего языка
```javascript
const targetLang = user.settings.target_language;
const newLevels = {
  ...user.settings.language_levels,
  [targetLang]: "A1"  // Повышаем уровень
};
```

### Добавление нового языка
```javascript
const newLanguage = "fr";
const updatedLevels = {
  ...user.settings.language_levels,
  [newLanguage]: "A0"  // Начинаем с A0
};
const updatedLearningLanguages = [
  ...user.learning_languages,
  newLanguage
];
```

## API Endpoints

### Получение карточек для изучения
```
GET /api/spaced/{user_id}/card
```
Использует:
- `user.settings.target_language` - какой язык изучаем
- `user.settings.language_levels[target_language]` - какой уровень

### Обновление настроек пользователя
```
PATCH /api/users/me
{
  "settings": {
    "target_language": "es",
    "language_levels": {
      "en": "A1",
      "es": "A0"
    }
  }
}
```

## Миграция данных

### Старая структура (до миграции)
```json
{
  "settings": {
    "target_language": "en",
    "language_level": "A0"  // Одно поле для всех языков
  }
}
```

### Новая структура (после миграции)
```json
{
  "settings": {
    "target_language": "en",
    "language_levels": {
      "en": "A0"  // Уровень привязан к конкретному языку
    }
  }
}
```

## Будущие улучшения

1. **Переименование target_language → session_language**
   - Более понятное название
   - Подчеркивает, что это для текущей сессии

2. **UI для переключения языков**
   - Выбор языка сессии в главном меню
   - Отображение прогресса по всем языкам

3. **Статистика по языкам**
   - Прогресс изучения каждого языка
   - История изменения уровней

## Важные замечания

⚠️ **target_language** - это язык ТЕКУЩЕЙ сессии, не постоянная настройка!

✅ **language_levels** - постоянное хранилище уровней по всем языкам

🔄 При переключении языка сессии меняется только `target_language`, уровни сохраняются
