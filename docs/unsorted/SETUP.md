# Начальная настройка проекта

## 🛠️ Требования

- Node.js 18+
- Python 3.10+
- MongoDB 6.0+ (рекомендуется MongoDB Atlas)
- Expo CLI
- Учетная запись MongoDB Atlas (бесплатный тариф доступен)

## 🚀 Быстрый старт

### 1. Клонирование репозитория

```bash
git clone <repo-url>
cd EnApp
```

### 2. Настройка фронтенда

1. Установите зависимости:
   ```bash
   cd frontend
   npm install
   ```

2. Запустите приложение:
   ```bash
   expo start
   # или с туннелем (если используете VPN)
   npx expo start --tunnel
   ```

### 3. Настройка бэкенда

1. **Настройка переменных окружения**:
   - Скопируйте файл `.env.example` в `.env`:
     ```bash
     cd backend
     cp .env.example .env
     ```
   - Откройте файл `.env` и замените значения на свои:
     - `MONGODB_URL` - строка подключения к вашему кластеру MongoDB
     - `DATABASE_NAME` - имя базы данных (по умолчанию `word_master`)
     - `SECRET_KEY` - секретный ключ для JWT токенов

   > ⚠️ **Важно**: Файл `.env` находится в `.gitignore` и не должен попадать в репозиторий.
   > Все конфиденциальные данные должны храниться только локально.

2. **Настройка виртуального окружения**:
   Виртуальное окружение уже добавлено в `.gitignore` (папка `venv/`), поэтому его не нужно добавлять вручную.
   
   Создайте и активируйте виртуальное окружение:
   ```bash
   cd backend
   
   # Для macOS/Linux
   python3 -m venv venv
   source venv/bin/activate
   
   # Для Windows
   python -m venv venv
   .\venv\Scripts\activate
   ```

3. Установите зависимости:
   ```bash
   pip install -r requirements.txt
   ```

4. Запустите сервер разработки:
   ```bash
   uvicorn app.main:app --reload
   ```

### 4. Импорт слов в базу данных

См. [документацию по добавлению слов](vocabulary/_WORDS_CREATION_GUIDE_WORKFLOW.md)

## 🔍 Проверка установки

1. Фронтенд должен быть доступен по адресу: http://localhost:19006
2. Бэкенд API должен быть доступен по адресу: http://localhost:8000/docs

## 🆘 Получение помощи

Если у вас возникли проблемы с настройкой, проверьте:

1. Правильность строки подключения к MongoDB
2. Установлены ли все зависимости
3. Открыты ли необходимые порты (8000 для бэкенда, 19006 для фронтенда)
4. Логи в `backend/logs/` для бэкенда
