# Подробное руководство по интеграции системы интервального повторения

## Оглавление
1. [Введение](#введение)
2. [API Endpoints](#api-endpoints)
3. [Интеграция во фронтенд](#интеграция-во-фронтенд)
4. [Обработка ошибок](#обработка-ошибок)
5. [Рекомендации по производительности](#рекомендации-по-производительности)
6. [Тестирование](#тестирование)

## Введение

Данный документ содержит подробные инструкции по интеграции системы интервального повторения в приложение EnApp.

## API Endpoints

### 1. Получение следующей карточки
```
GET /api/spaced/next?user_id=USER_ID&native_lang=ru&target_lang=en
```

### 2. Отправка ответа пользователя
```
POST /api/cards/{card_id}/review
```

### 3. Получение случайной карточки
```
GET /api/cards/random?native_lang=ru&target_lang=en&level=A1
```

## Интеграция во фронтенд

### 1. Обновление сервиса API

Добавьте следующие методы в ваш API сервис:

```typescript
// services/api.ts

export const fetchNextCard = async (userId: string): Promise<Card> => {
  const response = await fetch(`/api/spaced/next?user_id=${userId}`);
  return response.json();
};

export const submitCardResponse = async (cardId: string, rating: number): Promise<void> => {
  await fetch(`/api/cards/${cardId}/review`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ rating })
  });
};
```

### 2. Обновление компонента TrainingScreen

```typescript
// screens/TrainingScreen.tsx

const TrainingScreen = () => {
  const [currentCard, setCurrentCard] = useState<Card | null>(null);
  const { user } = useAuth();

  const loadNextCard = async () => {
    if (!user) return;
    const card = await fetchNextCard(user.id);
    setCurrentCard(card);
  };

  const handleResponse = async (rating: number) => {
    if (!currentCard) return;
    await submitCardResponse(currentCard.id, rating);
    loadNextCard();
  };
};
```

## Обработка ошибок

Добавьте обработку следующих сценариев:
- Отсутствие интернет-соединения
- Невалидные ответы сервера
- Истекший токен авторизации

## Рекомендации по производительности

1. Используйте кеширование карточек
2. Реализуйте предзагрузку следующей карточки
3. Оптимизируйте перерисовки с помощью React.memo

## Тестирование

Рекомендуется покрыть тестами:
- Корректность отображения интервалов
- Обработку ответов пользователя
- Загрузку и отображение карточек

## Дополнительные материалы

- [Документация по API](API_DOCS.md)
- [Гайд по отладке](DEBUG_GUIDE.md)
