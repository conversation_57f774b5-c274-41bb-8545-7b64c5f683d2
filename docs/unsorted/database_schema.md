# Database Schema

## Оглавление
1. [Обзор структуры](#обзор-структуры)
2. [База данных: words_db](#база-данных-words_db)
   - [Коллекция words_en](#коллекция-words_en)
   - [Коллекция words_ru](#коллекция-words_ru)
3. [База данных: users_db](#база-данных-users_db)
   - [Коллекция users](#коллекция-users)
   - [Коллекция user_words](#коллекция-user_words)
4. [Примеры запросов](#примеры-запросов)
5. [Индексы](#индексы)
6. [Принципы работы](#принципы-работы)

## Обзор структуры

```
БАЗЫ ДАННЫХ:

[БАЗА ДАННЫХ] words_db/           # Все словарные данные
├── [КОЛЛЕКЦИЯ] words/            # Все слова всех языков
│   ├── Документ 1:
│   ├── Документ 2:
│   └── ...
│
[БАЗА ДАННЫХ] users_db/           # Данные пользователей
├── [КОЛЛЕКЦИЯ] users/           # Профили пользователей
└── [КОЛЛЕКЦИЯ] user_words/      # Прогресс изучения
```

## База данных: `words_db`

### Назначение
Хранение всех словарных данных в единой коллекции `words`. Каждое слово связано с переводами через общий `concept_id`.

Каждое слово должно было независимым документом, а связи между переводами устанавливались через поле concept_id. Но при создании важно не потерять связь между ними с помощью concept_id. Поэтому при добавлении нового языка надо сделать механизм анализа. 

### Коллекция: `words`

Инструкция по добавлению новых слов в vocabulary/_WORDS_CREATION_GUIDE_WORKFLOW.md

#### Схема документа

Полное описание схемы документа и примеры использования находятся в файле [EXAMPLE.md](../../backend/data/words/EXAMPLE.md).

Примеры предложений в "sentence" создаются из слов текущего уровня и с грамматикой из уровня. Или на 1 ниже если это возможно. Потому что важно чтобы пользователь не видел тяжелый предложения с тяжелой граматикой. Он их пока не знает. 


**Как это работает:**

1. **concept_id** - уникальный идентификатор концепта, общий для всех переводов слова
   - Позволяет легко находить все переводы слова в любых языках
   - Новый язык автоматически становится доступен для изучения со всех существующих языков
   - Не требует обновления существующих записей при добавлении нового языка
   - В формате UUID

2. **Пример запроса для получения всех переводов слова:**
   ```javascript
   // Найти все переводы слова с concept_id = "abc123"
   db.words.find({ concept_id: "abc123" })
   ```

3. **Пример запроса для поиска перевода на определенный язык:**
   ```javascript
   // Найти русский перевод английского слова "name"
   const enWord = db.words.findOne({ word: "name", language: "en" });
   const ruWord = db.words.findOne({ 
     concept_id: enWord.concept_id, 
     language: "ru" 
   });
   ```

4. **Преимущества:**
   - Масштабируемость: добавление нового языка не требует изменений в существующих данных
   - Гибкость: любой язык может быть как исходным, так и целевым
   - Производительность: быстрый поиск всех переводов слова
   - Поддержка N языков: каждый новый язык автоматически становится доступен для изучения со всех существующих языков и наоборот

## Принципы работы

### Многоязычность
- Все слова хранятся в единой коллекции `words`
- Связи между переводами через общее поле `concept_id`
- Поддержка неограниченного количества языков
- Любой язык может быть как изучаемым, так и родным

### Отслеживание прогресса
- Для каждого пользователя ведется отдельная запись прогресса по каждому слову
- Адаптивная система интервального повторения
- Детальная статистика ответов

### Пример работы с карточкой
1. Пользователь (родной: ru, изучает: en) запрашивает новую карточку
2. Система находит слово на английском (`language: "en"`)
3. По `concept_id` находится перевод на русский (`language: "ru"`)
4. Возвращаются объединенные данные на фронтенд

### Добавление нового языка
1. Добавляем слова нового языка в коллекцию `words` с новыми `concept_id`
2. Для существующих слов добавляем переводы с теми же `concept_id`
3. Новый язык автоматически становится доступен для изучения со всех существующих языков




#### Индексы:
```javascript
// Для быстрого поиска по языку и слову
db.words.createIndex({ "language": 1, "word": 1 });

// Для поиска всех переводов слова
db.words.createIndex({ "concept_id": 1 });

// Для поиска по уровню сложности
db.words.createIndex({ "language": 1, "level": 1 });
```

### Примеры запросов

1. **Найти все переводы слова:**
   ```javascript
   // Найти все переводы слова "name" на английском
   const word = db.words.findOne({ word: "name", language: "en" });
   const translations = db.words.find({ 
     concept_id: word.concept_id,
     language: { $ne: "en" } // Исключаем исходный язык
   }).toArray();
   ```

2. **Найти случайное слово определенного уровня:**
   ```javascript
   // Найти случайное слово уровня A1 на английском
   const word = db.words.aggregate([
     { $match: { language: "en", level: "A1" } },
     { $sample: { size: 1 } }
   ]);
   ```

3. **Добавление нового языка:**
   ```javascript
   // Добавляем новое слово на тайском
   db.words.insertOne({
     concept_id: "abc123", // Тот же ID, что и у других переводов
     word: "ชื่อ",
     language: "th",
     level: "A1",
     // ... остальные поля
   });
   ```

## База данных: `users_db`

### Назначение
Хранение данных пользователей и их прогресса в изучении слов. Разделение на отдельную базу данных улучшает безопасность и масштабируемость.

### Коллекция: `users`
```json
{
  "_id": ObjectId,
  "email": "<EMAIL>",
  "password_hash": "$2b$12$...",
  "name": "Иван Иванов",
  "native_language": "ru",
  "learning_languages": ["en", "es"],
  "settings": {
    "daily_goal": 20,
    "notifications_enabled": true,
    "difficulty_level": "beginner"
  },
  "created_at": ISODate(),
  "last_login": ISODate()
}
```

### Коллекция: `user_progress`

Хранит индивидуальный прогресс пользователя по каждому изучаемому слову для интервального повторения.

```json
{
  "_id": ObjectId,
  "user_id": "user123",               // ID пользователя
  "concept_id": "abc123",             // ID концепта (общий для всех языков)
  "from_language": "ru",              // Язык, с которого изучает
  "to_language": "en",                // Язык, который изучает
  "next_review": "2025-06-09T12:00",  // Когда нужно повторить
  "interval": 3,                      // Интервал в днях
  "repetition": 4,                    // Количество повторений
  "ease_factor": 2.5,                 // Коэффициент лёгкости (для SM-2)
  "last_review": "2025-06-06T15:00",  // Дата последнего повторения
  "status": "learning",               // Статус (new, learning, mastered)
  "created_at": ISODate(),
  "updated_at": ISODate()
}
```

**Ключевые особенности:**
- Один документ = одно слово для одного пользователя в одном направлении перевода
- Поддержка разных направлений перевода (например, EN→RU и RU→EN для одного слова)
- Гибкая система интервального повторения

**Рекомендуемые индексы:**
```javascript
// Для быстрого поиска слов на повторение
db.user_progress.createIndex({ 
  "user_id": 1, 
  "next_review": 1 
});

// Для уникальности прогресса по слову
db.user_progress.createIndex({ 
  "user_id": 1, 
  "concept_id": 1, 
  "from_language": 1, 
  "to_language": 1 
}, { unique: true });
```

## Примеры запросов

### 1. Получение случайного слова для изучения
```javascript
// 1. Находим слово на изучаемом языке (например, английском)
const enWord = db.words.aggregate([
  { $match: { language: "en", level: "A1" } },
  { $sample: { size: 1 } }
]).next();

// 2. Получаем все переводы через concept_id
const translations = db.words.find({ 
  concept_id: enWord.concept_id,
  language: { $ne: "en" } // Исключаем исходный язык
}).toArray();

// 3. Получаем перевод на родной язык пользователя
const nativeTranslation = await db.words.findOne({
  concept_id: enWord.concept_id,
  language: "ru" // Родной язык пользователя
});
```

### 2. Обновление прогресса изучения слова
```javascript
// Увеличиваем счетчик правильных ответов и обновляем интервал
await db.user_words.updateOne(
  { 
    user_id: ObjectId("..."),
    concept_id: "abc123",
    language: "en"
  },
  {
    $inc: { "progress.correct_answers": 1 },
    $set: { 
      "progress.interval_level": 5,  // Новый уровень интервала
      "progress.next_review": new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // +3 дня
      "progress.last_reviewed": new Date(),
      "updated_at": new Date()
    }
  },
  { upsert: true }  // Создать запись, если не существует
);
```

### 3. Получение слов для повторения
```javascript
const wordsToReview = await db.user_words.aggregate([
  {
    $match: {
      user_id: ObjectId("..."),
      "progress.next_review": { $lte: new Date() }
    }
  },
  { $lookup: {
      from: "words",
      let: { concept_id: "$concept_id", lang: "$language" },
      pipeline: [
        { $match: {
            $expr: {
              $and: [
                { $eq: ["$concept_id", "$$concept_id"] },
                { $eq: ["$language", "$$lang"] }
              ]
            }
        }}
      ],
      as: "word_data"
  }},
  { $unwind: "$word_data" },
  { $limit: 10 } // Лимит слов за раз
]).toArray();
```

## Индексы

Для оптимальной производительности рекомендуются следующие индексы:

```javascript
// Для быстрого поиска по языку и уровню
db.words.createIndex({ "language": 1, "level": 1 });

// Для поиска по concept_id
db.words.createIndex({ "concept_id": 1 });

// Для поиска по слову (регистронезависимый поиск)
db.words.createIndex({ "word": 1 }, { collation: { locale: 'en', strength: 2 } });

// Для быстрого поиска прогресса пользователя
db.user_words.createIndex({ 
  "user_id": 1, 
  "concept_id": 1,
  "language": 1 
}, { unique: true });

db.user_words.createIndex({ 
  "user_id": 1, 
  "progress.next_review": 1 
});
```

## Масштабируемость

1. **Шардирование**: При большом количестве данных можно шардировать по полю `language`
2. **Репликация**: Настроить репликацию для отказоустойчивости
3. **Кэширование**: Использовать Redis для кэширования часто запрашиваемых слов
4. **Архивация**: Старые или редко используемые слова можно перемещать в архивную коллекцию и искать слово

2. **Прогресс изучения**:
   - При первом показе слова создается запись со статусом `new`
   - При правильном ответе увеличивается `interval_level`
   - При ошибке `interval_level` сбрасывается или уменьшается

3. **Индексы для оптимизации**:
   ```javascript
   // Для быстрого поиска слов на повторение
   db.user_words.createIndex({
     "user_id": 1,
     "language": 1,
     "progress.next_review": 1
   })
   
   // Для поиска прогресса по конкретному слову
   db.user_words.createIndex({
     "user_id": 1,
     "word_id": 1,
     "language": 1
   }, { unique: true })
   ```

## Примеры запросов

1. Найти слово и его переводы:
```javascript
// 1. Находим английское слово
const enWord = db.words_en.findOne({ word: "name" })

// 2. Получаем все связанные переводы
const translations = enWord.relations.map(async rel => {
  const collection = db[`words_${rel.language}`]
  return await collection.findOne({ _id: rel.word_id })
})
```

2. Получить карточку для обучения:
```javascript
async function getLearningCard(userId, nativeLang, targetLang) {
  // 1. Находим слово, которое нужно повторить
  const wordToReview = await db.user_words.findOne({
    user_id: userId,
    "progress.next_review": { $lte: new Date() },
    language: targetLang
  }).sort({ "progress.next_review": 1 })

  // 2. Получаем слово на целевом языке
  const targetWord = await db[`words_${targetLang}`].findOne({ 
    _id: wordToReview.word_id 
  })

  // 3. Находим перевод на родной язык
  const nativeRelation = targetWord.relations.find(
    r => r.language === nativeLang
  )
  const nativeWord = await db[`words_${nativeLang}`].findOne({
    _id: nativeRelation.word_id
  })

  // 4. Возвращаем карточку
  return {
    target: {
      word: targetWord.word,
      example: targetWord.examples[0]
    },
    native: {
      word: nativeWord.word,
      example: nativeWord.examples[0]
    }
  }
}
```
