# Анализ оптимизации запросов к базе данных

## 🎯 Задача 4.2.6: Оптимизация запросов

### 📊 Результаты тестирования производительности

**Размер данных:**
- words: 162 документа
- user_progress: 5 документов

**Производительность $sample:**
- На всей коллекции: 1803ms
- С фильтром по языку: 51ms ✅
- С $nin (5 элементов): 52ms ✅
- С $nin (100 элементов): 53ms ✅
- count + skip альтернатива: 152ms (в 3 раза медленнее)

**Подготовка данных:**
- Текущий подход: 832ms
- Оптимизированная агрегация: 330ms ✅ (в 2.5 раза быстрее)

## 🔍 Анализ проблемы

### ❌ $sample НЕ является проблемой
Вопреки утверждению в todo.md, `$sample` работает быстро (50-53ms) при наличии правильных индексов и фильтров.

### ✅ Реальные проблемы производительности:

1. **Подготовка данных** (832ms) - основная проблема
2. **Отсутствие оптимизированных индексов** для некоторых запросов
3. **Неэффективные запросы** для получения списка изученных слов

## 💡 Рекомендации по оптимизации

### 1. Оптимизация подготовки данных ⭐⭐⭐ (DONE)
**Приоритет: ВЫСОКИЙ**

Заменить текущий подход:
```python
# МЕДЛЕННО (832ms)
target_lang_words = await words_collection.find({"language": target_lang.lower()}, {"_id": 1}).to_list(None)
target_lang_word_ids = [doc["_id"] for doc in target_lang_words]
cursor = user_progress_collection.find({"user_id": user_id, "word_id": {"$in": target_lang_word_ids}}, {"word_id": 1, "_id": 0})
learned_word_ids = [doc["word_id"] async for doc in cursor]
```

На оптимизированную агрегацию:
```python
# БЫСТРО (330ms)
pipeline = [
    {"$match": {"user_id": user_id, "word_id": {"$exists": True}}},
    {"$lookup": {"from": "words", "localField": "word_id", "foreignField": "_id", "as": "word_info"}},
    {"$match": {"word_info.language": target_lang.lower()}},
    {"$project": {"word_id": 1, "_id": 0}}
]
cursor = user_progress_collection.aggregate(pipeline)
learned_word_ids = [doc["word_id"] async for doc in cursor]
```

### 2. Добавление недостающих индексов ⭐⭐ (DONE)
**Приоритет: СРЕДНИЙ**

```javascript
// Для оптимизации запросов по языку и приоритету
db.words.createIndex({"language": 1, "level": 1, "priority": 1})

// Для оптимизации lookup операций
db.words.createIndex({"_id": 1, "language": 1})
```

### 3. Кэширование результатов ⭐
**Приоритет: НИЗКИЙ** (при текущем размере данных)

Реализовать кэширование списка изученных слов на уровне приложения.

## 🚀 Ожидаемые результаты

### При текущем размере данных (162 слова):
- Ускорение в 2.5 раза (с 1034ms до ~400ms)
- $sample остается быстрым (50ms)

### При росте до 10,000+ слов:
- $sample может замедлиться до 200-500ms
- Подготовка данных может замедлиться до 2-5 секунд без оптимизации
- С оптимизацией: стабильные 100-300ms

## 🎯 Вывод

**$sample НЕ нужно оптимизировать** при текущем размере данных. 

**Реальная оптимизация нужна** в подготовке данных - замена двух отдельных запросов на одну агрегацию даст ускорение в 2.5 раза.

## 📋 План действий

1. ✅ **Сделать**: Оптимизировать подготовку данных в `_get_new_word`
2. ✅ **Сделать**: Добавить составной индекс `language + level + priority`
3. ❌ **НЕ делать**: Заменять $sample на альтернативы
4. 🔮 **Будущее**: Мониторить производительность при росте данных

## 🔬 Методология тестирования

Тестирование проводилось на реальных данных с использованием:
- MongoDB Atlas (облачная база)
- Реальные пользовательские данные
- Измерение времени выполнения в миллисекундах
- Сравнение различных подходов к запросам
