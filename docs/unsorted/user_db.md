# Документация по базе данных пользователей (user_db)

## 📌 Общая информация
- **Имя базы данных**: `user_db`
- **Расположение**: MongoDB Atlas
- **Доступ**: Через бэкенд с использованием переменных окружения

## 📂 Коллекции

### 1. Коллекция: `users`
Основная коллекция для хранения данных пользователей.

#### Схема документа:
```javascript
{
  "_id": ObjectId,
  "email": "string (уникальный, обязательный)",
  "username": "string (опционально)",
  "hashed_password": "string (обязательный, bcrypt)",
  "native_language": "string (например, 'ru')",
  "learning_languages": ["string"],
  "settings": {
    "daily_goal": number,
    "notifications_enabled": boolean,
    "difficulty_level": "string"
  },
  "created_at": ISODate(),
  "last_login": ISODate(),
  "is_guest": boolean,
  "guest_progress": {
    "cards_completed": number,
    "words_learned": ["ObjectId"]
  }
}
```

#### Индексы:
```javascript
db.users.createIndex({ "email": 1 }, { unique: true });
```

### 2. Коллекция: `user_progress`
Отслеживает прогресс пользователя по словам.

#### Схема документа:
```javascript
{
  "_id": ObjectId, // Создается автоматически в MongoDB
  "user_id": "ObjectId (ref: users._id)",
  "concept_id": "string (ID концепта слова)",
  "from_language": "string",
  "to_language": "string",
  "next_review": ISODate(),
  "interval": number,
  "repetition": number,
  "ease_factor": number,
  "last_review": ISODate(),
  "status": "string (new/learning/mastered)",
  "created_at": ISODate(),
  "updated_at": ISODate()
}
```

#### Индексы:
```javascript
db.user_progress.createIndex({ "user_id": 1, "next_review": 1 });
db.user_progress.createIndex({ "user_id": 1, "status": 1 });
```

## 🔐 Аутентификация
- Используется JWT (JSON Web Tokens)
- Токен передается в заголовке `Authorization: Bearer <token>`
- Срок действия токена: 24 часа

## 🔄 Миграции
При изменении схемы базы данных необходимо:
1. Создать скрипт миграции в `/backend/migrations/`
2. Обновить версию схемы
3. Протестировать на тестовой базе

## 🔍 Примеры запросов

### Создание пользователя
```javascript
db.users.insertOne({
  email: "<EMAIL>",
  hashed_password: "$2b$10$...", // bcrypt hash
  native_language: "ru",
  learning_languages: ["en"],
  created_at: new Date(),
  is_guest: false
});
```

### Поиск пользователя по email
```javascript
const user = await db.users.findOne({ email: "<EMAIL>" });
```

### Обновление последнего входа
```javascript
db.users.updateOne(
  { _id: userId },
  { $set: { last_login: new Date() } }
);
```

## 🛠️ Управление базой данных

### Подключение через MongoDB Compass
1. Получить строку подключения из переменных окружения
2. Использовать формат: `mongodb+srv://<username>:<password>@<cluster>.mongodb.net/user_db`

### Резервное копирование
```bash
mongodump --uri="<connection-string>" --db=user_db --out=./backups/
```

### Восстановление из резервной копии
```bash
mongorestore --uri="<connection-string>" ./backups/user_db
```
