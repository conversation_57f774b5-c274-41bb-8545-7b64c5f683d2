# MVP План: Word Master - Приложение для изучения английских слов

## 🚀 Текущий статус проекта (Обновлено: 10.06.2025)

### ✅ Выполнено:
1. **Инфраструктура и БД**
   - Настроено подключение к MongoDB Atlas
   - Создана структура для хранения карточек
   - Реализована загрузка и валидация документов

2. **Фронтенд**
   - Создан экран тренировки карточек
   - Настроено отображение карточек
   - Реализована базовая навигация

3. **Бэкенд**
   - Созданы базовые эндпоинты для работы с карточками
   - Настроена валидация данных
   - Реализовано подключение к БД

### 🎯 Текущие приоритеты:
1. Реализация системы пользователей
2. Аутентификация и авторизация
3. Система прогресса и статистики
4. Доработка интерфейса

## 📋 Обновленный план разработки

1. **🔵 Система пользователей и аутентификация** (ГОТОВО)
2. **🟡 API для работы с карточками** (ГОТОВО)
3. **🟠 Логика интервального повторения** (ЗАПЛАНИРОВАНО)
4. **⚫ Интеграция фронтенда** (ЧАСТИЧНО ГОТОВО)
5. **⚫ Тестирование и отладка** (ЗАПЛАНИРОВАНО)

---

---

## 1. 🔵 Система пользователей и аутентификация

### 1.1 Модель пользователя
**Статус:** ГОТОВО

**Структура пользователя:**
```json
{
  "_id": "ObjectId",
  "email": "string (уникальный)",
  "username": "string (опционально)",
  "hashed_password": "string",
  "created_at": "datetime",
  "last_login": "datetime",
  "is_guest": "boolean (гостевой аккаунт)",
  "guest_progress": {
    "cards_completed": "number (до 20)",
    "words_learned": ["word1_id", "word2_id"]
  },
  "settings": {
    "native_language": "ru",
    "target_language": "en",
    "daily_goal": 20,
    "notifications_enabled": true
  },
  "progress": {
    "words_learned": ["word1_id", "word2_id"],
    "daily_goal_streak": 0,
    "last_active": "datetime"
  },
  "is_active": true,
  "is_verified": false
}
```

### 1.2 Аутентификация и гостевой режим
**Статус:** ГОТОВО

**Гостевой режим:**
- [ ] Возможность начать обучение без регистрации (до 20 карточек)
- [ ] Предложение зарегистрироваться после 20 карточек с сохранением прогресса
- [ ] Сохранение прогресса в локальном хранилище для гостей

**Эндпоинты:**
- [ ] `POST /auth/register` - Регистрация нового пользователя
- [ ] `POST /auth/login` - Вход в систему
- [ ] `POST /auth/refresh` - Обновление токена
- [ ] `POST /auth/password-reset` - Сброс пароля
- [ ] `GET /auth/me` - Получение данных текущего пользователя
- [ ] `POST /auth/guest/convert` - Конвертация гостевого аккаунта в полный

### 1.3 Управление профилем и настройки
**Статус:** ГОТОВО

**Основные настройки:**
- [ ] Редактирование профиля (имя, email)
- [ ] Смена пароля
- [ ] Выбор языков (родной и целевой)
- [ ] Настройка дневной цели (20-50 карточек)

**Уведомления:**
- [ ] Напоминания о тренировке
- [ ] Уведомления о достижениях

**Внешний вид:**
- [ ] Темная/светлая тема
- [ ] Размер шрифта

---

## 2. 🟡 API для работы с карточками

### 2.1 Текущее состояние
**Статус:** ЧАСТИЧНО ГОТОВО

**Что сделано:**
- [x] Базовые эндпоинты для работы с карточками
- [x] Валидация данных
- [x] Подключение к БД

### 2.2 Основные эндпоинты
**Статус:** ГОТОВО

**Получение карточек:**
- [ ] `GET /api/cards/next` - Получение следующей карточки
  - Учитывает прогресс пользователя
  - Возвращает слова из активной очереди (новые + с наступившим интервалом)
  - Формат ответа:
    ```json
    {
      "card_id": "string",
      "sentence_en": "She ___ to the store yesterday.",
      "sentence_ru": "Она ___ в магазин вчера.",
      "hint": "bought",
      "word_level": 1,
      "progress": {
        "is_new": true,
        "level": 0,
        "next_review": null
      }
    }
    ```

**Отправка ответов:**
- [ ] `POST /api/cards/answer` - Отправка ответа пользователя
  - Параметры: `card_id`, `answer`
  - Возвращает результат проверки и следующую карточку
  - Формат ответа:
    ```json
    {
      "is_correct": true,
      "correct_answer": "bought",
      "explanation": "Правильно использовано Past Simple",
      "next_card": { ... }
    }
    ```

**Прогресс и статистика:**
- [ ] `GET /api/progress` - Общий прогресс пользователя
  - Возвращает статистику по изученным словам, текущую серию и т.д.
- [ ] `GET /api/levels` - Список доступных уровней сложности
  - Информация о количестве слов на каждом уровне
  - Прогресс прохождения уровней



## 3. 🟠 Логика интервального повторения
`spaced_repetition.py` - основной файл интервального повторения

### 3.1 Модель прогресса
**Статус:** ГОТОВО

Задача: Хранить, сколько раз пользователь повторял слово, когда он повторял, правильно или нет, и на каком интервале он сейчас.


Создать коллекцию user_progress в MongoDB:

**Структура:**
```json
{
  "user_id": "ObjectId", // Связь на документ пользователя
  "word_id": "ObjectId", // Связь на документ слова
  "next_review": "datetime", // Дата следующего повторения
  "interval_level": 3, // Уровень интервала повторения
  "correct_answers": 0, // Количество правильных ответов
  "incorrect_answers": 0, // Количество неправильных ответов
  "last_reviewed": "datetime", // Дата последнего повторения
  "is_learned": false // Флаг, указывающий, что слово изучено
}

```

**Интервалы повторения:**
0. Новый [-1]
1. 1 минута [0]
2. 5 минут [1]
3. 10 минут [2]
4. 1 час [3]
5. 1 день [4]
6. 3 дня [5]
7. 7 дней [6]          
8. 14 дней [7]
9. 30 дней [8]
10. 2 месяца [9]
11. 4 месяца [10]
12. 8 месяцев [11]
13. 1 год (основное закрепление) [12]
14. 1,5 года (доп. закрепление) [13]
15. 2 года (доп. закрепление) [14]
16. 3 года (доп. закрепление) [15]

Первый интервал = `-1`. 

1. Добавленная на изучение карточка (один неправильный ответ) - по умолчанию имеет интервал `-1`. 
- При первом правильном ответе увеличиваем интервал на 1. То - есть `0`. И так далее. 


2.	При правильном ответе:
	- увеличить interval_level на 1
	- пересчитать next_review 
	- обновить last_review

3.	При неправильном ответе:
	- при первом неправильном ответе снизить interval_level на 1
  - при потворном неправильном ответе сбросить interval_level = 0
  - показывать следующей карточкой вне очереди, пока не введет парвильно.

4. Когда "изучено" (СТАТУС: НЕ РЕАЛИЗОВАН)
  - важно показать статус "изучено" на карточке №13 (1 год)
  - если ждать 3 года, то у юзеров будет демотивация
  - поэтому мы сделаем 2 статуса 
    - первый - частично изучено (1 год)
    - второй - полностью изучено (3 года)



### 3.2 Очереди карточек
**Статус:** ЧАСТИЧНО ГОТОВО

**Очереди карточек:**
- **Активная очередь**: слова с наступившим интервалом и если их нет, то новые слова
- **Неактивная очередь**: слова, ожидающие следующего интервала
- **Изученные слова**:
  - Введенные правильно с первого раза
  - Прошедшие 13 интервалов (1 год)


Задача: Автоматически показывать слова, которые пора повторить, и внедрить механизм показа новой лексики после завершения активной очереди.

📌 Что надо сделать:
	1.	На старте приложения:
	•	Сделай запрос к user_progress
	•	Получи все слова с next_review <= now → это активная очередь
	2.	Показывай только эти слова.
	•	После правильного ответа — обновляй next_review и interval_level
	•	После неправильного ответа — добавляй слово в “форсированную очередь” следующей картчокой
	3.	Когда очередь пустая:
	•	Загружай 3–5 новых слов по изучаемому языку, которых нет в user_progress

Тесты: 
- test_spaced_repetition.py - тесты интервального повторения
- test_api_flow.py - тесты API интервального повторения

### 3.3 Внедрить изменения во фронтенд (на экране тренировки)
**Статус:** ГОТОВО (бэкенд) / В ПРОЦЕССЕ (фронтенд)

#### Реализовано на бэкенде (готово):
- [x] API для обновления прогресса пользователя
- [x] Обработка правильных/неправильных ответов
- [x] Интервальное повторение с 16 уровнями (от 1 минуты до 3 лет)
- [x] Флаги is_learned и force_review
- [x] Полные тесты API (test_api_flow.py)

#### Требуется реализовать во фронтенде:

3.3.1. [x] **Интеграция с API**  
   - [x] **Использовать реальный user_id**  

     Я нашел проблему: в коде фронтенда используется URL /api/cards/{card_id}/review, но в бэкенде такого эндпоинта нет. Вместо этого, у нас есть эндпоинт /spaced/next для получения следующей карточки и /spaced/review для отправки ответа.

   - [x] **Обрабатывать пустую очередь**  
     Если бэкенд вернул 404, запросить новое слово:  

   - [x] **Добавить отладочную информацию**  
     Временно выводить интервал карточки (удалить в продакшене):  
     ```

3.3.2. [x] **Визуализация прогресса**  
   - [ ] Настроить отображение полосок прогресса:
     - 1 полоска 
        - (оранжевая) - только для новых слова
        - (голубая) - если слово не новое, но интервал меньше 1 дня
     - 2 полоски - уровень 5-8 (от 1 днядо 1 месяца)
     - 3 полоски - уровень 9-11 (1-6 месяцев)
     - 4 полоски - уровень 12-14 (6-12 месяцев)
     - 5 полосок (зелёная) - уровень 15+ (более 1 года)



3.3.3. **Отображение интервала**  
   - [x] Добавить отладочную информацию о текущем интервале (об изучаемом слове на target_language). 
   - Например: "Int = 1" "Int = -1", "Int = 1440", "Int = 4320"
   - Серым шрифтом (внизу карточки)

3.3.4. **Статус изучения**:  (позже)
   - [ ] Отображение "частично изучено" (1 год)  
   - [ ] Отображение "полностью изучено" (3 года)




### Тесты
Тесты находятся в 
- для бэкенда в файле test_spaced_repetition.py
- для api в файле test_api_flow.py

Можно сделать во фэкране тренировки для каждого слова вывести интервал. Для отладки. ЧТо еще надо поестить? 
- При неправильном ответе карточка повторяется - api-ok
- При правильном ответе интервал увеличивается (как проверить? в коллекции?) - api-ok
- При двух неправильном ответе интервал сбрасывается в 0 - api-ok
- Когда все карточки из активной очереди закончились, то предлагаются ли новые слова из коллекции words


## 4. Модульная архитектура

Задача: Слова = не просто строка, а “модуль” с примерами, переводами, формами. Перевод генерируется автоматически.

📌 Что надо сделать:
### 4.1.	Измени структуру документа examples:
```json
{
  "sentence": "She ___ to the store yesterday.",
  "correct_answers": ["bought"],
  "word_info": "bought"
  "form_level": "A1", // Необходимый уровень пользователя для изучения формы
  "id": ""  // id слова
}
```

### 4.2. Внедрить механизм прогресса для леммы

Первый уровень - лемма выучена. Через иконку циферблата. Если интервал месяц, то лемма выучена. 

Потом приступаем к другим формам слова. Но в зависимости от уровня пользователя. 


### 4.3. Внедрить механизм генерации переводов для новых слов через AI  


Для новых слов — генерируй перевод через AI:
	•	Используй бесплатные open-source модели, например, OpenRouter (там можно GPT-3.5 через API бесплатно с лимитом) или локальные модели (при желании)
	•	Или просто подключи Hugging Face model (например, Helsinki-NLP/opus-mt-en-ru) через Python
	3.	Можешь использовать FastAPI эндпоинт, который получает sentence и возвращает translated
→ POST /translate → { "text": "I like apples", "target_lang": "ru" }

УБедиться что структура переводимого правильная правильная. 

```json
{
  "text": "Я вышел из зеленой ___",
  "correct_answers": ["двери"],
  "target_lang": "ru"
}
```

### 4.4. Добавить 100 слов для тестирования

На всех языках чтобы было А1

Например "Я большой мальчик"


## 4.5. Добавить слова A0

Самые нужные для языка слова. 

- Придумать когда их корректно давать? 
- Когда переходить на A1? 


### 4.6. Тесты
В принципе база по МВП готова. Можно тестить. 
И если все ок, то улучшать дальше. Скелет готов. 


## 5. ⚫ Фронтенд

### 5.1 Экран тренировки
**Статус:** ЧАСТИЧНО ГОТОВО

**Что сделано:**
- [x] Базовый экран тренировки
- [x] Отображение карточек
- [x] Навигация между карточками

**Что нужно доработать:**
- [ ] Прогресс-бар в верхней части экрана
- [ ] Индикатор текущей карточки (например, 7/20)
- [ ] Подсветка правильного слова в русском предложении
- [ ] Автоматическая проверка по нажатию Enter
- [ ] Визуальная обратная связь при правильном/неправильном ответе
- [ ] Анимации перехода между карточками

### 5.2 Гостевой режим
**Статус:** В РАЗРАБОТКЕ

**Функционал:**
- [ ] Возможность начать обучение без регистрации
- [ ] Счетчик оставшихся карточек (20 максимум)
- [ ] Модальное окно с предложением зарегистрироваться после 20 карточек
- [ ] Сохранение прогресса при регистрации

### 5.3 Экран регистрации/входа
**Статус:** ЧАСТИЧНО ГОТОВО

**Элементы:**
- [ ] Форма входа (email/пароль)
- [ ] Форма регистрации
- [ ] Кнопки входа через соцсети
- [ ] Ссылка "Войти как гость"
- [ ] Форма восстановления пароля

### 5.4 Профиль и настройки
**Статус:** ГОТОВО

**Разделы:**
- [ ] Основная информация (имя, email)
- [ ] Языковые настройки
- [ ] Цель на день (20-50 карточек)
- [ ] Уведомления
- [ ] Внешний вид (тема, размер шрифта)
- [ ] Выход из аккаунта
- [ ] Уровень пользователя в изучаемом языке
- [ ] Язык хранится в сессии и берется из сессии а не из изучаемых языков

### 6. 🚀 Развертывание и тестирование

### 6.1 Инфраструктура
**Статус:** В ПЛАНЕ

**Компоненты:**
- [ ] **Бэкенд:**
  - FastAPI на Python 3.10+
  - Gunicorn + Uvicorn для ASGI
  - Nginx как обратный прокси
  - Сертификаты Let's Encrypt
  
- [ ] **База данных:**
  - MongoDB Atlas (бесплатный кластер M0)
  - Ежедневное резервное копирование
  
- [ ] **Хранилище:**
  - AWS S3 для статических файлов
  - CloudFront для CDN

### 6.2 CI/CD
**Статус:** В ПЛАНЕ

**Процесс:**
1. Автоматический запуск тестов при пуше в ветку
2. Сборка и тестирование Docker-образа
3. Деплой на тестовый сервер для ветки develop
4. Ручная проверка и релиз в production

### 6.3 Мобильные приложения
**Статус:** В ПЛАНЕ

**Публикация:**
- [ ] Настройка аккаунтов разработчика
- [ ] Подготовка скриншотов и описаний
- [ ] Тестирование на реальных устройствах
- [ ] Отправка на модерацию
- [ ] Постепенный релиз (10% → 50% → 100% пользователей)

**Мониторинг:**
- [ ] Sentry для отслеживания ошибок
- [ ] Google Analytics для аналитики
- [ ] Firebase Performance для метрик производительности

### 6.4 Тестирование
**Статус:** В ПЛАНЕ

**Типы тестов:**
- [ ] Модульное тестирование
- [ ] Интеграционное тестирование
- [ ] Нагрузочное тестирование

**Цели:**
- [ ] Обеспечить стабильную работу приложения
- [ ] Протестировать на разных устройствах
- [ ] Проверить работу оффлайн-режима
- [ ] Протестировать сценарии восстановления

### 6.5 Релиз
**Статус:** В ПЛАНЕ

**План релиза:**
- [ ] Альфа-версия для внутреннего тестирования
- [ ] Бета-версия для внешнего тестирования
- [ ] Полный релиз в App Store и Google Play

---

## 5. 🟢 Тестирование

### 5.1 Модульное тестирование
**Статус:** В ПЛАНЕ

**Бэкенд:**
- [ ] Валидация входящих данных
- [ ] Работа с MongoDB
- [ ] Логика интервального повторения
- [ ] Генерация карточек

**Фронтенд:**
- [ ] Компоненты React Native
- [ ] Навигация
- [ ] Обработка пользовательского ввода
- [ ] Работа с локальным хранилищем

### 5.2 Интеграционное тестирование
**Статус:** В ПЛАНЕ

**Сценарии:**
- [ ] Полный цикл регистрации
- [ ] Процесс обучения (получение карточки → ответ → сохранение прогресса)
- [ ] Гостевой режим с последующей регистрацией
- [ ] Синхронизация прогресса между устройствами

### 5.3 Нагрузочное тестирование
**Статус:** В ПЛАНЕ

**Цели:**
- [ ] Обработка 100+ одновременных пользователей
- [ ] Время отклика API < 500мс при 95% перцентиле
- [ ] Отказоустойчивость при потере соединения
- [ ] Эффективное использование ресурсов на мобильных устройствах

---

## 📌 Ближайшие шаги

1. **Сейчас в работе:**
   - Реализация модели пользователя
   - Создание эндпоинтов аутентификации

2. **Следующие шаги:**
   - Разработка экранов регистрации/входа
   - Интеграция с бэкендом
{{ ... }}

3. **В перспективе:**
   - Реализация системы прогресса
   - Доработка интерфейса
   - Тестирование и отладка

## 📅 Активные задачи

### Высокий приоритет
- [ ] Реализовать регистрацию пользователя
- [ ] Создать экран входа
- [ ] Настроить JWT аутентификацию

### Средний приоритет
- [ ] Реализовать систему прогресса
- [ ] Доработать API карточек
- [ ] Создать экран профиля

### Низкий приоритет
- [ ] Настройка уведомлений
- [ ] Дополнительная аналитика
- [ ] Оптимизация производительности

---

## 🔄 История изменений

### 10.06.2025
- Обновлен MVP-план
- Добавлен текущий статус проекта
- Определены приоритеты разработки
- Структурированы задачи

### Ранее
- Создана базовая структура проекта
- Реализован экран тренировки карточек
- Настроено подключение к MongoDB Atlas

---

## 📚 Архитектура базы данных

### Общая структура хранения данных

Приложение использует MongoDB Atlas с двумя основными базами данных:

1. **`words_db`** - словарные данные
   - `words` - основная коллекция с карточками
   - `sentences` - примеры предложений
   - `levels` - настройки уровней сложности

2. **`users_db`** - данные пользователей
   - `users` - профили и настройки
   - `user_progress` - прогресс изучения
   - `sessions` - активные сессии

### Ключевые принципы

1. **Многоязычность**
   - Все слова хранятся в одной коллекции с указанием языка
   - Связи между переводами через общий `concept_id`
   - Поддержка любых языковых пар
   - Любой язык может быть как изучаемым, так и родным

2. **Прогресс пользователя**
   - Отслеживание прогресса по каждому слову
   - Алгоритм интервального повторения (SM-2)
   - Гибкие настройки сложности и интервалов

**Полная документация:** [database_schema.md](database_schema.md)

---

## 🛠 Технический стек

### Бэкенд (FastAPI)
- Асинхронная работа с MongoDB
- JWT аутентификация
- Валидация данных через Pydantic
- Логирование и обработка ошибок
- Кэширование часто используемых данных

### Фронтенд (React Native)
- Нативные компоненты
### 6.2 Тестирование приложения
**Цель:** Обеспечить стабильную работу приложения.

**Задачи:**
- Протестировать на разных устройствах
- Проверить работу оффлайн-режима
- Протестировать сценарии восстановления

## Рекомендации по базе данных

### MongoDB
**Плюсы:**
- Гибкая схема данных
- Хорошо подходит для хранения карточек и предложений
- Масштабируемость

**Минусы:**
- Нет встроенных джойнов (но для данного случая это не критично)

### MongoDB
**Плюсы:**
- Гибкая схема данных
- Хорошая масштабируемость
- Удобная работа с массивами и вложенными документами
- Единая база данных для всего приложения

**Рекомендация:**
Использовать MongoDB для всех данных приложения, включая пользователей, подписки и прогресс. Это упростит архитектуру, уменьшит количество зависимостей и упростит развертывание.
Использовать обе СУБД, так как они дополняют друг друга. MongoDB для хранения карточек и предложений, PostgreSQL для пользовательских данных и подписок.

## 🗺️ Дорожная карта развития

### 7.1 Текущий статус (Июнь 2024)
- [x] Базовый функционал карточек
- [x] Интеграция с MongoDB
- [ ] Гостевая авторизация
- [ ] Полная система пользователей
- [ ] Интервальное повторение
- [ ] Базовый фронтенд

### 7.2 Приоритеты на MVP

**1. Фаза 1: Ядро приложения (Июль 2024)**
- [ ] Гостевая авторизация
- [ ] Базовый экран тренировки
- [ ] Логика интервального повторения
- [ ] Сохранение прогресса

**2. Фаза 2: Пользовательский опыт (Август 2024)**
- [ ] Полная регистрация/вход
- [ ] Профиль пользователя
- [ ] Статистика и прогресс
- [ ] Настройки приложения

**3. Фаза 3: Оптимизация (Сентябрь 2024)**
- [ ] Оффлайн-режим
- [ ] Синхронизация между устройствами
- [ ] Улучшение производительности
- [ ] Подготовка к релизу

### 7.3 Пост-MVP (Q4 2024)
- [ ] Дополнительные языки
- [ ] Геймификация (достижения, бейджи)
- [ ] Социальные функции
- [ ] Партнерская программа

### 7.5 Неразобранные идеи (Backlog)
- [ ] **Расширение языковой системы** - добавление новых языков, региональных вариантов, систем письма, лингвистических семей. См. [language_system_expansion.md](future/language_system_expansion.md)
- [ ] Система диалектов и макроязыков
- [ ] Поддержка RTL языков (арабский, персидский, урду)
- [ ] Группировка языков по регионам и семьям
- [ ] Автоматизация создания переводов с MCP серверами

### 7.4 Метрики успеха
- [ ] 1000+ установок в первый месяц
- [ ] Удержание пользователей > 30% на 7-й день
- [ ] Среднее время в приложении > 10 минут
- [ ] Оценка в маркетах > 4.5/5

## Заключение

Этот план охватывает все ключевые аспекты разработки MVP приложения Word Master. Каждый этап можно дробить на более мелкие подзадачи по мере необходимости. Рекомендую начать с настройки инфраструктуры и разработки API, а затем переходить к интеграции с фронтендом.

Для ускорения разработки можно использовать готовые решения для аутентификации и работы с базой данных, а также автоматизировать процесс тестирования.
