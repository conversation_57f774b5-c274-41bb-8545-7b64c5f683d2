# MCP Серверы для проекта MEMO

## 🎯 **Уже доступные MCP сервера для вашего проекта:**

### **1. ВЫСОКИЙ ПРИОРИТЕТ - для архитектурных задач:**

**Context7** (уже у вас есть) - отлично для документации и исследования библиотек
**Sequential Thinking** (уже у вас есть) - для сложного планирования
**Playwright** (уже у вас есть) - для E2E тестирования

**Дополнительно рекомендую:**

**Memory MCP Server** - для системы интервального повторения:
- Хранение графа знаний
- Персистентная память для пользователей
- Идеально для отслеживания прогресса изучения слов

**Git MCP Server** - для управления кодом:
- Чтение, поиск, манипуляции с Git репозиторием
- Поможет с задачей "Прибраться в проекте"

**Filesystem MCP Server** - для файловых операций:
- Безопасные операции с файлами
- Настраиваемый контроль доступа
- Поможет с организацией проекта

### **2. СРЕДНИЙ ПРИОРИТЕТ - для разработки и тестирования:**

**PostgreSQL/MongoDB MCP Servers** - для работы с базой данных:
- Инспекция схемы
- Оптимизация запросов (у вас есть проблема с `$sample`)
- Анализ производительности

**Redis MCP Server** - для кэширования:
- Управление кэшем
- Оптимизация производительности

**Fetch MCP Server** - для веб-контента:
- Конвертация веб-контента для LLM
- Поможет с исследованием конкурентов

### **3. СПЕЦИФИЧНЫЕ ДЛЯ МОБИЛЬНОЙ РАЗРАБОТКИ:**

**React Native/Expo MCP Servers** (если есть) - для мобильной разработки
**Testing frameworks MCP** - для автоматизации тестов

## 🔍 **Новые MCP сервера, которые стоит рассмотреть:**

### **Для LLM интеграции (задача "Перевод через LLM"):**
- **OpenAI MCP Server** - для интеграции с GPT
- **Anthropic MCP Server** - для Claude API

### **Для деплоя и DevOps:**
- **Docker MCP Server** - для контейнеризации
- **AWS/Vercel MCP Servers** - для деплоя

### **Для аналитики и мониторинга:**
- **Analytics MCP Servers** - для отслеживания использования
- **Error tracking MCP** - для мониторинга ошибок

## 🧪 **ТЕСТИРОВАНИЕ - React Native/Expo и Testing MCP серверы**

### **Ваша проблема с тестированием:**
Классическая #проблема регрессионного тестирования# в мобильной разработке. E2E тесты часто не отражают реальное поведение приложения.

### **Рекомендуемые MCP серверы для решения:**

#### **A. Detox MCP Server** (если существует или создать)
- **Detox** - это "gray-box" фреймворк специально для React Native
- Тестирует приложение на реальном устройстве/симуляторе
- Взаимодействует как реальный пользователь
- **Преимущество**: максимально близко к реальному опыту

#### **B. Playwright MCP** (уже у вас есть) + адаптация
- Можно адаптировать для мобильного тестирования
- Поддерживает мобильные браузеры
- Хорошо для веб-части приложения

#### **C. Memory MCP** для тестовых сценариев
- Хранение тестовых данных и сценариев
- Отслеживание регрессий
- База знаний о найденных багах

### **Конкретное решение для вашего проекта:**

```json
{
  "mcpServers": {
    "detox-testing": {
      "command": "npx",
      "args": ["-y", "detox-mcp-server", "--project", "/Users/<USER>/MEMO"]
    },
    "memory-testing": {
      "command": "npx", 
      "args": ["-y", "@modelcontextprotocol/server-memory"]
    },
    "filesystem": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/MEMO"]
    }
  }
}
```

### **Стратегия тестирования с MCP:**

1. **Memory MCP** - создать базу тестовых сценариев:
   - Сценарий: "Пользователь отвечает правильно → переход к следующей карточке"
   - Сценарий: "Пользователь отвечает неправильно → карточка повторяется"
   - Сценарий: "Hint кнопка → фиолетовое свечение + подсказка"

2. **Sequential Thinking** - для планирования тестов:
   - Анализ критических путей пользователя
   - Приоритизация тестовых сценариев
   - Планирование регрессионных тестов

3. **Detox/Playwright** - для выполнения тестов:
   - Автоматизация реальных действий пользователя
   - Проверка UI состояний
   - Валидация данных в БД

## 💾 **MEMORY MCP для системы слов и словоформ**

### **Ваши задачи со словами:**
- 151 A0 слово × 25 языков = 3,775 записей
- Потом A1, B1, B2... = десятки тысяч слов
- Модульная архитектура с словоформами
- Система concept_id для связывания переводов

### **Как Memory MCP поможет:**

#### **A. Создание и валидация слов:**

```javascript
// Memory MCP может хранить:
{
  "concept_id": "uuid-123",
  "base_concept": "мама",
  "translations": {
    "ru": "мама",
    "en": "mother", 
    "es": "madre",
    "de": "Mutter"
    // ... все 25 языков
  },
  "validation_status": {
    "ru": "validated",
    "en": "needs_review",
    "es": "validated"
  },
  "linguistic_notes": {
    "ru": "им.п., без склонения в примере",
    "de": "Nominativ, артикль не нужен в контексте"
  }
}
```

#### **B. Автоматизация проверки качества:**

**Memory MCP + Sequential Thinking** для:
1. **Проверка естественности предложений**
2. **Валидация грамматики по языкам**
3. **Контроль качества переводов**
4. **Автоматическое создание word_info**

#### **C. Система словоформ (модульная архитектура):**

```javascript
// Memory MCP для хранения словоформ:
{
  "base_word": "работать",
  "concept_id": "uuid-work",
  "word_forms": {
    "ru": [
      {"form": "работаю", "info": "1л., ед.ч., наст.вр."},
      {"form": "работаешь", "info": "2л., ед.ч., наст.вр."},
      {"form": "работает", "info": "3л., ед.ч., наст.вр."}
    ],
    "en": [
      {"form": "work", "info": "base form"},
      {"form": "works", "info": "3rd person singular"},
      {"form": "worked", "info": "past tense"}
    ]
  }
}
```

### **Дополнительные MCP серверы для слов:**

#### **Context7** - для исследования языков:
- Изучение грамматических правил каждого языка
- Поиск примеров естественных предложений
- Валидация лингвистических конструкций

#### **LLM Integration MCP** (если есть):
- Автоматическая генерация переводов
- Проверка естественности предложений
- Создание word_info автоматически

### **Конкретный workflow с MCP:**

1. **Sequential Thinking** → планирование создания слов для уровня
2. **Memory MCP** → хранение базовых концептов и переводов  
3. **Context7** → исследование грамматики для сложных языков
4. **LLM MCP** → автогенерация и валидация
5. **Filesystem MCP** → создание JSON файлов
6. **Git MCP** → версионирование и отслеживание изменений

### **Автоматизация процесса:**

```bash
# Вместо ручной работы:
1. Memory MCP создает концепт
2. LLM MCP генерирует переводы на 25 языков  
3. Context7 валидирует сложные языки (финский, турецкий)
4. Sequential Thinking проверяет качество
5. Filesystem MCP создает JSON
6. Автоматический импорт в БД
```

### **Результат:**
- **Время создания**: с недель до часов
- **Качество**: автоматическая валидация
- **Консистентность**: единые правила для всех языков
- **Масштабируемость**: легко добавлять новые языки

## 🚀 **Как начать использовать:**

1. **Установите Memory MCP Server:**
```bash
npx -y @modelcontextprotocol/server-memory
```

2. **Добавьте в Claude Desktop config:**
```json
{
  "mcpServers": {
    "memory": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-memory"]
    },
    "filesystem": {
      "command": "npx", 
      "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/MEMO"]
    },
    "git": {
      "command": "uvx",
      "args": ["mcp-server-git", "--repository", "/Users/<USER>/MEMO"]
    }
  }
}
```

3. **Начните с Memory MCP** для планирования системы интервального повторения - это самая критичная часть вашего приложения.

## 📋 **Конкретные рекомендации по задачам из todo.md:**

### **Модульная архитектура:**
- **Sequential Thinking** - для планирования архитектуры
- **Memory MCP** - для системы лемм и словоформ
- **Filesystem MCP** - для организации файлов

### **Тесты всего:**
- **Playwright MCP** (уже есть) - для E2E тестов
- **Git MCP** - для управления тестовыми данными
- **Memory MCP** - для тестовых сценариев

### **Перенос на сервер:**
- **Sequential Thinking** - для планирования архитектуры
- **Database MCP servers** - для миграции данных
- **Docker/Cloud MCP** - для деплоя

### **Hint система:**
- **Sequential Thinking** - для UX планирования
- **Filesystem MCP** - для управления ресурсами
