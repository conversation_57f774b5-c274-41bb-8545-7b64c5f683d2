# Структура FastAPI бэкенда

```
backend/
├── app/
│   ├── __init__.py
│   ├── main.py             # Точка входа в приложение
│   ├── config.py           # Конфигурация приложения
│   ├── database.py         # Настройка подключения к БД
│   ├── models/             # Модели данных
│   │   ├── __init__.py
│   │   ├── user.py         # Модель пользователя
│   │   ├── word.py         # Модель слова и предложений
│   │   └── training.py     # Модели тренировок и сессий
│   ├── services/           # Бизнес-логика
│   │   ├── __init__.py
│   │   ├── spaced_repetition.py  # Логика интервального повторения
│   │   ├── training.py           # Логика тренировок
│   │   └── word_service.py       # Работа со словарем
│   ├── routers/            # API эндпоинты
│   │   ├── __init__.py
│   │   ├── users.py        # Управление пользователями
│   │   ├── words.py        # Работа со словарем
│   │   └── training.py     # API тренировок
│   └── utils/              # Вспомогательные функции
│       ├── __init__.py
│       └── helpers.py
├── scripts/                # Скрипты для администрирования
│   ├── words/              # Утилиты для работы со словами
│   │   ├── __init__.py
│   │   ├── __main__.py     # Основной интерфейс командной строки
│   │   ├── check_db.py     # Проверка подключения к БД
│   │   ├── check_recent.py # Просмотр недавно добавленных слов
│   │   ├── clear_words.py  # Очистка коллекции слов
│   │   └── validate_word.py # Валидация JSON-файлов
│   └── ...
├── data/                  # Данные приложения
│   └── words/             # Словари и примеры
│       ├── new/           # Новые файлы для импорта
│       ├── imported/      # Успешно импортированные файлы
│       ├── invalid/       # Файлы с ошибками
│       └── EXAMPLE.md     # Пример формата файла
├── requirements.txt        # Зависимости Python
└── .env                    # Переменные окружения
```

## Основные команды для работы со словами

```bash
# Активация виртуального окружения
source venv/bin/activate

# Проверка подключения к БД
python -m scripts.words check-db

# Импорт новых слов из папки new/
python -m scripts.words import

# Проверка недавно добавленных слов
python -m scripts.words recent --minutes 5

# Валидация файла со словами
python -m scripts.words validate путь/к/файлу.json

# Очистка коллекции слов (с подтверждением)
python -m scripts.words clear
```

## Основные компоненты

### 1. Модели данных (`models/`)
- `user.py`: Пользователи, настройки, подписки
- `word.py`: Слова, переводы, примеры предложений
- `training.py`: Сессии тренировок, прогресс пользователя

### 2. Сервисы (`services/`)
- `spaced_repetition.py`: Алгоритм интервального повторения
- `training.py`: Генерация карточек, обработка ответов
- `word_service.py`: Поиск и фильтрация слов

### 3. API эндпоинты (`routers/`)
- `users.py`: Регистрация, аутентификация, профиль
- `words.py`: Получение слов и предложений
- `training.py`: Управление тренировками

## Настройка окружения

Создайте файл `.env` в корневой директории бэкенда:

```env
# Настройки приложения
APP_ENV=development
DEBUG=True

# Настройки MongoDB
MONGODB_URL=mongodb+srv://username:<EMAIL>/
DATABASE_NAME=wordmaster

# Настройки JWT
SECRET_KEY=your-secret-key
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=1440  # 24 часа
```

## Установка и запуск

1. Создайте виртуальное окружение:
```bash
python -m venv venv
source venv/bin/activate  # На Windows: venv\Scripts\activate
```

2. Установите зависимости:
```bash
pip install -r requirements.txt
```

3. Запустите приложение:
```bash
uvicorn app.main:app --reload
```

API будет доступно по адресу: http://localhost:8000

Документация API:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc
