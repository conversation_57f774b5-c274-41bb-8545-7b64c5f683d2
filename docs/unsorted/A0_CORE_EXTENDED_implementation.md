# Внедрение системы CORE/EXTENDED для A0 уровня

## Обзор

Система автоматической прогрессии A0 слов разделяет 151 слово на три группы:
- **ULTRA-CORE (15 слов)** - "первые слова ребенка", абсолютный минимум
- **CORE (35 слов)** - базовый набор для выживания
- **EXTENDED (101 слово)** - расширенный набор для полноценного общения

## Автоматическая логика

1. **Начало изучения A0** → показываем только ULTRA-CORE слова (15 слов)
2. **ULTRA-CORE слова кончаются** → переходим на CORE слова (35 слов)
3. **CORE слова кончаются** → переходим на EXTENDED слова (101 слово)
4. **Никаких настроек** - все происходит незаметно для пользователя

## Внесенные изменения

### Backend

#### 1. Модель слова (`backend/app/models/word.py`)
```python
class WordPriority(str, Enum):
    ULTRA_CORE = "ultra_core"  # Самые базовые слова A0 (15 слов)
    CORE = "core"              # Базовые слова A0 (35 слов)
    EXTENDED = "extended"      # Расширенные слова A0 (101 слово)

class WordBase(BaseModel):
    word: str
    level: WordLevel
    priority: Optional[WordPriority] = None  # Только для A0 уровня
    # ... остальные поля
```

#### 2. Сервис интервального повторения (`backend/app/services/spaced_repetition.py`)
Добавлены методы:
- `_get_a0_priority_filter()` - определяет какие слова показывать
- `_calculate_a0_core_progress()` - рассчитывает прогресс CORE слов

#### 3. Логика фильтрации
```python
async def _get_a0_priority_filter(self, user_id: ObjectId, target_lang: str = None):
    # 1. Проверяем есть ли доступные ULTRA-CORE слова
    ultra_core_available = await self._check_available_words(user_id, target_lang, "ultra_core")
    if ultra_core_available > 0:
        return {"level": "A0", "priority": "ultra_core"}

    # 2. Если ULTRA-CORE кончились, проверяем CORE слова
    core_available = await self._check_available_words(user_id, target_lang, "core")
    if core_available > 0:
        return {"level": "A0", "priority": "core"}

    # 3. Если и CORE кончились, показываем EXTENDED
    return {"level": "A0", "priority": "extended"}
```

### Frontend

#### 1. Интерфейс Card (`frontend/screens/TrainingScreen.tsx`)
```typescript
interface Card {
  // ... существующие поля
  priority?: 'ultra_core' | 'core' | 'extended'; // Приоритет для A0 уровня
}
```

#### 2. Отображение приоритета в debug информации
```tsx
// В функции getIntervalDisplayText
if (card.level === 'A0' && card.priority) {
  let priorityText = '';
  switch (card.priority) {
    case 'ultra_core': priorityText = 'ULTRA'; break;
    case 'core': priorityText = 'CORE'; break;
    case 'extended': priorityText = 'EXT'; break;
  }
  intervalText += ` | ${priorityText}`;
}
```

#### 3. API интерфейсы (`frontend/services/api.ts`)
```typescript
export interface Word {
  // ... существующие поля
  priority?: 'ultra_core' | 'core' | 'extended'; // Приоритет для A0 уровня
}
```

### Данные

#### 1. Список A0 слов (`md/A0_word_list.md`)
- 151 слово разделено на ULTRA-CORE (15), CORE (35) и EXTENDED (101)
- Каждое слово помечено приоритетом

#### 2. Пример JSON (`backend/data/words/A0_CORE_example.json`)
```json
{
  "concept_id": "a0-ultra-001",
  "word": "я",
  "language": "ru",
  "level": "A0",
  "priority": "ultra_core",
  "examples": [...]
}
```

## Тестирование

### Автоматический тест (`backend/test_a0_priorities.py`)
```bash
cd backend
python test_a0_priorities.py
```

Тест проверяет:
1. Наличие A0 слов с приоритетами в БД
2. Логику фильтрации (ULTRA-CORE → CORE → EXTENDED)
3. Переключение между уровнями когда слова кончаются

## Создание A0 слов

### 1. Подготовка JSON файла
Используйте формат из `backend/data/words/A0_CORE_example.json`:
- Поле `priority`: "ultra_core", "core" или "extended"
- Следуйте списку из `md/A0_word_list.md`

### 2. Импорт в базу данных
```bash
cd backend
python -m app.scripts.import_words data/words/A0_words.json
```

### 3. Проверка работы
```bash
python test_a0_priorities.py
```

## Преимущества системы

✅ **Автоматическая прогрессия** - пользователь не выбирает настройки
✅ **Мотивация** - "разблокировка" новых слов при прогрессе
✅ **Минимальные изменения** - только поле `priority` в JSON
✅ **Обратная совместимость** - другие уровни не затронуты
✅ **Простота** - логика только для A0 уровня

## Статистика

- **ULTRA-CORE слов**: 15 ("первые слова ребенка")
- **CORE слов**: 35 (базовый набор для выживания)
- **EXTENDED слов**: 101 (расширенный набор)
- **Всего A0**: 151 слово
- **Записей в БД**: 151 × 37 языков = 5,587 записей
- **Логика переключения**: когда слова кончаются, а не по проценту

## Следующие шаги

1. **Создать полный JSON файл** с 151 A0 словом для всех 37 языков
2. **Импортировать в базу данных**
3. **Протестировать на реальных пользователях**
4. **Мониторить метрики** прогрессии CORE → EXTENDED

## Связанные документы

- [Список A0 слов](A0_word_list.md)
- [Правила создания A0 слов](_WORDS_CREATION_GUIDE_WORKFLOW.md#создание-a0-слов-базовый-уровень)
- [Схема базы данных](database_schema.md)
