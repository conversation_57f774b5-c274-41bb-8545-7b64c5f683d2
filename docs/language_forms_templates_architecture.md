# Система генерации форм слов и шаблонов предложений для многоязычного приложения

## 1. Проблема и мотивация

### ❓ В чем суть проблемы

Если у нас есть, например, 6000 слов и 37 языков, и для каждого слова — от 5 до 20 грамматических форм (падежи, числа, времена и т.д.), то:

- Нам нужно создавать **сотни тысяч переводов вручную**:  
  _Например:_ форма слова «собаку» на немецкий, испанский, японский и т.д.
- Каждая из этих форм требует **отдельного предложения-примера** — иначе язык не изучишь в контексте.
- Каждую такую фразу нужно **перевести на десятки языков**.
- При добавлении нового языка (например, хинди), придётся **переводить всё заново** — тысячи форм и фраз.
- Все эти фразы и формы нужно еще и **согласовывать по смыслу, грамматике и контексту**, чтобы перевод был корректным.
- В итоге: если идти вручную — это десятки человеко-месяцев ручной и рутинной работы, масштабируемость — нулевая.

### 🔥 Почему обычный (немодульный) подход не подходит

- **Переводы между языками делаются по парам**: ru → en, en → de, de → ja и т.д.  
  Это **O(n²)** связей. Для 37 языков это 1332 связки.
- **Формы слов не синхронизируются между языками** — в русском 6 падежей, в финском — 15, в английском — 2 формы.  
  Всё это приводит к «переводному аду»: не ясно, что чему соответствует.
- **Трудно вносить правки**: одна ошибка — и править придётся 30-40 переводов сразу.
- **Трудно масштабировать**: добавить новое слово = создать 20 форм + 37 переводов каждой формы = 740 записей.

### ✅ Что решает предложенный модульный подход

- Все слова и формы хранятся **внутри одного языка**:  
  → "dog" знает, что у него есть форма "dogs", но не знает ничего о русском языке.  
  → "собака" знает, что у неё есть "собаку", "собаки", "собаками" и т.д.
- Переводы не делаются между всеми парами языков, а только **через центральный концепт (lemmaId / conceptId)**.
- Все формы слова обозначаются **семантическими ролями**: субъект, объект, получатель, инструмент и т.д.  
  Они понятны независимо от грамматики (работают даже для китайского и японского).
- Для каждой роли используется **шаблон фразы**, который адаптируется под конкретный язык.
- При добавлении нового языка:
  - создаются формы слова для этого языка
  - адаптируются 10–20 шаблонов  
  → и весь словарь сразу начинает работать для этого языка

### 💡 Альтернативные подходы

| Подход | Проблемы |
|-------|----------|
| ✅ Модульный через роли и шаблоны | Нужно настроить шаблоны для каждого языка, но один раз. Дальше — масштабируется. |
| ❌ Перевод каждой формы вручную | Миллионы комбинаций, невозможно поддерживать. |
| ❌ GPT/AI-переводы в рантайме | Ошибки, несогласованность, нет контроля качества. |
| ❌ Перевод только лемм без форм | Не работает для обучения — теряется грамматика и контекст. |

---

## 2. Основные концепции

### Лемма и формы слова

- **Лемма** — базовая форма слова, например, «собака»  
- **Формы слова** — различные вариации леммы по падежам, числам, временам, лицам и другим грамматическим категориям

### Шаблоны предложений (Templates)

- Представляют собой конструкции с заполнителями, например:  
  - Русский: `{subject} {verb} {object}`  
  - Японский: `{subject} {object} を {verb}`  
- Шаблоны разные для каждого языка (учитывают порядок слов, частицы, предлоги)

### Категории слов (семантика)

- Классификация слов для выбора подходящих шаблонов и глаголов (например, животные, предметы, абстрактные понятия)

---

## 3. Архитектура данных

### Структура документа слова (пример на MongoDB)

```json
{
  "_id": ObjectId("..."),
  "language": "ru",
  "lemma": "собака",
  "category": "животное",
  "forms": [
    {
      "case": "именительный",
      "number": "единственное",
      "form": "собака",
      "level": "A1"
    },
    {
      "case": "винительный",
      "number": "единственное",
      "form": "собаку",
      "level": "A1"
    }
    // ... другие формы
  ]
}
```

### Структура шаблонов предложений

```json
{
  "language": "ru",
  "templates": [
    {
      "id": "see_object",
      "pattern": "{subject} {verb} {object}",
      "applicable_categories": ["животное", "предмет"],
      "subjects": [
        { "word": "я", "person": "1", "number": "s" },
        { "word": "ты", "person": "2", "number": "s" },
        { "word": "он", "person": "3", "number": "s" },
        { "word": "мы", "person": "1", "number": "p" }
      ],
      "verbs": {
        "видеть": {
          "1s": "вижу",
          "2s": "видишь",
          "3s": "видит",
          "1p": "видим",
          "2p": "видите",
          "3p": "видят"
        }
      }
    }
  ]
}
```

---

## 4. Алгоритм генерации предложения (Python)

```python
def generate_sentence(word_doc, template, subject_word):
    person = subject_word['person']
    number = subject_word['number']
    subject = subject_word['word']

    verb_dict = template['verbs']['видеть']
    verb_form_key = f"{person}{number}"
    verb = verb_dict.get(verb_form_key, "вижу")

    obj_form = next(
        (f['form'] for f in word_doc['forms'] 
         if f['case'] == 'винительный' and f['number'] == 'единственное'),
        word_doc['lemma']
    )

    sentence = template['pattern'].format(subject=subject, verb=verb, object=obj_form)
    return sentence

# Пример использования
word_doc = {
    "lemma": "собака",
    "forms": [
        {"case": "винительный", "number": "единственное", "form": "собаку"},
        {"case": "именительный", "number": "единственное", "form": "собака"}
    ]
}

template = {
    "pattern": "{subject} {verb} {object}",
    "verbs": {
        "видеть": {
            "1s": "вижу",
            "2s": "видишь",
            "3s": "видит",
            "1p": "видим",
            "2p": "видите",
            "3p": "видят"
        }
    }
}

subject_word = {"word": "я", "person": "1", "number": "s"}

print(generate_sentence(word_doc, template, subject_word))
# Output: Я вижу собаку
```

### Алгоритм генерации предложения
  1.  Определить слово и его категорию (например, «собака» → «животное»).
  2.  Выбрать подходящий шаблон для категории (например, see_object).
  3.  Выбрать субъект (например, «я», «ты» и т.д.).
  4.  По субъекту определить форму глагола из словаря (например, для «я» → «вижу»).
  5.  Выбрать форму слова в нужном падеже и числе (например, винительный падеж, единственное число → «собаку»).
  6.  Подставить элементы в шаблон и сформировать предложение.

⸻

## Особенности работы с разными языками
  • Для каждого языка хранится свой набор шаблонов с учётом порядка слов и грамматических конструкций.
  • Для каждого языка глаголы имеют собственные словари форм для лица, числа, времени.
  • Для языков без склонений (например, английский) формы слова минимальны — чаще только единственное/множественное число.
  • Для сложных языков с большим количеством форм (эстонский, финский, японский) — добавляются дополнительные категории и формы.
  • В шаблонах могут использоваться специальные конструкции, частицы и предлоги.

⸻

## Работа с разнообразием шаблонов и предложений
  • Шаблоны делятся на категории по смыслу слова (животные, предметы, абстрактные и др.).
  • Для каждой категории несколько шаблонов с разными действиями и субъектами.
  • В шаблонах предусмотрено использование разных субъектов (я, ты, он, она, они).
  • Это позволяет генерировать множество различных примеров из небольшого набора шаблонов.
  • Категоризация и шаблоны хранятся отдельно от слов — для лёгкого масштабирования и поддержки.


---

## 5. Масштабируемость и обновления

- **Добавление нового языка**: нужно только добавить формы и шаблоны, не менять остальные языки.
- **Добавление нового слова**: прописать формы и категорию, шаблоны уже есть.
- **Миграции данных**: можно скриптом обновить 5000+ слов, не теряя прогресс пользователей.
- **Отделение данных**: прогресс (userProgress) хранится отдельно от слов, чтобы избежать конфликтов при обновлении.

---

## 6. Итог

- Модульный подход позволяет создать мощную, масштабируемую и многоязычную систему.
- Прост в сопровождении, легко адаптируется под грамматику и порядок слов каждого языка.
- Даёт разнообразие фраз при минимуме шаблонов.
- Подходит для обучения, автоматизации и работы с любыми языками, включая сложные (японский, арабский, финский и др.).

---

*Подготовлено: ChatGPT · 2025-07-16*
