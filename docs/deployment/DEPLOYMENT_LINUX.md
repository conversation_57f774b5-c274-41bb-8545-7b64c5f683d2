# 🐧 Развертывание Word Master на Linux

Руководство по развертыванию приложения Word Master на Linux системах.

## 📋 Системные требования

- **ОС**: Linux (протестировано на Red Hat/CentOS/Fedora)
- **Python**: 3.13+ 
- **Node.js**: 18+ (для фронтенда)
- **MongoDB**: Atlas (облачная) или локальная установка
- **Память**: минимум 2GB RAM
- **Место**: минимум 5GB свободного места

## 🔧 Установка зависимостей

### 1. Установка Python и pip

```bash
# Проверяем версию Python
python3 --version

# Устанавливаем pip если его нет
python3 -m ensurepip --upgrade

# Добавляем pip в PATH
export PATH=$PATH:/home/<USER>/.local/bin
echo 'export PATH=$PATH:/home/<USER>/.local/bin' >> ~/.bashrc
```

### 2. Установка Node.js (для фронтенда)

```bash
# Для Red Hat/CentOS/Fedora
sudo yum install nodejs npm -y

# Или через NodeSource
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install nodejs -y
```

### 3. Установка MongoDB (опционально, если не используете Atlas)

```bash
# Для Red Hat/CentOS/Fedora
sudo yum install mongodb-server -y
sudo systemctl start mongod
sudo systemctl enable mongod
```

## 🚀 Развертывание бэкенда

### 1. Создание виртуального окружения для Linux

```bash
cd backend

# Создаем отдельное виртуальное окружение для Linux
# (НЕ удаляем существующее venv, оно используется на macOS)
python3 -m venv venv-linux

# Активируем виртуальное окружение
source venv-linux/bin/activate

# Обновляем pip
pip install --upgrade pip

# Устанавливаем основные зависимости (совместимые с Python 3.13)
pip install fastapi uvicorn python-dotenv pymongo motor python-jose[cryptography] passlib[bcrypt] python-multipart

# Если нужны дополнительные пакеты из requirements.txt
# pip install -r requirements.txt  # Может не работать с Python 3.13
```

### 2. Настройка переменных окружения

Создайте или проверьте файл `.env` в папке `backend/`:

```bash
# MongoDB Connection (замените на ваши данные)
MONGODB_URL=mongodb+srv://username:<EMAIL>/?retryWrites=true&w=majority

# База данных для слов
WORDS_DATABASE_NAME=word_master

# База данных для пользователей  
USERS_DATABASE_NAME=users_db

# JWT Settings
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=1440

# App Settings
DEBUG=True
ENVIRONMENT=development
API_PREFIX=/api
```

### 3. Импорт слов в базу данных

```bash
cd backend

# Активируем виртуальное окружение Linux
source venv-linux/bin/activate

# Проверка готовности файлов
python3 scripts/check_import_ready.py

# Импорт слов в MongoDB Atlas
python3 scripts/words/import_words.py
```

**Примечание**: Файл `.env` должен содержать правильную строку подключения к MongoDB Atlas. Тот же файл работает на macOS и Linux.

### 4. Запуск бэкенда

```bash
cd backend

# Активируем виртуальное окружение Linux
source venv-linux/bin/activate

# Запуск сервера
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

## 📱 Развертывание фронтенда

### 1. Установка зависимостей

```bash
cd frontend

# Установка зависимостей
npm install

# Или с yarn
yarn install
```

### 2. Настройка API URL

Проверьте файл конфигурации API в `frontend/config/api.ts`:

```typescript
export const API_URL = 'http://YOUR_SERVER_IP:8000';
```

### 3. Запуск фронтенда

```bash
cd frontend

# Запуск в режиме разработки
npm start

# Или с yarn
yarn start

# Для Expo
npx expo start
```

## 🔧 Решение проблем

### Проблема с MongoDB Atlas

**SSL handshake timeout** - распространенная проблема на некоторых Linux серверах:

1. **Проверьте сетевое подключение**:
   ```bash
   ping cluster0.ezh1r.mongodb.net
   nslookup cluster0.ezh1r.mongodb.net
   ```

2. **Проверьте брандмауэр**:
   ```bash
   sudo firewall-cmd --list-all
   sudo firewall-cmd --add-port=27017/tcp --permanent
   sudo firewall-cmd --reload
   ```

3. **Альтернативные решения**:
   - Попробуйте с другого сервера/сети
   - Используйте VPN
   - Обратитесь к администратору сети
   - Используйте Docker (см. раздел Docker ниже)

4. **Временное решение - локальная MongoDB через Docker**:
   ```bash
   # Запуск MongoDB в Docker
   sudo docker run -d --name mongodb -p 27017:27017 mongo:7.0

   # Изменить MONGODB_URL в .env
   MONGODB_URL=mongodb://localhost:27017
   ```

### Проблема с Python зависимостями

**Python 3.13 слишком новый** для некоторых пакетов из requirements.txt:

```bash
# Активируем виртуальное окружение
source venv-linux/bin/activate

# Удаляем конфликтующие пакеты
pip uninstall bson -y  # Удаляем конфликтующий bson

# Устанавливаем совместимые версии
pip install fastapi uvicorn python-dotenv pymongo motor python-jose[cryptography] passlib[bcrypt] python-multipart

# Если нужны дополнительные пакеты, устанавливаем по одному
pip install httpx pytest
```

### Проблема с правами доступа

```bash
# Если нужны права sudo для установки
sudo yum install python3-pip python3-devel -y

# Создание виртуального окружения
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

## 📊 Проверка работы

### 1. Проверка бэкенда

```bash
curl http://localhost:8000/api/health
```

### 2. Проверка базы данных

```bash
cd backend
python3 scripts/check_import_ready.py
```

### 3. Проверка фронтенда

Откройте в браузере: `http://localhost:19006` (Expo) или `http://localhost:3000` (React Native Web)

## 🔄 Автоматический запуск

### Создание systemd сервиса для бэкенда

```bash
sudo nano /etc/systemd/system/wordmaster-backend.service
```

```ini
[Unit]
Description=Word Master Backend
After=network.target

[Service]
Type=simple
User=ivan
WorkingDirectory=/home/<USER>/Memo/backend
Environment=PATH=/home/<USER>/.local/bin:/usr/bin:/bin
ExecStart=/home/<USER>/.local/bin/uvicorn app.main:app --host 0.0.0.0 --port 8000
Restart=always

[Install]
WantedBy=multi-user.target
```

```bash
sudo systemctl daemon-reload
sudo systemctl enable wordmaster-backend
sudo systemctl start wordmaster-backend
```

## 🚀 Быстрое развертывание (два клика)

Создайте скрипт `quick-setup-linux.sh` для автоматической установки:

```bash
#!/bin/bash
echo "🚀 Быстрое развертывание Word Master на Linux"

# Переходим в папку backend
cd backend

# Создаем виртуальное окружение для Linux
echo "📦 Создание виртуального окружения..."
python3 -m venv venv-linux
source venv-linux/bin/activate

# Устанавливаем зависимости
echo "⬇️ Установка зависимостей..."
pip install --upgrade pip
pip install fastapi uvicorn python-dotenv pymongo motor python-jose[cryptography] passlib[bcrypt] python-multipart

# Создаем папки
mkdir -p logs

# Проверяем файлы
echo "🔍 Проверка файлов..."
python3 scripts/check_import_ready.py

# Импортируем слова (если MongoDB доступна)
echo "📥 Импорт слов..."
python3 scripts/words/import_words.py

echo "✅ Установка завершена!"
echo "Для запуска: source venv-linux/bin/activate && uvicorn app.main:app --host 0.0.0.0 --port 8000"
```

Сделайте скрипт исполняемым и запустите:
```bash
chmod +x quick-setup-linux.sh
./quick-setup-linux.sh
```

## 📝 Полезные команды

```bash
# Проверка статуса сервисов
sudo systemctl status wordmaster-backend
sudo systemctl status mongod

# Просмотр логов
sudo journalctl -u wordmaster-backend -f
sudo journalctl -u mongod -f

# Перезапуск сервисов
sudo systemctl restart wordmaster-backend
sudo systemctl restart mongod

# Проверка портов
sudo netstat -tlnp | grep :8000
sudo netstat -tlnp | grep :27017
```

## 🆘 Поддержка

При возникновении проблем:

1. Проверьте логи сервисов
2. Убедитесь, что все порты открыты
3. Проверьте подключение к MongoDB
4. Убедитесь, что все зависимости установлены

---

**Примечание**: Этот документ создан для развертывания на Linux после переноса проекта с macOS.
