# Документация API

## Введение

Добро пожаловать в документацию API для приложения изучения языков. Этот документ предоставляет информацию о доступных эндпоинтах, их параметрах и примерах использования.

## Базовый URL

```
http://localhost:8001/api
```

## Аутентификация

Для доступа к защищенным эндпоинтам требуется аутентификация с использованием JWT токена.

## Эндпоинты

### Получение случайной карточки

Получить случайную карточку для изучения.

**URL**: `/cards/random`

**Метод**: `GET`

**Параметры запроса**:

| Параметр     | Тип     | Обязательный | Описание                                   |
|--------------|---------|--------------|-------------------------------------------|
| native_lang  | string  | Да           | Родной язык пользователя (например, 'ru')  |
| target_lang  | string  | Да           | Изучаемый язык (например, 'en')            |
| level        | string  | Нет          | Уровень сложности (например, 'A1')         |


**Пример запроса**:

```bash
curl -X 'GET' \
  'http://localhost:8001/api/cards/random?native_lang=ru&target_lang=en&level=A1' \
  -H 'accept: application/json'
```

**Пример успешного ответа (200 OK)**:

```json
{
  "native_word": {
    "text": "привет",
    "language": "ru",
    "concept_id": "e4f5g6h7-i8j9-k0l1-m2n3-o4p5q6r7s8t9",
    "translations": [],
    "examples": [
      {
        "sentence": "___, как дела?",
        "correct_answers": ["Привет"],
        "word_info": "Междометие"
      }
    ],
    "tags": ["greeting", "basic"],
    "level": "A1"
  },
  "target_word": {
    "text": "hello",
    "language": "en",
    "concept_id": "e4f5g6h7-i8j9-k0l1-m2n3-o4p5q6r7s8t9",
    "translations": [],
    "examples": [
      {
        "sentence": "___, how are you?",
        "correct_answers": ["Hello"],
        "word_info": "Interjection"
      }
    ],
    "tags": ["greeting", "basic"],
    "level": "A1"
  }
}
```

**Возможные ошибки**:

- `404 Not Found` - Не найдены карточки с указанными параметрами
- `500 Internal Server Error` - Внутренняя ошибка сервера

### Получение карточки по ID концепта

Получить карточку по идентификатору концепта.

**URL**: `/cards/{concept_id}`

**Метод**: `GET`

**Параметры пути**:

| Параметр    | Тип     | Обязательный | Описание                                   |
|-------------|---------|--------------|-------------------------------------------|
| concept_id  | string  | Да           | Идентификатор концепта                     |

**Параметры запроса**:

| Параметр     | Тип     | Обязательный | Описание                                   |
|--------------|---------|--------------|-------------------------------------------|
| native_lang  | string  | Да           | Родной язык пользователя (например, 'ru')  |
| target_lang  | string  | Да           | Изучаемый язык (например, 'en')            |

**Пример запроса**:

```bash
curl -X 'GET' \
  'http://localhost:8001/api/cards/e4f5g6h7-i8j9-k0l1-m2n3-o4p5q6r7s8t9?native_lang=ru&target_lang=en' \
  -H 'accept: application/json'
```

**Пример успешного ответа (200 OK)**:

```json
{
  "native_word": {
    "text": "привет",
    "language": "ru",
    "concept_id": "e4f5g6h7-i8j9-k0l1-m2n3-o4p5q6r7s8t9",
    "translations": [],
    "examples": [
      {
        "sentence": "___, как дела?",
        "correct_answers": ["Привет"],
        "word_info": "Междометие"
      }
    ],
    "tags": ["greeting", "basic"],
    "level": "A1"
  },
  "target_word": {
    "text": "hello",
    "language": "en",
    "concept_id": "e4f5g6h7-i8j9-k0l1-m2n3-o4p5q6r7s8t9",
    "translations": [],
    "examples": [
      {
        "sentence": "___, how are you?",
        "correct_answers": ["Hello"],
        "word_info": "Interjection"
      }
    ],
    "tags": ["greeting", "basic"],
    "level": "A1"
  }
}
```

**Возможные ошибки**:

- `404 Not Found` - Карточка с указанным concept_id не найдена
- `500 Internal Server Error` - Внутренняя ошибка сервера

## Коды состояния HTTP

| Код | Описание                     |
|-----|-----------------------------|
| 200 | Успешный запрос             |
| 400 | Неверные параметры запроса  |
| 401 | Не авторизован              |
| 403 | Доступ запрещен             |
| 404 | Ресурс не найден           |
| 500 | Внутренняя ошибка сервера   |

## Примеры использования

### Получение случайной карточки (JavaScript)

```javascript
const getRandomCard = async () => {
  try {
    const response = await fetch(
      'http://localhost:8001/api/cards/random?native_lang=ru&target_lang=en&level=A1',
      {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Authorization': 'Bearer YOUR_JWT_TOKEN'
        }
      }
    );
    
    if (!response.ok) {
      throw new Error(`Ошибка HTTP: ${response.status}`);
    }
    
    const data = await response.json();
    console.log('Получена карточка:', data);
    return data;
  } catch (error) {
    console.error('Ошибка при получении карточки:', error);
  }
};
```

### Получение карточки по ID концепта (Python)

```python
import requests

def get_card_by_concept_id(concept_id, native_lang='ru', target_lang='en'):
    url = f'http://localhost:8001/api/cards/{concept_id}'
    params = {
        'native_lang': native_lang,
        'target_lang': target_lang
    }
    headers = {
        'Accept': 'application/json',
        'Authorization': 'Bearer YOUR_JWT_TOKEN'
    }
    
    try:
        response = requests.get(url, params=params, headers=headers)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f'Ошибка при получении карточки: {e}')
        return None

# Пример использования
card = get_card_by_concept_id('e4f5g6h7-i8j9-k0l1-m2n3-o4p5q6r7s8t9')
if card:
    print(f"Найдена карточка: {card['target_word']['text']} - {card['native_word']['text']}")
```

## Обратная связь

Если у вас есть вопросы или предложения по улучшению API, пожалуйста, свяжитесь с нами по адресу <EMAIL>.
