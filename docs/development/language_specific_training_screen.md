# Адаптация экрана тренировки для разных языков

## Общий подход
- Базовый стиль для всех языков
- Специфические модификации для проблемных языков
- Система языковых адаптеров для централизованного управления

## Языковые группы и их особенности

### 1. Языки с частицами (KO, JA)
- **Проблема**: Частицы должны визуально соединяться со словом
- **Решение**: 
  - Добавить поле `particles: { prefix: "", suffix: "를" }` в JSON
  - Убрать отступы между инпутом и частицей
  - Принимать ответы как с частицей, так и без

### 2. RTL языки (AR, HE, FA, UR)
- **Проблема**: Направление текста справа налево
- **Решение**:
  - Изменить порядок элементов в предложении
  - Выравнивание по правому краю
  - Специальные стили для RTL контейнеров

### 3. Языки без пробелов (ZH, JA, TH)
- **Проблема**: Сложно определить границы слова
- **Решение**:
  - Визуальное выделение места для ввода
  - Возможно небольшие отступы для визуального разделения

### 4. Языки с диакритическими знаками (TH, VI, KM, MY)
- **Проблема**: Знаки могут выходить за границы строки
- **Решение**:
  - Увеличенная высота строки
  - Специальные шрифты с поддержкой диакритических знаков

## Техническая реализация

```typescript
// Пример системы языковых адаптеров
const languageAdapters = {
  'ko': koreanAdapter,
  'ja': japaneseAdapter,
  'ar': arabicAdapter,
  // ...другие языки
};

// Получение адаптера для конкретного языка
const getLanguageAdapter = (language) => 
  languageAdapters[language] || defaultAdapter;
```

Полная техническая документация в коде.