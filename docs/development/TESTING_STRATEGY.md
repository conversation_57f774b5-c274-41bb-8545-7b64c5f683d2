# Стратегия тестирования ZAK

## 📋 Оглавление тестов

1. Проблема зеленых полосок
2. Проблема повторения выученных слов в предзагрузке
3. ✅ РЕГРЕССИОННЫЙ ТЕСТ - Приоритеты A0 (19 июня 2025) - РЕШЕНА
4. 🚨 БУДУЩИЕ ТЕСТЫ: A1, A2, B1, B2, C1, C2 приоритеты



## 🔧 Требования для имитации реального приложения
Первым делом при создании теста нужно воспроизвести проблему, возникшую на реальном устройстве. А потом уже решать ее (это будет быстрее)

1. **Максимальная эмуляция:** Тест должен максимально эмулировать реальные устройства пользователей. Поэтому:
2. **🚨 ОБЯЗАТЕЛЬНО: Детальный анализ фронтенда** - Перед созданием теста глубоко изучите TrainingScreen и всю логику работы приложения максимально детально (потому что большая часть логики прописана локально во фронтенде)
3. **Предзагрузка карточек:** Используйте точную последовательность как в реальном приложении
4. **Последовательность запросов:** Основной запрос → предзагрузка → ответ → использование предзагруженной карточки → предзагрузка
5. **Исключение карточек:** Используйте `exclude_card_id` параметр точно как во фронтенде
6. **Временные интервалы:** Учитывайте реальные задержки между действиями
7. **Состояние пользователя:** Тестируйте с реальным состоянием прогресса
8. Делай задержку перед ответом 1-3 секунды, чтобы эмулировать реальное использование приложения и размышление пользователя


## 🧹 КРИТИЧЕСКИ ВАЖНО: Очистка данных тестирования
**ОБЯЗАТЕЛЬНОЕ ПРАВИЛО:** Очищать прогресс пользователя перед каждого тестом для обеспечения стабильности и воспроизводимости результатов. И выводить в логи теста что тестовые записи были очищены.

**Почему это важно:**
- Тесты должны быть независимыми и воспроизводимыми
- Остаточные данные от предыдущих тестов могут влиять на результаты
- Выученные слова в предыдущих тестах не должны влиять на новые тесты
- Обеспечивает чистое состояние для каждого теста

**Реализация:**
```javascript
// В начале каждого теста
await clearUserProgress(USER_ID);
```

### Обязательные действия после обнаружения проблем
1. **Документирование проблемы:** ОБЯЗАТЕЛЬНО добавить обнаруженную проблему в файл `problems.md` согласно требованиям в файле `problems.md` и в соответсвии с форматом проблем (№ - заголовок - дата и пр.) 
2. **Исправление проблемы:** Создать и применить исправление
3. **Проверка исправления:** Убедиться, что тест проходит после исправления
4. **КРИТИЧЕСКИ ВАЖНО: Регрессионное тестирование** - Запустить ВСЕ остальные тесты чтобы убедиться, что исправление не сломало другие функции
5. **Очистка тестовых данных:** Очистить прогресс пользователя после завершения тестирования
6. **Обновление документации:** Обновить статус теста в документации

⚠️ **ВНИМАНИЕ:** Часто исправление одной проблемы (например, удаление кэша) ломает другие функции. Регрессионное тестирование ОБЯЗАТЕЛЬНО!

## 🧪 Детальное описание тестов

### 1. Тест зеленых полосок
**Файл:** [`greenBarsInheritance.e2e.test.js`](frontend/tests/e2e/greenBarsInheritance.e2e.test.js)
**Проблема:** [Наследование зеленых полосок](problems.md#проблема-наследования-зеленых-полосок)
**Статус:** ✅ РЕШЕНА

**Описание проблемы:**
Новые карточки наследовали зеленые полоски от выученных слов, что создавало ложное впечатление о прогрессе пользователя.

**Как решали:**
- Детальный анализ TrainingScreen.tsx и логики отображения полосок
- Создание E2E теста с реальным API
- Симуляция логики getProgressBarsInfo() из фронтенда
- Проверка состояния React и данных с сервера

**Результат:**
Проблема оказалась в React Native компоненте, а не в API. Тест подтвердил корректность backend логики.

### 2. Тест повторения выученных слов в предзагрузке
**Файл:** [`realAppSimulationTest.js`](frontend/tests/e2e/realAppSimulationTest.js)
**Проблема:** [Повторение выученных слов](problems.md#проблема-повторения-выученных-слов-в-предзагрузке)
**Статус:** ✅ РЕШЕНА

**Описание проблемы:**
Выученные слова (interval_level=15) появлялись снова в предзагрузке с уровнем 16, что нарушало логику интервального повторения.

**Как решали:**
- Детальный анализ useCardPreloader() и логики предзагрузки
- Создание точной имитации Expo Go приложения
- Воспроизведение полной последовательности: основной запрос → предзагрузка → ответ → переход
- Исправление кэширования в backend для предзагрузки

**Результат:**
Проблема решена отключением кэша для предзагрузки. Все регрессионные тесты проходят успешно.

### 3. Тест исчерпания словаря при предзагрузке
**Файл:** [`vocabularyExhaustionTest.js`](frontend/tests/e2e/vocabularyExhaustionTest.js)
**Проблема:** [Ошибка 404 при предзагрузке на 7-8 карточке](problems.md#проблема-7-404-ошибки-в-предзагрузке-при-исчерпании-словаря)
**Статус:** ✅ ПРОБЛЕМА ВОСПРОИЗВЕДЕНА

**Описание проблемы:**
На карточке 15 (после изучения 14 слов) предзагрузка получает ошибку 404 "No cards available for review", хотя основной запрос продолжает работать.

**Результат тестирования:**
✅ **Проблема успешно воспроизведена!**
- Карточки 1-14: Все работает нормально
- Карточка 15: Основной запрос работает, предзагрузка возвращает 404
- Корневая причина: Разная логика между основным запросом и предзагрузкой

**Корневая причина:**
Функция `_add_word_to_progress` возвращает `False` для уже изученных слов, что приводит к 404 в предзагрузке, но основной запрос обрабатывает это через рекурсию.

**Следующие шаги:**
Требуется исправление логики в backend для унификации поведения основного запроса и предзагрузки.

### 📋 Дополнительные тесты
- [`simpleApiTest.js`](frontend/tests/e2e/simpleApiTest.js) - Простая проверка API
- [`intervalProgressionTest.js`](frontend/tests/e2e/intervalProgressionTest.js) - Проверка интервалов
- [`quickLearnedWordsTest.js`](frontend/tests/e2e/quickLearnedWordsTest.js) - Быстрая проверка
- [`databaseDiagnosticTest.js`](frontend/tests/e2e/databaseDiagnosticTest.js) - Диагностика базы
- [`clearUserProgress.js`](frontend/tests/e2e/clearUserProgress.js) - Очистка прогресса

## 🎯 Цель
Создать комплексную систему тестирования для предотвращения регрессий и автоматизации проверки функционала.

## 📊 Структура тестирования

### 1. **Backend Tests (pytest)**
- **Путь**: `backend/tests/`
- **Покрытие**: API endpoints, spaced repetition логика, база данных
- **Запуск**: `pytest backend/tests/`

### 2. **Frontend Unit Tests (Jest)**
- **Путь**: `frontend/tests/unit/`
- **Покрытие**: Компоненты React, хуки, утилиты
- **Запуск**: `npm test`

### 3. **Frontend Integration Tests**
- **Путь**: `frontend/tests/integration/`
- **Покрытие**: Взаимодействие компонентов, API calls
- **Запуск**: `npm run test:integration`

### 4. **E2E Tests (Detox)**
- **Путь**: `frontend/e2e/`
- **Покрытие**: Полные пользовательские сценарии
- **Запуск**: `npm run e2e:test`

## 🧪 Критические тестовые сценарии

### Сценарий 1: "Новое слово с первого раза"
```
1. Загрузить новую карточку
2. Ответить правильно с первого раза
3. Проверить: interval_level = 15, is_learned = true
4. Проверить: 5 зеленых полосок
5. Перейти к следующей карточке
6. Проверить: новая карточка БЕЗ наследования прогресса
```

### Сценарий 2: "Новое слово НЕ с первого раза"
```
1. Загрузить новую карточку
2. Ответить неправильно
3. Ответить правильно
4. Проверить: interval_level < 15, is_learned = false
5. Проверить: НЕ 5 зеленых полосок
```

### Сценарий 3: "Использование подсказок"
```
1. Загрузить новую карточку
2. Нажать "I don't know" / "I don't remember"
3. Ответить правильно
4. Проверить: НЕ считается выученным
5. Проверить: НЕ 5 зеленых полосок
```

### Сценарий 4: "Предзагрузка и переходы"
```
1. Загрузить карточку A
2. Проверить предзагрузку карточки B
3. Ответить на карточку A
4. Проверить мгновенный переход к B
5. Проверить: карточка B БЕЗ наследования от A
```

## 🚀 Этапы внедрения

### Этап 1: Базовая структура (сегодня)
- [x] Создать документацию
- [ ] Настроить Jest для frontend
- [ ] Создать первые unit тесты
- [ ] Настроить pytest для backend

### Этап 2: Критические сценарии (завтра)
- [ ] Тесты прогресса карточек
- [ ] Тесты переходов между карточками
- [ ] Тесты подсказок

### Этап 3: E2E тестирование (послезавтра)
- [ ] Настроить Detox
- [ ] Полные пользовательские сценарии
- [ ] CI/CD интеграция

## 📁 Структура файлов

```
backend/
├── tests/
│   ├── test_spaced_repetition.py
│   ├── test_api_endpoints.py
│   └── test_user_progress.py

frontend/
├── tests/
│   ├── unit/
│   │   ├── TrainingScreen.test.tsx
│   │   ├── cardPreloader.test.ts
│   │   └── progressBars.test.ts
│   ├── integration/
│   │   ├── cardTransitions.test.tsx
│   │   └── apiIntegration.test.tsx
│   └── e2e/
│       ├── newWordFlow.e2e.js
│       └── hintsFlow.e2e.js
```

## 🔧 Инструменты

- **Backend**: pytest, pytest-asyncio, httpx
- **Frontend**: Jest, React Testing Library, MSW (mocking)
- **E2E**: Detox, Appium
- **CI/CD**: GitHub Actions

## 📊 Метрики успеха

- **Покрытие кода**: >80%
- **Время выполнения**: <5 минут для всех тестов
- **Стабильность**: 0 регрессий в критических сценариях

---

## 🚨 БУДУЩИЕ ТЕСТЫ: A1, A2, B1, B2, C1, C2 приоритеты

### 📋 ОБЯЗАТЕЛЬНЫЕ ТЕСТЫ ДЛЯ КАЖДОГО НОВОГО УРОВНЯ:

#### 🧪 Тест №4: A1 приоритеты (БУДУЩИЙ)
**Файл:** `frontend/tests/e2e/a1PriorityExhaustionTest.js` (создать)
**Цель:** Проверить переходы ultra_core → core → extended для A1
**Базовый шаблон:** Скопировать `vocabularyExhaustionTest.js`
**Адаптация:** Изменить уровень с "A0" на "A1"

#### 🧪 Тест №5: A2 приоритеты (БУДУЩИЙ)
**Файл:** `frontend/tests/e2e/a2PriorityExhaustionTest.js` (создать)
**Цель:** Проверить переходы ultra_core → core → extended для A2
**Базовый шаблон:** Скопировать `vocabularyExhaustionTest.js`
**Адаптация:** Изменить уровень с "A0" на "A2"

#### 🧪 Тест №6: B1 приоритеты (БУДУЩИЙ)
**Файл:** `frontend/tests/e2e/b1PriorityExhaustionTest.js` (создать)
**Цель:** Проверить переходы ultra_core → core → extended для B1
**Базовый шаблон:** Скопировать `vocabularyExhaustionTest.js`
**Адаптация:** Изменить уровень с "A0" на "B1"

#### 🧪 Тест №7: B2 приоритеты (БУДУЩИЙ)
**Файл:** `frontend/tests/e2e/b2PriorityExhaustionTest.js` (создать)
**Цель:** Проверить переходы ultra_core → core → extended для B2
**Базовый шаблон:** Скопировать `vocabularyExhaustionTest.js`
**Адаптация:** Изменить уровень с "A0" на "B2"

#### 🧪 Тест №8: C1 приоритеты (БУДУЩИЙ)
**Файл:** `frontend/tests/e2e/c1PriorityExhaustionTest.js` (создать)
**Цель:** Проверить переходы ultra_core → core → extended для C1
**Базовый шаблон:** Скопировать `vocabularyExhaustionTest.js`
**Адаптация:** Изменить уровень с "A0" на "C1"

#### 🧪 Тест №9: C2 приоритеты (БУДУЩИЙ)
**Файл:** `frontend/tests/e2e/c2PriorityExhaustionTest.js` (создать)
**Цель:** Проверить переходы ultra_core → core → extended для C2
**Базовый шаблон:** Скопировать `vocabularyExhaustionTest.js`
**Адаптация:** Изменить уровень с "A0" на "C2"

### 🔧 ШАБЛОН ДЛЯ СОЗДАНИЯ НОВЫХ ТЕСТОВ:

```javascript
// Пример для A1 уровня
const LEVEL = "A1";
const EXPECTED_ULTRA_CORE_COUNT = 7; // Обновить по факту
const EXPECTED_CORE_COUNT = 6;       // Обновить по факту
const EXPECTED_EXTENDED_COUNT = 2;   // Обновить по факту

// Остальная логика остается такой же как в vocabularyExhaustionTest.js
```

### 🚨 КРИТИЧЕСКИЕ ТРЕБОВАНИЯ:

1. **СОЗДАВАЙТЕ ТЕСТ СРАЗУ** при добавлении нового уровня
2. **ЗАПУСКАЙТЕ ВСЕ ТЕСТЫ** после изменений в системе приоритетов
3. **ДОКУМЕНТИРУЙТЕ РЕЗУЛЬТАТЫ** в problems.md
4. **ОБНОВЛЯЙТЕ ЭТОТ ФАЙЛ** при добавлении новых тестов

### 📞 АЛГОРИТМ ДЕЙСТВИЙ ПРИ ДОБАВЛЕНИИ НОВОГО УРОВНЯ:

1. **Добавить слова в базу** с приоритетами ultra_core, core, extended
2. **Скопировать тест** `vocabularyExhaustionTest.js` → `{level}PriorityExhaustionTest.js`
3. **Адаптировать тест** для нового уровня
4. **Запустить тест** и убедиться, что он проходит
5. **Добавить в регрессионный набор** тестов
6. **Обновить документацию** (этот файл и problems.md)

**Дата создания:** 2025-06-19
**Статус:** 🚨 ОБЯЗАТЕЛЬНО К ВЫПОЛНЕНИЮ
