import React, { useState, useRef, useCallback, useEffect } from 'react';
import { 
  StyleSheet, 
  View, 
  Text, 
  TextInput, 
  TouchableOpacity, 
  SafeAreaView, 
  ScrollView, 
  Animated, 
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  StatusBar,
  Vibration
} from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { Canvas, RadialGradient, Rect, vec, Circle, rrect, rect, BackdropBlur, Blur, Group } from '@shopify/react-native-skia';
import * as Haptics from 'expo-haptics';

// API
import { fetchRandomCard, Example, Word as APIWord, Card as APICard } from './services/api';

// Types
interface Card {
  id: string;
  word: string;
  translation: string;
  translationWord: string;
  english: string;
  progress: number;
  level: string;
  examples: Example[];
  wordInfo?: {
    type: string;
    form: string;
    transcription: string;
  };
  type: string;
  form: string;
  transcription: string;
  native_word: APIWord;
  target_word: APIWord;
};

// Screen dimensions
const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
const baseScreenSize = Math.min(screenWidth, screenHeight);

// Gradient settings
const spheres = [
  {
    id: 'sphere-1',
    cx: screenWidth * 0.15,
    cy: screenHeight * 0.2,
    r: baseScreenSize * 1.2,
    colors: ['rgba(170, 70, 255, 0.3)', 'rgba(170, 70, 255, 0)']
  },
  {
    id: 'sphere-2',
    cx: screenWidth * 0.85,
    cy: screenHeight * 0.3,
    r: baseScreenSize * 0.7,
    colors: ['rgba(255, 220, 50, 0.3)', 'rgba(255, 220, 50, 0)']
  },
  {
    id: 'sphere-3',
    cx: screenWidth * 0.15,
    cy: screenHeight * 0.6,
    r: baseScreenSize * 1.8,
    colors: ['rgba(0, 200, 200, 0.3)', 'rgba(0, 200, 200, 0)']
  },
  {
    id: 'sphere-4',
    cx: screenWidth * 0.95,
    cy: screenHeight * 0.75,
    r: baseScreenSize * 1.0,
    colors: ['rgba(220, 100, 170, 0.3)', 'rgba(220, 100, 170, 0)']
  }
];

// Haptic feedback function
const triggerHaptic = (type: 'success' | 'error' | 'warning' = 'success') => {
  if (type === 'success') {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium).catch(() => {});
  } else if (type === 'error') {
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error).catch(() => {});
  } else {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy).catch(() => {});
  }
};

export default function App() {
  const [isLoading, setIsLoading] = useState(true);
  const [currentCard, setCurrentCard] = useState<Card | null>(null);
  const [currentCardNumber, setCurrentCardNumber] = useState(1);
  const [totalCardsInDeck] = useState(20);
  const [userInput, setUserInput] = useState('');
  const [isCorrect, setIsCorrect] = useState<boolean | null>(null);
  const [showHint, setShowHint] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [wordProgress, setWordProgress] = useState(1);
  const [feedbackMessage, setFeedbackMessage] = useState('');
  const inputRef = useRef<TextInput>(null);
  const feedbackAnim = useRef(new Animated.Value(0)).current;

  // Animation values
  const slideAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const shakeAnim = useRef(new Animated.Value(0)).current;
  
  // Shake animation configuration
  const shakeAnimation = {
    0: { translateX: 0 },
    0.1: { translateX: -10 },
    0.2: { translateX: 10 },
    0.3: { translateX: -8 },
    0.4: { translateX: 8 },
    0.5: { translateX: -5 },
    0.6: { translateX: 5 },
    0.7: { translateX: -3 },
    0.8: { translateX: 3 },
    0.9: { translateX: -1 },
    1: { translateX: 0 },
  };
  
  // Animate card out - updated with smooth transition
  const animateCardOut = useCallback(() => {
    // Reset animations
    slideAnim.setValue(0);
    
    // Animate out to the left with slight rotation
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: -screenWidth,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 0.9,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(rotateAnim, {
        toValue: -5,
        duration: 300,
        useNativeDriver: true,
      })
    ]).start(() => {
      // Reset animations for next card before fetching
      slideAnim.setValue(screenWidth);
      scaleAnim.setValue(0.9);
      rotateAnim.setValue(5);
      
      // Update card number
      setCurrentCardNumber(prev => (prev % totalCardsInDeck) + 1);
      
      // Fetch new card
      fetchNewCard().then(() => {
        // Animate new card in after fetch completes
        Animated.parallel([
          Animated.spring(slideAnim, {
            toValue: 0,
            useNativeDriver: true,
            friction: 8,
            tension: 60,
          }),
          Animated.spring(scaleAnim, {
            toValue: 1,
            useNativeDriver: true,
            friction: 8,
            tension: 60,
          }),
          Animated.spring(rotateAnim, {
            toValue: 0,
            useNativeDriver: true,
            friction: 8,
            tension: 60,
          })
        ]).start();
        
        // Focus input after animation
        setTimeout(() => {
          inputRef.current?.focus();
        }, 50);
      });
    });
  }, [totalCardsInDeck, fetchNewCard]);

  // Check user's answer
  const checkAnswer = useCallback(() => {
    if (!currentCard) return;
    
    const isAnswerCorrect = currentCard.target_word.examples?.[0]?.correct_answers?.some(
      answer => answer.toLowerCase() === userInput.trim().toLowerCase()
    ) || false;
    
    setIsCorrect(isAnswerCorrect);
    
    if (isAnswerCorrect) {
      // Correct answer
      triggerHaptic('success');
      
      // Show feedback and go to next card
      setTimeout(() => {
        setUserInput('');
        setIsCorrect(null);
        setShowHint(false);
        // Use animateCardOut for smooth transition
        animateCardOut();
      }, 1000);
    } else {
      // Wrong answer - trigger haptic feedback
      triggerHaptic('error');
      
      // Enhanced shake animation
      Animated.sequence([
        // Shake left
        Animated.timing(shakeAnim, {
          toValue: -10,
          duration: 50,
          useNativeDriver: true,
        }),
        // Shake right
        Animated.timing(shakeAnim, {
          toValue: 10,
          duration: 50,
          useNativeDriver: true,
        }),
        // Shake left
        Animated.timing(shakeAnim, {
          toValue: -10,
          duration: 50,
          useNativeDriver: true,
        }),
        // Shake right
        Animated.timing(shakeAnim, {
          toValue: 10,
          duration: 50,
          useNativeDriver: true,
        }),
        // Return to center
        Animated.timing(shakeAnim, {
          toValue: 0,
          duration: 50,
          useNativeDriver: true,
        })
      ]).start();
      
      // Auto-advance after delay if still incorrect
      setTimeout(() => {
        setUserInput('');
        setIsCorrect(null);
        setShowHint(false);
        // Use animateCardOut for smooth transition
        animateCardOut();
      }, 1500);
    }
  }, [currentCard, userInput, shakeAnim, animateCardOut]);

  // Handle input submit
  const handleSubmit = useCallback(() => {
    if (userInput.trim()) {
      checkAnswer();
    }
  }, [userInput, checkAnswer]);

  // Get Russian example sentence with correct answer inserted
  const { russianSentence, russianParts, englishSentence } = React.useMemo(() => {
    if (!currentCard?.native_word?.examples?.[0]) {
      return { 
        russianSentence: '',
        russianParts: { before: '', word: '', after: '' },
        englishSentence: currentCard?.examples?.[0]?.sentence || ''
      };
    }
    
    const russianExample = currentCard.native_word.examples[0];
    const russianText = russianExample.sentence || '';
    const correctAnswer = russianExample.correct_answers?.[0] || currentCard.native_word.word;
    
    // Find the position of the blank (___)
    const blankIndex = russianText.indexOf('___');
    
    if (blankIndex === -1) {
      // If no blank, just return the sentence as is
      return {
        russianSentence: russianText,
        russianParts: { before: russianText, word: '', after: '' },
        englishSentence: currentCard.examples?.[0]?.sentence || ''
      };
    }
    
    // Replace the blank with the correct answer and split for highlighting
    const beforeBlank = russianText.substring(0, blankIndex);
    const afterBlank = russianText.substring(blankIndex + 3); // 3 characters for '___'
    
    return {
      russianSentence: beforeBlank + correctAnswer + afterBlank,
      russianParts: {
        before: beforeBlank,
        word: correctAnswer,
        after: afterBlank
      },
      englishSentence: currentCard.examples?.[0]?.sentence || ''
    };
  }, [currentCard]);

  // Split the English sentence for input
  const [beforeBlank, afterBlank] = React.useMemo(() => {
    if (!currentCard?.examples?.[0]) return ['', ''];
    
    const example = currentCard.examples[0];
    const sentence = example.sentence || '';
    const index = sentence.indexOf('___');
    
    if (index === -1) return [sentence, ''];
    
    return [
      sentence.substring(0, index),
      sentence.substring(index + 3) // 3 characters for '___'
    ];
  }, [currentCard]);

  // Get translation parts for the word hint
  const translationParts = React.useMemo(() => {
    if (!currentCard?.native_word) return ['', '', ''];
    
    const translation = currentCard.native_word.word || '';
    
    // Return empty strings for before and after, and the full translation for the middle part
    // The word_info will be added separately in the JSX
    return ['', translation, ''];
  }, [currentCard?.native_word]);

  // Calculate input width based on the target word length
  const inputWidth = React.useMemo(() => {
    if (!currentCard?.examples?.[0]?.correct_answers?.[0]) return 100;
    return currentCard.examples[0].correct_answers[0].length * 12 + 16;
  }, [currentCard]);

  // Fetch a new random card
  const fetchNewCard = useCallback(async () => {
    try {
      setIsLoading(true);
      const data = await fetchRandomCard();
      
      if (!data || !data.target_word) {
        throw new Error('Invalid card data received');
      }
      
      // Логируем структуру данных и получаем word_info из примера, если он есть
      const example = data.target_word.examples?.[0];
      // Отладочный вывод структуры слова закомментирован
      // console.log('Target word structure:', {
      //   allProps: Object.keys(data.target_word),
      //   word: data.target_word.word,
      //   examples: example 
      //     ? {
      //         allProps: Object.keys(example),
      //         sentence: example.sentence,
      //         correct_answers: example.correct_answers,
      //         word_info: (example as any).word_info
      //       } 
      //     : 'No examples'
      // });
      
      const wordInfo = example?.word_info; // Будет undefined, если поля нет
      
      // Transform API data to card format
      const newCard: Card = {
        id: data.target_word._id,
        word: data.target_word.word,
        translation: data.native_word?.word || '',
        translationWord: data.native_word?.word || '',
        english: example?.sentence || '',
        progress: 0, // Initial progress
        level: data.target_word.level || 'A1',
        examples: data.target_word.examples || [],
        wordInfo: wordInfo ? {
          type: wordInfo, // Используем word_info из примера
          form: '',
          transcription: ''
        } : undefined, // Не создаем wordInfo, если нет данных
        type: '',
        form: '',
        transcription: '',
        native_word: data.native_word,
        target_word: data.target_word
      };
      
      setCurrentCard(newCard);
      setUserInput('');
      setIsCorrect(null);
      setFeedbackMessage('');
      setShowHint(false);
      setWordProgress(1); // Reset progress for new word
      setCurrentCardNumber(prev => (prev % totalCardsInDeck) + 1);
    } catch (error) {
      console.error('Error fetching card:', error);
      // Retry after delay
      setTimeout(() => fetchNewCard(), 2000);
    } finally {
      setIsLoading(false);
    }
  }, [totalCardsInDeck]);

  // Load the first card on mount
  useEffect(() => {
    fetchNewCard();
  }, [fetchNewCard]);



  // Animate feedback in when isCorrect changes
  useEffect(() => {
    if (isCorrect !== null) {
      // Reset and animate in
      feedbackAnim.setValue(0);
      Animated.spring(feedbackAnim, {
        toValue: 1,
        useNativeDriver: true,
        friction: 8,
        tension: 40,
      }).start();
    } else {
      feedbackAnim.setValue(0);
    }
  }, [isCorrect, feedbackAnim]);

  // Handle text input changes
  const handleInputChange = (text: string) => {
    setUserInput(text);
    // Прогресс больше не обновляется при вводе
  };

  // Show loading indicator while fetching card
  if (isLoading || !currentCard) {
    return (
      <SafeAreaProvider>
        <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
          <ActivityIndicator size="large" color="#7fb4ff" />
          <Text style={{ marginTop: 16, color: '#fff' }}>Loading card...</Text>
        </View>
      </SafeAreaProvider>
    );
  }

  return (
    <SafeAreaProvider>
      <StatusBar barStyle="light-content" />
      <View style={styles.container}>
        {/* Gradient Background */}
        <View style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, backgroundColor: '#000000' }}>
          <Canvas style={{ flex: 1 }}>
            {spheres.map((sphere) => (
              <Circle
                key={sphere.id}
                cx={sphere.cx}
                cy={sphere.cy}
                r={sphere.r}
              >
                <RadialGradient
                  c={vec(sphere.cx, sphere.cy)}
                  r={sphere.r}
                  colors={sphere.colors}
                />
              </Circle>
            ))}
            {/* Semi-transparent overlay */}
            <Rect 
              x={0} 
              y={0} 
              width={screenWidth} 
              height={screenHeight} 
              color="rgba(0, 0, 0, 0.5)" 
              blendMode="srcOver"
            />
          </Canvas>
        </View>
        
        <SafeAreaView style={styles.safeArea}>
          {/* Progress Bar Header */}
          <View style={styles.progressHeader}>
            <View style={styles.progressContainer}>
              <Text style={styles.progressText}>
                {currentCardNumber} of {totalCardsInDeck} cards
              </Text>
              <View style={styles.progressBarContainer}>
                <Animated.View style={[styles.progressBar, { width: `${(currentCardNumber / totalCardsInDeck) * 100}%` }]}>
                  <LinearGradient
                    colors={['#7fb4ff', '#4a7dff']}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    style={styles.progressGradient}
                  />
                </Animated.View>
              </View>
            </View>
          </View>
          
          <ScrollView 
            contentContainerStyle={styles.scrollView} 
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
          >
            {/* Main content */}
            <View style={styles.content}>
              {/* Main Card */}
              <Animated.View 
                style={[
                  styles.card,
                  {
                    transform: [
                      { 
                        translateX: Animated.add(
                          slideAnim,
                          shakeAnim
                        )
                      },
                      { scale: scaleAnim },
                      { 
                        rotate: rotateAnim.interpolate({
                          inputRange: [-360, 360],
                          outputRange: ['-360deg', '360deg']
                        })
                      }
                    ]
                  }
                ]}
              >
                <View style={StyleSheet.absoluteFill}>
                  <Canvas style={{ flex: 1 }}>
                    <BackdropBlur blur={20} clip={rrect(rect(0, 0, screenWidth - 16, 400), 20, 20)}>
                      <Rect x={0} y={0} width={screenWidth} height={screenHeight} color="rgba(20, 20, 35, 0.25)" />
                      {/* Fog effect sphere */}
                      <Group>
                        <Circle cx={-50} cy={450} r={350}>
                          <RadialGradient
                            c={vec(-50, 450)}
                            r={350}
                            colors={[
                              'rgba(120, 200, 255, 0.4)',
                              'rgba(120, 200, 255, 0.25)',
                              'rgba(120, 200, 255, 0.1)',
                              'rgba(120, 200, 255, 0.02)',
                              'rgba(120, 200, 255, 0)'
                            ]}
                            positions={[0, 0.2, 0.5, 0.8, 1]}
                          />
                        </Circle>
                      </Group>
                      <Blur blur={15} />
                    </BackdropBlur>
                  </Canvas>
                </View>
                <View style={styles.cardContent}>
                  {/* Russian translation with grammar hint */}
                  <View style={styles.translationContainer}>
                    <View style={styles.grammarHintContainer}>
                      <Text style={styles.wordWithHint}>
                        {translationParts[1]}
                        {currentCard.wordInfo?.type ? (
                          <>
                            <Text style={styles.wordHintSeparator}> — </Text>
                            <Text style={styles.wordInfoText}>
                              {currentCard.wordInfo.type}
                            </Text>
                          </>
                        ) : null}
                      </Text>
                    </View>
                    <Text style={styles.translation}>
                      {russianParts.before}
                      <Text style={styles.highlightedWord}>{russianParts.word}</Text>
                      {russianParts.after}
                    </Text>
                    
                    <View style={styles.dividerContainer}>
                      <View style={[styles.dividerLine, styles.dividerTop]} />
                      <View style={[styles.dividerLine, styles.dividerBottom]} />
                    </View>
                  </View>

                  {/* English sentence with inline input */}
                  <View style={styles.sentenceContainer}>
                    <Text style={styles.sentence}>
                      {beforeBlank}
                      {' '}
                      <View style={[styles.inputWrapper, { width: inputWidth }]}>
                        <TextInput
                          ref={inputRef}
                          style={[
                            styles.inlineInput,
                            isFocused && styles.inlineInputFocused,
                            isCorrect === true && styles.inlineInputCorrect,
                            isCorrect === false && styles.inlineInputIncorrect,
                            { width: inputWidth }
                          ]}
                          value={userInput}
                          onChangeText={setUserInput}
                          placeholder=""
                          placeholderTextColor="transparent"
                          autoCapitalize="none"
                          autoCorrect={false}
                          autoFocus
                          onFocus={() => setIsFocused(true)}
                          onBlur={() => setIsFocused(false)}
                          onSubmitEditing={handleSubmit}
                          returnKeyType="done"
                          selectionColor="rgba(127, 180, 255, 0.3)"
                          cursorColor="#7fb4ff"
                          underlineColorAndroid="transparent"
                          caretHidden={false}
                          editable={isCorrect === null}
                        />
                      </View>
                      {' '}
                      {afterBlank}
                    </Text>
                  </View>
                  
                  {/* Word progress indicator */}
                  <View style={styles.wordProgressContainer}>
                    {[1, 2, 3, 4, 5].map((step) => {
                      let color = '#3a3a4a'; // Default gray
                      if (step <= wordProgress) {
                        // Set color based on progress
                        if (wordProgress <= 1) color = '#FFD700'; // Yellow
                        else if (wordProgress === 2) color = '#FFA500'; // Orange
                        else if (wordProgress === 3) color = '#7fb4ff'; // Blue
                        else if (wordProgress === 4) color = '#4a7dff'; // Darker blue
                        else color = '#4CAF50'; // Green
                      }
                      return (
                        <View 
                          key={step}
                          style={[
                            styles.wordProgressStep,
                            { backgroundColor: color }
                          ]}
                        />
                      );
                    })}
                  </View>

                  {/* Hint icon */}
                  <View style={styles.hintIconContainer}>
                    <TouchableOpacity 
                      onPress={() => setShowHint(!showHint)}
                      style={styles.hintIconButton}
                      disabled={isCorrect !== null}
                    >
                      <Ionicons 
                        name={showHint ? "bulb" : "bulb-outline"} 
                        size={24} 
                        color="rgba(255, 255, 255, 0.6)" 
                      />
                    </TouchableOpacity>
                  </View>

                  {/* Hint text */}
                  {showHint && currentCard.examples?.[0]?.correct_answers?.[0] && (
                    <View style={styles.hintContainer}>
                      <Text style={styles.hintText}>
                        {currentCard.examples[0].correct_answers[0].split('').map((letter: string, index: number) => (
                          <Text key={index} style={styles.hintLetter}>
                            {index < userInput.length ? userInput[index] : '_'}
                          </Text>
                        ))}
                      </Text>
                    </View>
                  )}
                </View>
              </Animated.View>

              {/* Feedback container */}
              <View style={styles.bottomContainer}>
                {/* Feedback message */}
                {isCorrect !== null && (
                  <Animated.View 
                    style={[
                      feedbackStyles.feedbackContainer,
                      isCorrect ? feedbackStyles.feedbackCorrect : feedbackStyles.feedbackIncorrect,
                      { 
                        opacity: feedbackAnim,
                        transform: [
                          { 
                            translateY: feedbackAnim.interpolate({
                              inputRange: [0, 1],
                              outputRange: [10, 0]
                            })
                          }
                        ]
                      }
                    ]}
                  >
                    <Ionicons 
                      name={isCorrect ? "checkmark-circle" : "close-circle"} 
                      size={24} 
                      color={isCorrect ? "#4caf50" : "#f44336"} 
                    />
                    <Text style={feedbackStyles.feedbackText}>
                      {isCorrect ? 'Правильно! Молодец!' : 'Попробуйте еще раз'}
                    </Text>
                  </Animated.View>
                )}
              </View>
            </View>
          </ScrollView>
        </SafeAreaView>
      </View>
    </SafeAreaProvider>
  );
}

// Word progress styles
const progressStyles = StyleSheet.create({
  wordProgressContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 10,
  },
  wordProgressStep: {
    width: 24,
    height: 4,
    borderRadius: 2,
    marginHorizontal: 2,
    backgroundColor: '#3a3a4a',
  },
});

// Feedback styles
const feedbackStyles = StyleSheet.create({
  feedbackContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginTop: 16,
    marginBottom: 8,
    alignSelf: 'center',
  },
  feedbackCorrect: {
    backgroundColor: 'rgba(76, 175, 80, 0.15)',
  },
  feedbackIncorrect: {
    backgroundColor: 'rgba(244, 67, 54, 0.15)',
  },
  feedbackText: {
    color: 'white',
    fontSize: 16,
    marginLeft: 8,
    fontWeight: '500',
  },
});

// Main styles
const styles = StyleSheet.create({
  // Bottom container for progress and feedback
  bottomContainer: {
    marginTop: 20,
    marginBottom: 10,
    alignItems: 'center',
  },
  // Layout
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  safeArea: {
    flex: 1,
    paddingHorizontal: 8,
    paddingTop: 80,
    paddingBottom: 24,
  },
  scrollView: {
    flex: 1,
    padding: 0,
  },
  content: {
    flex: 1,
    paddingHorizontal: 8,
    paddingTop: 16,
  },
  
  // Cards
  card: {
    borderRadius: 14,
    overflow: 'hidden',
    marginBottom: 16,
    marginTop: 10,
    backgroundColor: 'rgba(20, 20, 35, 0.25)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.08)',
    width: '100%',
  },
  cardContent: {
    position: 'relative',
    zIndex: 1,
    paddingTop: 44,
    paddingHorizontal: 32,
    paddingBottom: 24,
  },
  
  // Progress Header
  progressHeader: {
    backgroundColor: 'rgba(20, 20, 35, 0.7)',
    paddingTop: 0,
    paddingBottom: 30,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  progressContainer: {
    width: '100%',
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
    textAlign: 'center',
  },
  progressBarContainer: {
    width: '100%',
    height: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    borderRadius: 4,
  },
  progressGradient: {
    flex: 1,
    borderRadius: 6,
    height: '100%',
  },
  
  // Hint icon styles
  hintIconContainer: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    top: undefined,
  },
  hintIconButton: {
    padding: 8,
  },
  
  // Hint styles
  hintContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    padding: 12,
    borderRadius: 8,
    marginTop: 16,
  },
  hintText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 16,
    fontFamily: 'monospace',
    letterSpacing: 2,
    textAlign: 'center',
  },
  hintLetter: {
    color: '#7fb4ff',
    fontWeight: 'bold',
    fontSize: 18,
  },
  
  // Sentence and input
  sentenceContainer: {
    marginTop: 4,
    marginBottom: 0,
    paddingBottom: 8,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'center',
    height: 32,
    position: 'relative',
    verticalAlign: 'bottom',
    marginHorizontal: 12,
    display: 'flex' as 'flex',
  },
  sentence: {
    fontSize: 22,
    color: '#e6e4ff',
    lineHeight: 34,
    textAlign: 'left',
    marginBottom: 12,
    marginTop: 4,
    textAlignVertical: 'center',
    fontWeight: '400',
    paddingHorizontal: 2,
  },
  inlineInput: {
    color: '#f0f5ff',
    fontSize: 22,
    lineHeight: 28,
    height: 28,
    padding: 0,
    paddingHorizontal: 5,
    margin: 0,
    marginBottom: -7,
    marginHorizontal: 5,
    textAlign: 'center',
    minWidth: 60,
    backgroundColor: 'rgba(200, 210, 230, 0.08)',
    borderBottomWidth: 2,
    borderBottomColor: 'rgba(127, 180, 255, 0.6)',
    borderWidth: 0,
    borderRadius: 4,
    textAlignVertical: 'center',
    includeFontPadding: false,
  },
  inlineInputFocused: {
    backgroundColor: 'rgba(180, 200, 240, 0.15)',
    borderBottomColor: '#7fb4ff',
    borderBottomWidth: 2.5,
  },
  inlineInputCorrect: {
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    borderBottomColor: '#4caf50',
  },
  inlineInputIncorrect: {
    backgroundColor: 'rgba(244, 67, 54, 0.1)',
    borderBottomColor: '#f44336',
  },
  highlightedWord: {
    color: '#7fb4ff',
    fontWeight: '500',
    backgroundColor: 'rgba(127, 180, 255, 0.12)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    fontStyle: 'normal',
    textDecorationLine: 'underline',
    textDecorationStyle: 'dotted',
    textDecorationColor: 'rgba(127, 180, 255, 0.7)',
  },
  
  // Grammar hint styles
  grammarHintContainer: {
    marginBottom: 8,
    paddingHorizontal: 6,
    borderLeftWidth: 2,
    borderLeftColor: 'rgba(127, 180, 255, 0.3)',
    paddingLeft: 8,
    alignSelf: 'flex-start',
    width: '100%',
  },
  grammarHintText: {
    color: 'rgba(160, 180, 220, 0.6)',
    fontSize: 11,
    lineHeight: 15,
  },
  wordWithHint: {
    fontWeight: '500',
    color: 'rgba(200, 210, 240, 0.9)',
  },
  wordHintSeparator: {
    color: 'rgba(160, 180, 220, 0.4)',
  },
  wordInfoText: {
    color: 'rgba(200, 210, 240, 0.7)',
    fontSize: 14,
  },
  
  // Word progress indicator
  wordProgressContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginTop: 35,
    marginBottom: 18,
    marginLeft: 2,
    paddingHorizontal: 0,
  },
  wordProgressStep: {
    width: 24,
    height: 4,
    borderRadius: 2,
    marginHorizontal: 2,
    backgroundColor: '#3a3a4a',
  },
  
  // Translation
  translationContainer: {
    marginTop: 0,
    padding: 0,
    paddingHorizontal: 0,
    backgroundColor: 'transparent',
    alignSelf: 'flex-start',
    width: '100%',
  },
  translation: {
    position: 'relative',
    fontSize: 16,
    color: 'rgba(220, 220, 240, 0.8)',
    fontStyle: 'normal',
    textAlign: 'left',
    lineHeight: 24,
    paddingTop: 4,
    paddingBottom: 8,
    paddingHorizontal: 2,
    width: '100%',
    fontWeight: '300',
  },
  
  // Divider
  dividerContainer: {
    width: '40%',
    marginTop: 8,
    marginBottom: 4,
    alignSelf: 'center',
    height: 2,
  },
  dividerLine: {
    width: '100%',
    height: 1,
    position: 'absolute',
    left: 0,
    right: 0,
  },
  dividerTop: {
    top: 0,
    backgroundColor: 'rgba(200, 200, 200, 0.08)',
    shadowColor: 'rgba(0, 0, 0, 0.2)',
    shadowOffset: { width: 0, height: 1 },
    shadowRadius: 1,
    elevation: 1,
  },
  dividerBottom: {
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  
  // Feedback
  feedbackContainer: {
    marginTop: 8,
    padding: 14,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
    alignSelf: 'stretch',
    marginHorizontal: 16,
  },
  feedbackCorrect: {
    backgroundColor: 'rgba(76, 175, 80, 0.15)',
    borderColor: 'rgba(76, 175, 80, 0.3)',
  },
  feedbackIncorrect: {
    backgroundColor: 'rgba(244, 67, 54, 0.15)',
    borderColor: 'rgba(244, 67, 54, 0.3)',
  },
  feedbackText: {
    color: '#fff',
    marginLeft: 10,
    fontSize: 15,
    fontWeight: '500',
  },
  
  // Next button
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(127, 180, 255, 0.2)',
    padding: 16,
    borderRadius: 12,
    marginTop: 16,
  },
  nextButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
});
