import * as React from 'react';
import { useState } from 'react';
import { View, Text, TextInput, StyleSheet, ActivityIndicator, Keyboard, SafeAreaView } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../App';
import { useAuth } from '../contexts/AuthContext';

// Импортируем новые компоненты дизайна
import { AnimatedGradientBackground, backgroundThemes } from '../components/AnimatedGradientBackground';
import { GlassButton } from '../components/GlassButton';
import { PasswordInput } from '../components/PasswordInput';
import { SafeAreaWrapper, SafeAreaWrapperPresets } from '../components/SafeAreaWrapper';

// Импортируем хуки для переводов
import { useAppTranslation } from '../src/i18n/hooks';
import { log, LogCategory } from '../utils/logger';

type LoginScreenProps = NativeStackScreenProps<RootStackParamList, 'Login'>;

const LoginScreen: React.FC<LoginScreenProps> = ({ navigation }) => {
  const [email, setEmail] = useState('<EMAIL>'); // Предзаполнен для тестирования
  const [password, setPassword] = useState('password'); // Предзаполнен для тестирования
  const [isLoading, setIsLoading] = useState(false);
  const [isEmailFocused, setIsEmailFocused] = useState(false);
  const [isPasswordFocused, setIsPasswordFocused] = useState(false);
  const { login, error, setError } = useAuth();
  const { t } = useAppTranslation('auth');

  const handleLogin = async () => {
    if (!email || !password) {
      setError(t('errors.fillAllFields'));
      return;
    }

    // Валидация email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError(t('errors.invalidEmail'));
      return;
    }
    
    try {
      Keyboard.dismiss(); // Скрываем клавиатуру при отправке формы
      setIsLoading(true);
      setError(null); // Сбрасываем предыдущие ошибки
      
      log.authLogin(email);
      const result = await login(email, password);

      if (result.success) {
        log.authLoginSuccess('user');
        // Навигация происходит автоматически через AuthProvider
      } else if (result.error) {
        log.authLoginError(result.error);
        setError(result.error);
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : t('errors.unknownError');
      log.authLoginError(errorMsg);
      let displayError = errorMsg;

      // Более понятные сообщения для пользователя
      if (errorMsg.includes('Network request failed')) {
        displayError = t('errors.networkError');
      } else if (errorMsg.includes('timeout') || errorMsg.includes('timed out')) {
        displayError = t('errors.timeoutError');
      } else if (errorMsg.includes('401')) {
        displayError = t('errors.invalidCredentials');
      }

      setError(displayError);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      {/* Animated Gradient Background */}
      <AnimatedGradientBackground {...backgroundThemes.minimal} />

      <SafeAreaWrapper {...SafeAreaWrapperPresets.form}>
        <View style={styles.content}>
          <Text style={styles.title}>{t('login.title')}</Text>

          <View style={styles.formContainer}>
            {error && (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>{error}</Text>
              </View>
            )}

            <TextInput
              style={[styles.input, error ? styles.inputError : null]}
              placeholder={!isEmailFocused ? t('login.emailPlaceholder') : ""}
              placeholderTextColor="rgba(255, 255, 255, 0.5)"
              value={email}
              onChangeText={setEmail}
              autoCapitalize="none"
              keyboardType="email-address"
              editable={!isLoading}
              onFocus={() => setIsEmailFocused(true)}
              onBlur={() => !email && setIsEmailFocused(false)}
            />

            <PasswordInput
              value={password}
              onChangeText={setPassword}
              placeholder={t('login.passwordPlaceholder')}
              editable={!isLoading}
              isFocused={isPasswordFocused}
              onFocus={() => setIsPasswordFocused(true)}
              onBlur={() => !password && setIsPasswordFocused(false)}
              onSubmitEditing={handleLogin}
              returnKeyType="done"
            />

            <GlassButton
              variant="primary"
              size="medium"
              fullWidth
              disabled={isLoading}
              onPress={handleLogin}
              style={styles.loginButton}
            >
              {isLoading ? <ActivityIndicator color="#fff" size="small" /> : t('login.loginButton')}
            </GlassButton>

            <GlassButton
              variant="success"
              size="medium"
              fullWidth
              disabled={isLoading}
              onPress={() => !isLoading && navigation.navigate('Register')}
              style={styles.registerButton}
            >
              {t('register.title')}
            </GlassButton>
          </View>
        </View>
      </SafeAreaWrapper>
    </View>
  );
};

export default LoginScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000', // Fallback цвет
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    padding: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 40,
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  formContainer: {
    width: '100%',
    maxWidth: 400,
    alignSelf: 'center',
  },
  input: {
    backgroundColor: 'rgba(20, 20, 35, 0.4)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.12)',
    borderRadius: 14,
    padding: 18,
    color: '#fff',
    marginBottom: 16,
    fontSize: 16,
    // backdropFilter is not supported in React Native, using opacity instead
    opacity: 0.9,
  },
  inputError: {
    borderColor: '#ff4757',
    backgroundColor: 'rgba(255, 71, 87, 0.1)',
  },
  loginButton: {
    marginTop: 24,
    marginBottom: 16,
  },
  registerButton: {
    marginBottom: 8,
  },
  errorContainer: {
    backgroundColor: 'rgba(255, 71, 87, 0.15)',
    borderWidth: 1,
    borderColor: 'rgba(255, 71, 87, 0.3)',
    padding: 12,
    borderRadius: 8,
    marginBottom: 20,
  },
  errorText: {
    color: '#ff4757',
    textAlign: 'center',
    fontSize: 14,
  },
});
