import * as React from 'react';
import { useState } from 'react';
import { View, Text, TextInput, StyleSheet, ActivityIndicator, Keyboard, SafeAreaView, ScrollView, KeyboardAvoidingView, Platform } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../App';
import { useAuth } from '../contexts/AuthContext';

// Импортируем новые компоненты дизайна
import { AnimatedGradientBackground, backgroundThemes } from '../components/AnimatedGradientBackground';
import { GlassButton } from '../components/GlassButton';
import { PasswordInput } from '../components/PasswordInput';
import { SafeAreaWrapper, SafeAreaWrapperPresets } from '../components/SafeAreaWrapper';

// Импортируем хуки для переводов
import { useAppTranslation } from '../src/i18n/hooks';

type RegisterScreenProps = NativeStackScreenProps<RootStackParamList, 'Register'>;

const RegisterScreen: React.FC<RegisterScreenProps> = ({ navigation }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [username, setUsername] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isEmailFocused, setIsEmailFocused] = useState(false);
  const [isUsernameFocused, setIsUsernameFocused] = useState(false);
  const [isPasswordFocused, setIsPasswordFocused] = useState(false);
  const [isConfirmPasswordFocused, setIsConfirmPasswordFocused] = useState(false);
  const { register, error, setError } = useAuth();
  const { t } = useAppTranslation('auth');

  const handleRegister = async () => {
    // Защита от множественных вызовов
    if (isLoading) {
      console.log('Registration already in progress, ignoring duplicate call');
      return;
    }

    if (!email || !password || !confirmPassword) {
      setError(t('errors.fillAllFields'));
      return;
    }

    // Валидация email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError(t('errors.invalidEmail'));
      return;
    }

    // Валидация пароля
    if (password.length < 6) {
      setError(t('errors.passwordTooShort'));
      return;
    }

    // Проверка совпадения паролей
    if (password !== confirmPassword) {
      setError(t('errors.passwordsNotMatch'));
      return;
    }

    if (password !== confirmPassword) {
      setError(t('errors.passwordsNotMatch'));
      return;
    }

    if (password.length < 6) {
      setError(t('errors.passwordTooShort'));
      return;
    }

    if (!email.includes('@')) {
      setError(t('errors.invalidEmail'));
      return;
    }

    try {
      Keyboard.dismiss(); // Скрываем клавиатуру при отправке формы
      setIsLoading(true);
      setError(null); // Сбрасываем предыдущие ошибки
      
            const result = await register(email, password, username);
      console.log('Registration result:', result);
      
      if (result.success) {
        console.log('Registration successful, navigation should happen automatically');
        // Навигация происходит автоматически через AuthProvider
      } else if (result.error) {
        console.log('Registration failed with error:', result.error);
        setError(result.error);
      }
    } catch (err) {
      console.error('Registration error:', err);
      const errorMsg = err instanceof Error ? err.message : t('errors.unknownError');
      let displayError = `${t('errors.registrationError')}: ${errorMsg}`;

      // Более понятные сообщения для пользователя
      if (errorMsg.includes('Network request failed')) {
        displayError = t('errors.networkError');
      } else if (errorMsg.includes('timeout') || errorMsg.includes('timed out')) {
        displayError = t('errors.timeoutError');
      } else if (errorMsg.includes('400')) {
        displayError = t('errors.userExists');
      }
      
      setError(displayError);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      {/* Animated Gradient Background */}
      <AnimatedGradientBackground {...backgroundThemes.profile} />

      <SafeAreaWrapper {...SafeAreaWrapperPresets.form}>
        <KeyboardAvoidingView
          style={styles.keyboardAvoidingView}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
        >
          <ScrollView
            style={styles.scrollView}
            contentContainerStyle={styles.content}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
            bounces={true}
            scrollEventThrottle={16}
          >
          <Text style={styles.title}>{t('register.title')}</Text>

          <View style={styles.formContainer}>
            {error && (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>{error}</Text>
              </View>
            )}

            <TextInput
              style={[styles.input, error ? styles.inputError : null]}
              placeholder={!isEmailFocused ? t('register.emailPlaceholder') : ""}
              placeholderTextColor="rgba(255, 255, 255, 0.5)"
              value={email}
              onChangeText={setEmail}
              autoCapitalize="none"
              keyboardType="email-address"
              editable={!isLoading}
              onFocus={() => setIsEmailFocused(true)}
              onBlur={() => !email && setIsEmailFocused(false)}
            />

            <TextInput
              style={styles.input}
              placeholder={!isUsernameFocused ? t('register.usernamePlaceholder') : ""}
              placeholderTextColor="rgba(255, 255, 255, 0.5)"
              value={username}
              onChangeText={setUsername}
              autoCapitalize="none"
              editable={!isLoading}
              onFocus={() => setIsUsernameFocused(true)}
              onBlur={() => !username && setIsUsernameFocused(false)}
            />

            <PasswordInput
              value={password}
              onChangeText={setPassword}
              placeholder={t('register.passwordPlaceholder')}
              editable={!isLoading}
              isFocused={isPasswordFocused}
              onFocus={() => setIsPasswordFocused(true)}
              onBlur={() => !password && setIsPasswordFocused(false)}
            />

            <PasswordInput
              value={confirmPassword}
              onChangeText={setConfirmPassword}
              placeholder={t('register.confirmPasswordPlaceholder')}
              editable={!isLoading}
              isFocused={isConfirmPasswordFocused}
              onFocus={() => setIsConfirmPasswordFocused(true)}
              onBlur={() => !confirmPassword && setIsConfirmPasswordFocused(false)}
              onSubmitEditing={handleRegister}
              returnKeyType="done"
            />

            <Text style={styles.infoText}>
              {t('register.requirements')}
            </Text>

            <GlassButton
              variant="success"
              size="medium"
              fullWidth
              disabled={isLoading}
              onPress={handleRegister}
              style={styles.registerButton}
            >
              {isLoading ? <ActivityIndicator color="#fff" size="small" /> : t('register.registerButton')}
            </GlassButton>

            <GlassButton
              variant="secondary"
              size="medium"
              fullWidth
              disabled={isLoading}
              onPress={() => !isLoading && navigation.navigate('Login')}
              style={styles.loginButton}
            >
              {`${t('register.hasAccount')} ${t('register.signIn')}`}
            </GlassButton>
          </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </SafeAreaWrapper>
    </View>
  );
};

export default RegisterScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000', // Fallback цвет
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    flexGrow: 1,
    padding: 20,
    paddingTop: 60,
    paddingBottom: 40,
    justifyContent: 'flex-start',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 40,
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  formContainer: {
    width: '100%',
    maxWidth: 400,
    alignSelf: 'center',
  },
  input: {
    backgroundColor: 'rgba(20, 20, 35, 0.4)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.12)',
    borderRadius: 14,
    padding: 18,
    color: '#fff',
    marginBottom: 16,
    fontSize: 16,
    // backdropFilter is not supported in React Native, using opacity instead
    opacity: 0.9,
  },
  inputError: {
    borderColor: '#ff4757',
    backgroundColor: 'rgba(255, 71, 87, 0.1)',
  },
  registerButton: {
    marginTop: 24,
    marginBottom: 16,
  },
  loginButton: {
    marginBottom: 8,
  },
  errorContainer: {
    backgroundColor: 'rgba(255, 71, 87, 0.15)',
    borderWidth: 1,
    borderColor: 'rgba(255, 71, 87, 0.3)',
    padding: 12,
    borderRadius: 8,
    marginBottom: 20,
  },
  errorText: {
    color: '#ff4757',
    textAlign: 'center',
    fontSize: 14,
  },
  infoText: {
    color: 'rgba(255, 255, 255, 0.5)',
    fontSize: 13,
    textAlign: 'center',
    marginBottom: 20,
    marginTop: 8,
  },
});
