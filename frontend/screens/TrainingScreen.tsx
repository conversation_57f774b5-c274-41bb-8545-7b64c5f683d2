/**
 * TrainingScreen - Основной экран обучения с системой spaced repetition
 *
 * 📖 ПОЛНАЯ ДОКУМЕНТАЦИЯ: backend/docs/SPACED_REPETITION_SYSTEM.md
 *
 * 🔑 КЛЮЧЕВЫЕ ОСОБЕННОСТИ:
 * - Интеграция с системой предзагрузки карточек (cardPreloader.ts)
 * - Локальная обработка неправильных ответов (повтор текущей карточки)
 * - Мгновенное обновление UI для новых выученных слов
 * - Двухуровневая фильтрация дублирующихся карточек
 * - Автоматический фокус на поле ввода
 *
 * ⚠️ ВАЖНО: Часть логики spaced repetition обрабатывается здесь:
 * - Предзагрузка следующих карточек в фоне
 * - Проверка дублирования при использовании предзагруженных карточек
 * - Локальное определение правильности ответа для UI
 * - Мгновенные переходы без задержек загрузки
 */

import React, { useState, useRef, useCallback, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Animated,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  StatusBar,
  Vibration,
  Button,
  AppState
} from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';

// Импортируем новые компоненты
import { AnimatedGradientBackground, backgroundThemes } from '../components/AnimatedGradientBackground';
import { GlassmorphismCard, glassmorphismPresets } from '../components/GlassmorphismCard';
import CircleCountdown from '../components/CircleCountdown';
import { Canvas, RadialGradient, Rect, vec, Circle, rrect, rect, BackdropBlur, Blur, Group } from '@shopify/react-native-skia';
import * as Haptics from 'expo-haptics';

// API
import { fetchRandomCard, Example, Word as APIWord, Card as APICard } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import { useTraining } from '../contexts/TrainingContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { API_URL } from '../config/api';
import { useCardPreloader, cardPreloader } from '../services/cardPreloader';
import { processCardData, fetchRawCardData } from '../services/cardDataProcessor';
import { log, LogCategory } from '../utils/logger';
import retryService from '../services/retryService';

// Импортируем хуки для переводов
import { useAppTranslation } from '../src/i18n/hooks';

// Navigation imports
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../App';

// Расширяем тип Word для совместимости с нашими нуждами
interface ExtendedWord extends Omit<APIWord, 'translations' | 'tags' | 'created_at' | 'updated_at'> {
  text?: string;
  examples?: Array<{
    sentence: string;
    translation?: string;
    word_info?: any;
  }>;
}

// Types
interface WordInfo {
  type: string;
  form: string;
  transcription: string;
}

interface CardExample {
  sentence: string;
  translation?: string;
  word_info?: WordInfo | string;
  correct_answers?: string[];
}

interface CardWord {
  _id: string;
  word: string;
  text: string;
  language: string;
  level?: string;
  examples?: CardExample[];
  [key: string]: any; // Для совместимости с дополнительными полями
}

interface CardProgress {
  progress: number;
  next_review?: Date;
  interval_level?: number;
  // Поля из API
  ease_factor?: number;
  interval?: number;
  repetitions?: number;
  review_date?: Date | string;
  last_reviewed?: Date | string;
}

interface Card {
  id: string;
  word: string;
  translation: string;
  translationWord: string;
  english: string;
  progress: number; // Текущий прогресс (0-1)
  level: string;
  priority?: 'ultra_core' | 'core' | 'extended'; // Приоритет для A0 уровня
  examples: CardExample[];
  wordInfo?: WordInfo;
  type: string;
  form: string;
  transcription: string;
  native_word: CardWord;
  target_word: CardWord;
  // Поля для совместимости с API интервального повторения
  _id?: string;
  word_id?: string;
  example?: {
    sentence: string;
    translation: string;
  };
  // Дополнительная информация о прогрессе
  progressInfo?: CardProgress;
  // Поля интервального повторения
  is_new?: boolean;
  is_learned?: boolean;
  is_forced_review?: boolean;
  interval_level?: number;
  concept_id?: string;
  part_of_speech?: string;
  ipa_pronunciation?: string;
}

// Screen dimensions
const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
const baseScreenSize = Math.min(screenWidth, screenHeight);

// Timing constants
const AUTO_ADVANCE_DELAY_MS = 2400; // 2.4 seconds delay before auto-advance on incorrect answer
const TIMER_DISPLAY_DURATION = Math.round(AUTO_ADVANCE_DELAY_MS * 0.7); // 70% of actual delay for timer display

// Gradient settings теперь в backgroundThemes.training

// Helper functions for language handling
const getLanguageSettings = (user: any) => ({
  nativeLang: user?.settings?.native_language || 'en', // Изменили дефолт с 'ru' на 'en'
  targetLang: user?.settings?.target_language || 'en'
});

const getWordByLanguage = (card: Card | null, language: string) => {
  if (!card) return null;
  return card.native_word.language === language ? card.native_word : 
         card.target_word.language === language ? card.target_word : null;
};

const getOtherWord = (card: Card | null, language: string) => {
  if (!card) return null;
  return card.native_word.language !== language ? card.native_word : 
         card.target_word.language !== language ? card.target_word : null;
};

// Haptic feedback function
const triggerHaptic = (type: 'success' | 'error' | 'warning' = 'success') => {
  if (type === 'success') {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium).catch(() => {});
  } else if (type === 'error') {
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error).catch(() => {});
  } else {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy).catch(() => {});
  }
};

export default function App() {
  // Состояния приложения
  const [isLoading, setIsLoading] = useState(true);
  const [currentCard, setCurrentCard] = useState<Card | null>(null);
  const [currentCardNumber, setCurrentCardNumber] = useState(1);
  const { user } = useAuth();
  const { t } = useAppTranslation('training');
  const { onTrainingComplete } = useTraining();
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();

  // Предзагрузчик карточек
  const {
    startPreloading,
    getPreloadedCard,
    waitForPreload,
    clearPreloadedCard,
    isLoading: isPreloading
  } = useCardPreloader();

  // Обработчик восстановления сети для retry сервиса
  useEffect(() => {
    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'active') {
        // Приложение стало активным - возможно, сеть восстановилась
        // Принудительно обрабатываем очередь повторов
        retryService.processQueue().catch(error => {
          log.error(LogCategory.API, 'Ошибка принудительной обработки очереди при активации приложения', { error: error.message });
        });
      }
    };

    // Подписываемся на изменения состояния приложения
    const subscription = AppState?.addEventListener?.('change', handleAppStateChange);

    return () => {
      subscription?.remove?.();
    };
  }, []);
  const totalCardsInDeck = user?.settings?.daily_goal || 10; // Используем настройку пользователя или значение по умолчанию 10
  const [userInput, setUserInput] = useState('');
  const [isCorrect, setIsCorrect] = useState<boolean | null>(null);
  const [showHint, setShowHint] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [wordProgress, setWordProgress] = useState(0);
  const [feedbackMessage, setFeedbackMessage] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [inputWidth, setInputWidth] = useState(100);
  const [usedHelpButton, setUsedHelpButton] = useState(false); // Отслеживаем использование подсказки
  const [showNavigationButtons, setShowNavigationButtons] = useState(true); // Показывать кнопки навигации всегда
  const [correctAnswer, setCorrectAnswer] = useState<string>(''); // Правильный ответ для показа
  const [timeLeft, setTimeLeft] = useState<number>(0); // Время до автоперехода

  // Простое состояние для кнопки "назад" - запоминаем только предыдущую карточку
  const [previousCard, setPreviousCard] = useState<Card | null>(null);
  const [previousCardAnswer, setPreviousCardAnswer] = useState<string>(''); // Правильный ответ предыдущей карточки
  const [previousCardState, setPreviousCardState] = useState<{
    interval_level: number;
    is_learned: boolean;
    is_new: boolean;
  }>({ interval_level: -1, is_learned: false, is_new: true }); // Полное состояние предыдущей карточки ПОСЛЕ ответа
  const [canGoBack, setCanGoBack] = useState(false); // Кнопка назад активна только если есть предыдущая карточка
  const [canGoForward, setCanGoForward] = useState(false); // Кнопка вперед активна только после ввода ответа
  const [isViewingPrevious, setIsViewingPrevious] = useState(false); // Просматриваем ли предыдущую карточку

  // Сохраняем состояние текущей карточки для восстановления после просмотра предыдущей
  const [savedCurrentCard, setSavedCurrentCard] = useState<Card | null>(null);
  const [savedUserInput, setSavedUserInput] = useState<string>('');
  const [savedIsCorrect, setSavedIsCorrect] = useState<boolean | null>(null);
  const [savedCorrectAnswer, setSavedCorrectAnswer] = useState<string>('');
  const [savedTimeLeft, setSavedTimeLeft] = useState<number>(0);
  
  // Рефы
  const inputRef = useRef<TextInput>(null);
  
  // Анимации
  const feedbackAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const shakeAnim = useRef(new Animated.Value(0)).current;
  const buttonsAnim = useRef(new Animated.Value(1)).current; // Анимация для кнопок

  // Animation values удалены, так как уже объявлены выше
  
  // Shake animation configuration
  const shakeAnimation = {
    0: { translateX: 0 },
    0.1: { translateX: -10 },
    0.2: { translateX: 10 },
    0.3: { translateX: -8 },
    0.4: { translateX: 8 },
    0.5: { translateX: -5 },
    0.6: { translateX: 5 },
    0.7: { translateX: -3 },
    0.8: { translateX: 3 },
    0.9: { translateX: -1 },
    1: { translateX: 0 },
  };

  // Animate card out with preloaded data (оптимизированная версия)
  const animateCardOutWithPreload = useCallback((preloadedCardPromise: Promise<any>) => {
    console.log('Starting card out animation with preloaded data...');

    // 🔧 ИСПРАВЛЕНИЕ: Сначала скрываем feedback сообщение
    Animated.timing(feedbackAnim, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start();

    // Reset animations
    slideAnim.setValue(0);

    // Animate out to the left with slight rotation
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: -screenWidth,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 0.9,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(rotateAnim, {
        toValue: -5,
        duration: 300,
        useNativeDriver: true,
      })
    ]).start(() => {
      // Reset animations for next card before fetching
      slideAnim.setValue(screenWidth);
      scaleAnim.setValue(0.9);
      rotateAnim.setValue(5);

      // Use preloaded card data (уже загружается в фоне!)
      preloadedCardPromise.then((newCard) => {
        console.log('Using preloaded card:', newCard ? 'success' : 'null');

        // 🔧 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ПОЛНАЯ очистка состояния прогресса ПЕРЕД установкой новой карточки
        console.log('🧹 КРИТИЧЕСКОЕ: Очищаем ВСЕ состояние прогресса перед установкой новой карточки');

        console.log('🔧 DEBUG: Состояние ПЕРЕД очисткой:', {
          currentCard_word: currentCard?.word,
          currentCard_interval_level: currentCard?.interval_level,
          currentCard_is_learned: currentCard?.is_learned,
          currentCard_is_new: currentCard?.is_new,
          userInput,
          isCorrect,
          correctAnswer,
          timeLeft
        });

        setUserInput('');
        setIsCorrect(null);
        setShowHint(false);
        setUsedHelpButton(false); // Сбрасываем флаг использования подсказки
        setCanGoForward(false); // Сбрасываем активность кнопки "вперед"
        setCorrectAnswer(''); // Сбрасываем правильный ответ
        setTimeLeft(0); // Сбрасываем таймер

        // 🔧 ИСПРАВЛЕНИЕ: НЕ очищаем состояние currentCard принудительно
        // Это состояние будет правильно установлено при получении новой карточки
        console.log('🔧 DEBUG: Сохраняем текущее состояние currentCard без принудительной очистки');

        // 🚀 ВАЖНО: Создаем копию карточки с правильным начальным состоянием
        const cleanCard = newCard ? {
          ...newCard,
          // Убеждаемся, что новая карточка имеет правильное начальное состояние
          is_new: newCard.is_new ?? true, // Новые карточки должны быть помечены как новые
          is_learned: newCard.is_learned ?? false, // По умолчанию не выучены
          interval_level: newCard.interval_level ?? -1, // Начальный уровень
        } : null;

        // 🔧 ИСПРАВЛЕНИЕ: Защита от перезаписи карточки предзагрузкой
        const currentCardId = currentCard?.word_id || currentCard?.id || currentCard?._id;
        const newCardId = cleanCard?.word_id || cleanCard?.id || cleanCard?._id;

        // Если это та же карточка, не перезаписываем
        if (currentCardId === newCardId) {
          console.log(`[PRELOADER] ⚠️ Предотвращена перезапись карточки: ${currentCardId}`);
          setIsLoading(false);
          return;
        }

        console.log('🔧 DEBUG: Устанавливаем НОВУЮ карточку:', {
          new_word: cleanCard?.word,
          new_interval_level: cleanCard?.interval_level,
          new_is_learned: cleanCard?.is_learned,
          new_is_new: cleanCard?.is_new,
          previous_word: currentCard?.word,
          previous_interval_level: currentCard?.interval_level,
          previous_is_learned: currentCard?.is_learned,
          previous_is_new: currentCard?.is_new
        });

        // 🔧 ИСПРАВЛЕНИЕ: Используем правильные данные от сервера, НЕ очищаем их принудительно
        // Только для новых карточек (interval_level === -1) устанавливаем is_new = true
        const finalCard = {
          ...cleanCard,
          // Сохраняем правильные данные от сервера
          is_new: cleanCard?.interval_level === -1 ? true : (cleanCard?.is_new ?? false),
          is_learned: cleanCard?.is_learned ?? false,
          interval_level: cleanCard?.interval_level ?? -1,
          progressInfo: {
            ...cleanCard?.progressInfo,
            progress: cleanCard?.progressInfo?.progress ?? 0,
            interval_level: cleanCard?.interval_level ?? -1,
          }
        };

        console.log('🔧 DEBUG: Карточка с правильными данными от сервера:', {
          word: finalCard?.word,
          interval_level: finalCard?.interval_level,
          is_learned: finalCard?.is_learned,
          is_new: finalCard?.is_new
        });

        // Update the current card state
        setCurrentCard(finalCard);

        // 🔧 ИСПРАВЛЕНИЕ: Убираем принудительный перерендер - React сам обновит компонент
        console.log('🔧 DEBUG: Карточка установлена, React автоматически обновит отображение');

        setIsLoading(false);

        // Update card number
        setCurrentCardNumber(prev => prev + 1);

        // 🚀 ЗАПУСКАЕМ ПРЕДЗАГРУЗКУ следующей карточки ПОСЛЕ установки новой
        if (cleanCard && user) {
          const { nativeLang, targetLang } = getLanguageSettings(user);
          const currentCardId = cleanCard.word_id || cleanCard.id || cleanCard._id;

          // Не запускаем предзагрузку для последней карточки
          if (currentCardNumber < totalCardsInDeck - 1) {
            console.log(`[PRELOADER] 🎯 Запускаем предзагрузку после установки карточки: ${currentCardId}`);
            startPreloading(user._id, nativeLang, targetLang, currentCardId).catch(error => {
              log.error(LogCategory.PRELOADER, `Предзагрузка не удалась: ${error.message}`);
            });
          } else {
            console.log('🎯 Последняя карточка - пропускаем предзагрузку');
          }
        }

        // Animate new card in after fetch completes
        Animated.parallel([
          Animated.spring(slideAnim, {
            toValue: 0,
            useNativeDriver: true,
            friction: 8,
            tension: 60,
          }),
          Animated.spring(scaleAnim, {
            toValue: 1,
            useNativeDriver: true,
            friction: 8,
            tension: 60,
          }),
          Animated.spring(rotateAnim, {
            toValue: 0,
            useNativeDriver: true,
            friction: 8,
            tension: 60,
          })
        ]).start();

        // 🔧 ИСПРАВЛЕНИЕ: Убираем фокус из анимации - управление фокусом теперь централизовано в useEffect
        // Focus input after animation
        // setTimeout(() => {
        //   if (inputRef.current) {
        //     inputRef.current.focus();
        //   }
        // }, 100);
      }).catch((error) => {
        console.error('Error in preloaded card:', error);
        if (!error.message.includes('No cards available for review')) {
          setFeedbackMessage(t('common:errors.networkError'));
        }
        setIsLoading(false);
      });
    });
  }, [slideAnim, scaleAnim, rotateAnim, screenWidth]);

  // Animate card out - updated with smooth transition
  const animateCardOut = useCallback(() => {
    // 🔧 ИСПРАВЛЕНИЕ: Сначала скрываем feedback сообщение
    Animated.timing(feedbackAnim, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start();

    // Reset animations
    slideAnim.setValue(0);

    // Animate out to the left with slight rotation
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: -screenWidth,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 0.9,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(rotateAnim, {
        toValue: -5,
        duration: 300,
        useNativeDriver: true,
      })
    ]).start(() => {
      // Reset animations for next card before fetching
      slideAnim.setValue(screenWidth);
      scaleAnim.setValue(0.9);
      rotateAnim.setValue(5);

      // Fetch new card and update state
      fetchNewCard().then((newCard) => {
        console.log('Setting new card from animateCardOut:', newCard ? 'success' : 'null');

        // Reset feedback state before setting new card
        setUserInput('');
        setIsCorrect(null);
        setShowHint(false);
        setUsedHelpButton(false); // Сбрасываем флаг использования подсказки
        setCorrectAnswer(''); // Сбрасываем правильный ответ
        setTimeLeft(0); // Сбрасываем таймер
        setCanGoForward(false); // Сбрасываем активность кнопки "вперед"

        // Update the current card state
        setCurrentCard(newCard);
        setIsLoading(false); // Завершаем загрузку после успешного получения карточки

        // 🔧 ИСПРАВЛЕНИЕ: Очищаем состояние прогресса для новой карточки
        setUserInput('');
        setIsCorrect(null);
        setCorrectAnswer('');
        setUsedHelpButton(false);

        // Update card number (увеличиваем только при переходе к следующей карточке)
        setCurrentCardNumber(prev => prev + 1);

        // Animate new card in after fetch completes
        Animated.parallel([
          Animated.spring(slideAnim, {
            toValue: 0,
            useNativeDriver: true,
            friction: 8,
            tension: 60,
          }),
          Animated.spring(scaleAnim, {
            toValue: 1,
            useNativeDriver: true,
            friction: 8,
            tension: 60,
          }),
          Animated.spring(rotateAnim, {
            toValue: 0,
            useNativeDriver: true,
            friction: 8,
            tension: 60,
          })
        ]).start();

        // 🔧 ИСПРАВЛЕНИЕ: Убираем фокус из анимации - управление фокусом теперь централизовано в useEffect
        // Focus input after animation
        // setTimeout(() => {
        //   if (inputRef.current) {
        //     inputRef.current.focus();
        //   }
        // }, 100);
      }).catch((error) => {
        console.error('Error in animateCardOut fetchNewCard:', error);
        // Если ошибка содержит "No cards available", не показываем ошибку
        if (!error.message.includes('No cards available for review')) {
          setFeedbackMessage(t('common:errors.networkError'));
        }
        setIsLoading(false);
      });
    });
  }, [totalCardsInDeck, fetchNewCard]);

  // Track when the user starts thinking about the answer
  const [answerStartTime, setAnswerStartTime] = useState<number | null>(null);
  
  // Set answer start time when card changes
  useEffect(() => {
    setAnswerStartTime(Date.now());
  }, [currentCard]);

  // Send user's answer to the server and check if it's correct
  const checkAnswer = useCallback(async () => {
    if (!currentCard || !user?._id) {
      console.error('No current card or user ID');
      return;
    }
    
    // Пробуем получить ID карточки из разных возможных полей
    const cardId = currentCard.id || currentCard._id || currentCard.word_id || currentCard.target_word?._id;
    log.debug(LogCategory.USER_INPUT, 'Отправляем ответ на карточку', {
      cardId,
      word: currentCard.word
    });
    
    if (!cardId) {
      console.error('No card ID available in any field');
      return;
    }
    
    try {
      setIsLoading(true);
      
      // Calculate response time in seconds
      const responseTime = answerStartTime ? (Date.now() - answerStartTime) / 1000 : 0;
      
      // Check if the answer is correct locally first
      const { targetLang } = getLanguageSettings(user);
      const targetWord = getWordByLanguage(currentCard, targetLang);
      const isAnswerCorrect = targetWord?.examples?.[0]?.correct_answers?.some(
        answer => answer.toLowerCase() === userInput.trim().toLowerCase()
      ) || false;

      // Определяем, что отправлять на сервер
      // Если пользователь использовал подсказку, отправляем как неправильный ответ
      const serverIsCorrect = usedHelpButton ? false : isAnswerCorrect;

      // 🚀 ИСПРАВЛЕНИЕ: Логика повторения при использовании подсказки
      // Если пользователь использовал подсказку - всегда повторяем карточку (независимо от правильности)
      // Это заставит пользователя ввести ответ еще раз без подсказки
      const shouldAdvanceToNext = isAnswerCorrect && !usedHelpButton;

      // 🔍 DEBUG: Логируем все переменные для отладки
      console.log('🔍 DEBUG checkAnswer:', {
        userInput: userInput.trim(),
        isAnswerCorrect,
        usedHelpButton,
        serverIsCorrect,
        shouldAdvanceToNext
      });

      log.userAnswer(currentCard.word, isAnswerCorrect, responseTime, usedHelpButton);

      // 🚀 МГНОВЕННОЕ ОБНОВЛЕНИЕ UI - НЕ ЖДЕМ СЕРВЕР!
      setIsCorrect(isAnswerCorrect);
      setCanGoForward(true);

      // 🚀 АСИНХРОННАЯ отправка на сервер в фоне с retry логикой (НЕ блокируем UI)
      const sendToServerAsync = async () => {
        try {
          const response = await retryService.sendWithRetry(
            `${API_URL}/api/spaced/${cardId}/response`,
            {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                user_id: user._id,
                is_correct: serverIsCorrect, // Отправляем логически правильное значение
                response_time: responseTime
              })
            },
            {
              cardId: cardId,
              userId: user._id,
              isCorrect: serverIsCorrect,
              responseTime: responseTime
            }
          );

          if (response) {
            const responseData = await response.json();
            log.success(LogCategory.API, 'Ответ сервера получен', {
              isCorrect: responseData.is_correct,
              intervalLevel: responseData.interval_level,
              isLearned: responseData.is_learned
            });

          // 🔧 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: НЕ ОБНОВЛЯЕМ currentCard от сервера!
          // Ответ сервера относится к ПРЕДЫДУЩЕЙ карточке, а currentCard уже может быть НОВОЙ карточкой
          console.log('🔧 DEBUG: НЕ обновляем currentCard от сервера - это может перезаписать новую карточку');

          // Ответ сервера используется только для сохранения в previousCardState
          // setCurrentCard НЕ ВЫЗЫВАЕМ!

            console.log('💾 Сохранена предыдущая карточка с состоянием:', {
              interval_level: responseData.interval_level,
              is_learned: responseData.is_learned,
              is_new: responseData.is_new
            });
          } else {
            // Запрос добавлен в очередь повторов
            log.info(LogCategory.API, 'Ответ добавлен в очередь повторной отправки', {
              cardId: cardId,
              isCorrect: serverIsCorrect
            });
          }
        } catch (error) {
          console.error('❌ Критическая ошибка retry сервиса:', error);
          // В случае критической ошибки retry сервиса логируем, но НЕ блокируем UI
          log.error(LogCategory.API, 'Критическая ошибка retry сервиса', { error: error.message });
        }
      };

      // Запускаем отправку в фоне
      sendToServerAsync();

      // ИСПОЛЬЗУЕМ ТУ ЖЕ ЛОГИКУ, что и для отображения оранжевой полоски!
      const isNewCard = currentCard.is_new ?? true;
      const isNewWordCorrect = isNewCard && isAnswerCorrect && !usedHelpButton;

      console.log('🔍 DEBUG: Анализ логики обновления полосок:', {
        word: currentCard.word,
        current_interval_level: currentCard.interval_level,
        current_is_new: currentCard.is_new,
        current_is_learned: currentCard.is_learned,
        isNewCard,
        isAnswerCorrect,
        usedHelpButton,
        isNewWordCorrect,
        willUpdateToLevel15: isNewWordCorrect,
        willUpdateAsIncorrect: usedHelpButton
      });

      log.debug(LogCategory.PROGRESS, 'Анализ нового слова', {
        isNewCard,
        isAnswerCorrect,
        usedHelpButton,
        isNewWordCorrect
      });

      // МГНОВЕННОЕ обновление полосок для новых слов с правильным ответом
      if (isNewWordCorrect) {
        console.log('🎉 Новое слово отвечено правильно! МГНОВЕННО обновляем полоски прогресса...');

        // 🚀 МГНОВЕННОЕ обновление полосок для ТЕКУЩЕЙ карточки
        setCurrentCard(prev => {
          if (!prev) return prev;

          console.log('🔧 DEBUG: Обновляем currentCard с прогрессом:', {
            word: prev.word,
            old_interval_level: prev.interval_level,
            old_is_learned: prev.is_learned,
            old_is_new: prev.is_new,
            new_interval_level: 15,
            new_is_learned: true,
            new_is_new: false
          });

          return {
            ...prev,
            is_new: false,           // Больше не новое
            is_learned: true,        // Выучено
            interval_level: 15,      // Максимальный уровень (соответствует MAX_INTERVAL_LEVEL)
            progressInfo: {
              ...prev.progressInfo,
              progress: prev.progressInfo?.progress || 0,
              interval_level: 15,
            }
          };
        });
      } else if (usedHelpButton) {
        // 🔧 ИСПРАВЛЕНИЕ ПРОБЛЕМЫ №1: При использовании подсказки обновляем полоски как при неправильном ответе
        console.log('🔄 Использована подсказка - обновляем полоски как при неправильном ответе');

        setCurrentCard(prev => {
          if (!prev) return prev;

          const currentIntervalLevel = prev.interval_level ?? -1;
          let newIntervalLevel;

          // Логика уменьшения интервала при неправильном ответе
          if (currentIntervalLevel <= 0) {
            newIntervalLevel = -1; // Сбрасываем до минимума
          } else {
            newIntervalLevel = Math.max(-1, currentIntervalLevel - 1); // Уменьшаем на 1
          }

          console.log('🔧 DEBUG: Обновляем currentCard после подсказки:', {
            word: prev.word,
            old_interval_level: currentIntervalLevel,
            new_interval_level: newIntervalLevel,
            old_is_learned: prev.is_learned,
            new_is_learned: false,
            old_is_new: prev.is_new,
            new_is_new: false // 🔧 КРИТИЧЕСКОЕ: Больше не новое!
          });

          return {
            ...prev,
            is_new: false,           // 🔧 КРИТИЧЕСКОЕ: Больше не новое слово!
            is_learned: false,       // Больше не выучено
            interval_level: newIntervalLevel,
            progressInfo: {
              ...prev.progressInfo,
              progress: prev.progressInfo?.progress || 0,
              interval_level: newIntervalLevel,
            }
          };
        });
      } else if (!isAnswerCorrect) {
        // 🔧 ИСПРАВЛЕНИЕ ПРОБЛЕМЫ №10 + №12: При неправильном ответе обновляем is_new в false И уменьшаем интервал
        console.log('❌ Неправильный ответ - обновляем is_new в false и уменьшаем интервал');

        setCurrentCard(prev => {
          if (!prev) return prev;

          const currentIntervalLevel = prev.interval_level ?? -1;
          let newIntervalLevel;

          // Логика уменьшения интервала при неправильном ответе (та же что и для подсказок)
          if (currentIntervalLevel <= 0) {
            newIntervalLevel = -1; // Сбрасываем до минимума
          } else {
            newIntervalLevel = Math.max(-1, currentIntervalLevel - 1); // Уменьшаем на 1
          }

          console.log('🔧 DEBUG: Обновляем currentCard после неправильного ответа:', {
            word: prev.word,
            old_interval_level: currentIntervalLevel,
            new_interval_level: newIntervalLevel,
            old_is_learned: prev.is_learned,
            new_is_learned: false,
            old_is_new: prev.is_new,
            new_is_new: false // 🔧 КРИТИЧЕСКОЕ: Больше не новое!
          });

          return {
            ...prev,
            is_new: false,           // 🔧 КРИТИЧЕСКОЕ: Больше не новое слово!
            is_learned: false,       // Больше не выучено
            interval_level: newIntervalLevel,
            progressInfo: {
              ...prev.progressInfo,
              progress: prev.progressInfo?.progress || 0,
              interval_level: newIntervalLevel,
            }
          };
        });
      } else if (isAnswerCorrect && !isNewWordCorrect) {
        // 🔧 ИСПРАВЛЕНИЕ ПРОБЛЕМЫ №11: Обычный правильный ответ на существующее слово
        console.log('✅ Правильный ответ на существующее слово - обновляем интервал');

        setCurrentCard(prev => {
          if (!prev) return prev;

          const currentIntervalLevel = prev.interval_level ?? -1;
          let newIntervalLevel;

          // Логика увеличения интервала при правильном ответе
          if (currentIntervalLevel >= 14) {
            newIntervalLevel = 15; // Максимальный уровень
          } else {
            newIntervalLevel = currentIntervalLevel + 1; // Увеличиваем на 1
          }

          console.log('🔧 DEBUG: Обновляем currentCard после правильного ответа:', {
            word: prev.word,
            old_interval_level: currentIntervalLevel,
            new_interval_level: newIntervalLevel,
            old_is_learned: prev.is_learned,
            new_is_learned: newIntervalLevel >= 15,
            old_is_new: prev.is_new,
            new_is_new: false // Больше не новое
          });

          return {
            ...prev,
            is_new: false,           // Больше не новое слово
            is_learned: newIntervalLevel >= 15, // Выучено если достигли максимального уровня
            interval_level: newIntervalLevel,
            progressInfo: {
              ...prev.progressInfo,
              progress: prev.progressInfo?.progress || 0,
              interval_level: newIntervalLevel,
            }
          };
        });
      }

      // Update card with response data from server будет обработано в асинхронной функции

      // 🔧 ИСПРАВЛЕНИЕ: НЕ запускаем предзагрузку здесь!
      // Предзагрузка будет запущена ПОСЛЕ перехода к новой карточке в animateCardOutWithPreload()

      if (shouldAdvanceToNext) {
        // Проверяем дневную цель перед переходом к следующей карточке
        // currentCardNumber будет увеличен на 1 при переходе, поэтому проверяем currentCardNumber + 1
        if (currentCardNumber + 1 > totalCardsInDeck) {
          console.log('🎯 Дневная цель будет достигнута после этой карточки! Завершаем тренировку');
          setIsLoading(false);
          // Принудительно устанавливаем currentCard в null для показа экрана завершения
          setTimeout(() => {
            setCurrentCard(null);
          }, 1000); // Даем время показать feedback
          return;
        }

        // Correct answer
        triggerHaptic('success');

        // 📝 Сохраняем текущую карточку в историю перед переходом к следующей
        if (currentCard) {
          const { targetLang } = getLanguageSettings(user);
          const targetWord = getWordByLanguage(currentCard, targetLang);
          const correctAnswerText = targetWord?.examples?.[0]?.correct_answers?.[0] || targetWord?.word || '';

          // Получаем полное состояние ПОСЛЕ ответа (будет обновлено асинхронно)
          const newIntervalLevel = currentCard.interval_level || -1;
          const newIsLearned = currentCard.is_learned || false;
          const newIsNew = currentCard.is_new || true;

          setPreviousCard(currentCard);
          setPreviousCardAnswer(correctAnswerText);
          setPreviousCardState({
            interval_level: newIntervalLevel,
            is_learned: newIsLearned,
            is_new: newIsNew
          });
          setCanGoBack(true);

          console.log('💾 Сохранена предыдущая карточка с состоянием:', {
            interval_level: newIntervalLevel,
            is_learned: newIsLearned,
            is_new: newIsNew
          });
        }

        // 🚀 ОПТИМИЗАЦИЯ: Используем предзагруженную карточку или загружаем новую
        setIsLoading(true);

        // 🔧 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: НЕ УСТАНАВЛИВАЕМ карточку сразу!
        // Создаем промис, который будет разрешен в анимации
        let nextCardPromise: Promise<Card | null>;

        // Проверяем, есть ли предзагруженная карточка БЕЗ её извлечения
        const preloadStatus = cardPreloader.getStatus();

        if (preloadStatus.card && !preloadStatus.isLoading) {
          // 🎯 ПРОВЕРЯЕМ: не та ли это же карточка, что и текущая
          const currentCardId = currentCard?.word_id || currentCard?.id || currentCard?._id;
          const preloadedCardId = preloadStatus.card.word_id || preloadStatus.card.id || preloadStatus.card._id;

          if (currentCardId && preloadedCardId === currentCardId) {
            log.warning(LogCategory.PRELOADER, 'Предзагруженная карточка та же, что и текущая - игнорируем', {
              currentCardId,
              preloadedCardId,
              word: preloadStatus.card.word || 'unknown'
            });
            log.preloadMissed();
            nextCardPromise = fetchNewCard();
          } else {
            // 🔧 СОЗДАЕМ ПРОМИС, который извлечет карточку ТОЛЬКО в анимации
            nextCardPromise = Promise.resolve(preloadStatus.card);
            log.preloadUsed(preloadStatus.card.word || 'unknown');
            // Очищаем предзагруженную карточку СЕЙЧАС, чтобы не использовать её дважды
            clearPreloadedCard();
          }
        } else {
          // Если карточка еще загружается, ждем её с таймаутом
          // 🔧 УВЕЛИЧЕН ТАЙМАУТ: с 800ms до 1500ms для ожидания медленных запросов
          const awaitedCard = await waitForPreload(1500); // Ждем максимум 1500ms

          if (awaitedCard) {
            // 🎯 ПРОВЕРЯЕМ: не та ли это же карточка, что и текущая
            const currentCardId = currentCard?.word_id || currentCard?.id || currentCard?._id;
            const awaitedCardId = awaitedCard.word_id || awaitedCard.id || awaitedCard._id;

            if (currentCardId && awaitedCardId === currentCardId) {
              log.warning(LogCategory.PRELOADER, 'Ожидаемая предзагруженная карточка та же, что и текущая - игнорируем', {
                currentCardId,
                preloadedCardId: awaitedCardId,
                word: awaitedCard.word || 'unknown'
              });
              // НЕ дублируем log.preloadMissed() - уже будет вызван ниже
              nextCardPromise = fetchNewCard();
            } else {
              log.preloadUsed(awaitedCard.word || 'unknown');
              nextCardPromise = Promise.resolve(awaitedCard);
            }
          } else {
            // Вызываем log.preloadMissed() только один раз
            log.preloadMissed();
            nextCardPromise = fetchNewCard();
          }
        }



        // Show feedback and go to next card
        setTimeout(() => {
          log.animationStart('переход к следующей карточке');
          const animationStart = Date.now();

          // Reset answer start time for next card
          setAnswerStartTime(Date.now());

          // Use optimized animation with preloaded data
          animateCardOutWithPreload(nextCardPromise);

          // Логируем время анимации
          setTimeout(() => {
            const animationTime = Date.now() - animationStart;
            log.animationComplete('переход к следующей карточке', animationTime);
          }, 400); // Примерное время анимации
        }, 800); // Уменьшили с 1000 до 800ms для более быстрого перехода
      } else {
        // Wrong answer OR used help button - different handling
        if (usedHelpButton) {
          // Used help button - no error haptic or shake, just gentle feedback
          triggerHaptic('light');
          console.log('🔄 Использована подсказка - повторяем карточку без тряски');
        } else {
          // Actually wrong answer - trigger error feedback
          triggerHaptic('error');
          console.log('🔄 Неправильный ответ - повторяем текущую карточку');
        }

        // 🔧 ИСПРАВЛЕНИЕ: Увеличиваем номер карточки при неправильном ответе с задержкой
        // чтобы пользователь сначала увидел информацию об ошибке, а потом обновился счетчик
        // 📍 МЕСТО НАСТРОЙКИ СЧЕТЧИКА КАРТОЧЕК В ПРОГРЕСС-БАРЕ
        setTimeout(() => {
          setCurrentCardNumber(prev => prev + 1);
        }, 2400); // Задержка 1.7 секунды (было 1.2, добавили еще 0.5)

        // Don't replace user input - keep what they typed to show the mistake
        // Store the correct answer for separate display
        const { targetLang } = getLanguageSettings(user);
        const targetWord = getWordByLanguage(currentCard, targetLang);
        const correctAnswerText = targetWord?.examples?.[0]?.correct_answers?.[0] || targetWord?.word || '';
        setCorrectAnswer(correctAnswerText);

        // Start countdown timer
        setTimeLeft(TIMER_DISPLAY_DURATION); // Use 70% of actual delay for better sync

        // Enhanced shake animation - only for actual wrong answers, not for help button usage
        if (!usedHelpButton) {
          Animated.sequence([
          // Shake left
          Animated.timing(shakeAnim, {
            toValue: -10,
            duration: 50,
            useNativeDriver: true,
          }),
          // Shake right
          Animated.timing(shakeAnim, {
            toValue: 10,
            duration: 50,
            useNativeDriver: true,
          }),
          // Shake left
          Animated.timing(shakeAnim, {
            toValue: -10,
            duration: 50,
            useNativeDriver: true,
          }),
          // Shake right
          Animated.timing(shakeAnim, {
            toValue: 10,
            duration: 50,
            useNativeDriver: true,
          }),
          // Return to center
          Animated.timing(shakeAnim, {
            toValue: 0,
            duration: 50,
            useNativeDriver: true,
          })
        ]).start();
        }

        // 🚀 ОПТИМИЗАЦИЯ: Для неправильного ответа или использования подсказки повторяем текущую карточку

        // Для неправильного ответа мы не переходим к следующей карточке,
        // а повторяем текущую (форсированная очередь)
        // Поэтому просто сбрасываем состояние без загрузки новой карточки

        // Auto-advance after delay if still incorrect - увеличиваем задержку в 2 раза
        setTimeout(() => {
          resetCardState();
        }, 2400); // Увеличили до 2400ms (в 3 раза больше правильного ответа) для изучения ошибки
      }
    } catch (error) {
      console.error('Error submitting answer:', error);

      // Проверяем тип ошибки
      const errorMessage = error instanceof Error ? error.message : String(error);

      if (errorMessage.includes('ngrok') || errorMessage.includes('503')) {
        setFeedbackMessage('Проблема с подключением к серверу. Попробуйте еще раз.');
        log.error(LogCategory.API, 'Ошибка ngrok туннеля', { error: errorMessage });
      } else {
        setFeedbackMessage(t('common:errors.networkError'));
        log.error(LogCategory.API, 'Ошибка отправки ответа', { error: errorMessage });
      }

      // Auto-advance even on error with preload
      console.log('🚀 Starting preload of next card (error fallback)...');
      setIsLoading(true);
      const nextCardPromise = fetchNewCard();

      setTimeout(() => {
        animateCardOutWithPreload(nextCardPromise);
      }, 1200);
    } finally {
      // setIsLoading(false) теперь управляется в animateCardOut
    }
  }, [currentCard, userInput, shakeAnim, animateCardOut, user?._id, resetCardState]);

  // Handle input submit
  const handleSubmit = useCallback(async () => {
    if (userInput.trim()) {
      try {
        await checkAnswer();
      } catch (error) {
        console.error('Error in handleSubmit:', error);
        setFeedbackMessage(t('common:errors.unknownError'));
      }
    }
  }, [userInput, checkAnswer]);

  // Handle "Don't Know" button press for new cards
  const handleDontKnow = useCallback(async () => {
    if (!currentCard || !user?._id || isCorrect !== null) {
      return;
    }

    try {
      // Show the correct answer
      const { targetLang } = getLanguageSettings(user);
      const targetWord = getWordByLanguage(currentCard, targetLang);
      const correctAnswer = targetWord?.examples?.[0]?.correct_answers?.[0] || targetWord?.word || '';

      setUserInput(correctAnswer);
      // Don't set isCorrect to avoid triggering incorrect answer animation

      // Mark that help button was used
      setUsedHelpButton(true);

      // Trigger haptic feedback for learning
      triggerHaptic('light');

      // Focus the input to keep keyboard open
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);

      // 🚀 ИСПРАВЛЕНИЕ: НЕ отправляем запрос на сервер здесь
      // Запрос будет отправлен в checkAnswer() когда пользователь нажмет Enter

      // 🚀 ИСПРАВЛЕНИЕ: Логика повторения теперь обрабатывается в checkAnswer()
      // когда пользователь нажмет Enter, checkAnswer() увидит usedHelpButton=true
      // и обработает это как неправильный ответ (shouldAdvanceToNext=false)
      console.log('🔄 "I don\'t know" - ответ будет обработан как неправильный при отправке');

    } catch (error) {
      console.error('Error handling don\'t know:', error);
      setFeedbackMessage(t('common:errors.unknownError'));
    }
  }, [currentCard, user?._id, isCorrect, targetLang, answerStartTime]);

  // Handle "Don't Remember" button press for old cards
  const handleDontRemember = useCallback(async () => {
    if (!currentCard || !user?._id || isCorrect !== null) {
      return;
    }

    try {
      // Show the correct answer (same logic as "don't know")
      const { targetLang } = getLanguageSettings(user);
      const targetWord = getWordByLanguage(currentCard, targetLang);
      const correctAnswer = targetWord?.examples?.[0]?.correct_answers?.[0] || targetWord?.word || '';

      setUserInput(correctAnswer);
      // Don't set isCorrect to avoid triggering incorrect answer animation

      // Mark that help button was used
      setUsedHelpButton(true);

      // Trigger haptic feedback for learning
      triggerHaptic('light');

      // Focus the input to keep keyboard open
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);

      // 🚀 ИСПРАВЛЕНИЕ: НЕ отправляем запрос на сервер здесь
      // Запрос будет отправлен в checkAnswer() когда пользователь нажмет Enter

      // 🚀 ИСПРАВЛЕНИЕ: Логика повторения теперь обрабатывается в checkAnswer()
      // когда пользователь нажмет Enter, checkAnswer() увидит usedHelpButton=true
      // и обработает это как неправильный ответ (shouldAdvanceToNext=false)
      console.log('🔄 "I don\'t remember" - ответ будет обработан как неправильный при отправке');

    } catch (error) {
      console.error('Error handling don\'t remember:', error);
      setFeedbackMessage(t('common:errors.unknownError'));
    }
  }, [currentCard, user?._id, isCorrect, targetLang, answerStartTime]);

  // Handle hint button press - insert first letter into input
  const handleHint = useCallback(() => {
    if (!currentCard || isCorrect !== null) {
      return;
    }

    const { targetLang } = getLanguageSettings(user);
    const targetWord = getWordByLanguage(currentCard, targetLang);
    const correctAnswer = targetWord?.examples?.[0]?.correct_answers?.[0] || '';

    if (correctAnswer && userInput.length === 0) {
      // Insert first letter if input is empty
      setUserInput(correctAnswer[0]);
      triggerHaptic('light');

      // Focus the input to keep keyboard open
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [currentCard, user, userInput, isCorrect]);

  // Функция для сброса состояния карточки (используется при неправильном ответе и принудительном переходе)
  const resetCardState = useCallback(() => {
    console.log('🔄 Сброс состояния карточки для повторного ввода');

    // Reset answer start time for next attempt
    setAnswerStartTime(Date.now());

    // Сбрасываем состояние для повторного ввода
    setUserInput('');
    setIsCorrect(null);
    setShowHint(false);
    setCorrectAnswer('');
    setTimeLeft(0);
    setIsLoading(false);
    setUsedHelpButton(false);
    setCanGoForward(false);

    // Фокусируем input для повторного ввода с улучшенной надежностью
    setTimeout(() => {
      if (inputRef.current && !isViewingPrevious) {
        try {
          inputRef.current.focus();
          console.log('🎯 Автофокус при сбросе состояния карточки');

          // Дополнительная проверка через небольшую задержку
          setTimeout(() => {
            if (inputRef.current && !inputRef.current.isFocused() && !isViewingPrevious) {
              console.log('🔄 Повторная попытка фокуса при сбросе');
              inputRef.current.focus();
            }
          }, 100);
        } catch (error) {
          console.log('⚠️ Ошибка при установке фокуса (сброс состояния):', error);
        }
      }
    }, 200); // Увеличили с 100ms до 200ms
  }, []);

  // Handle manual navigation to next card
  const handleNextCard = useCallback(async () => {
    try {
      // Если мы просматриваем предыдущую карточку, восстанавливаем сохраненное состояние
      if (isViewingPrevious) {
        console.log('🔄 Возврат к текущей карточке из просмотра предыдущей');

        setIsViewingPrevious(false);

        // Восстанавливаем сохраненное состояние
        setCurrentCard(savedCurrentCard);
        setUserInput(savedUserInput);
        setIsCorrect(savedIsCorrect);
        setCorrectAnswer(savedCorrectAnswer);
        setTimeLeft(savedTimeLeft);

        // Восстанавливаем состояние кнопки "вперед" - если был ответ, кнопка должна быть активна
        setCanGoForward(savedIsCorrect !== null);

        // Сбрасываем сохраненное состояние
        setSavedCurrentCard(null);
        setSavedUserInput('');
        setSavedIsCorrect(null);
        setSavedCorrectAnswer('');
        setSavedTimeLeft(0);

        // Фокусируем инпут для ввода ответа - увеличиваем задержку для надежного восстановления состояния
        setTimeout(() => {
          if (inputRef.current) {
            inputRef.current.focus();
            console.log('🎯 Фокус восстановлен на текущей карточке');
          }
        }, 300);

        return;
      }

      // Если кнопка неактивна, не делаем ничего
      if (!canGoForward) {
        console.log('🚫 Кнопка "вперед" неактивна - ответ еще не введен');
        return;
      }

      // Проверяем, правильный ли был ответ
      if (isCorrect === true) {
        // Проверяем дневную цель перед принудительным переходом
        if (currentCardNumber + 1 > totalCardsInDeck) {
          console.log('🎯 Дневная цель будет достигнута! Завершаем тренировку вместо принудительного перехода');
          // Принудительно устанавливаем currentCard в null для показа экрана завершения
          setCurrentCard(null);
          return;
        }

        // Правильный ответ - переходим к следующей карточке с анимацией
        console.log('⏭️ Принудительный переход к следующей карточке (правильный ответ)');
        setIsLoading(true);
        const nextCardPromise = fetchNewCard();

        // Reset answer start time for next card
        setAnswerStartTime(Date.now());
        // Use optimized animation with preloaded data
        animateCardOutWithPreload(nextCardPromise);
      } else if (isCorrect === false) {
        // Неправильный ответ - сбрасываем состояние без анимации (та же карточка)
        console.log('⏭️ Принудительный сброс таймера (неправильный ответ)');
        resetCardState();
      }
    } catch (error) {
      console.error('Error in handleNextCard:', error);
      setFeedbackMessage(t('common:errors.unknownError'));
    }
  }, [isViewingPrevious, canGoForward, fetchNewCard, animateCardOutWithPreload]);

  // Handle manual navigation to previous card
  const handlePreviousCard = useCallback(async () => {
    try {
      if (!previousCard) {
        console.log('No previous card available');
        return;
      }

      // Сохраняем текущее состояние для восстановления
      setSavedCurrentCard(currentCard);
      setSavedUserInput(userInput);
      setSavedIsCorrect(isCorrect);
      setSavedCorrectAnswer(correctAnswer);
      setSavedTimeLeft(timeLeft);

      // Показываем предыдущую карточку с заполненным правильным ответом
      setIsViewingPrevious(true);

      // Обновляем предыдущую карточку с полным состоянием ПОСЛЕ ответа
      const updatedPreviousCard = {
        ...previousCard,
        interval_level: previousCardState.interval_level,
        is_learned: previousCardState.is_learned,
        is_new: previousCardState.is_new,
        progressInfo: {
          ...previousCard.progressInfo,
          interval_level: previousCardState.interval_level,
          is_learned: previousCardState.is_learned,
          is_new: previousCardState.is_new,
        }
      };

      console.log('📖 Показана предыдущая карточка с обновленным состоянием:', {
        interval_level: previousCardState.interval_level,
        is_learned: previousCardState.is_learned,
        is_new: previousCardState.is_new
      });

      setCurrentCard(updatedPreviousCard);
      setUserInput(previousCardAnswer); // Заполняем инпут правильным ответом
      setIsCorrect(true); // Показываем как правильный ответ
      setCorrectAnswer(previousCardAnswer);

      // Сбрасываем другие состояния
      setShowHint(false);
      setUsedHelpButton(false);
      setTimeLeft(0);

      // Убираем фокус с инпута и скрываем клавиатуру
      setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.blur();
        }
      }, 50);

      console.log('Показана предыдущая карточка:', previousCard.word);

      // TODO: В будущем здесь нужно будет добавить отображение ошибки пользователя, если она была

    } catch (error) {
      console.error('Error in handlePreviousCard:', error);
      setFeedbackMessage(t('common:errors.unknownError'));
    }
  }, [previousCard, previousCardAnswer, currentCard, userInput, isCorrect, correctAnswer, timeLeft, t]);

  // Get native language example sentence with correct answer inserted
  const { nativeSentence, nativeParts, targetSentence } = React.useMemo(() => {
    if (!currentCard?.native_word?.examples?.[0]) {
      return { 
        nativeSentence: '',
        nativeParts: { before: '', word: '', after: '' },
        targetSentence: currentCard?.examples?.[0]?.sentence || ''
      };
    }
    
    const nativeExample = currentCard.native_word.examples[0];
    const nativeText = nativeExample.sentence || '';
    const correctAnswer = nativeExample.correct_answers?.[0] || currentCard.native_word.word;
    
    // Find the position of the blank (___)
    const blankIndex = nativeText.indexOf('___');
    
    if (blankIndex === -1) {
      // If no blank, just return the sentence as is
      return {
        nativeSentence: nativeText,
        nativeParts: { before: nativeText, word: '', after: '' },
        targetSentence: currentCard.examples?.[0]?.sentence || ''
      };
    }
    
    const beforeBlank = nativeText.substring(0, blankIndex);
    const afterBlank = nativeText.substring(blankIndex + 3); // 3 characters for '___'
    
    return {
      nativeSentence: beforeBlank + correctAnswer + afterBlank,
      nativeParts: {
        before: beforeBlank,
        word: correctAnswer,
        after: afterBlank
      },
      targetSentence: currentCard.examples?.[0]?.sentence || ''
    };
  }, [currentCard]);

  // Get language settings
  const { nativeLang, targetLang } = getLanguageSettings(user);
  
  // Get the current example sentence parts
  const getExampleParts = useCallback((): [string, string] => {
    if (!currentCard?.examples?.[0]) return ['', ''];
    
    const example = currentCard.examples[0];
    const sentence = example.sentence;
    const answer = example.correct_answers?.[0] || '';
    const parts = sentence.split(answer);
    
    return [parts[0] || '', parts[1] || ''];
  }, [currentCard]);

  // Split the English sentence for input
  const [beforeBlank, afterBlank] = React.useMemo(() => {
    if (!currentCard?.examples?.[0]) return ['', ''];
    
    const example = currentCard.examples[0];
    const sentence = example.sentence || '';
    const index = sentence.indexOf('___');
    
    if (index === -1) return [sentence, ''];
    
    return [
      sentence.substring(0, index),
      sentence.substring(index + 3) // 3 characters for '___'
    ];
  }, [currentCard]);

  // Get translation parts for the word hint
  const translationParts = React.useMemo(() => {
    if (!currentCard?.native_word) return ['', '', ''];
    
    const translation = currentCard.native_word.word || '';
    
    // Return empty strings for before and after, and the full translation for the middle part
    // The word_info will be added separately in the JSX
    return ['', translation, ''];
  }, [currentCard?.native_word]);

  // Calculate input width based on text length
  useEffect(() => {
    if (!currentCard) {
      setInputWidth(100);
      return;
    }

    const baseWidth = currentCard.word.length * 10 + 30; // 10px per char + padding
    const minWidth = 100;
    const maxWidth = 300;

    // Set the calculated width with min/max constraints
    setInputWidth(Math.max(minWidth, Math.min(maxWidth, baseWidth)));
  }, [currentCard]);

  // Calculate progress bars based on interval level
  const getProgressBarsInfo = useCallback((card: Card) => {
    if (!card) {
      return { count: 1, color: '#3a3a4a' };
    }

    const intervalLevel = card.interval_level ?? -1;
    const isNew = card.is_new ?? true;
    const isLearned = card.is_learned ?? false;

    // Логика согласно требованиям:
    // 5 полосок (зелёная) - выученные слова (is_learned = true)
    // 1 полоска (оранжевая) - только для новых слов (is_new = true)
    // 1 полоска (голубая) - интервал меньше 1 дня (уровни -1, 0-4)
    // 2 полоски - уровень 5-8 (1 день - 1 месяц)
    // 3 полоски - уровень 9-11 (1-6 месяцев)
    // 4 полоски - уровень 12-14 (6-12 месяцев)
    // 5 полосок (зелёная) - уровень 15+ (более 1 года)

    if (isLearned) {
      return { count: 5, color: '#4CAF50' }; // 5 зелёных полосок для выученных слов
    } else if (isNew) {
      return { count: 1, color: '#FFA500' }; // Оранжевая только для новых слов
    } else if (intervalLevel >= -1 && intervalLevel <= 4) {
      return { count: 1, color: '#7fb4ff' }; // Голубая для интервала меньше 1 дня
    } else if (intervalLevel >= 5 && intervalLevel <= 8) {
      return { count: 2, color: '#7fb4ff' }; // 2 полоски для 1 день - 1 месяц
    } else if (intervalLevel >= 9 && intervalLevel <= 11) {
      return { count: 3, color: '#4a7dff' }; // 3 полоски для 1-6 месяцев
    } else if (intervalLevel >= 12 && intervalLevel <= 14) {
      return { count: 4, color: '#4a7dff' }; // 4 полоски для 6-12 месяцев
    } else {
      return { count: 5, color: '#4CAF50' }; // 5 полосок зелёная для более 1 года
    }
  }, []);

  // Get interval display text for debugging
  const getIntervalDisplayText = useCallback((card: Card) => {
    if (!card) return '';

    const intervalLevel = card.interval_level ?? -1;
    const isNew = card.is_new ?? true;

    // Debug logs removed for cleaner output

    // Базовая информация об интервале
    let intervalText = '';
    if (isNew) {
      intervalText = 'New';
    } else {
      intervalText = `${intervalLevel}`;
    }

    // Добавляем информацию о приоритете для A0 уровня
    if (card.level === 'A0' && card.priority) {
      let priorityText = '';
      switch (card.priority) {
        case 'ultra_core':
          priorityText = 'ULTRA';
          break;
        case 'core':
          priorityText = 'CORE';
          break;
        case 'extended':
          priorityText = 'EXT';
          break;
        default:
          priorityText = 'A0';
      }
      intervalText += ` | ${priorityText}`;
    }

    return intervalText;
  }, []);

  // Функция для получения перевода по concept_id и языку
  const fetchTranslation = useCallback(async (conceptId: string, lang: string) => {
    try {
      // Получаем токен из AsyncStorage
      const token = await AsyncStorage.getItem('auth_token');
      
      if (!token) {
        console.warn('No auth token found in AsyncStorage');
        return null;
      }
      
      const url = `${API_URL}/api/words/concept/${encodeURIComponent(conceptId)}?lang=${encodeURIComponent(lang)}`;
      console.log(`Fetching translation from: ${url}`);
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`HTTP error! status: ${response.status}, body: ${errorText}`);
        return null;
      }

      const data = await response.json();
      console.log('Received translation data:', data);
      return data;
    } catch (error) {
      console.error(`Error fetching translation for concept ${conceptId} (${lang}):`, error);
      return null;
    }
  }, []);

  // Fetch the next card using spaced repetition
  const fetchNewCard = useCallback(async () => {
    const fetchStartTime = Date.now();

    if (!user?._id) {
      log.cardError('No user ID available');
      return null;
    }



    const nativeLang = user?.settings?.native_language || 'ru';
    const targetLang = user?.settings?.target_language || 'en';

    // Проверяем, что target_language действительно есть в списке изучаемых языков
    const actualTargetLang = user?.learning_languages?.includes(targetLang)
      ? targetLang
      : user?.learning_languages?.[0] || targetLang;

    log.cardStart(user._id, actualTargetLang);

    try {
      setIsLoading(true);
      setFeedbackMessage('');

      // 🚀 Используем общую логику получения и обработки данных
      const rawData = await fetchRawCardData({
        userId: user._id,
        nativeLang,
        targetLang: actualTargetLang
      });

      if (!rawData) {
        // Нет карточек для повторения
        return null;
      }

      const cardData = await processCardData({
        data: rawData,
        userId: user._id,
        nativeLang,
        targetLang: actualTargetLang
      });

      const totalFetchTime = Date.now() - fetchStartTime;
      log.cardSuccess(cardData.word, cardData.translation, totalFetchTime);

      return cardData;
    } catch (error) {
      log.cardError(error.message);
      throw error;
    } finally {
      // setIsLoading(false) будет вызван в вызывающем коде
    }
  }, [user?.settings?.target_language, user?.settings?.native_language, user?.learning_languages]);

  // Загрузка карточки при монтировании
  useEffect(() => {
    let isMounted = true;
    
    const loadCard = async () => {
      try {
        const newCard = await fetchNewCard();
        
        if (isMounted) {
          // 🔧 ИСПРАВЛЕНИЕ: Защита от перезаписи карточки
          const currentCardId = currentCard?.word_id || currentCard?.id || currentCard?._id;
          const newCardId = newCard?.word_id || newCard?.id || newCard?._id;

          // Если это та же карточка, не перезаписываем
          if (currentCardId === newCardId) {
            console.log(`[PRELOADER] ⚠️ Предотвращена перезапись карточки в loadCard: ${currentCardId}`);
            return;
          }

          setCurrentCard(newCard);

          // 🔧 ИСПРАВЛЕНИЕ: Полная очистка состояния для новой карточки
          setUserInput('');
          setIsCorrect(null);
          setCorrectAnswer('');
          setUsedHelpButton(false); // Сбрасываем флаг использования подсказки
          setCanGoForward(false); // Сбрасываем активность кнопки "вперед"
          setIsLoading(false);

          // 🚀 ЗАПУСКАЕМ ПРЕДЗАГРУЗКУ для первой карточки
          if (newCard && user) {
            const nativeLang = user?.settings?.native_language || 'ru';
            const targetLang = user?.settings?.target_language || 'en';
            const actualTargetLang = user?.learning_languages?.includes(targetLang)
              ? targetLang
              : user?.learning_languages?.[0] || targetLang;

            // Отложенная предзагрузка, чтобы currentCard точно установилась
            setTimeout(() => {
              if (newCard) {
                const currentCardId = newCard.word_id || newCard.id || newCard._id;
                log.info(LogCategory.PRELOADER, 'Запускаем предзагрузку для первой карточки', {
                  cardId: currentCardId,
                  word: newCard.word || 'unknown'
                });
                startPreloading(user._id, nativeLang, actualTargetLang, currentCardId).catch(error => {
                  log.error(LogCategory.PRELOADER, `Предзагрузка первой карточки не удалась: ${error.message}`);
                });
              } else {
                log.warning(LogCategory.PRELOADER, 'Нет карточки для запуска предзагрузки');
              }
            }, 200);
          }

          // Фокус на input после загрузки первой карточки
          setTimeout(() => {
            if (inputRef.current) {
              inputRef.current.focus();
            }
          }, 200);
        }
      } catch (error) {
        log.cardError(error.message);
        if (isMounted) {
          setFeedbackMessage(t('common:errors.networkError'));
          setIsLoading(false);
        }
      }
    };

    setIsLoading(true);
    loadCard();

    return () => {
      isMounted = false;
    };
  }, [fetchNewCard]);

  // Автофокус при смене карточки (но не при просмотре предыдущей)
  useEffect(() => {
    if (currentCard && !isLoading && !isViewingPrevious && isCorrect === null) {
      // Увеличиваем задержку и добавляем дополнительные проверки для надежности
      const focusTimeout = setTimeout(() => {
        if (inputRef.current && !isViewingPrevious && isCorrect === null) {
          try {
            inputRef.current.focus();
            console.log('🎯 Автофокус на новой карточке');

            // Дополнительная проверка через небольшую задержку
            setTimeout(() => {
              if (inputRef.current && !inputRef.current.isFocused()) {
                console.log('🔄 Повторная попытка фокуса');
                inputRef.current.focus();
              }
            }, 100);
          } catch (error) {
            console.log('⚠️ Ошибка при установке фокуса:', error);
          }
        }
      }, 400); // 🔧 ИСПРАВЛЕНИЕ: Увеличили с 250ms до 400ms чтобы дать анимациям время завершиться

      return () => clearTimeout(focusTimeout);
    }
  }, [currentCard, isLoading, isViewingPrevious, isCorrect]);

  // Animate feedback in when isCorrect changes
  useEffect(() => {
    if (isCorrect !== null) {
      // Reset and animate in
      feedbackAnim.setValue(0);
      Animated.spring(feedbackAnim, {
        toValue: 1,
        useNativeDriver: true,
        friction: 8,
        tension: 40,
      }).start();
    } else {
      feedbackAnim.setValue(0);
      // 🔧 ИСПРАВЛЕНИЕ: Убираем дублирующий фокус - управление фокусом теперь централизовано в основном useEffect
      // Когда isCorrect сбрасывается в null (новая карточка), фокусируем input
      // НО только если это не новая карточка (чтобы не конфликтовать с основным автофокусом)
      // if (currentCard && !isViewingPrevious) {
      //   setTimeout(() => {
      //     if (inputRef.current && !isViewingPrevious) {
      //       try {
      //         inputRef.current.focus();
      //         console.log('🎯 Автофокус при сбросе состояния');
      //       } catch (error) {
      //         console.log('⚠️ Ошибка при установке фокуса (сброс):', error);
      //       }
      //     }
      //   }, 150); // Увеличили с 100ms до 150ms
      // }
    }
  }, [isCorrect, feedbackAnim]);

  // Timer countdown effect for incorrect answers
  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 100) { // 100ms intervals
            return 0;
          }
          return prev - 100;
        });
      }, 100);

      return () => clearInterval(timer);
    }
  }, [timeLeft]);

  // Вызываем onTrainingComplete при завершении тренировки
  useEffect(() => {
    // Проверяем условие завершения: нет карточек от API (обычное завершение или достигнута дневная цель)
    const noCardsAvailable = !isLoading && !currentCard;

    if (noCardsAvailable && onTrainingComplete) {
      console.log('🎯 Тренировка завершена - вызываем обновление статистики');
      onTrainingComplete();
    }
  }, [isLoading, currentCard, onTrainingComplete]);

  // Удаляем дублирующиеся объявления состояний и рефов, так как они уже объявлены в начале компонента

  // Handle text input changes
  const handleInputChange = (text: string) => {
    setUserInput(text);
    // Прогресс больше не обновляется при вводе
  };

  // Show loading indicator only when there's no current card and we're loading
  if (isLoading && !currentCard) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#7fb4ff" />
        <Text style={styles.loadingText}>{t('common:buttons.loading')}</Text>
      </View>
    );
  }

  // Show completion screen if no card is available
  if (!currentCard) {
    console.log('🎯 Showing completion screen - no current card available');
    return (
      <SafeAreaProvider>
        <StatusBar barStyle="light-content" />
        <View style={styles.container}>
          {/* Animated Gradient Background */}
          <AnimatedGradientBackground {...backgroundThemes.training} />

          {/* Completion Message */}
          <View style={styles.completionContainer}>
            <GlassmorphismCard
              {...glassmorphismPresets.training}
              style={styles.completionCard}
              width="90%"
              height="auto"
            >
              <View style={styles.completionContent}>
                <Text style={styles.completionEmoji}>🎉</Text>
                <Text style={styles.completionTitle}>
                  {t('completion.congratulations')}
                </Text>
                <Text style={styles.completionMessage}>
                  {t('completion.sessionComplete')}
                </Text>

                {/* Single Start New Training Button - Modern style with glow */}
                <TouchableOpacity
                  style={styles.completionModernButton}
                  onPress={() => {
                    console.log('Starting new training session...');
                    setIsLoading(true);
                    setCurrentCardNumber(1); // Reset card counter
                    fetchNewCard()
                      .then((newCard) => {
                        // 🔧 ИСПРАВЛЕНИЕ: Защита от перезаписи карточки
                        const currentCardId = currentCard?.word_id || currentCard?.id || currentCard?._id;
                        const newCardId = newCard?.word_id || newCard?.id || newCard?._id;

                        // Если это та же карточка, не перезаписываем
                        if (currentCardId === newCardId) {
                          console.log(`[PRELOADER] ⚠️ Предотвращена перезапись карточки в fallback: ${currentCardId}`);
                          setIsLoading(false);
                          return;
                        }

                        setCurrentCard(newCard);

                        // 🔧 ИСПРАВЛЕНИЕ: Очищаем состояние прогресса
                        setUserInput('');
                        setIsCorrect(null);
                        setCorrectAnswer('');
                        setUsedHelpButton(false);

                        setIsLoading(false);
                      })
                      .catch(e => {
                        console.error('Error in retry:', e);
                        setIsLoading(false);
                      });
                  }}
                >
                  <LinearGradient
                    colors={['#87CEFD', '#B19CD9', '#D8BFD8']} // 🎨 ТОТ ЖЕ МЯГКИЙ ГРАДИЕНТ КАК НА ГЛАВНОМ ЭКРАНЕ
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                    style={styles.completionModernGradient}
                  >
                    <View style={styles.completionButtonContent}>
                      <Text style={styles.completionButtonText}>
                        {t('common:buttons.startNewTraining')}
                      </Text>
                      <Text style={styles.completionButtonIcon}>▶</Text>
                    </View>
                  </LinearGradient>
                </TouchableOpacity>
              </View>
            </GlassmorphismCard>
          </View>
        </View>
      </SafeAreaProvider>
    );
  }

  return (
    <SafeAreaProvider>
      <StatusBar barStyle="light-content" />
      <View style={styles.container}>
        {/* Animated Gradient Background */}
        <AnimatedGradientBackground {...backgroundThemes.training} />

        <SafeAreaView style={styles.safeArea}>
          {/* Progress Bar Header */}
          <View style={styles.progressHeader}>
              <View style={styles.progressContainer}>
                <Text style={styles.progressText}>
                  {currentCardNumber} of {totalCardsInDeck} cards
                </Text>
                <View style={styles.progressBarContainer}>
                  <Animated.View style={[styles.progressBar, {
                    width: `${(currentCardNumber / totalCardsInDeck) * 100}%`
                  }]}>
                    <LinearGradient
                      colors={['#7fb4ff', '#4a7dff']}
                      start={{ x: 0, y: 0 }}
                      end={{ x: 1, y: 0 }}
                      style={styles.progressGradient}
                    />
                  </Animated.View>
                </View>
              </View>
          </View>

          <KeyboardAvoidingView
            style={styles.keyboardAvoidingView}
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
          >
            <ScrollView
              contentContainerStyle={styles.scrollView}
              keyboardShouldPersistTaps="handled"
              showsVerticalScrollIndicator={false}
            >
            {/* Main content */}
            <View style={styles.content}>
              {/* Main Card */}
              <Animated.View
                style={[
                  {
                    transform: [
                      {
                        translateX: Animated.add(
                          slideAnim,
                          shakeAnim
                        )
                      },
                      { scale: scaleAnim },
                      {
                        rotate: rotateAnim.interpolate({
                          inputRange: [-360, 360],
                          outputRange: ['-360deg', '360deg']
                        })
                      }
                    ]
                  }
                ]}
              >
                <GlassmorphismCard
                  {...glassmorphismPresets.training}
                  style={styles.card}
                  width="100%"
                  height="auto"
                >
                  {/* Native language word with grammar hint */}
                  <View style={styles.translationContainer}>
                    <View style={styles.grammarHintContainer}>
                      {currentCard && (() => {
                        const nativeWord = getWordByLanguage(currentCard, nativeLang);
                        if (!nativeWord) return null;

                        return (
                          <Text style={styles.wordWithHint}>
                            {nativeWord.text || nativeWord.word}
                            {currentCard.wordInfo?.type && (
                              <>
                                <Text style={styles.wordHintSeparator}> — </Text>
                                <Text style={styles.wordInfoText}>
                                  {currentCard.wordInfo.type}
                                  {currentCard.wordInfo.form && `, ${currentCard.wordInfo.form}`}
                                </Text>
                              </>
                            )}
                          </Text>
                        );
                      })()}

                      {/* Help buttons positioned at native word level */}
                      <Animated.View
                        style={[
                          styles.helpButtonsAtWordLevel,
                          {
                            opacity: buttonsAnim,
                            transform: [
                              {
                                scale: buttonsAnim.interpolate({
                                  inputRange: [0, 1],
                                  outputRange: [0.8, 1]
                                })
                              }
                            ]
                          }
                        ]}
                        pointerEvents={isCorrect === null ? 'auto' : 'none'}
                      >
                        <TouchableOpacity
                          onPress={currentCard?.is_new ? handleDontKnow : handleDontRemember}
                          style={styles.helpTextButtonWithBorder}
                        >
                          <Text style={currentCard?.is_new ? styles.dontKnowText : styles.dontRememberText}>
                            {currentCard?.is_new ? t('card.dontKnow') : t('card.dontRemember')}
                          </Text>
                        </TouchableOpacity>
                      </Animated.View>
                    </View>
                    <Text style={styles.translation}>
                      {(() => {
                        const nativeWord = getWordByLanguage(currentCard, nativeLang);
                        if (!nativeWord?.examples?.[0]) return null;
                        
                        const example = nativeWord.examples[0];
                        const sentence = example.sentence;
                        const answer = example.correct_answers?.[0] || nativeWord.word || '';
                        const parts = sentence.split('___');
                        
                        return (
                          <>
                            {parts[0]}
                            <Text style={styles.highlightedWord}>
                              {answer}
                            </Text>
                            {parts[1]}
                          </>
                        );
                      })()}
                    </Text>
                    
                    <View style={styles.dividerContainer}>
                      <View style={[styles.dividerLine, styles.dividerTop]} />
                      <View style={[styles.dividerLine, styles.dividerBottom]} />
                    </View>
                  </View>

                  {/* Target language sentence with inline input */}
                  <View style={styles.sentenceContainer}>
                    <Text style={styles.sentence}>
                      {(() => {
                        const targetWord = getWordByLanguage(currentCard, targetLang);
                        if (!targetWord?.examples?.[0]) return null;

                        const example = targetWord.examples[0];
                        const sentence = example.sentence;
                        const answer = example.correct_answers?.[0] || targetWord.word || '';
                        const parts = sentence.split('___');

                        return (
                          <>
                            {parts[0]}
                            <View style={[styles.inputWrapper, { width: inputWidth }]}>
                              <TextInput
                                ref={inputRef}
                                style={[
                                  styles.inlineInput,
                                  isFocused && styles.inlineInputFocused,
                                  isCorrect === true && styles.inlineInputCorrect,
                                  isCorrect === false && styles.inlineInputIncorrect,
                                  { width: inputWidth }
                                ]}
                                value={userInput}
                                onChangeText={setUserInput}
                                placeholder=""
                                placeholderTextColor="transparent"
                                autoCapitalize="none"
                                autoCorrect={false}
                                onFocus={() => {
                                                                    setIsFocused(true);
                                }}
                                onBlur={() => {
                                                                    setIsFocused(false);
                                }}
                                onSubmitEditing={handleSubmit}
                                returnKeyType="done"
                                selectionColor="rgba(127, 180, 255, 0.3)"
                                cursorColor="#7fb4ff"
                                underlineColorAndroid="transparent"
                                caretHidden={false}
                                editable={isCorrect === null}
                              />
                            </View>
                            {parts[1]}
                          </>
                        );
                      })()}
                    </Text>

                  </View>
                  
                  {/* Word progress indicator with hint button */}
                  <View style={styles.wordProgressContainer}>
                    {(() => {
                      const progressInfo = getProgressBarsInfo(currentCard);
                      return [1, 2, 3, 4, 5].map((step) => {
                        const isActive = step <= progressInfo.count;
                        const color = isActive ? progressInfo.color : '#3a3a4a';

                        return (
                          <View
                            key={step}
                            style={[
                              styles.wordProgressStep,
                              { backgroundColor: color }
                            ]}
                          />
                        );
                      });
                    })()}

                    {/* Hint button positioned at progress level */}
                    <Animated.View
                      style={[
                        styles.hintIconAtProgressLevel,
                        {
                          opacity: buttonsAnim,
                          transform: [
                            {
                              scale: buttonsAnim.interpolate({
                                inputRange: [0, 1],
                                outputRange: [0.8, 1]
                              })
                            }
                          ]
                        }
                      ]}
                      pointerEvents={isCorrect === null ? 'auto' : 'none'}
                    >
                      <TouchableOpacity
                        onPress={handleHint}
                        style={styles.hintIconButtonLarge}
                        disabled={isCorrect !== null}
                      >
                        <Ionicons
                          name="bulb-outline"
                          size={24}
                          color="rgba(255, 255, 255, 0.6)"
                        />
                      </TouchableOpacity>
                    </Animated.View>
                  </View>

                  {/* Interval debug info */}
                  {currentCard && (
                    <View style={styles.intervalDebugContainer}>
                      <Text style={styles.intervalDebugText}>
                        {getIntervalDisplayText(currentCard)}
                      </Text>
                    </View>
                  )}






                </GlassmorphismCard>
              </Animated.View>

              {/* Navigation buttons - always shown */}
              <View style={styles.bottomContainer}>
                {/* Navigation buttons in first row */}
                <View style={styles.navigationButtonsContainer}>
                  <TouchableOpacity
                    onPress={handlePreviousCard}
                    style={[
                      styles.navigationButton,
                      (!canGoBack || isViewingPrevious) && styles.navigationButtonDisabled
                    ]}
                    disabled={!canGoBack || isViewingPrevious}
                  >
                    <Ionicons
                      name="chevron-back"
                      size={20}
                      color={(canGoBack && !isViewingPrevious) ? "#7fb4ff" : "rgba(255, 255, 255, 0.3)"}
                    />
                    <Text style={[
                      styles.navigationButtonText,
                      (!canGoBack || isViewingPrevious) && styles.navigationButtonTextDisabled
                    ]}>
                      {t('common:buttons.back')}
                    </Text>
                  </TouchableOpacity>

                  {/* Animated circular timer for auto-advance - only after incorrect answer */}
                  {/* OR History icon when viewing previous card */}
                  {isViewingPrevious ? (
                    <View style={styles.historyIconContainer}>
                      <Ionicons
                        name="time-outline"
                        size={20}
                        color="rgba(255, 255, 255, 0.4)"
                      />
                    </View>
                  ) : (
                    <CircleCountdown
                      timeLeft={timeLeft}
                      totalDuration={TIMER_DISPLAY_DURATION} // Use 70% of actual delay for better sync
                      isActive={isCorrect === false && timeLeft > 0}
                    />
                  )}

                  <TouchableOpacity
                    onPress={handleNextCard}
                    style={[
                      styles.navigationButton,
                      (!canGoForward && !isViewingPrevious) && styles.navigationButtonDisabled
                    ]}
                    disabled={!canGoForward && !isViewingPrevious}
                  >
                    <Text style={[
                      styles.navigationButtonText,
                      (!canGoForward && !isViewingPrevious) && styles.navigationButtonTextDisabled
                    ]}>
                      {t('common:buttons.next')}
                    </Text>
                    <Ionicons
                      name="chevron-forward"
                      size={20}
                      color={(canGoForward || isViewingPrevious) ? "#7fb4ff" : "rgba(255, 255, 255, 0.3)"}
                    />
                  </TouchableOpacity>
                </View>

                {/* Feedback section - only after answer */}
                {isCorrect !== null && (
                  <Animated.View
                    style={[
                      styles.feedbackSection,
                      {
                        opacity: feedbackAnim,
                        transform: [
                          {
                            translateY: feedbackAnim.interpolate({
                              inputRange: [0, 1],
                              outputRange: [10, 0]
                            })
                          }
                        ]
                      }
                    ]}
                  >
                    {/* Feedback message - always in same position */}
                    <View style={[
                      styles.feedbackMessageContainer,
                      isCorrect ? styles.feedbackCorrect : styles.feedbackIncorrect
                    ]}>
                      <Ionicons
                        name={isCorrect ? "checkmark-circle" : "close-circle"}
                        size={20}
                        color={isCorrect ? "#4caf50" : "#f44336"}
                      />
                      <Text style={styles.feedbackMessageText}>
                        {isViewingPrevious
                          ? t('training:feedback.correct')
                          : (isCorrect ? t('training:feedback.correct') : t('training:feedback.incorrect'))
                        }
                      </Text>
                    </View>

                    {/* Correct answer (only when wrong) - positioned lower */}
                    {isCorrect === false && correctAnswer && !isViewingPrevious && (
                      <View style={styles.correctAnswerContainer}>
                        <Text style={styles.correctAnswerLabel}>
                          {t('training:feedback.correctAnswer')}:
                        </Text>
                        <Text style={styles.correctAnswerText}>{correctAnswer}</Text>
                      </View>
                    )}
                  </Animated.View>
                )}

              </View>
            </View>
            </ScrollView>
          </KeyboardAvoidingView>
        </SafeAreaView>
      </View>
    </SafeAreaProvider>
  );
}

// Word progress styles
const progressStyles = StyleSheet.create({
  wordProgressContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 10,
  },
  wordProgressStep: {
    width: 24,
    height: 4,
    borderRadius: 2,
    marginHorizontal: 2,
    backgroundColor: '#3a3a4a',
  },
});



// Main styles
const styles = StyleSheet.create({
  // Loading container style
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000000',
  },
  loadingText: {
    color: '#FFFFFF',
    fontSize: 16,
    marginTop: 16,
    textAlign: 'center',
  },
  // 🎨 СТИЛИ ЭКРАНА ЗАВЕРШЕНИЯ ТРЕНИРОВКИ
  // 📍 ЗДЕСЬ НАСТРАИВАЕТСЯ ПОЗИЦИОНИРОВАНИЕ КАРТОЧКИ ЗАВЕРШЕНИЯ
  completionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginTop: -120, // 📍 ПОДНИМАЕМ КАРТОЧКУ: используем marginTop (работает с отрицательными значениями)
  },
  completionCard: {
    padding: 30,
    paddingBottom: 40, // Больше отступа от кнопки до нижнего края карточки
    alignItems: 'center',
  },
  completionContent: {
    alignItems: 'center',
  },
  completionEmoji: {
    fontSize: 60,
    marginBottom: 20,
  },
  completionTitle: {
    fontSize: 24, // Уменьшили с 28 до 24
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 15,
    textAlign: 'center',
  },
  completionMessage: {
    fontSize: 18,
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 10,
    lineHeight: 24,
  },
  completionSubMessage: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 22,
  },
  // 🎨 СТИЛИ КНОПКИ ЗАВЕРШЕНИЯ ТРЕНИРОВКИ - такие же как на главном экране
  // 📍 ЗДЕСЬ НАСТРАИВАЕТСЯ ОТСТУП ОТ ТЕКСТА ДО КНОПКИ
  completionMainButton: {
    marginTop: 40, // 📍 ОТСТУП ОТ ТЕКСТА: было 20, стало 40 (добавили 20 пикселей)
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  completionButtonGradient: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  // 🎨 НОВЫЕ СТИЛИ - Компактная кнопка для экрана завершения
  completionModernButton: {
    marginTop: 40,
    borderRadius: 14, // 📍 УМЕНЬШИЛИ СКРУГЛЕНИЕ: было 20, стало 14
    // Такое же яркое пурпурное свечение как на главном экране
    shadowColor: '#8A2BE2',
    shadowOffset: { width: 0, height: 18 },
    shadowOpacity: 0.8,
    shadowRadius: 35,
    elevation: 25,
  },
  completionModernGradient: {
    paddingVertical: 14, // 📍 УМЕНЬШИЛИ ВЫСОТУ: было 20, стало 14
    paddingHorizontal: 24, // 📍 УМЕНЬШИЛИ ШИРИНУ: было 28, стало 24
    borderRadius: 14, // 📍 УМЕНЬШИЛИ СКРУГЛЕНИЕ: было 20, стало 14
    alignItems: 'center',
    justifyContent: 'center',
  },
  completionButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  completionButtonText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#fff',
    marginRight: 8,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  completionButtonIcon: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    marginLeft: 8,
    marginTop: 1,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },

  retryButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 25,
    paddingVertical: 12,
    borderRadius: 25,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  // Bottom container for progress and feedback
  bottomContainer: {
    marginTop: 20,
    marginBottom: 10,
    alignItems: 'center',
  },
  // Layout
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  safeArea: {
    flex: 1,
    paddingHorizontal: 8,
    paddingTop: 80,
    paddingBottom: 24,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    padding: 0,
  },
  content: {
    flex: 1,
    paddingHorizontal: 8,
    paddingTop: 16,
  },
  
  // Cards
  card: {
    borderRadius: 14,
    overflow: 'hidden',
    marginBottom: 16,
    marginTop: 10,
    backgroundColor: 'rgba(20, 20, 35, 0.25)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.08)',
    width: '100%',
  },
  cardContent: {
    position: 'relative',
    zIndex: 1,
    paddingTop: 44,
    paddingHorizontal: 32,
    paddingBottom: 24,
  },
  
  // Progress Header
  progressHeader: {
    width: '100%',
    paddingHorizontal: 20,
    marginTop: -50,  // Уменьшаем отступ сверху
    marginBottom: 10,
    backgroundColor: 'transparent',
  },
  progressContainer: {
    width: '100%',
  },
  progressText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 8,
    textAlign: 'left',
    fontWeight: '500',
    paddingLeft: 0,
  },

  progressBarContainer: {
    width: '100%',
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    borderRadius: 2,
  },
  progressGradient: {
    flex: 1,
    borderRadius: 6,
    height: '100%',
  },
  
  // Top right button container for "don't know" buttons
  topRightButtonContainer: {
    position: 'absolute',
    top: 16,
    right: 16,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  // Hint icon styles (back to bottom)
  hintIconContainer: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  hintIconButton: {
    padding: 8,
  },
  helpButtonsAtWordLevel: {
    position: 'absolute',
    right: -5, // Правее на 4 пикселя (было 4, стало 0)
    top: -5, // Ниже на 3 пикселя (было -8, стало -5)
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    zIndex: 10,
  },
  hintIconAtProgressLevel: {
    position: 'absolute',
    right: -8, // Выравниваем с кнопками помощи
    top: -6, // Поднимаем еще выше
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    zIndex: 10,
  },
  helpTextButton: {
    padding: 8,
  },
  helpTextButtonWithBorder: {
    paddingHorizontal: 16, // Шире
    paddingVertical: 4, // Меньше по высоте
    borderRadius: 14, // Скругленные углы
    borderWidth: 1, // Тонкая обводка
    borderColor: 'rgba(255, 255, 255, 0.3)', // Полупрозрачная обводка
    backgroundColor: 'transparent', // Без фона
  },
  hintIconButtonLarge: {
    padding: 20, // Еще больше увеличиваем область нажатия
    margin: -12, // Компенсируем увеличенный padding
  },
  dontKnowText: {
    color: '#FFC107',
    fontSize: 12, // Меньше шрифт
    fontWeight: 'normal',
  },
  dontRememberText: {
    color: '#808080',
    fontSize: 12, // Меньше шрифт
    fontWeight: 'normal',
  },
  
  // Hint styles
  hintContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    padding: 12,
    borderRadius: 8,
    marginTop: 16,
  },
  hintText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 16,
    fontFamily: 'monospace',
    letterSpacing: 2,
    textAlign: 'center',
  },
  hintLetter: {
    color: '#7fb4ff',
    fontWeight: 'bold',
    fontSize: 18,
  },
  
  // Sentence and input
  sentenceContainer: {
    marginTop: 4,
    marginBottom: 0,
    paddingBottom: 8,
    position: 'relative', // Для абсолютного позиционирования кнопок помощи
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'center',
    height: 32,
    position: 'relative',
    verticalAlign: 'bottom',
    marginHorizontal: 13,
    display: 'flex' as 'flex',
  },
  sentence: {
    fontSize: 22,
    color: '#e6e4ff',
    lineHeight: 34,
    textAlign: 'left',
    marginBottom: 12,
    marginTop: 4,
    textAlignVertical: 'center',
    fontWeight: '400',
    paddingHorizontal: 2,
  },
  inlineInput: {
    color: '#f0f5ff',
    fontSize: 22,
    lineHeight: 28,
    height: 36,
    padding: 0,
    paddingHorizontal: 8,  // Отступы по бокам
    margin: 0,
    marginBottom: -12,
    marginHorizontal: 4,   // Внешние отступы
    textAlign: 'center',
    minWidth: 50,          // Минимальная ширина
    maxWidth: 280,         // Максимальная ширина с запасом
    letterSpacing: 0.3,    // Небольшой кернинг для лучшей читаемости
    backgroundColor: 'rgba(200, 210, 230, 0.08)',
    borderBottomWidth: 2,
    borderBottomColor: 'rgba(127, 180, 255, 0.6)',
    borderWidth: 0,
    borderRadius: 6,
    textAlignVertical: 'center',
    includeFontPadding: false,
  },
  inlineInputFocused: {
    backgroundColor: 'rgba(180, 200, 240, 0.15)',
    borderBottomColor: '#7fb4ff',
    borderBottomWidth: 2.5,
  },
  inlineInputCorrect: {
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    borderBottomColor: '#4caf50',
  },
  inlineInputIncorrect: {
    backgroundColor: 'rgba(244, 67, 54, 0.1)',
    borderBottomColor: '#f44336',
  },
  highlightedWord: {
    color: '#7fb4ff',
    fontWeight: '500',
    backgroundColor: 'rgba(127, 180, 255, 0.12)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    fontStyle: 'normal',
    textDecorationLine: 'underline',
    textDecorationStyle: 'dotted',
    textDecorationColor: 'rgba(127, 180, 255, 0.7)',
  },
  
  // Grammar hint styles
  grammarHintContainer: {
    marginBottom: 8,
    paddingHorizontal: 6,
    borderLeftWidth: 2,
    borderLeftColor: 'rgba(127, 180, 255, 0.3)',
    paddingLeft: 8,
    alignSelf: 'flex-start',
    width: '100%',
    position: 'relative', // Для абсолютного позиционирования кнопок помощи
  },
  grammarHintText: {
    color: 'rgba(160, 180, 220, 0.6)',
    fontSize: 11,
    lineHeight: 15,
  },
  wordWithHint: {
    fontWeight: '500',
    color: 'rgba(200, 210, 240, 0.9)',
  },
  wordHintSeparator: {
    color: 'rgba(160, 180, 220, 0.4)',
  },
  wordInfoText: {
    color: 'rgba(200, 210, 240, 0.7)',
    fontSize: 14,
  },
  
  // Word progress indicator
  wordProgressContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    marginTop: 35,
    marginBottom: 8,
    marginLeft: 2,
    paddingHorizontal: 0,
    position: 'relative', // Для абсолютного позиционирования кнопки подсказки
  },
  wordProgressStep: {
    width: 24,
    height: 4,
    borderRadius: 2,
    marginHorizontal: 2,
    backgroundColor: '#3a3a4a',
  },

  // Interval debug info
  intervalDebugContainer: {
    marginTop: 4,
    marginBottom: 10,
    marginLeft: 2,
  },
  intervalDebugText: {
    fontSize: 12,
    color: 'rgba(160, 160, 160, 0.7)',
    fontFamily: 'monospace',
  },
  
  // Translation
  translationContainer: {
    marginTop: 0,
    padding: 0,
    paddingHorizontal: 0,
    backgroundColor: 'transparent',
    alignSelf: 'flex-start',
    width: '100%',
  },
  translation: {
    position: 'relative',
    fontSize: 16,
    color: 'rgba(220, 220, 240, 0.8)',
    fontStyle: 'normal',
    textAlign: 'left',
    lineHeight: 24,
    paddingTop: 4,
    paddingBottom: 8,
    paddingHorizontal: 2,
    width: '100%',
    fontWeight: '300',
  },
  
  // Divider
  dividerContainer: {
    width: '40%',
    marginTop: 8,
    marginBottom: 4,
    alignSelf: 'center',
    height: 2,
  },
  dividerLine: {
    width: '100%',
    height: 1,
    left: 0,
    right: 0,
  },
  dividerTop: {
    top: 0,
    backgroundColor: 'rgba(200, 200, 200, 0.08)',
    shadowColor: 'rgba(0, 0, 0, 0.2)',
    shadowOffset: { width: 0, height: 1 },
    shadowRadius: 1,
    elevation: 1,
  },
  dividerBottom: {
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  
  // Feedback
  feedbackContainer: {
    marginTop: 8,
    padding: 14,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
    alignSelf: 'stretch',
    marginHorizontal: 16,
  },

  feedbackText: {
    color: '#fff',
    marginLeft: 10,
    fontSize: 15,
    fontWeight: '500',
  },
  
  // Next button
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(127, 180, 255, 0.2)',
    padding: 16,
    borderRadius: 12,
    marginTop: 16,
  },
  nextButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },



  // Navigation buttons
  navigationButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 16,
    paddingHorizontal: 20,
    width: '100%',
  },
  navigationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    backgroundColor: 'rgba(127, 180, 255, 0.15)',
    borderWidth: 1,
    borderColor: 'rgba(127, 180, 255, 0.3)',
  },
  navigationButtonDisabled: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  navigationButtonText: {
    color: '#7fb4ff',
    fontSize: 14,
    fontWeight: '500',
    marginHorizontal: 4,
  },
  navigationButtonTextDisabled: {
    color: 'rgba(255, 255, 255, 0.3)',
  },

  // History icon styles (replaces timer when viewing previous card)
  historyIconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 40,
    height: 40,
  },

  // Feedback row (50/50 split)
  feedbackRow: {
    flexDirection: 'row',
    marginTop: 16,
    width: '100%',
    paddingHorizontal: 20,
  },
  feedbackLeft: {
    flex: 1,
    marginRight: 8,
  },
  feedbackRight: {
    flex: 1,
    marginLeft: 8,
  },
  feedbackMessageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    height: 50, // Фиксированная высота для одинакового позиционирования
    marginBottom: 12,
  },
  feedbackMessageText: {
    color: 'white',
    fontSize: 16,
    marginLeft: 8,
    fontWeight: '500',
  },

  // Feedback section (vertical layout)
  feedbackSection: {
    marginTop: 50, // Опускаем еще на 20px (было 70, стало 90)
    width: '100%',
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  feedbackCorrect: {
    backgroundColor: 'rgba(76, 175, 80, 0.15)',
  },
  feedbackIncorrect: {
    backgroundColor: 'rgba(244, 67, 54, 0.15)',
  },

  // Correct answer display
  correctAnswerContainer: {
    marginTop: 30, // Увеличиваем отступ на 50px (было 8, стало 58)
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(76, 175, 80, 0.3)',
    alignItems: 'center',
    width: '100%',
  },
  correctAnswerLabel: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 14,
    marginBottom: 6,
  },
  correctAnswerText: {
    color: '#4CAF50',
    fontSize: 18,
    fontWeight: '600',
  },


});
