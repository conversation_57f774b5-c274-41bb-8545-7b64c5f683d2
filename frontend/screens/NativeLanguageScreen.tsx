import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { useAuth } from '../contexts/AuthContext';
import { useNotification } from '../hooks/useNotification';
import { useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import { CustomNotification } from '../components/CustomNotification';
import { colors } from '../theme/colors';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../App';
import { NATIVE_LANGUAGE_OPTIONS } from '../constants/languages';
import { SafeAreaWrapper, SafeAreaWrapperPresets } from '../components/SafeAreaWrapper';

// Импортируем хуки для переводов
import { useAppTranslation, useAutoLanguageSwitch } from '../src/i18n/hooks';

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

const NativeLanguageScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const { user, updateUser } = useAuth();
  const { notification, showSuccess, showError, hideNotification } = useNotification();
  const { t } = useAppTranslation('onboarding');
  const { switchToNativeLanguage, isLanguageSupported } = useAutoLanguageSwitch();

  const [selectedLanguage, setSelectedLanguage] = useState<string>(user?.native_language || 'en');

  const handleLanguageSelect = (languageCode: string) => {
    setSelectedLanguage(languageCode);
  };

  const handleContinue = async () => {
    if (!selectedLanguage) {
      showError('Пожалуйста, выберите родной язык');
      return;
    }

    try {
      const updates = {
        native_language: selectedLanguage
      };

      await updateUser(updates);

      // Автоматически переключаем язык приложения на родной язык пользователя
      const languageChanged = await switchToNativeLanguage(selectedLanguage);

      if (languageChanged) {
        console.log('App language switched to:', selectedLanguage);
      } else {
        console.log('App language remains English (language not supported)');
      }

      // Переходим к выбору целевого языка (без уведомления в онбординге)
      navigation.navigate('AddLanguage');

    } catch (error) {
      console.error('Ошибка при сохранении родного языка:', error);
      showError('Ошибка при сохранении языка');
    }
  };

  return (
    <SafeAreaWrapper {...SafeAreaWrapperPresets.form} backgroundColor="#1a1a2e">
      <View style={styles.container}>
        <LinearGradient
          colors={['#1a1a2e', '#16213e', '#0f3460']}
          style={StyleSheet.absoluteFillObject}
        />

        <View style={styles.card}>
        <Text style={styles.title}>{t('nativeLanguage.title')}</Text>
        <Text style={styles.subtitle}>{t('nativeLanguage.subtitle')}</Text>
        <Text style={styles.note}>{t('nativeLanguage.appLanguageNote')}</Text>
        
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {NATIVE_LANGUAGE_OPTIONS.map((language) => {
            const isSupported = isLanguageSupported(language.value);
            return (
              <TouchableOpacity
                key={language.value}
                onPress={() => handleLanguageSelect(language.value)}
                style={[
                  styles.languageItem,
                  selectedLanguage === language.value && styles.selectedLanguageItem,
                  !isSupported && styles.unsupportedLanguageItem
                ]}
                activeOpacity={0.7}
              >
                <View style={styles.languageContent}>
                  <Text style={styles.languageFlag}>{language.flag}</Text>
                  <View style={styles.languageTextContainer}>
                    <Text style={[
                      styles.languageName,
                      selectedLanguage === language.value && styles.selectedLanguageName,
                      !isSupported && styles.unsupportedLanguageName
                    ]}>
                      {language.label}
                    </Text>
                    {isSupported && (
                      <Text style={styles.supportedBadge}>App supported</Text>
                    )}
                  </View>
                </View>
                {selectedLanguage === language.value && (
                  <View style={styles.checkmark}>
                    <Text style={styles.checkmarkText}>✓</Text>
                  </View>
                )}
              </TouchableOpacity>
            );
          })}
        </ScrollView>
        
        <TouchableOpacity
          style={[
            styles.continueButton,
            !selectedLanguage && styles.continueButtonDisabled
          ]}
          onPress={handleContinue}
          activeOpacity={0.8}
          disabled={!selectedLanguage}
        >
          <Text style={[
            styles.continueButtonText,
            !selectedLanguage && styles.continueButtonTextDisabled
          ]}>
            {t('common:buttons.continue')}
          </Text>
        </TouchableOpacity>
        </View>

        <CustomNotification
          message={notification.message}
          visible={notification.visible}
          type={notification.type}
          onHide={hideNotification}
          duration={1500}
        />
      </View>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a2e',
  },
  card: {
    flex: 1,
    padding: 20,
    backgroundColor: 'rgba(20, 20, 35, 0.85)',
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
    margin: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: colors.text.primary,
    textAlign: 'center',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: 10,
  },
  note: {
    fontSize: 13,
    color: colors.text.tertiary,
    textAlign: 'center',
    marginBottom: 30,
    fontStyle: 'italic',
    lineHeight: 18,
  },
  scrollView: {
    flex: 1,
  },
  languageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 20,
    marginVertical: 4,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.08)',
  },
  selectedLanguageItem: {
    backgroundColor: 'rgba(120, 200, 255, 0.15)',
    borderColor: 'rgba(120, 200, 255, 0.3)',
  },
  unsupportedLanguageItem: {
    opacity: 0.7,
  },
  languageContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  languageFlag: {
    fontSize: 24,
    marginRight: 12,
  },
  languageTextContainer: {
    flex: 1,
  },
  languageName: {
    fontSize: 18,
    fontWeight: '500',
    color: colors.text.secondary,
  },
  selectedLanguageName: {
    color: colors.text.primary,
    fontWeight: '600',
  },
  unsupportedLanguageName: {
    color: colors.text.tertiary,
  },
  supportedBadge: {
    fontSize: 11,
    color: 'rgba(120, 200, 255, 0.8)',
    fontWeight: '500',
    marginTop: 2,
  },
  checkmark: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: 'rgba(120, 200, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkmarkText: {
    color: colors.text.primary,
    fontSize: 12,
    fontWeight: 'bold',
  },
  continueButton: {
    marginTop: 20,
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    backgroundColor: 'rgba(120, 200, 255, 0.8)',
    alignItems: 'center',
  },
  continueButtonDisabled: {
    backgroundColor: 'rgba(120, 120, 120, 0.3)',
  },
  continueButtonText: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
  },
  continueButtonTextDisabled: {
    color: colors.text.tertiary,
  },
});

export default NativeLanguageScreen;
