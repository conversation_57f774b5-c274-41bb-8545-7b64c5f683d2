import React, { useState, useEffect, useLayoutEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../App';
import { LinearGradient } from 'expo-linear-gradient';

import { CustomNotification } from '../components/CustomNotification';
import { useAuth } from '../contexts/AuthContext';
import { useNotification } from '../hooks/useNotification';
import { colors } from '../theme/colors';
import { ALL_LANGUAGES, LanguageOption } from '../constants/languages';
import { SafeAreaWrapper, SafeAreaWrapperPresets } from '../components/SafeAreaWrapper';

// Импортируем хуки для переводов
import { useAppTranslation } from '../src/i18n/hooks';

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;



const LANGUAGE_LEVELS = [
  { value: 'A0', label: 'A0 - Начальный', description: 'ТОП-100 самых важных базовых слов' },
  { value: 'A1', label: 'A1 - Элементарный' },
  { value: 'A2', label: 'A2 - Предпороговый' },
  { value: 'B1', label: 'B1 - Пороговый' },
  { value: 'B2', label: 'B2 - Пороговый продвинутый' },
  { value: 'C1', label: 'C1 - Уровень профессионального владения' },
  { value: 'C2', label: 'C2 - Уровень владения в совершенстве' },
];

const AddLanguageScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const { user, updateUser, needsOnboarding } = useAuth();
  const { notification, showSuccess, showError, hideNotification } = useNotification();
  const { t } = useAppTranslation('common');
  
  const [selectedLanguage, setSelectedLanguage] = useState<string | null>(null);
  const [selectedLevel, setSelectedLevel] = useState<string>('A0');
  const [step, setStep] = useState<'language' | 'level'>('language');
  const [wasOnboarding, setWasOnboarding] = useState<boolean>(needsOnboarding()); // Инициализируем правильно
  const [isCompleting, setIsCompleting] = useState<boolean>(false);

  // Функция для обработки кнопки "Назад"
  const handleBack = () => {
    if (step === 'level') {
      setStep('language');
      setSelectedLanguage(null);
    } else {
      // Если онбординг завершен, не пытаемся вернуться назад
      if (!needsOnboarding() && wasOnboarding) {
        console.log('Онбординг завершен, не возвращаемся назад');
        return;
      }

      // Проверяем, можем ли мы вернуться назад
      try {
        if (navigation.canGoBack()) {
          navigation.goBack();
        } else {
          console.log('Cannot go back, navigation stack changed');
        }
      } catch (error) {
        console.log('Error going back:', error);
      }
    }
  };

  // Настройка хедера со стандартной кнопкой "Назад"
  useLayoutEffect(() => {
    navigation.setOptions({
      headerShown: true,
      headerTitle: step === 'language' ? t('navigation.chooseLanguage') : t('chooseLevel'),
      headerStyle: {
        backgroundColor: 'rgba(20, 20, 35, 0.95)',
      },
      headerTintColor: colors.text.primary,
      headerTitleStyle: {
        fontWeight: '600',
        fontSize: 18,
      },
      headerBackTitle: 'Назад',
    });
  }, [navigation, step]);

  // Обработчик для стандартной кнопки "Назад"
  useLayoutEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', (e) => {
      // Если это системное действие (свайп назад или кнопка), обрабатываем
      if (e.data.action.type === 'GO_BACK') {
        e.preventDefault();
        handleBack();
      }
    });

    return unsubscribe;
  }, [navigation, handleBack]);

  // Отслеживаем завершение онбординга
  useEffect(() => {
    const isOnboarding = needsOnboarding();

    console.log('useEffect onboarding check:', {
      wasOnboarding,
      isOnboarding,
      isCompleting,
      shouldNavigate: wasOnboarding && !isOnboarding && !isCompleting
    });

    // Если был онбординг, а теперь его нет - онбординг завершен
    if (wasOnboarding && !isOnboarding && !isCompleting) {
      console.log('Онбординг завершен, принудительно переходим на главный экран');
      // Принудительно переходим на главный экран
      setTimeout(() => {
        try {
          navigation.reset({
            index: 0,
            routes: [{ name: 'Home' }],
          });
        } catch (error) {
          console.log('Ошибка при переходе на главный экран:', error);
        }
      }, 500);
    }

    setWasOnboarding(isOnboarding);
  }, [needsOnboarding(), wasOnboarding, isCompleting, navigation]);

  // Фильтруем языки, исключая родной язык и уже изучаемые
  const availableLanguages = ALL_LANGUAGES.filter(lang => {
    // Исключаем родной язык
    if (lang.code === user?.settings?.native_language) {
      return false;
    }

    // Исключаем уже изучаемые языки
    return !(user?.learning_languages || []).includes(lang.code);
  });

  const handleLanguageSelect = (languageCode: string) => {
    console.log('Выбран язык:', languageCode, 'переходим к выбору уровня');
    setSelectedLanguage(languageCode);
    setStep('level');
  };

  const handleLevelSelect = (level: string) => {
    setSelectedLevel(level);
  };

  const handleAddLanguage = async () => {
    if (!selectedLanguage || !user || isCompleting) return;

    try {
      setIsCompleting(true);
      console.log('Начинаем добавление языка:', selectedLanguage, 'уровень:', selectedLevel);
      const currentLearningLanguages = user.learning_languages || [];
      const currentLanguageLevels = user.settings?.language_levels || {};

      // Добавляем выбранный язык к существующим (или создаем первый)
      const learningLanguages = [...currentLearningLanguages, selectedLanguage];

      const updates = {
        learning_languages: learningLanguages,
        settings: {
          ...user.settings,
          language_levels: {
            ...currentLanguageLevels,
            [selectedLanguage]: selectedLevel
          },
          target_language: selectedLanguage, // Устанавливаем как текущий язык сессии
          // Если это онбординг, отмечаем его как завершенный
          ...(needsOnboarding() ? { onboarding_completed: true } : {})
        }
      };

      console.log('Отправляем обновления:', updates);
      await updateUser(updates);
      console.log('Обновления успешно отправлены');

      const selectedLang = ALL_LANGUAGES.find(lang => lang.code === selectedLanguage);

      // ВАЖНО: Проверяем isOnboarding ДО отправки обновлений
      // После updateUser() needsOnboarding() уже вернет false
      if (wasOnboarding) {
        // После завершения онбординга принудительно переходим на главный экран
        console.log('Онбординг завершен, принудительно переходим на главный экран');
        setTimeout(() => {
          try {
            setIsCompleting(false);
            navigation.reset({
              index: 0,
              routes: [{ name: 'Home' }],
            });
          } catch (error) {
            console.log('Ошибка при переходе на главный экран:', error);
            setIsCompleting(false);
          }
        }, 1000);
      } else {
        // Для обычного добавления языка возвращаемся в профиль
        setIsCompleting(false);
        showSuccess(`Язык ${selectedLang?.name || selectedLanguage} успешно добавлен`);

        // Очищаем стек навигации и возвращаемся в профиль, сохраняя Home в стеке
        setTimeout(() => {
          navigation.reset({
            index: 1,
            routes: [
              { name: 'Home' },
              { name: 'Profile' }
            ],
          });
        }, 1500);
      }

    } catch (error) {
      console.error('Ошибка при добавлении языка:', error);
      showError('Ошибка при добавлении языка');
      setIsCompleting(false);
    }
  };

  const renderLanguageSelection = () => (
    <>
      <Text style={styles.title}>{t('chooseLanguageToLearn')}</Text>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {availableLanguages.length > 0 ? (
          availableLanguages.map((language) => (
            <TouchableOpacity
              key={language.code}
              onPress={() => handleLanguageSelect(language.code)}
              style={styles.languageItem}
              activeOpacity={0.7}
            >
              <View style={styles.languageContent}>
                <Text style={styles.languageFlag}>{language.flag}</Text>
                <Text style={styles.languageName}>{language.name}</Text>
              </View>
              <View style={styles.arrow}>
                <Text style={styles.arrowText}>→</Text>
              </View>
            </TouchableOpacity>
          ))
        ) : (
          <View style={styles.emptyState}>
            <Text style={styles.emptyText}>
              Все доступные языки уже добавлены! 🎉
            </Text>
          </View>
        )}
      </ScrollView>
    </>
  );

  const renderLevelSelection = () => {
    const selectedLang = ALL_LANGUAGES.find(lang => lang.code === selectedLanguage);
    
    return (
      <>
        <View style={styles.headerWithLanguage}>
          <Text style={styles.selectedLanguageFlag}>{selectedLang?.flag}</Text>
          <Text style={styles.title}>{selectedLang?.name}</Text>
        </View>
        <Text style={styles.subtitle}>{t('chooseCurrentLevel')}</Text>
        
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {LANGUAGE_LEVELS.map((level) => (
            <TouchableOpacity
              key={level.value}
              onPress={() => handleLevelSelect(level.value)}
              style={[
                styles.levelItem,
                selectedLevel === level.value && styles.selectedLevelItem
              ]}
              activeOpacity={0.7}
            >
              <View style={styles.levelContent}>
                <Text style={[
                  styles.levelText,
                  selectedLevel === level.value && styles.selectedLevelText
                ]}>
                  {level.label}
                </Text>
                {level.description && (
                  <Text style={[
                    styles.levelDescription,
                    selectedLevel === level.value && styles.selectedLevelDescription
                  ]}>
                    {level.description}
                  </Text>
                )}
              </View>
              {selectedLevel === level.value && (
                <View style={styles.checkmark}>
                  <Text style={styles.checkmarkText}>✓</Text>
                </View>
              )}
            </TouchableOpacity>
          ))}
        </ScrollView>
        
        <TouchableOpacity
          style={[
            styles.addButton,
            isCompleting && styles.addButtonDisabled
          ]}
          onPress={handleAddLanguage}
          activeOpacity={0.8}
          disabled={isCompleting}
        >
          <Text style={[
            styles.addButtonText,
            isCompleting && styles.addButtonTextDisabled
          ]}>
            {isCompleting
              ? t('buttons.loading')
              : (needsOnboarding() ? t('startLearning') : t('navigation.addLanguage'))
            }
          </Text>
        </TouchableOpacity>
      </>
    );
  };

  console.log('AddLanguageScreen render:', {
    step,
    selectedLanguage,
    selectedLevel,
    isCompleting,
    needsOnboarding: needsOnboarding()
  });

  return (
    <SafeAreaWrapper {...SafeAreaWrapperPresets.form} backgroundColor="#1a1a2e">
      <View style={styles.container}>
        <LinearGradient
          colors={['#1a1a2e', '#16213e', '#0f3460']}
          style={StyleSheet.absoluteFillObject}
        />

        <View style={styles.card}>
          {step === 'language' ? renderLanguageSelection() : renderLevelSelection()}
        </View>

        <CustomNotification
          message={notification.message}
          visible={notification.visible}
          type={notification.type}
          onHide={hideNotification}
          duration={1500}
        />
      </View>
    </SafeAreaWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a2e',
  },
  card: {
    flex: 1,
    padding: 20,
    backgroundColor: 'rgba(20, 20, 35, 0.85)',
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
    margin: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: colors.text.primary,
    textAlign: 'center',
    marginBottom: 20,
    marginTop: 4, // Опускаем заголовок для лучшего выравнивания с флагом
  },
  subtitle: {
    fontSize: 16,
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: 20,
  },
  scrollView: {
    flex: 1,
  },
  languageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 20,
    marginVertical: 4,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.08)',
  },
  languageContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  languageFlag: {
    fontSize: 24,
    marginRight: 12,
  },
  languageName: {
    fontSize: 18,
    fontWeight: '500',
    color: colors.text.secondary,
  },
  arrow: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  arrowText: {
    fontSize: 18,
    color: colors.text.tertiary,
  },
  headerWithLanguage: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 10,
  },
  selectedLanguageFlag: {
    fontSize: 32,
    marginRight: 12,
  },
  levelItem: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    marginVertical: 4,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.08)',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  selectedLevelItem: {
    backgroundColor: 'rgba(120, 200, 255, 0.15)',
    borderColor: 'rgba(120, 200, 255, 0.3)',
  },
  levelText: {
    fontSize: 16,
    color: colors.text.secondary,
    flex: 1,
  },
  selectedLevelText: {
    color: colors.text.primary,
    fontWeight: '500',
  },
  levelContent: {
    flex: 1,
  },
  levelDescription: {
    fontSize: 14,
    color: colors.text.tertiary,
    marginTop: 4,
    lineHeight: 18,
  },
  selectedLevelDescription: {
    color: colors.text.secondary,
  },
  checkmark: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: 'rgba(120, 200, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkmarkText: {
    color: colors.text.primary,
    fontSize: 12,
    fontWeight: 'bold',
  },
  addButton: {
    marginTop: 20,
    marginBottom: 40, // Увеличиваем отступ снизу для современных телефонов
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    backgroundColor: 'rgba(120, 200, 255, 0.8)',
    alignItems: 'center',
  },
  addButtonDisabled: {
    backgroundColor: 'rgba(120, 120, 120, 0.4)',
  },
  addButtonText: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
  },
  addButtonTextDisabled: {
    color: colors.text.tertiary,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 18,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
  },

});

export default AddLanguageScreen;
