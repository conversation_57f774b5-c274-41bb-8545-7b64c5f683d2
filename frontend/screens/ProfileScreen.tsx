import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal, TextInput, ScrollView } from 'react-native';
import { useAuth, User } from '../contexts/AuthContext';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from 'react-native-paper';

// Импортируем новые компоненты дизайна
import { AnimatedGradientBackground, backgroundThemes } from '../components/AnimatedGradientBackground';
import { GlassmorphismCard, glassmorphismPresets } from '../components/GlassmorphismCard';
import { GlassButton } from '../components/GlassButton';
import { LanguagePicker } from '../components/LanguagePicker';
import { CustomSlider } from '../components/CustomSlider';
import { SectionHeader } from '../components/SectionHeader';
import { CustomNotification } from '../components/CustomNotification';
import { useNotification } from '../hooks/useNotification';
import { LanguageLevelPicker } from '../components/LanguageLevelPicker';
import { LearningLanguagesModal } from '../components/LearningLanguagesModal';
import { SafeAreaWrapper, SafeAreaWrapperPresets } from '../components/SafeAreaWrapper';
import { colors } from '../theme/colors';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../App';
import { NATIVE_LANGUAGE_OPTIONS, getLanguageByCode } from '../constants/languages';

// Импортируем хуки для переводов
import { useAppTranslation, useLanguageSwitcher } from '../src/i18n/hooks';




type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

const ProfileScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const { user, logout, updateUser, refreshUser } = useAuth();
  const theme = useTheme();
  const { notification, showSuccess, showError, hideNotification } = useNotification();
  const { t } = useAppTranslation('profile');
  const { changeLanguage, getCurrentLanguage, getSupportedLanguages } = useLanguageSwitcher();

  const [editingField, setEditingField] = useState<string | null>(null);
  const [tempValue, setTempValue] = useState<string>('');
  const [dailyGoal, setDailyGoal] = useState<string>(user?.settings?.daily_goal?.toString() || '20');
  const [showLanguagePicker, setShowLanguagePicker] = useState<boolean>(false);
  const [showSlider, setShowSlider] = useState<boolean>(false);
  const [showLevelPicker, setShowLevelPicker] = useState<boolean>(false);
  const [showLearningLanguagesModal, setShowLearningLanguagesModal] = useState<boolean>(false);
  const [showAppLanguagePicker, setShowAppLanguagePicker] = useState<boolean>(false);

  // Обновляем данные пользователя при загрузке экрана
  useEffect(() => {
    refreshUser();
  }, []);

  if (!user) {
    return null;
  }

  const startEditing = (field: string, currentValue: string) => {
    setEditingField(field);
    setTempValue(currentValue);

    // Если это языковое поле, показываем кастомный пикер
    if (field === 'native_language' || field === 'target_language') {
      setShowLanguagePicker(true);
    }
    // Если это цель на день, показываем слайдер
    else if (field === 'daily_goal') {
      setShowSlider(true);
    }
    // Если это уровень языка, показываем пикер уровня
    else if (field === 'language_level') {
      setShowLevelPicker(true);
    }
  };

  const saveSetting = async () => {
    if (!editingField || !user?.settings) return;

    try {
      const updates: Partial<User> = {
        settings: {
          ...user.settings, // Сохраняем все существующие настройки
          [editingField]: editingField === 'daily_goal' ? parseInt(tempValue, 10) : tempValue
        }
      };

      await updateUser(updates);
      setEditingField(null);
    } catch (error) {
      console.error('Ошибка при сохранении настроек:', error);
      // Можно добавить уведомление об ошибке
    }
  };

  const handleLanguageSelect = async (value: string) => {
    if (!editingField || !user?.settings) return;

    try {
      const updates: Partial<User> = {
        settings: {
          ...user.settings, // Сохраняем все существующие настройки, включая onboarding_completed
          [editingField]: value
        }
      };

      await updateUser(updates);
      setEditingField(null);
      setShowLanguagePicker(false);
      showSuccess(t('common:success.updated'));
    } catch (error) {
      console.error('Ошибка при сохранении языка:', error);
      showError(t('common:errors.unknownError'));
    }
  };

  const handleLanguagePickerClose = () => {
    setShowLanguagePicker(false);
    setEditingField(null);
  };

  const handleSliderValueChange = async (value: number) => {
    if (!user?.settings) return;

    try {
      const updates: Partial<User> = {
        settings: {
          ...user.settings, // Сохраняем все существующие настройки
          daily_goal: value
        }
      };

      await updateUser(updates);
      setEditingField(null);
      setShowSlider(false);
      showSuccess(t('common:success.updated'));
    } catch (error) {
      console.error('Ошибка при сохранении цели:', error);
      showError(t('common:errors.unknownError'));
    }
  };

  const handleSliderClose = () => {
    setShowSlider(false);
    setEditingField(null);
  };

  const handleLevelSelect = async (value: string) => {
    if (!user?.settings || !editingField) return;

    try {
      // Извлекаем код языка из editingField (например, "language_level_en" -> "en")
      const languageCode = editingField.startsWith('language_level_')
        ? editingField.replace('language_level_', '')
        : user.settings.target_language || 'en';

      const currentLanguageLevels = user.settings.language_levels || {};

      const updates: Partial<User> = {
        settings: {
          ...user.settings,
          language_levels: {
            ...currentLanguageLevels,
            [languageCode]: value  // Обновляем уровень для конкретного языка
          }
        }
      };

      await updateUser(updates);
      setEditingField(null);
      setShowLevelPicker(false);
      showSuccess('Уровень языка обновлен');
    } catch (error) {
      console.error('Ошибка при сохранении уровня языка:', error);
      showError('Ошибка при сохранении уровня языка');
    }
  };

  const handleLevelPickerClose = () => {
    setShowLevelPicker(false);
    setEditingField(null);
  };

  const handleLearningLanguageLevelChange = async (languageCode: string, level: string) => {
    if (!user?.settings) return;

    try {
      const currentLanguageLevels = user.settings.language_levels || {};

      const updates: Partial<User> = {
        settings: {
          ...user.settings,
          language_levels: {
            ...currentLanguageLevels,
            [languageCode]: level
          }
        }
      };

      await updateUser(updates);
      showSuccess(t('common:success.updated'));
    } catch (error) {
      console.error('Ошибка при сохранении уровня языка:', error);
      showError(t('common:errors.unknownError'));
    }
  };

  const handleAddLanguageFromModal = () => {
    setShowLearningLanguagesModal(false);
    navigation.navigate('AddLanguage');
  };

  const handleRemoveLanguage = async (languageCode: string) => {
    if (!user?.learning_languages) return;

    // Не позволяем удалить последний язык
    if (user.learning_languages.length <= 1) {
      showError('Нельзя удалить последний изучаемый язык');
      return;
    }

    try {
      const updatedLearningLanguages = user.learning_languages.filter(lang => lang !== languageCode);
      const updatedLanguageLevels = { ...user.settings?.language_levels };
      delete updatedLanguageLevels[languageCode];

      // Если удаляемый язык был текущим языком сессии, выбираем первый из оставшихся
      let newTargetLanguage = user.settings?.target_language;
      if (newTargetLanguage === languageCode) {
        newTargetLanguage = updatedLearningLanguages[0]; // Выбираем первый из оставшихся языков
      }

      const updates: Partial<User> = {
        learning_languages: updatedLearningLanguages,
        settings: {
          ...user.settings,
          language_levels: updatedLanguageLevels,
          target_language: newTargetLanguage // Обновляем текущий язык сессии
        }
      };

      await updateUser(updates);
      showSuccess(t('common:success.updated'));
    } catch (error) {
      console.error('Ошибка при удалении языка:', error);
      showError(t('common:errors.unknownError'));
    }
  };

  // Обработчик для переключения языка приложения
  const handleAppLanguageSelect = async (languageCode: string) => {
    try {
      await changeLanguage(languageCode as any);
      setShowAppLanguagePicker(false);
      showSuccess(t('common:success.updated'));
    } catch (error) {
      console.error('Ошибка при смене языка приложения:', error);
      showError(t('common:errors.unknownError'));
    }
  };

  const handleAppLanguagePickerClose = () => {
    setShowAppLanguagePicker(false);
  };





  // Новый единообразный рендер для всех полей
  const renderUniformField = (label: string, content: React.ReactNode, onPress: () => void) => {
    return (
      <TouchableOpacity
        style={styles.uniformField}
        onPress={onPress}
        activeOpacity={0.7}
      >
        <Text style={styles.uniformLabel}>{label}:</Text>
        <View style={styles.uniformContent}>
          {content}
          <Text style={styles.editIcon}>✏️</Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderEditableField = (label: string, value: string, fieldName: string) => {
    const selectedLanguage = NATIVE_LANGUAGE_OPTIONS.find(lang => lang.value === value);

    return (
      <TouchableOpacity
        style={styles.infoRow}
        onPress={() => startEditing(fieldName, value)}
        activeOpacity={0.7}
      >
        <Text style={styles.label}>{label}:</Text>
        <View style={styles.valueContainer}>
          {selectedLanguage && (
            <Text style={styles.flag}>{selectedLanguage.flag}</Text>
          )}
          <Text style={styles.value}>
            {selectedLanguage?.label || value}
          </Text>
          <Text style={styles.editIcon}>✏️</Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderEditableNumber = (label: string, value: string, fieldName: string) => (
    <TouchableOpacity
      style={styles.infoRow}
      onPress={() => startEditing(fieldName, value)}
      activeOpacity={0.7}
    >
      <Text style={styles.label}>{label}:</Text>
      <View style={styles.valueContainer}>
        <Text style={styles.value}>{value} {label.includes('цель') ? 'карточек' : ''}</Text>
        <Text style={styles.editIcon}>✏️</Text>
      </View>
    </TouchableOpacity>
  );

  const renderEditableLevel = (label: string, value: string, fieldName: string) => {
    return (
      <TouchableOpacity
        style={styles.infoRow}
        onPress={() => startEditing(fieldName, value)}
        activeOpacity={0.7}
      >
        <Text style={styles.label}>{label}:</Text>
        <View style={styles.valueContainer}>
          <Text style={styles.value}>
            {value}
          </Text>
          <Text style={styles.editIcon}>✏️</Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderLanguagesSection = () => {
    const learningLanguages = user?.learning_languages || ['en'];
    const languageLevels = user?.settings?.language_levels || { en: 'A0' };



    return (
      <View style={styles.languagesSection}>
        <Text style={styles.languagesSectionTitle}>Изучаемые языки:</Text>
        {learningLanguages.map((langCode) => {
          const language = getLanguageByCode(langCode) || { flag: '🌐', name: langCode.toUpperCase() };
          const level = languageLevels[langCode] || 'A0';

          return (
            <TouchableOpacity
              key={langCode}
              style={styles.languageItem}
              onPress={() => {
                setTempValue(level);
                setEditingField(`language_level_${langCode}`);
                setShowLevelPicker(true);
              }}
              activeOpacity={0.7}
            >
              <View style={styles.languageContent}>
                <Text style={styles.languageFlag}>{language.flag}</Text>
                <Text style={styles.languageName}>{language.name}</Text>
                <Text style={styles.languageLevel}>- {level}</Text>
              </View>
              <Text style={styles.editIcon}>✏️</Text>
            </TouchableOpacity>
          );
        })}

        {/* Кнопка добавления нового языка */}
        <TouchableOpacity
          style={styles.addLanguageButton}
          onPress={() => navigation.navigate('AddLanguage')}
          activeOpacity={0.7}
        >
          <View style={styles.addLanguageContent}>
            <Text style={styles.addLanguageIcon}>➕</Text>
            <Text style={styles.addLanguageText}>{t('common:navigation.addLanguage')}</Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };



  const handleLogout = async () => {
    await logout();
    navigation.navigate('Home');
  };

  return (
    <View style={styles.container}>
      {/* Animated Gradient Background */}
      <AnimatedGradientBackground {...backgroundThemes.profile} />

      <SafeAreaWrapper {...SafeAreaWrapperPresets.profile}>
        <ScrollView style={styles.scrollView} contentContainerStyle={styles.content}>


        <GlassmorphismCard
          backgroundColor="rgba(20, 20, 35, 0.25)"
          borderColor="rgba(255, 255, 255, 0.08)"
          borderWidth={1}
          borderRadius={14}
          blurIntensity={20}
          withFogEffect={true}
          style={styles.section}
        >
          <SectionHeader
            title={t('sections.basicInfo')}
            colors={['#87CEFD', '#9370DB']} // Голубой к фиолетовому
          />
          <View style={styles.infoRow}>
            <Text style={styles.label}>{t('fields.name')}:</Text>
            <Text style={styles.value}>{user.username || t('placeholders.notSpecified')}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.label}>{t('fields.email')}:</Text>
            <Text style={styles.value}>{user.email}</Text>
          </View>
        </GlassmorphismCard>

        <GlassmorphismCard
          backgroundColor="rgba(20, 20, 35, 0.25)"
          borderColor="rgba(255, 255, 255, 0.08)"
          borderWidth={1}
          borderRadius={14}
          blurIntensity={20}
          withFogEffect={true}
          style={styles.infoSection}
        >
          <SectionHeader
            title={t('sections.settings')}
            colors={['#87CEFD', '#9370DB']} // Тот же градиент: голубой к фиолетовому
          />

          {/* Родной язык */}
          {renderUniformField(
            t('fields.nativeLanguage'),
            (() => {
              const selectedLanguage = NATIVE_LANGUAGE_OPTIONS.find(lang => lang.value === user.settings?.native_language);
              return (
                <View style={styles.uniformValueContainer}>
                  <Text style={styles.uniformFlag}>{selectedLanguage?.flag || '🌐'}</Text>
                </View>
              );
            })(),
            () => startEditing('native_language', user.settings?.native_language || '')
          )}

          {/* Изучаемые языки */}
          {renderUniformField(
            t('fields.learningLanguages'),
            (() => {
              const learningLanguages = user?.learning_languages || ['en'];
              return (
                <View style={styles.uniformValueContainer}>
                  {learningLanguages.slice(0, 3).map((langCode, index) => {
                    const language = getLanguageByCode(langCode);
                    return (
                      <Text key={langCode} style={styles.uniformFlag}>
                        {language?.flag || '🌐'}
                      </Text>
                    );
                  })}
                  {learningLanguages.length > 3 && (
                    <Text style={styles.moreLanguages}>+{learningLanguages.length - 3}</Text>
                  )}
                </View>
              );
            })(),
            () => setShowLearningLanguagesModal(true)
          )}

          {/* Цель карточек в день */}
          {renderUniformField(
            t('fields.dailyGoal'),
            <Text style={styles.uniformValue}>{user.settings?.daily_goal || 20}</Text>,
            () => startEditing('daily_goal', (user.settings?.daily_goal || 20).toString())
          )}
        </GlassmorphismCard>

        {/* Настройки приложения */}
        <GlassmorphismCard
          backgroundColor="rgba(20, 20, 35, 0.25)"
          borderColor="rgba(255, 255, 255, 0.08)"
          borderWidth={1}
          borderRadius={14}
          blurIntensity={20}
          withFogEffect={true}
          style={styles.infoSection}
        >
          <SectionHeader
            title={t('sections.appSettings')}
            colors={['#87CEFD', '#9370DB']}
          />

          {/* Язык приложения */}
          {renderUniformField(
            t('fields.appLanguage'),
            (() => {
              const currentLang = getCurrentLanguage();
              const supportedLangs = getSupportedLanguages();
              const currentLangInfo = supportedLangs.find(lang => lang.code === currentLang);
              return (
                <View style={styles.uniformValueContainer}>
                  <Text style={styles.uniformFlag}>
                    {currentLang === 'en' ? '🇺🇸' : currentLang === 'ru' ? '🇷🇺' : '🌐'}
                  </Text>
                  <Text style={styles.uniformValue}>{currentLangInfo?.name || currentLang}</Text>
                </View>
              );
            })(),
            () => setShowAppLanguagePicker(true)
          )}
        </GlassmorphismCard>

        <View style={styles.actions}>
          <GlassButton
            variant="danger"
            size="large"
            fullWidth
            onPress={handleLogout}
          >
            {t('actions.logout')}
          </GlassButton>
        </View>
        </ScrollView>
      </SafeAreaWrapper>

      {/* Кастомный языковой пикер */}
      {showLanguagePicker && (
        <LanguagePicker
          options={NATIVE_LANGUAGE_OPTIONS}
          selectedValue={tempValue}
          onSelect={handleLanguageSelect}
          onClose={handleLanguagePickerClose}
          title={`${t('actions.changeLanguage')} ${editingField?.includes('native') ? t('fields.nativeLanguage').toLowerCase() : 'изучаемый язык'}`}
        />
      )}

      {/* Пикер языка приложения */}
      {showAppLanguagePicker && (
        <LanguagePicker
          options={getSupportedLanguages().map(lang => ({
            value: lang.code,
            label: lang.name,
            flag: lang.code === 'en' ? '🇺🇸' : lang.code === 'ru' ? '🇷🇺' : '🌐'
          }))}
          selectedValue={getCurrentLanguage()}
          onSelect={handleAppLanguageSelect}
          onClose={handleAppLanguagePickerClose}
          title={t('actions.changeLanguage')}
        />
      )}

      {/* Кастомный слайдер для цели на день */}
      {showSlider && (
        <CustomSlider
          value={parseInt(tempValue, 10) || 20}
          onValueChange={handleSliderValueChange}
          onClose={handleSliderClose}
          title={t('fields.dailyGoal')}
          minValue={5}
          maxValue={100}
        />
      )}

      {/* Кастомный пикер уровня языка */}
      {showLevelPicker && (
        <LanguageLevelPicker
          selectedValue={tempValue}
          onSelect={handleLevelSelect}
          onClose={handleLevelPickerClose}
          title="Выберите уровень языка"
        />
      )}

      {/* Модальное окно изучаемых языков */}
      <LearningLanguagesModal
        visible={showLearningLanguagesModal}
        onClose={() => setShowLearningLanguagesModal(false)}
        learningLanguages={user?.learning_languages || []}
        languageLevels={user?.settings?.language_levels || {}}
        onLevelChange={handleLearningLanguageLevelChange}
        onAddLanguage={handleAddLanguageFromModal}
        onRemoveLanguage={handleRemoveLanguage}
      />

      {/* Кастомные уведомления */}
      <CustomNotification
        message={notification.message}
        visible={notification.visible}
        type={notification.type}
        onHide={hideNotification}
        duration={1000}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  actions: {
    marginTop: 20,
    alignItems: 'center',
  },
  container: {
    flex: 1,
    backgroundColor: '#000000', // Fallback цвет
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
    paddingBottom: 40, // Дополнительный отступ снизу
  },

  section: {
    marginBottom: 20,
  },
  infoSection: {
    marginBottom: 20,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
    paddingHorizontal: 10,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 8,
    marginVertical: 2,
  },
  valueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  flag: {
    fontSize: 18,
    marginRight: 8,
  },
  editIcon: {
    marginLeft: 10,
    opacity: 0.6,
  },
  label: {
    color: '#a0a0a0',
    fontSize: 16,
  },
  value: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  // Новые стили для единообразных полей
  uniformField: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
    paddingHorizontal: 10,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 8,
    marginVertical: 2,
  },
  uniformLabel: {
    color: '#a0a0a0',
    fontSize: 16,
  },
  uniformContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  uniformValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  uniformFlag: {
    fontSize: 18,
  },
  uniformValue: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  moreLanguages: {
    color: '#a0a0a0',
    fontSize: 14,
    marginLeft: 4,
  },
  button: {
    backgroundColor: '#4a80f5',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  languagesSection: {
    marginTop: 8,
  },
  languagesSectionTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text.secondary,
    marginBottom: 8,
  },
  languageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginVertical: 2,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.03)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.05)',
  },
  languageContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  languageFlag: {
    fontSize: 18,
    marginRight: 8,
  },
  languageName: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text.secondary,
    marginRight: 4,
  },
  languageLevel: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text.primary,
  },
  addLanguageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 12,
    marginTop: 8,
    borderRadius: 8,
    backgroundColor: 'rgba(120, 200, 255, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(120, 200, 255, 0.3)',
    borderStyle: 'dashed',
  },
  addLanguageContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  addLanguageIcon: {
    fontSize: 14,
    marginRight: 6,
  },
  addLanguageText: {
    fontSize: 13,
    fontWeight: '500',
    color: colors.text.secondary,
  },
});

export default ProfileScreen;
