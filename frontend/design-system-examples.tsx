// Примеры использования новой системы дизайна
// Этот файл содержит примеры того, как использовать вынесенные компоненты

import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, SafeAreaView } from 'react-native';
import { AnimatedGradientBackground, backgroundThemes } from './components/AnimatedGradientBackground';
import { GlassmorphismCard, glassmorphismPresets } from './components/GlassmorphismCard';
import { colors } from './theme/colors';
import { glassmorphismStyles } from './theme/glassmorphism';

// Пример 1: Экран с анимированным фоном и glassmorphism карточками
export function ExampleScreen() {
  return (
    <View style={styles.container}>
      {/* Анимированный фон */}
      <AnimatedGradientBackground {...backgroundThemes.profile} />
      
      <SafeAreaView style={styles.content}>
        <Text style={styles.title}>Пример экрана</Text>
        
        {/* Основная карточка */}
        <GlassmorphismCard {...glassmorphismPresets.profileCard}>
          <Text style={styles.cardTitle}>Основная информация</Text>
          <Text style={styles.cardText}>
            Это пример использования glassmorphism карточки с эффектом размытия и тумана.
          </Text>
        </GlassmorphismCard>
        
        {/* Минимальная карточка */}
        <GlassmorphismCard {...glassmorphismPresets.minimal}>
          <Text style={styles.cardTitle}>Минимальная карточка</Text>
          <Text style={styles.cardText}>
            Эта карточка использует минимальный стиль без эффекта тумана.
          </Text>
        </GlassmorphismCard>
        
        {/* Акцентная карточка */}
        <GlassmorphismCard {...glassmorphismPresets.accent}>
          <Text style={styles.cardTitle}>Акцентная карточка</Text>
          <Text style={styles.cardText}>
            Карточка с акцентным стилем и фиолетовым туманом.
          </Text>
        </GlassmorphismCard>
      </SafeAreaView>
    </View>
  );
}

// Пример 2: Кастомная карточка с собственными настройками
export function CustomGlassCard({ children, title }: { children: React.ReactNode; title: string }) {
  return (
    <GlassmorphismCard
      blurIntensity={15}
      backgroundColor="rgba(50, 20, 80, 0.3)"
      borderColor="rgba(170, 70, 255, 0.2)"
      borderWidth={1.5}
      borderRadius={18}
      withFogEffect={true}
      fogColor={[
        'rgba(255, 100, 200, 0.4)',
        'rgba(255, 100, 200, 0.25)',
        'rgba(255, 100, 200, 0.1)',
        'rgba(255, 100, 200, 0.02)',
        'rgba(255, 100, 200, 0)'
      ]}
      fogPosition={{ x: 100, y: 200 }}
      fogRadius={250}
    >
      <Text style={styles.customCardTitle}>{title}</Text>
      {children}
    </GlassmorphismCard>
  );
}

// Пример 3: Кнопка с glassmorphism эффектом
export function GlassButton({ 
  onPress, 
  children, 
  variant = 'default' 
}: { 
  onPress: () => void; 
  children: React.ReactNode;
  variant?: 'default' | 'accent' | 'minimal';
}) {
  const getPreset = () => {
    switch (variant) {
      case 'accent':
        return glassmorphismPresets.accent;
      case 'minimal':
        return glassmorphismPresets.minimal;
      default:
        return glassmorphismPresets.button;
    }
  };

  return (
    <TouchableOpacity onPress={onPress} activeOpacity={0.8}>
      <GlassmorphismCard {...getPreset()}>
        <View style={styles.buttonContent}>
          {children}
        </View>
      </GlassmorphismCard>
    </TouchableOpacity>
  );
}

// Пример 4: Экран с кастомным фоном
export function CustomBackgroundScreen() {
  const customSpheres = [
    {
      id: 'custom-1',
      cx: 100,
      cy: 200,
      r: 150,
      colors: ['rgba(255, 100, 100, 0.3)', 'rgba(255, 100, 100, 0)']
    },
    {
      id: 'custom-2',
      cx: 300,
      cy: 400,
      r: 200,
      colors: ['rgba(100, 255, 100, 0.25)', 'rgba(100, 255, 100, 0)']
    }
  ];

  return (
    <View style={styles.container}>
      <AnimatedGradientBackground 
        spheres={customSpheres}
        backgroundColor="#001122"
        overlayColor="rgba(0, 50, 100, 0.4)"
      />
      
      <SafeAreaView style={styles.content}>
        <Text style={styles.title}>Кастомный фон</Text>
        
        <GlassmorphismCard {...glassmorphismPresets.profileCard}>
          <Text style={styles.cardText}>
            Этот экран использует кастомную конфигурацию фона с собственными сферами и цветами.
          </Text>
        </GlassmorphismCard>
      </SafeAreaView>
    </View>
  );
}

// Пример 5: Использование цветов из темы
export function ThemedComponents() {
  return (
    <View style={styles.container}>
      <AnimatedGradientBackground {...backgroundThemes.training} />
      
      <SafeAreaView style={styles.content}>
        {/* Использование цветов из темы */}
        <View style={[
          glassmorphismStyles.container,
          { 
            backgroundColor: colors.glassmorphism.background.secondary,
            borderColor: colors.glassmorphism.border.accent,
            marginBottom: 20
          }
        ]}>
          <View style={glassmorphismStyles.content}>
            <Text style={[styles.cardTitle, { color: colors.text.primary }]}>
              Использование цветов темы
            </Text>
            <Text style={[styles.cardText, { color: colors.text.secondary }]}>
              Этот компонент использует цвета из системы тем.
            </Text>
          </View>
        </View>
        
        {/* Кнопки с разными вариантами */}
        <GlassButton onPress={() => console.log('Default button')}>
          <Text style={styles.buttonText}>Обычная кнопка</Text>
        </GlassButton>
        
        <GlassButton variant="accent" onPress={() => console.log('Accent button')}>
          <Text style={styles.buttonText}>Акцентная кнопка</Text>
        </GlassButton>
        
        <GlassButton variant="minimal" onPress={() => console.log('Minimal button')}>
          <Text style={styles.buttonText}>Минимальная кнопка</Text>
        </GlassButton>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: colors.text.primary,
    marginBottom: 30,
    textAlign: 'center',
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: 12,
  },
  cardText: {
    fontSize: 16,
    color: colors.text.secondary,
    lineHeight: 24,
  },
  customCardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#ff64c8',
    marginBottom: 12,
  },
  buttonContent: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
  },
});
