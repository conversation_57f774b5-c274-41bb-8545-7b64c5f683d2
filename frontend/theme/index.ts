// Реэкспорт всех тем
import { colors } from './colors';
import { typography } from './typography';
import { spacing, borderRadius, shadows, blurIntensity } from './spacing';

export const theme = {
  colors,
  typography,
  spacing,
  borderRadius,
  shadows,
  blurIntensity,
} as const;

export type Theme = typeof theme;

// Реэкспорт всех компонентов и типов
export * from './colors';
export * from './typography';
export * from './spacing';
