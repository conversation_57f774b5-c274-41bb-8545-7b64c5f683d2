import { StyleSheet, ViewStyle } from 'react-native';
import { colors, spacing, borderRadius, shadows, blurIntensity } from './index';

// Базовые стили для glassmorphism эффектов
export const glassmorphismStyles = StyleSheet.create({
  // Основные контейнеры
  container: {
    backgroundColor: colors.glassmorphism.background.primary,
    borderWidth: 1,
    borderColor: colors.glassmorphism.border.primary,
    borderRadius: borderRadius.glassmorphism.medium,
    overflow: 'hidden'
  },
  
  containerSecondary: {
    backgroundColor: colors.glassmorphism.background.secondary,
    borderWidth: 1,
    borderColor: colors.glassmorphism.border.secondary,
    borderRadius: borderRadius.glassmorphism.large,
    overflow: 'hidden'
  },
  
  containerMinimal: {
    backgroundColor: colors.glassmorphism.background.tertiary,
    borderWidth: 1,
    borderColor: colors.glassmorphism.border.tertiary,
    borderRadius: borderRadius.glassmorphism.small,
    overflow: 'hidden'
  },
  
  containerAccent: {
    backgroundColor: colors.glassmorphism.background.accent,
    borderWidth: 1.5,
    borderColor: colors.glassmorphism.border.accent,
    borderRadius: borderRadius.glassmorphism.xlarge,
    overflow: 'hidden'
  },
  
  // Контент внутри glassmorphism контейнеров
  content: {
    position: 'relative',
    zIndex: 1,
    paddingTop: spacing.card.paddingTop,
    paddingHorizontal: spacing.card.padding,
    paddingBottom: spacing.card.paddingBottom,
  },
  
  contentCompact: {
    position: 'relative',
    zIndex: 1,
    padding: spacing.lg,
  },
  
  contentMinimal: {
    position: 'relative',
    zIndex: 1,
    padding: spacing.md,
  },
  
  // Абсолютное позиционирование для фоновых эффектов
  absoluteFill: {
    ...StyleSheet.absoluteFillObject,
  },
  
  // Canvas контейнер
  canvasContainer: {
    flex: 1,
  }
});

// Функции для создания динамических стилей
export const createGlassmorphismStyle = (
  preset: 'primary' | 'secondary' | 'minimal' | 'accent' = 'primary',
  customProps?: {
    backgroundColor?: string;
    borderColor?: string;
    borderWidth?: number;
    borderRadius?: number;
    padding?: number;
  }
): ViewStyle => {
  const baseStyles = {
    primary: glassmorphismStyles.container,
    secondary: glassmorphismStyles.containerSecondary,
    minimal: glassmorphismStyles.containerMinimal,
    accent: glassmorphismStyles.containerAccent
  };
  
  return {
    ...baseStyles[preset],
    ...customProps
  };
};

// Предустановленные конфигурации для разных компонентов
export const glassmorphismPresets = {
  // Карточка тренировки
  trainingCard: {
    style: glassmorphismStyles.container,
    blurIntensity: blurIntensity.heavy,
    withFogEffect: true,
    fogColor: colors.glassmorphism.fog.blue,
    fogPosition: { x: -50, y: 450 },
    fogRadius: 350
  },
  
  // Карточка профиля
  profileCard: {
    style: glassmorphismStyles.containerSecondary,
    blurIntensity: blurIntensity.medium,
    withFogEffect: true,
    fogColor: colors.glassmorphism.fog.purple,
    fogPosition: { x: 100, y: 300 },
    fogRadius: 250
  },
  
  // Минимальная карточка
  minimalCard: {
    style: glassmorphismStyles.containerMinimal,
    blurIntensity: blurIntensity.light,
    withFogEffect: false
  },
  
  // Акцентная карточка
  accentCard: {
    style: glassmorphismStyles.containerAccent,
    blurIntensity: blurIntensity.extreme,
    withFogEffect: true,
    fogColor: colors.glassmorphism.fog.purple,
    fogPosition: { x: 0, y: 200 },
    fogRadius: 300
  },
  
  // Модальное окно
  modal: {
    style: {
      ...glassmorphismStyles.containerSecondary,
      borderRadius: borderRadius.xl,
      margin: spacing.lg
    },
    blurIntensity: blurIntensity.heavy,
    withFogEffect: true,
    fogColor: colors.glassmorphism.fog.green,
    fogPosition: { x: 150, y: 100 },
    fogRadius: 200
  },
  
  // Кнопка с glassmorphism эффектом
  button: {
    style: {
      ...glassmorphismStyles.containerMinimal,
      borderRadius: borderRadius.button,
      paddingVertical: spacing.md,
      paddingHorizontal: spacing.lg,
      alignItems: 'center' as const,
      justifyContent: 'center' as const
    },
    blurIntensity: blurIntensity.light,
    withFogEffect: false
  }
};

// Типы для TypeScript
export type GlassmorphismPreset = keyof typeof glassmorphismPresets;
export type GlassmorphismStyleProps = {
  backgroundColor?: string;
  borderColor?: string;
  borderWidth?: number;
  borderRadius?: number;
  padding?: number;
};
