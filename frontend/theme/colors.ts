// Основная цветовая палитра приложения
export const colors = {
  // Основные цвета
  primary: {
    main: '#4a80f5',
    light: '#7fb4ff',
    dark: '#1a4dff',
    contrast: '#ffffff'
  },
  
  // Цвета фона
  background: {
    primary: '#000000',
    secondary: '#1a1a2e',
    tertiary: '#16213e',
    surface: 'rgba(20, 20, 35, 0.25)'
  },
  
  // Цвета текста
  text: {
    primary: '#ffffff',
    secondary: 'rgba(255, 255, 255, 0.8)',
    tertiary: 'rgba(255, 255, 255, 0.6)',
    hint: 'rgba(255, 255, 255, 0.4)',
    disabled: 'rgba(255, 255, 255, 0.3)'
  },
  
  // Градиентные сферы для фона
  gradientSpheres: {
    purple: {
      primary: 'rgba(170, 70, 255, 0.3)',
      secondary: 'rgba(170, 70, 255, 0)'
    },
    yellow: {
      primary: 'rgba(255, 220, 50, 0.3)',
      secondary: 'rgba(255, 220, 50, 0)'
    },
    cyan: {
      primary: 'rgba(0, 200, 200, 0.3)',
      secondary: 'rgba(0, 200, 200, 0)'
    },
    pink: {
      primary: 'rgba(220, 100, 170, 0.3)',
      secondary: 'rgba(220, 100, 170, 0)'
    },
    blue: {
      primary: 'rgba(100, 150, 255, 0.25)',
      secondary: 'rgba(100, 150, 255, 0)'
    },
    green: {
      primary: 'rgba(100, 255, 200, 0.15)',
      secondary: 'rgba(100, 255, 200, 0)'
    }
  },
  
  // Glassmorphism цвета
  glassmorphism: {
    background: {
      primary: 'rgba(20, 20, 35, 0.25)',
      secondary: 'rgba(30, 30, 50, 0.3)',
      tertiary: 'rgba(40, 40, 60, 0.2)',
      accent: 'rgba(50, 20, 80, 0.3)'
    },
    border: {
      primary: 'rgba(255, 255, 255, 0.08)',
      secondary: 'rgba(255, 255, 255, 0.12)',
      tertiary: 'rgba(255, 255, 255, 0.06)',
      accent: 'rgba(170, 70, 255, 0.2)'
    },
    fog: {
      blue: [
        'rgba(120, 200, 255, 0.4)',
        'rgba(120, 200, 255, 0.25)',
        'rgba(120, 200, 255, 0.1)',
        'rgba(120, 200, 255, 0.02)',
        'rgba(120, 200, 255, 0)'
      ],
      purple: [
        'rgba(170, 70, 255, 0.4)',
        'rgba(170, 70, 255, 0.25)',
        'rgba(170, 70, 255, 0.1)',
        'rgba(170, 70, 255, 0.02)',
        'rgba(170, 70, 255, 0)'
      ],
      green: [
        'rgba(100, 255, 200, 0.3)',
        'rgba(100, 255, 200, 0.2)',
        'rgba(100, 255, 200, 0.1)',
        'rgba(100, 255, 200, 0.05)',
        'rgba(100, 255, 200, 0)'
      ]
    }
  },
  
  // Статусные цвета
  status: {
    success: '#06ffa5',
    error: '#ff4757',
    warning: '#f9a825',
    info: '#00d9ff'
  },
  
  // Прогресс и градиенты
  progress: {
    gradient: ['#7fb4ff', '#4a7dff'],
    background: 'rgba(255, 255, 255, 0.1)'
  },
  
  // Overlay цвета
  overlay: {
    light: 'rgba(0, 0, 0, 0.3)',
    medium: 'rgba(0, 0, 0, 0.5)',
    heavy: 'rgba(0, 0, 0, 0.7)'
  }
} as const;

// Типы для TypeScript
export type Colors = typeof colors;
export type ColorKey = keyof Colors;
