// Система отступов и размеров
export const spacing = {
  // Базовые отступы
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
  
  // Специальные отступы для компонентов
  card: {
    padding: 32,
    paddingTop: 44,
    paddingBottom: 24,
    margin: 16,
    marginTop: 10
  },
  
  safeArea: {
    horizontal: 8,
    top: 80,
    bottom: 24
  },
  
  content: {
    horizontal: 8,
    top: 16
  }
} as const;

// Радиусы скругления
export const borderRadius = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 14,
  xl: 16,
  xxl: 18,
  round: 50,
  
  // Специальные радиусы для компонентов
  card: 14,
  button: 12,
  input: 8,
  glassmorphism: {
    small: 12,
    medium: 14,
    large: 16,
    xlarge: 18
  }
} as const;

// Тени и эффекты
export const shadows = {
  small: {
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2
  },
  
  medium: {
    shadowColor: 'rgba(0, 0, 0, 0.15)',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4
  },
  
  large: {
    shadowColor: 'rgba(0, 0, 0, 0.2)',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8
  },
  
  // Специальные тени для glassmorphism
  glassmorphism: {
    light: {
      shadowColor: 'rgba(255, 255, 255, 0.1)',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 1
    },
    medium: {
      shadowColor: 'rgba(255, 255, 255, 0.15)',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.15,
      shadowRadius: 4,
      elevation: 2
    }
  }
} as const;

// Размеры blur эффектов
export const blurIntensity = {
  light: 10,
  medium: 15,
  heavy: 20,
  extreme: 25
} as const;

// Типы для TypeScript
export type Spacing = typeof spacing;
export type BorderRadius = typeof borderRadius;
export type Shadows = typeof shadows;
export type BlurIntensity = typeof blurIntensity;
