// Типографическая система
export const typography = {
  // Размеры шрифтов
  fontSize: {
    xs: 10,
    sm: 12,
    md: 14,
    lg: 16,
    xl: 18,
    xxl: 20,
    xxxl: 24,
    
    // Специальные размеры
    title: 28,
    subtitle: 22,
    body: 16,
    caption: 12,
    hint: 10
  },
  
  // Веса шрифтов
  fontWeight: {
    light: '300' as const,
    normal: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const
  },
  
  // Высота строк
  lineHeight: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
    loose: 1.8,
    
    // Специальные значения
    title: 32,
    body: 24,
    caption: 16
  },
  
  // Стили текста для компонентов
  styles: {
    // Заголовки
    h1: {
      fontSize: 28,
      fontWeight: '700' as const,
      lineHeight: 32,
      color: '#ffffff'
    },
    
    h2: {
      fontSize: 24,
      fontWeight: '600' as const,
      lineHeight: 28,
      color: '#ffffff'
    },
    
    h3: {
      fontSize: 20,
      fontWeight: '600' as const,
      lineHeight: 24,
      color: '#ffffff'
    },
    
    // Основной текст
    body: {
      fontSize: 16,
      fontWeight: '400' as const,
      lineHeight: 24,
      color: 'rgba(255, 255, 255, 0.8)'
    },
    
    bodyLarge: {
      fontSize: 18,
      fontWeight: '400' as const,
      lineHeight: 26,
      color: 'rgba(255, 255, 255, 0.8)'
    },
    
    // Подписи и вспомогательный текст
    caption: {
      fontSize: 12,
      fontWeight: '400' as const,
      lineHeight: 16,
      color: 'rgba(255, 255, 255, 0.6)'
    },
    
    hint: {
      fontSize: 10,
      fontWeight: '400' as const,
      lineHeight: 14,
      color: 'rgba(255, 255, 255, 0.4)'
    },
    
    // Специальные стили для тренировки
    wordMain: {
      fontSize: 24,
      fontWeight: '600' as const,
      lineHeight: 32,
      color: '#ffffff'
    },
    
    wordTranslation: {
      fontSize: 16,
      fontWeight: '300' as const,
      lineHeight: 24,
      color: 'rgba(220, 220, 240, 0.8)'
    },
    
    wordHint: {
      fontSize: 14,
      fontWeight: '400' as const,
      lineHeight: 20,
      color: 'rgba(255, 255, 255, 0.6)'
    },
    
    // Прогресс и статистика
    progressText: {
      fontSize: 14,
      fontWeight: '500' as const,
      lineHeight: 18,
      color: 'rgba(255, 255, 255, 0.8)'
    },
    
    intervalDebug: {
      fontSize: 12,
      fontWeight: '400' as const,
      lineHeight: 16,
      color: 'rgba(160, 160, 160, 0.7)',
      fontFamily: 'monospace'
    },
    
    // Кнопки
    button: {
      fontSize: 16,
      fontWeight: '600' as const,
      lineHeight: 20,
      color: '#ffffff'
    },
    
    buttonSmall: {
      fontSize: 14,
      fontWeight: '500' as const,
      lineHeight: 18,
      color: '#ffffff'
    }
  }
} as const;

// Типы для TypeScript
export type Typography = typeof typography;
export type TypographyStyle = keyof typeof typography.styles;
