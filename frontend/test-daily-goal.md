# Тест дневной цели - Исправление бага с мерцанием карточек

## Проблемы
1. **Мерцание карточек** - сначала показывается одно слово, потом сразу же другое
2. **Зависание на последней карточке** - после достижения дневной цели экран завершения не показывается

## Причины
1. **Мерцание**: Конфликт между предзагруженными карточками и новыми запросами из-за изменения зависимостей в `fetchNewCard`
2. **Зависание**: Неправильная логика проверки дневной цели - проверка происходила до обновления `currentCardNumber`

## Исправления

### 1. Убрали лишние зависимости из fetchNewCard
```javascript
// БЫЛО:
}, [user?.settings?.target_language, user?.settings?.native_language, user?.learning_languages, currentCardNumber, totalCardsInDeck]);

// СТАЛО:
}, [user?.settings?.target_language, user?.settings?.native_language, user?.learning_languages]);
```

### 2. Исправили логику проверки дневной цели в checkAnswer
```javascript
if (shouldAdvanceToNext) {
  // Проверяем дневную цель перед переходом к следующей карточке
  // currentCardNumber будет увеличен на 1 при переходе, поэтому проверяем currentCardNumber + 1
  if (currentCardNumber + 1 > totalCardsInDeck) {
    console.log('🎯 Дневная цель будет достигнута после этой карточки! Завершаем тренировку');
    setIsLoading(false);
    // Принудительно устанавливаем currentCard в null для показа экрана завершения
    setTimeout(() => {
      setCurrentCard(null);
    }, 1000); // Даем время показать feedback
    return;
  }
  // ... остальная логика
}
```

### 3. Исправили проверку в handleNextCard
```javascript
if (isCorrect === true) {
  // Проверяем дневную цель перед принудительным переходом
  if (currentCardNumber + 1 > totalCardsInDeck) {
    console.log('🎯 Дневная цель будет достигнута! Завершаем тренировку вместо принудительного перехода');
    // Принудительно устанавливаем currentCard в null для показа экрана завершения
    setCurrentCard(null);
    return;
  }
  // ... остальная логика
}
```

### 4. Упростили useEffect для завершения тренировки
```javascript
// Вызываем onTrainingComplete при завершении тренировки
useEffect(() => {
  // Проверяем условие завершения: нет карточек от API (обычное завершение или достигнута дневная цель)
  const noCardsAvailable = !isLoading && !currentCard;

  if (noCardsAvailable && onTrainingComplete) {
    console.log('🎯 Тренировка завершена - вызываем обновление статистики');
    onTrainingComplete();
  }
}, [isLoading, currentCard, onTrainingComplete]);
```

### 5. Оптимизировали предзагрузку для последней карточки
```javascript
// НЕ запускаем предзагрузку для предпоследней карточки (чтобы избежать медленной загрузки последней)
if (isAnswerCorrect && currentCardNumber < totalCardsInDeck - 1) {
  // ... запускаем предзагрузку
} else if (isAnswerCorrect) {
  console.log('🎯 Предпоследняя карточка - пропускаем предзагрузку для оптимизации');
}
```

## Ожидаемый результат
1. ✅ Карточки больше не мерцают
2. ✅ Дневная цель работает корректно
3. ✅ Экран завершения показывается после достижения цели
4. ✅ Статистика обновляется при завершении тренировки
5. ✅ Последняя карточка загружается быстро (без медленной предзагрузки)

## Причина замедления последней карточки
**Проблема**: При малом словаре (10 карточек) предзагрузчик на предпоследней карточке:
1. Загружал дублирующуюся карточку
2. Обнаруживал дублирование
3. Запрашивал альтернативную карточку с `exclude_card_id`
4. Бэкенд медленно обрабатывал запрос с исключением (3079ms вместо ~1300ms)

**Решение**: Отключили предзагрузку для предпоследней карточки → последняя карточка загружается обычным способом без задержек.

## Тестирование
1. Установить дневную цель = 5 карточек
2. Пройти 5 карточек
3. Убедиться, что после 5-й карточки показывается экран завершения
4. Убедиться, что карточки не мерцают при переходах
5. Убедиться, что последняя карточка загружается быстро
