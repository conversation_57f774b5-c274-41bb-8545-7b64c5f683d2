# Унификация сфер - Одинаковые 4 рандомные сферы везде

## Проблема
На главном экране сферы все еще не видны, разные экраны имеют разное количество сфер и разную видимость.

## Решение
Унифицированы все темы - теперь везде используются одинаковые 4 рандомные сферы с отличной видимостью.

## Изменения

### ✅ Было (разные конфигурации):

**Training Theme**: 4 сферы с рандомными цветами
```tsx
spheres: defaultSpheres, // 4 сферы
overlayColor: 'rgba(0, 0, 0, 0.5)'
```

**Profile Theme**: 3 фиксированные сферы
```tsx
spheres: [
  { colors: ['rgba(110, 150, 180, 0.25)', ...] },  // Синий
  { colors: ['rgba(140, 100, 200, 0.23)', ...] },  // Фиолетовый
  { colors: ['rgba(110, 170, 150, 0.22)', ...] }   // Зеленый
],
overlayColor: 'rgba(0, 0, 0, 0.7)'
```

**Minimal Theme**: 2 фиксированные сферы
```tsx
spheres: [
  { colors: ['rgba(140, 140, 160, 0.22)', ...] },  // Серо-синий
  { colors: ['rgba(120, 150, 140, 0.20)', ...] }   // Зеленоватый
],
overlayColor: 'rgba(0, 0, 0, 0.6)'
```

### ✅ Стало (унифицированно):

**Все темы**: 4 рандомные сферы
```tsx
training: {
  spheres: defaultSpheres,        // 4 рандомные сферы
  overlayColor: 'rgba(0, 0, 0, 0.5)'
},

profile: {
  spheres: defaultSpheres,        // Те же 4 рандомные сферы
  overlayColor: 'rgba(0, 0, 0, 0.5)'
},

minimal: {
  spheres: defaultSpheres,        // Те же 4 рандомные сферы
  overlayColor: 'rgba(0, 0, 0, 0.5)'
}
```

## Конфигурация 4 сфер

### Позиции и размеры (одинаковые везде):
```tsx
const defaultSpheres = [
  {
    id: 'sphere-1',
    cx: screenWidth * 0.15,      // Левый верх
    cy: screenHeight * 0.2,
    r: baseScreenSize * 1.2,     // Большая сфера
  },
  {
    id: 'sphere-2', 
    cx: screenWidth * 0.85,      // Правый верх
    cy: screenHeight * 0.3,
    r: baseScreenSize * 0.7,     // Средняя сфера
  },
  {
    id: 'sphere-3',
    cx: screenWidth * 0.15,      // Левый низ
    cy: screenHeight * 0.6,
    r: baseScreenSize * 1.8,     // Самая большая сфера
  },
  {
    id: 'sphere-4',
    cx: screenWidth * 0.95,      // Правый низ
    cy: screenHeight * 0.75,
    r: baseScreenSize * 1.0,     // Стандартная сфера
  }
];
```

### Рандомные цвета (10 вариантов):
```tsx
const visibleColorPairs = [
  ['rgba(140, 100, 200, 0.25)', 'rgba(140, 100, 200, 0)'],  // Фиолетовый
  ['rgba(180, 170, 100, 0.23)', 'rgba(180, 170, 100, 0)'],  // Желтый
  ['rgba(100, 160, 170, 0.25)', 'rgba(100, 160, 170, 0)'],  // Бирюзовый
  ['rgba(170, 110, 150, 0.23)', 'rgba(170, 110, 150, 0)'],  // Розовый
  ['rgba(110, 150, 180, 0.25)', 'rgba(110, 150, 180, 0)'],  // Синий
  ['rgba(110, 170, 150, 0.23)', 'rgba(110, 170, 150, 0)'],  // Зеленый
  ['rgba(180, 150, 100, 0.24)', 'rgba(180, 150, 100, 0)'],  // Оранжевый
  ['rgba(170, 110, 160, 0.23)', 'rgba(170, 110, 160, 0)'],  // Розово-фиолетовый
  ['rgba(140, 140, 160, 0.20)', 'rgba(140, 140, 160, 0)'],  // Серо-синий
  ['rgba(150, 140, 140, 0.20)', 'rgba(150, 140, 140, 0)']   // Серо-коричневый
];
```

## Результат по экранам

### 🏠 Главный экран (App.tsx)
- **Было**: 2 едва заметные сферы
- **Стало**: 4 рандомные яркие сферы
- **Результат**: Отлично видны! ✨

### 🎯 Экран тренировки (TrainingScreen.tsx)
- **Было**: 4 рандомные сферы (хорошо)
- **Стало**: Те же 4 рандомные сферы
- **Результат**: Без изменений, отлично ✅

### 👤 Экран профиля (ProfileScreen.tsx)
- **Было**: 3 фиксированные сферы
- **Стало**: 4 рандомные сферы
- **Результат**: Больше разнообразия! 🌈

## Преимущества унификации

### Консистентность
- ✅ **Одинаковый опыт** на всех экранах
- ✅ **Единая система** позиционирования
- ✅ **Стандартная видимость** везде

### Разнообразие
- 🎲 **Рандомные цвета** на каждом экране
- 🌈 **10 цветовых схем** для вариативности
- 🔄 **Новые комбинации** при каждом рендере

### Простота
- 🛠️ **Одна конфигурация** вместо трех
- 📝 **Легче поддерживать** код
- 🎯 **Единые настройки** overlay

## Технические детали

### Рандомизация
```tsx
// Каждая сфера получает случайный цвет при рендере
const spheresWithRandomColors = useMemo(() => 
  spheres.map(sphere => ({
    ...sphere,
    colors: generateRandomSphereColors()
  })), [spheres]);
```

### Overlay
- Все экраны: `rgba(0, 0, 0, 0.5)` - оптимальный баланс
- Достаточно темный для читаемости контента
- Достаточно светлый для видимости сфер

Теперь все экраны имеют одинаково красивые и хорошо видимые сферы! 🎉
