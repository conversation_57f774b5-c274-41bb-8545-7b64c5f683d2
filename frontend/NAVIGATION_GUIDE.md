# 🧭 Руководство по навигации

Этот документ содержит лучшие практики и решения проблем навигации в React Native приложении.

## 📚 Содержание

- [Проблемы с навигационным стеком](#проблемы-с-навигационным-стеком)
- [Правильная очистка стека](#правильная-очистка-стека)
- [Лучшие практики](#лучшие-практики)
- [Примеры кода](#примеры-кода)

## 🚨 Проблемы с навигационным стеком

### Проблема: Накопление промежуточных экранов

**Сценарий:**
```
Главный экран → Профиль → Изучаемые языки → Добавить язык → Выбор уровня → Профиль
```

**Что происходит:**
- В стеке накапливаются все промежуточные экраны
- При нажатии "Назад" пользователь попадает на предыдущий экран в стеке
- Вместо выхода из профиля пользователь возвращается к выбору языка

**Стек до исправления:**
```
[Home, Profile, AddLanguage, LevelSelection, Profile]
```

## ✅ Правильная очистка стека

### Решение: `navigation.reset()` с сохранением иерархии

```typescript
// ❌ НЕПРАВИЛЬНО - полная очистка стека
navigation.reset({
  index: 0,
  routes: [{ name: 'Profile' }],
});
// Результат: кнопка "Назад" исчезает

// ❌ НЕПРАВИЛЬНО - добавление в стек
navigation.navigate('Profile');
// Результат: накопление промежуточных экранов

// ✅ ПРАВИЛЬНО - очистка с сохранением иерархии
navigation.reset({
  index: 1,
  routes: [
    { name: 'Home' },
    { name: 'Profile' }
  ],
});
// Результат: чистый стек с правильной навигацией
```

### Когда использовать каждый метод

| Метод | Когда использовать | Результат |
|-------|-------------------|-----------|
| `navigate()` | Обычные переходы | Добавляет экран в стек |
| `goBack()` | Возврат на предыдущий экран | Удаляет текущий экран |
| `reset()` | Завершение многошагового процесса | Заменяет весь стек |

## 🎯 Лучшие практики

### 1. Многошаговые процессы

Для процессов типа "онбординг", "добавление языка", "регистрация":

```typescript
// После завершения процесса
const completeMultiStepProcess = () => {
  navigation.reset({
    index: 1, // Активный экран (начинается с 0)
    routes: [
      { name: 'Home' },     // Корневой экран
      { name: 'Profile' }   // Целевой экран
    ],
  });
};
```

### 2. Онбординг

```typescript
// После завершения онбординга
const completeOnboarding = () => {
  navigation.reset({
    index: 0,
    routes: [{ name: 'Home' }], // Только главный экран
  });
};
```

### 3. Авторизация

```typescript
// После успешного входа
const handleLoginSuccess = () => {
  navigation.reset({
    index: 0,
    routes: [{ name: 'Home' }],
  });
};

// После выхода
const handleLogout = () => {
  navigation.reset({
    index: 0,
    routes: [{ name: 'Auth' }],
  });
};
```

## 📝 Примеры кода

### Пример 1: Добавление языка (из AddLanguageScreen.tsx)

```typescript
const handleAddLanguage = async () => {
  try {
    // ... логика добавления языка
    
    if (wasOnboarding) {
      // Онбординг завершен - переход на главный экран
      navigation.reset({
        index: 0,
        routes: [{ name: 'Home' }],
      });
    } else {
      // Обычное добавление - возврат в профиль с очисткой промежуточных экранов
      navigation.reset({
        index: 1,
        routes: [
          { name: 'Home' },
          { name: 'Profile' }
        ],
      });
    }
  } catch (error) {
    // Обработка ошибок
  }
};
```

### Пример 2: Условная навигация

```typescript
const handleProcessComplete = (isOnboarding: boolean) => {
  if (isOnboarding) {
    // Первый запуск - только главный экран
    navigation.reset({
      index: 0,
      routes: [{ name: 'Home' }],
    });
  } else {
    // Обычный процесс - сохраняем иерархию
    navigation.reset({
      index: 1,
      routes: [
        { name: 'Home' },
        { name: 'TargetScreen' }
      ],
    });
  }
};
```

## 🔍 Отладка навигации

### Проверка текущего стека

```typescript
import { useNavigationState } from '@react-navigation/native';

const NavigationDebugger = () => {
  const state = useNavigationState(state => state);
  
  console.log('Current navigation state:', state);
  console.log('Routes in stack:', state?.routes?.map(r => r.name));
  console.log('Current route index:', state?.index);
  
  return null;
};
```

### Логирование переходов

```typescript
const logNavigation = (action: string, target: string) => {
  console.log(`🧭 Navigation: ${action} → ${target}`);
};

// Использование
logNavigation('reset', 'Home + Profile');
navigation.reset({
  index: 1,
  routes: [{ name: 'Home' }, { name: 'Profile' }],
});
```

## ⚠️ Частые ошибки

1. **Использование `navigate()` вместо `reset()`** для завершения процессов
2. **Полная очистка стека** без сохранения родительских экранов
3. **Неправильный `index`** в `reset()` - должен указывать на активный экран
4. **Забывание про `goBack()`** для простых возвратов

## 📖 Дополнительные ресурсы

- [React Navigation - Reset](https://reactnavigation.org/docs/navigation-actions/#reset)
- [React Navigation - Navigation State](https://reactnavigation.org/docs/navigation-state/)
- [Stack Navigator](https://reactnavigation.org/docs/stack-navigator/)

---

**Последнее обновление:** 2025-01-15  
**Автор:** Команда разработки WordMaster
