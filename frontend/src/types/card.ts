export interface WordCard {
  _id: string;
  concept_id: string;
  word: string;
  language: string;
  level: string;
  part_of_speech: string;
  ipa_pronunciation: string;
  examples: Array<{
    sentence: string;
    highlight: string;
  }>;
  tags: string[];
  created_at: string;
  updated_at: string;
}

export interface LearningCard {
  id: string;
  source: WordCard;
  target: WordCard;
  userInput: string;
  isFlipped: boolean;
  isCorrect: boolean | null;
}
