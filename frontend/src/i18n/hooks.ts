import { useTranslation } from 'react-i18next';
import { useCallback } from 'react';
import { SupportedLanguage, saveLanguage, supportedLanguages } from './index';

// Хук для работы с переводами
export const useAppTranslation = (namespace?: string) => {
  const { t, i18n } = useTranslation(namespace);
  
  return {
    t,
    i18n,
    currentLanguage: i18n.language as SupportedLanguage,
    isReady: i18n.isInitialized,
  };
};

// Хук для переключения языка приложения
export const useLanguageSwitcher = () => {
  const { i18n } = useTranslation();
  
  const changeLanguage = useCallback(async (language: SupportedLanguage) => {
    try {
      await i18n.changeLanguage(language);
      await saveLanguage(language);
    } catch (error) {
      console.error('Failed to change language:', error);
    }
  }, [i18n]);
  
  const getCurrentLanguage = useCallback((): SupportedLanguage => {
    return i18n.language as SupportedLanguage;
  }, [i18n]);
  
  const getSupportedLanguages = useCallback(() => {
    return Object.entries(supportedLanguages).map(([code, name]) => ({
      code: code as SupportedLanguage,
      name,
    }));
  }, []);
  
  return {
    changeLanguage,
    getCurrentLanguage,
    getSupportedLanguages,
    currentLanguage: i18n.language as SupportedLanguage,
  };
};

// Хук для автоматического переключения языка на основе родного языка пользователя
export const useAutoLanguageSwitch = () => {
  const { changeLanguage, getCurrentLanguage } = useLanguageSwitcher();

  const switchToNativeLanguage = useCallback(async (nativeLanguageCode: string) => {
    console.log('Attempting to switch to native language:', nativeLanguageCode);
    console.log('Supported languages:', Object.keys(supportedLanguages));

    // Проверяем, поддерживается ли родной язык пользователя в приложении
    if (nativeLanguageCode in supportedLanguages) {
      console.log('Language is supported, switching to:', nativeLanguageCode);
      await changeLanguage(nativeLanguageCode as SupportedLanguage);
      return true; // Язык был изменен
    } else {
      console.log('Language not supported, staying on current language');
      // Если не поддерживается, остаемся на текущем языке (обычно английский)
      return false; // Язык не был изменен
    }
  }, [changeLanguage]);

  const isLanguageSupported = useCallback((languageCode: string): boolean => {
    return languageCode in supportedLanguages;
  }, []);

  return {
    switchToNativeLanguage,
    isLanguageSupported,
    supportedLanguages: Object.keys(supportedLanguages),
  };
};
