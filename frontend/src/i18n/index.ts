import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Импорт файлов переводов
import commonEn from '../../locales/en/common.json';
import authEn from '../../locales/en/auth.json';
import trainingEn from '../../locales/en/training.json';
import profileEn from '../../locales/en/profile.json';
import onboardingEn from '../../locales/en/onboarding.json';

import commonRu from '../../locales/ru/common.json';
import authRu from '../../locales/ru/auth.json';
import trainingRu from '../../locales/ru/training.json';
import profileRu from '../../locales/ru/profile.json';
import onboardingRu from '../../locales/ru/onboarding.json';

// Поддерживаемые языки
export const supportedLanguages = {
  en: 'English',
  ru: 'Русский',
} as const;

export type SupportedLanguage = keyof typeof supportedLanguages;

// Ключ для сохранения языка в AsyncStorage
const LANGUAGE_STORAGE_KEY = 'app_language';

// Функция для получения сохраненного языка
const getStoredLanguage = async (): Promise<SupportedLanguage> => {
  try {
    const storedLanguage = await AsyncStorage.getItem(LANGUAGE_STORAGE_KEY);
    if (storedLanguage && storedLanguage in supportedLanguages) {
      return storedLanguage as SupportedLanguage;
    }
  } catch (error) {
    console.warn('Failed to get stored language:', error);
  }
  return 'en'; // Fallback to English
};

// Функция для сохранения языка
export const saveLanguage = async (language: SupportedLanguage): Promise<void> => {
  try {
    await AsyncStorage.setItem(LANGUAGE_STORAGE_KEY, language);
  } catch (error) {
    console.warn('Failed to save language:', error);
  }
};

// Конфигурация i18next
i18n
  .use(initReactI18next)
  .init({
    // Язык по умолчанию
    lng: 'en',
    
    // Fallback язык
    fallbackLng: 'en',
    
    // Поддерживаемые языки
    supportedLngs: Object.keys(supportedLanguages),
    
    // Отладка (отключаем для чистых логов)
    debug: false,
    
    // Интерполяция
    interpolation: {
      escapeValue: false, // React уже экранирует
    },
    
    // Ресурсы переводов
    resources: {
      en: {
        common: commonEn,
        auth: authEn,
        training: trainingEn,
        profile: profileEn,
        onboarding: onboardingEn,
      },
      ru: {
        common: commonRu,
        auth: authRu,
        training: trainingRu,
        profile: profileRu,
        onboarding: onboardingRu,
      },
    },
    
    // Namespace по умолчанию
    defaultNS: 'common',
    
    // Разделитель для ключей
    keySeparator: '.',
    
    // Разделитель для namespace
    nsSeparator: ':',
  });

// Инициализация с сохраненным языком
getStoredLanguage().then((language) => {
  i18n.changeLanguage(language);
});

export default i18n;
