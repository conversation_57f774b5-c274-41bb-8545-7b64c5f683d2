// Типы для переводов
export interface CommonTranslations {
  buttons: {
    save: string;
    cancel: string;
    continue: string;
    back: string;
    next: string;
    done: string;
    edit: string;
    delete: string;
    confirm: string;
    close: string;
    retry: string;
    loading: string;
  };
  navigation: {
    training: string;
    profile: string;
    settings: string;
  };
  errors: {
    networkError: string;
    unknownError: string;
    validationError: string;
  };
  success: {
    saved: string;
    updated: string;
    deleted: string;
  };
  welcome: {
    greeting: string;
    subtitle: string;
    description: string;
  };
  auth: {
    loginPrompt: string;
    login: string;
    noAccount: string;
  };
  statistics: {
    title: string;
    learnedWords: string;
    totalWords: string;
  };
}

export interface AuthTranslations {
  login: {
    title: string;
    emailPlaceholder: string;
    passwordPlaceholder: string;
    loginButton: string;
    forgotPassword: string;
    noAccount: string;
    signUp: string;
  };
  register: {
    title: string;
    usernamePlaceholder: string;
    emailPlaceholder: string;
    passwordPlaceholder: string;
    confirmPasswordPlaceholder: string;
    registerButton: string;
    hasAccount: string;
    signIn: string;
  };
  errors: {
    invalidEmail: string;
    passwordTooShort: string;
    passwordsNotMatch: string;
    userExists: string;
    invalidCredentials: string;
    networkError: string;
  };
}

export interface TrainingTranslations {
  title: string;
  progress: string;
  streak: string;
  level: string;
  card: {
    typeAnswer: string;
    checkAnswer: string;
    nextCard: string;
    showHint: string;
    dontKnow: string;
    dontRemember: string;
  };
  feedback: {
    correct: string;
    incorrect: string;
    almostCorrect: string;
  };
  completion: {
    sessionComplete: string;
    congratulations: string;
    wordsLearned: string;
    accuracy: string;
    continueButton: string;
  };
}

export interface ProfileTranslations {
  title: string;
  sections: {
    basicInfo: string;
    settings: string;
    languages: string;
  };
  fields: {
    name: string;
    email: string;
    nativeLanguage: string;
    learningLanguages: string;
    dailyGoal: string;
    appLanguage: string;
  };
  actions: {
    logout: string;
    editProfile: string;
    changeLanguage: string;
  };
  placeholders: {
    notSpecified: string;
  };
}

export interface OnboardingTranslations {
  welcome: {
    title: string;
    subtitle: string;
    getStarted: string;
  };
  nativeLanguage: {
    title: string;
    subtitle: string;
    selectLanguage: string;
  };
  targetLanguage: {
    title: string;
    subtitle: string;
    selectLanguage: string;
  };
  level: {
    title: string;
    subtitle: string;
    selectLevel: string;
  };
  completion: {
    title: string;
    subtitle: string;
    startLearning: string;
  };
}

// Общий тип для всех переводов
export interface Resources {
  common: CommonTranslations;
  auth: AuthTranslations;
  training: TrainingTranslations;
  profile: ProfileTranslations;
  onboarding: OnboardingTranslations;
}

// Расширение модуля react-i18next для типизации
declare module 'react-i18next' {
  interface CustomTypeOptions {
    defaultNS: 'common';
    resources: Resources;
  }
}
