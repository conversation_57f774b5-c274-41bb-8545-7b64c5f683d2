/**
 * Демонстрация новых логов в реальном приложении
 */

console.log('🧪 === ДЕМОНСТРАЦИЯ НОВЫХ ЛОГОВ ===\n');

// Симуляция форматирования времени
function formatTime() {
  const now = new Date();
  return now.toTimeString().split(' ')[0] + '.' + now.getMilliseconds().toString().padStart(3, '0');
}

// Симуляция новых логов
function newLog(level, category, message, data) {
  const time = formatTime();
  const icons = {
    'INFO': 'ℹ️',
    'SUCCESS': '✅',
    'WARNING': '⚠️',
    'ERROR': '❌'
  };
  
  const categoryIcons = {
    'AUTH': '🔐',
    'CARD_LOADING': '📱',
    'PRELOADER': '🚀',
    'API': '🌐'
  };
  
  const icon = icons[level];
  const categoryIcon = categoryIcons[category];
  
  console.log(`${icon} [${time}] ${categoryIcon} ${category}: ${message}`);
  if (data) {
    console.log('   📋 Data:', data);
  }
}

console.log('🔸 === НОВЫЕ ЛОГИ АУТЕНТИФИКАЦИИ ===');
console.log('');

newLog('INFO', 'AUTH', 'Попытка входа: <EMAIL>');

setTimeout(() => {
  newLog('INFO', 'API', 'Запрос к /api/token', { email: '<EMAIL>' });
  
  setTimeout(() => {
    newLog('SUCCESS', 'API', 'Ответ от /api/token (200)', { responseTime: '850ms' });
    newLog('SUCCESS', 'AUTH', 'Успешный вход пользователя 3fcb316b');
  }, 800);
}, 500);

setTimeout(() => {
  console.log('');
  console.log('🔸 === НОВЫЕ ЛОГИ КАРТОЧЕК ===');
  console.log('');
  
  newLog('INFO', 'CARD_LOADING', 'Начинаем загрузку карточки для пользователя 3fcb316b', { language: 'ru' });
  
  setTimeout(() => {
    newLog('SUCCESS', 'CARD_LOADING', 'Карточка загружена: "что" (what)', { loadTime: '1250ms' });
    
    setTimeout(() => {
      newLog('INFO', 'PRELOADER', 'Начинаем предзагрузку для 3fcb316b', { language: 'ru' });
      
      setTimeout(() => {
        newLog('SUCCESS', 'PRELOADER', 'Предзагружена карточка: "нет"', { loadTime: '2100ms' });
      }, 2000);
    }, 500);
  }, 1200);
}, 3000);

setTimeout(() => {
  console.log('');
  console.log('🎉 === СРАВНЕНИЕ ===');
  console.log('');
  console.log('❌ СТАРЫЕ ЛОГИ:');
  console.log('   LOG  Fetching user data with token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...');
  console.log('   LOG  User data response status: 200');
  console.log('   LOG  User data received: {"_id": "684e89dc3fcb316b5a8e9758", ...}');
  console.log('');
  console.log('✅ НОВЫЕ ЛОГИ:');
  console.log('   🔐 [13:15:42.123] 🌐 API: Запрос к /api/users/me');
  console.log('   ✅ [13:15:42.456] 🌐 API: Ответ от /api/users/me (200)');
  console.log('   ✅ [13:15:42.458] 🔐 AUTH: Данные пользователя получены');
  console.log('');
  console.log('🎯 Преимущества:');
  console.log('   • Временные метки для отслеживания производительности');
  console.log('   • Категоризация по функциональности');
  console.log('   • Эмодзи для быстрого визуального поиска');
  console.log('   • Структурированные данные');
  console.log('   • Отключение i18next debug логов');
  console.log('');
}, 8000);

console.log('✅ Демонстрация запущена! Новые логи появятся в течение 10 секунд...\n');
