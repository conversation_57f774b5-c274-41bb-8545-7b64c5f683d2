/**
 * Тест исправления логгера
 */

// Импортируем логгер
const { log, LogCategory } = require('../utils/logger');

console.log('🧪 === ТЕСТ ИСПРАВЛЕНИЯ ЛОГГЕРА ===\n');

try {
  // Тестируем все методы
  console.log('Тестируем методы логгера...\n');
  
  // Основные методы
  log.cardStart('test123', 'ru');
  log.cardSuccess('тест', 'test', 1000);
  log.preloadStart('test123', 'ru');
  log.preloadSuccess('тест', 1500);
  log.userAnswer('тест', true, 2.5, false);
  
  // Общие методы (которые были проблемными)
  log.debug(LogCategory.AUTH, 'Тест debug метода');
  log.info(LogCategory.API, 'Тест info метода');
  log.success(LogCategory.CARD_LOADING, 'Тест success метода');
  log.warning(LogCategory.PRELOADER, 'Тест warning метода');
  log.error(LogCategory.AUTH, 'Тест error метода');
  
  console.log('\n✅ Все методы работают корректно!');
  console.log('🎉 Логгер исправлен и готов к использованию!');
  
} catch (error) {
  console.error('\n❌ Ошибка в логгере:', error.message);
  console.error('Стек:', error.stack);
}

console.log('\n🔧 === ТЕСТ ЗАВЕРШЕН ===');
