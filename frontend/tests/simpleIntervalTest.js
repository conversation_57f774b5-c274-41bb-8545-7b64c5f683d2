#!/usr/bin/env node
/**
 * Простой тест проблемы с повторением недавно отвеченных слов
 * 
 * Сценарий:
 * 1. Получаем новое слово
 * 2. Отвечаем неправильно → форсированная очередь
 * 3. Отвечаем правильно → interval_level 0
 * 4. Проверяем, что следующая карточка НЕ то же слово
 * 5. Проверяем, что предзагрузка тоже НЕ возвращает то же слово
 */

class SimpleIntervalTester {
  constructor() {
    this.userProgress = new Map();
    this.currentTime = Date.now();
    this.testWord = {
      _id: 'test_word_1',
      word: 'ako',
      translation: 'я'
    };
  }

  advanceTime(minutes) {
    this.currentTime += minutes * 60 * 1000;
    console.log(`⏰ Время продвинуто на ${minutes} минут`);
  }

  async getNextCard(preload = false) {
    const now = this.currentTime;
    const recentThreshold = now - (2 * 60 * 1000); // 2 минуты назад

    console.log(`\n🔍 getNextCard(preload=${preload})`);
    console.log(`   Текущее время: ${new Date(now).toLocaleTimeString()}`);
    console.log(`   Порог исключения: ${new Date(recentThreshold).toLocaleTimeString()}`);

    // Проверяем прогресс тестового слова
    const progress = this.userProgress.get(this.testWord._id);
    
    if (progress) {
      console.log(`   Прогресс слова "${this.testWord.word}":`);
      console.log(`     interval_level: ${progress.interval_level}`);
      console.log(`     force_review: ${progress.force_review}`);
      console.log(`     last_reviewed: ${new Date(progress.last_reviewed).toLocaleTimeString()}`);
      console.log(`     next_review: ${new Date(progress.next_review).toLocaleTimeString()}`);

      // 1. Форсированная очередь (только если не предзагрузка)
      if (!preload && progress.force_review) {
        console.log(`✅ Возвращаем из форсированной очереди: ${this.testWord.word}`);
        return {
          word_id: this.testWord._id,
          word: this.testWord.word,
          translation: this.testWord.translation,
          is_forced_review: true,
          interval_level: progress.interval_level
        };
      }

      // 2. Активная очередь (с исключением недавно отвеченных)
      if (
        progress.interval_level >= 0 && 
        !progress.force_review && 
        progress.next_review <= now
      ) {
        if (progress.last_reviewed <= recentThreshold) {
          console.log(`✅ Возвращаем из активной очереди: ${this.testWord.word}`);
          console.log(`     (время исключения прошло: ${((now - progress.last_reviewed) / 60000).toFixed(1)} мин)`);
          return {
            word_id: this.testWord._id,
            word: this.testWord.word,
            translation: this.testWord.translation,
            is_forced_review: false,
            interval_level: progress.interval_level
          };
        } else {
          console.log(`❌ Слово "${this.testWord.word}" исключено (недавно отвечено)`);
          console.log(`     Время с ответа: ${((now - progress.last_reviewed) / 60000).toFixed(1)} мин < 2 мин`);
        }
      }
    }

    // 3. Новое слово (если прогресса нет)
    if (!progress) {
      console.log(`✅ Возвращаем новое слово: ${this.testWord.word}`);
      
      // Создаем прогресс только для обычной загрузки
      if (!preload) {
        this.userProgress.set(this.testWord._id, {
          interval_level: -1,
          correct_answers: 0,
          incorrect_answers: 0,
          last_reviewed: now,
          next_review: now,
          is_learned: false,
          force_review: false
        });
      }
      
      return {
        word_id: this.testWord._id,
        word: this.testWord.word,
        translation: this.testWord.translation,
        is_new: true,
        interval_level: -1
      };
    }

    console.log(`❌ Нет доступных слов`);
    return null;
  }

  async processAnswer(cardId, isCorrect) {
    const now = this.currentTime;
    const progress = this.userProgress.get(cardId);

    console.log(`\n📝 processAnswer(${isCorrect ? 'CORRECT' : 'INCORRECT'})`);
    console.log(`   Время ответа: ${new Date(now).toLocaleTimeString()}`);

    if (!progress) {
      throw new Error(`Progress not found for card ${cardId}`);
    }

    const oldLevel = progress.interval_level;

    if (isCorrect) {
      progress.correct_answers++;
      progress.interval_level = oldLevel + 1;
      progress.force_review = false;
      
      // Устанавливаем next_review в зависимости от уровня
      const intervals = [0.5, 5, 10, 60, 1440]; // минуты
      if (progress.interval_level < intervals.length) {
        const intervalMinutes = intervals[progress.interval_level];
        progress.next_review = now + (intervalMinutes * 60 * 1000);
        console.log(`✅ Правильный ответ: ${oldLevel} → ${progress.interval_level}`);
        console.log(`   Следующий интервал: ${intervalMinutes} мин`);
        console.log(`   next_review: ${new Date(progress.next_review).toLocaleTimeString()}`);
      } else {
        progress.is_learned = true;
        progress.next_review = now + (365 * 24 * 60 * 60 * 1000); // год
        console.log(`🎓 Слово выучено!`);
      }
    } else {
      progress.incorrect_answers++;
      progress.interval_level = Math.max(-1, oldLevel - 1);
      progress.force_review = true;
      progress.next_review = now;
      console.log(`❌ Неправильный ответ: ${oldLevel} → ${progress.interval_level}`);
      console.log(`   Слово отправлено в форсированную очередь`);
    }

    progress.last_reviewed = now;

    return {
      interval_level: progress.interval_level,
      is_correct: isCorrect,
      is_learned: progress.is_learned || false,
      next_review: new Date(progress.next_review).toISOString()
    };
  }
}

async function runSimpleTest() {
  console.log('🧪 Простой тест проблемы с повторением недавно отвеченных слов');
  console.log('=' * 70);

  const tester = new SimpleIntervalTester();
  let passed = 0;
  let total = 0;

  function check(condition, message) {
    total++;
    if (condition) {
      console.log(`✅ ${message}`);
      passed++;
    } else {
      console.log(`❌ ${message}`);
    }
  }

  try {
    // Шаг 1: Получаем новое слово
    console.log('\n1️⃣ Получаем новое слово');
    const card1 = await tester.getNextCard(false);
    check(card1 && card1.word === 'ako', 'Получено новое слово "ako"');

    // Шаг 2: Отвечаем неправильно
    console.log('\n2️⃣ Отвечаем неправильно');
    await tester.processAnswer(card1.word_id, false);

    // Шаг 3: Проверяем форсированную очередь
    console.log('\n3️⃣ Проверяем форсированную очередь');
    const card2 = await tester.getNextCard(false);
    check(card2 && card2.word === 'ako', 'Слово вернулось в форсированную очередь');

    // Шаг 4: Отвечаем правильно
    console.log('\n4️⃣ Отвечаем правильно');
    const result = await tester.processAnswer(card2.word_id, true);
    check(result.interval_level === 0, 'interval_level стал 0');

    // Шаг 5: КЛЮЧЕВАЯ ПРОВЕРКА - следующая карточка НЕ должна быть тем же словом
    console.log('\n5️⃣ КЛЮЧЕВАЯ ПРОВЕРКА: Следующая карточка');
    const card3 = await tester.getNextCard(false);
    check(!card3 || card3.word !== 'ako', 'Следующая карточка НЕ то же слово (исключено)');

    // Шаг 6: КЛЮЧЕВАЯ ПРОВЕРКА - предзагрузка тоже НЕ должна возвращать то же слово
    console.log('\n6️⃣ КЛЮЧЕВАЯ ПРОВЕРКА: Предзагрузка');
    const preloaded = await tester.getNextCard(true);
    check(!preloaded || preloaded.word !== 'ako', 'Предзагрузка НЕ возвращает то же слово');

    // Шаг 7: Проверяем конфликт интервала (30 сек) и исключения (2 мин)
    console.log('\n7️⃣ ВАЖНО: Проверяем конфликт интервала и исключения');
    console.log('   Интервал: 30 секунд (next_review)');
    console.log('   Исключение: 2 минуты (last_reviewed)');

    tester.advanceTime(1.5); // 1.5 минуты - интервал прошел, но исключение еще действует

    const card4_early = await tester.getNextCard(false);
    check(!card4_early || card4_early.word !== 'ako', 'Слово НЕ появилось через 1.5 мин (исключение побеждает)');

    tester.advanceTime(1); // Еще 1 минута = общее время 2.5 минуты

    const card4 = await tester.getNextCard(false);
    check(card4 && card4.word === 'ako', 'Слово появилось после истечения времени исключения (2.5 мин)');

    // Шаг 8: Повторяем цикл для следующего интервала
    console.log('\n8️⃣ Тестируем следующий интервал');
    await tester.processAnswer(card4.word_id, true); // 0 → 1 (5 минут)
    
    const card5 = await tester.getNextCard(false);
    check(!card5 || card5.word !== 'ako', 'Слово исключено после перехода на interval_level 1');

    tester.advanceTime(6); // 6 минут (больше 5)
    
    const card6 = await tester.getNextCard(false);
    check(card6 && card6.word === 'ako', 'Слово появилось через 5+ минут');

  } catch (error) {
    console.error('\n❌ Ошибка в тесте:', error.message);
  }

  console.log(`\n🏁 Результат: ${passed}/${total} тестов пройдено`);
  
  if (passed === total) {
    console.log('🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ! Проблема решена!');
  } else {
    console.log('⚠️ Некоторые тесты не пройдены. Проблема остается.');
  }
}

runSimpleTest();
