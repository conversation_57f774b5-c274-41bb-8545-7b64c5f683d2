/**
 * Интеграционный тест для TrainingScreen
 * Тестирует взаимодействие между компонентами и API
 */

import React from 'react';
import { render, fireEvent, waitFor, act } from '@testing-library/react-native';
import TrainingScreen from '../screens/TrainingScreen';

// Мокаем зависимости
jest.mock('../services/cardPreloader', () => ({
  useCardPreloader: () => ({
    startPreloading: jest.fn(),
    getPreloadedCard: jest.fn(),
    waitForPreload: jest.fn(),
    clearPreloadedCard: jest.fn()
  })
}));

jest.mock('../services/cardDataProcessor', () => ({
  fetchNewCard: jest.fn(),
  processCardData: jest.fn()
}));

// Мок API с логикой исключения недавно отвеченных слов
const createMockAPI = () => {
  const userProgress = new Map();
  const words = [
    { _id: 'word1', word: 'ako', translation: 'я' },
    { _id: 'word2', word: 'ikaw', translation: 'ты' },
    { _id: 'word3', word: 'asa', translation: 'где' }
  ];
  let currentTime = Date.now();

  return {
    getNextCard: jest.fn(async (userId, nativeLang, targetLang, preload = false) => {
      const now = currentTime;
      const recentThreshold = now - (2 * 60 * 1000); // 2 минуты

      // Проверяем форсированную очередь (только если не предзагрузка)
      if (!preload) {
        for (const [wordId, progress] of userProgress) {
          if (progress.force_review) {
            const word = words.find(w => w._id === wordId);
            return {
              word_id: wordId,
              word: word.word,
              translation: word.translation,
              is_forced_review: true
            };
          }
        }
      }

      // Проверяем активную очередь (исключаем недавно отвеченные)
      for (const [wordId, progress] of userProgress) {
        if (
          progress.interval_level >= 0 && 
          !progress.force_review && 
          progress.next_review <= now &&
          progress.last_reviewed <= recentThreshold
        ) {
          const word = words.find(w => w._id === wordId);
          return {
            word_id: wordId,
            word: word.word,
            translation: word.translation,
            is_forced_review: false
          };
        }
      }

      // Возвращаем новое слово
      for (const word of words) {
        if (!userProgress.has(word._id)) {
          userProgress.set(word._id, {
            interval_level: -1,
            last_reviewed: now,
            next_review: now,
            force_review: false
          });
          
          return {
            word_id: word._id,
            word: word.word,
            translation: word.translation,
            is_new: true
          };
        }
      }

      return null;
    }),

    submitAnswer: jest.fn(async (cardId, isCorrect) => {
      const progress = userProgress.get(cardId);
      const now = currentTime;

      if (isCorrect) {
        progress.interval_level++;
        progress.force_review = false;
        progress.next_review = now + (5 * 60 * 1000); // 5 минут
      } else {
        progress.force_review = true;
        progress.next_review = now;
      }

      progress.last_reviewed = now;

      return {
        interval_level: progress.interval_level,
        is_correct: isCorrect
      };
    }),

    advanceTime: (minutes) => {
      currentTime += minutes * 60 * 1000;
    },

    getUserProgress: () => userProgress
  };
};

describe('TrainingScreen Integration Tests', () => {
  let mockAPI;
  let mockPreloader;

  beforeEach(() => {
    mockAPI = createMockAPI();
    
    // Мокаем предзагрузчик
    mockPreloader = {
      startPreloading: jest.fn(),
      getPreloadedCard: jest.fn(() => null),
      waitForPreload: jest.fn(() => Promise.resolve(null)),
      clearPreloadedCard: jest.fn()
    };

    // Подменяем API вызовы
    require('../services/cardDataProcessor').fetchNewCard.mockImplementation(mockAPI.getNextCard);
  });

  test('Исключение недавно отвеченных слов работает корректно', async () => {
    const { getByTestId, getByText } = render(<TrainingScreen />);

    // 1. Загружаем первое слово
    await waitFor(() => {
      expect(getByText('ako')).toBeTruthy();
    });

    // 2. Отвечаем неправильно
    const input = getByTestId('answer-input');
    const submitButton = getByTestId('submit-button');

    await act(async () => {
      fireEvent.changeText(input, 'wrong answer');
      fireEvent.press(submitButton);
    });

    // 3. Проверяем, что то же слово вернулось (форсированная очередь)
    await waitFor(() => {
      expect(getByText('ako')).toBeTruthy();
    });

    // 4. Отвечаем правильно
    await act(async () => {
      fireEvent.changeText(input, 'я');
      fireEvent.press(submitButton);
    });

    // 5. Проверяем, что получили другое слово (недавно отвеченное исключено)
    await waitFor(() => {
      expect(getByText('ikaw')).toBeTruthy();
    });

    // 6. Проверяем, что предзагрузка тоже исключает недавно отвеченные слова
    expect(mockPreloader.startPreloading).toHaveBeenCalled();
    
    // Симулируем предзагрузку
    const preloadedCard = await mockAPI.getNextCard('user1', 'ru', 'cb', true);
    expect(preloadedCard.word).not.toBe('ako'); // Недавно отвеченное слово исключено
  });

  test('Предзагрузка работает корректно с fallback', async () => {
    // Настраиваем предзагрузчик для возврата карточки
    const preloadedCard = { word: 'asa', translation: 'где', word_id: 'word3' };
    mockPreloader.getPreloadedCard.mockReturnValueOnce(preloadedCard);

    const { getByTestId, getByText } = render(<TrainingScreen />);

    // Ждем загрузки первой карточки
    await waitFor(() => {
      expect(getByText('ako')).toBeTruthy();
    });

    // Отвечаем правильно
    const input = getByTestId('answer-input');
    const submitButton = getByTestId('submit-button');

    await act(async () => {
      fireEvent.changeText(input, 'я');
      fireEvent.press(submitButton);
    });

    // Проверяем, что использовалась предзагруженная карточка
    await waitFor(() => {
      expect(getByText('asa')).toBeTruthy();
    });

    expect(mockPreloader.getPreloadedCard).toHaveBeenCalled();
  });

  test('Fallback к обычной загрузке при отсутствии предзагруженной карточки', async () => {
    // Предзагрузчик возвращает null
    mockPreloader.getPreloadedCard.mockReturnValue(null);
    mockPreloader.waitForPreload.mockResolvedValue(null);

    const { getByTestId, getByText } = render(<TrainingScreen />);

    await waitFor(() => {
      expect(getByText('ako')).toBeTruthy();
    });

    const input = getByTestId('answer-input');
    const submitButton = getByTestId('submit-button');

    await act(async () => {
      fireEvent.changeText(input, 'я');
      fireEvent.press(submitButton);
    });

    // Проверяем, что был вызван fallback к обычной загрузке
    await waitFor(() => {
      expect(mockAPI.getNextCard).toHaveBeenCalledWith(
        expect.any(String),
        expect.any(String), 
        expect.any(String),
        false // preload = false
      );
    });
  });

  test('Интервальная механика работает через несколько циклов', async () => {
    const { getByTestId, getByText } = render(<TrainingScreen />);

    // Цикл 1: Неправильно → Правильно
    await waitFor(() => expect(getByText('ako')).toBeTruthy());
    
    const input = getByTestId('answer-input');
    const submitButton = getByTestId('submit-button');

    // Неправильный ответ
    await act(async () => {
      fireEvent.changeText(input, 'wrong');
      fireEvent.press(submitButton);
    });

    await waitFor(() => expect(getByText('ako')).toBeTruthy());

    // Правильный ответ
    await act(async () => {
      fireEvent.changeText(input, 'я');
      fireEvent.press(submitButton);
    });

    // Должно появиться новое слово
    await waitFor(() => expect(getByText('ikaw')).toBeTruthy());

    // Продвигаем время на 6 минут
    mockAPI.advanceTime(6);

    // Цикл 2: Слово должно вернуться через интервал
    await act(async () => {
      fireEvent.changeText(input, 'ты');
      fireEvent.press(submitButton);
    });

    // Проверяем, что слово 'ako' может вернуться (время прошло)
    const nextCard = await mockAPI.getNextCard('user1', 'ru', 'cb', false);
    expect(['ako', 'asa']).toContain(nextCard.word); // Может быть ako или новое слово
  });
});

// Утилиты для запуска тестов
export const runTrainingScreenTests = () => {
  console.log('🧪 Запуск интеграционных тестов TrainingScreen...');
  
  // В реальном приложении здесь был бы Jest runner
  // Для демонстрации просто логируем
  console.log('✅ Тесты настроены и готовы к запуску');
  console.log('📝 Для запуска используйте: npm test или yarn test');
};
