/**
 * Быстрый тест: Проблема повторения выученных слов
 * 
 * Упрощенная версия для быстрой проверки:
 * 1. Выучить 3-5 слов
 * 2. Проверить, что они не повторяются в следующих 10 карточках
 */

const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);

const API_BASE = 'http://localhost:8000';
const USER_ID = '6848235d12259195693cb594';

async function makeApiCall(endpoint, method = 'GET', data = null) {
  let curlCommand = `curl -s -X ${method}`;
  
  if (data) {
    curlCommand += ` -H "Content-Type: application/json" -d '${JSON.stringify(data)}'`;
  }
  
  curlCommand += ` "${API_BASE}${endpoint}"`;
  
  console.log(`🌐 API: ${method} ${endpoint}`);
  
  try {
    const { stdout, stderr } = await execAsync(curlCommand);
    const response = JSON.parse(stdout);
    return response;
  } catch (error) {
    console.error(`❌ API Error:`, error.message);
    throw error;
  }
}

async function getNextCard() {
  return await makeApiCall(`/api/spaced/next?user_id=${USER_ID}&target_lang=cb`);
}

async function submitAnswer(wordId, isCorrect) {
  return await makeApiCall(`/api/spaced/${wordId}/response`, 'POST', {
    user_id: USER_ID,
    is_correct: isCorrect,
    response_time: 2.5,
    used_hint: false
  });
}

async function quickLearnedWordsTest() {
  console.log('🧪 === БЫСТРЫЙ ТЕСТ: Повторение выученных слов ===\n');
  
  const learnedWords = new Set();
  const targetLearnedCount = 3;
  let cardsChecked = 0;
  
  try {
    // Фаза 1: Выучить несколько слов
    console.log('📚 ФАЗА 1: Выучиваем слова');
    
    while (learnedWords.size < targetLearnedCount && cardsChecked < 10) {
      cardsChecked++;
      console.log(`\n📝 Карточка ${cardsChecked}:`);
      
      const card = await getNextCard();
      console.log(`   Слово: ${card.word} (${card.translation})`);
      console.log(`   ID: ${card.word_id}`);
      
      const result = await submitAnswer(card.word_id, true);
      console.log(`   Результат: interval_level=${result.interval_level}, is_learned=${result.is_learned}`);
      
      if (result.is_learned && result.interval_level === 15) {
        learnedWords.add(card.word_id);
        console.log(`   🎉 ВЫУЧЕНО! Всего выучено: ${learnedWords.size}`);
      }
    }
    
    console.log(`\n📊 ИТОГ ФАЗЫ 1: Выучено ${learnedWords.size} слов`);
    console.log(`📋 Выученные слова:`, Array.from(learnedWords));
    
    if (learnedWords.size === 0) {
      console.log('⚠️ Не удалось выучить ни одного слова. Тест невозможен.');
      return { success: false, reason: 'no_learned_words' };
    }
    
    // Фаза 2: Проверить, что выученные слова не повторяются
    console.log('\n🔍 ФАЗА 2: Проверяем повторения');
    
    const checkCount = 10;
    let repetitionFound = false;
    
    for (let i = 1; i <= checkCount; i++) {
      console.log(`\n🔍 Проверка ${i}/${checkCount}:`);
      
      const card = await getNextCard();
      console.log(`   Слово: ${card.word} (${card.translation})`);
      console.log(`   ID: ${card.word_id}`);
      
      if (learnedWords.has(card.word_id)) {
        console.log(`🚨 КРИТИЧЕСКАЯ ПРОБЛЕМА: Выученное слово повторяется!`);
        console.log(`   Слово: ${card.word}`);
        console.log(`   ID: ${card.word_id}`);
        console.log(`   interval_level: ${card.interval_level}`);
        console.log(`   is_learned: ${card.is_learned}`);
        repetitionFound = true;
        break;
      } else {
        console.log(`   ✅ Новое слово (не повторение)`);
      }
      
      // Отвечаем неправильно, чтобы не выучить это слово
      await submitAnswer(card.word_id, false);
    }
    
    // Финальный результат
    console.log('\n📋 === ФИНАЛЬНЫЙ РЕЗУЛЬТАТ ===');
    
    if (repetitionFound) {
      console.log('❌ ТЕСТ ПРОВАЛЕН: Обнаружено повторение выученных слов!');
      return { 
        success: false, 
        repetition_detected: true,
        learned_count: learnedWords.size,
        checks_performed: checkCount
      };
    } else {
      console.log('✅ ТЕСТ ПРОШЕЛ: Выученные слова не повторяются');
      return { 
        success: true, 
        repetition_detected: false,
        learned_count: learnedWords.size,
        checks_performed: checkCount
      };
    }
    
  } catch (error) {
    console.error('💥 ОШИБКА В ТЕСТЕ:', error.message);
    return { success: false, error: error.message };
  }
}

// Запуск теста
async function runTest() {
  console.log('🚀 Запуск быстрого теста повторения выученных слов\n');
  
  const result = await quickLearnedWordsTest();
  
  console.log('\n📊 РЕЗУЛЬТАТ ТЕСТА:');
  console.log(JSON.stringify(result, null, 2));
  
  if (result.success) {
    console.log('\n🎉 ТЕСТ ПРОШЕЛ');
    process.exit(0);
  } else {
    console.log('\n❌ ТЕСТ ПРОВАЛЕН');
    process.exit(1);
  }
}

// Запуск
runTest().catch(error => {
  console.error('💥 Неожиданная ошибка:', error);
  process.exit(1);
});
