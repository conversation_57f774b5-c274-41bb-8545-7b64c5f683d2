/**
 * E2E тест: Проблема повторения выученных слов
 * 
 * Проверяет КРИТИЧЕСКУЮ проблему:
 * 1. Пользователь выучивает слово (interval_level = 15, is_learned = true)
 * 2. Слово НЕ должно появляться в очереди снова
 * 3. Если появляется, то с interval_level = 16 (что неправильно)
 * 
 * Этот тест покажет:
 * - Правильно ли работает фильтрация выученных слов
 * - Не попадают ли выученные слова обратно в очередь
 * - Корректно ли работает логика "слово выучено навсегда"
 */

const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);

const API_BASE = 'http://localhost:8000';
const USER_ID = '6848235d12259195693cb594';

class LearnedWordsRepetitionTester {
  constructor() {
    this.testResults = [];
    this.learnedWords = new Set(); // Отслеживаем выученные слова
    this.cardHistory = [];
    this.repetitionDetected = false;
  }

  // Логирование с детальной информацией
  log(message, data = null) {
    const timestamp = new Date().toISOString();
    const logEntry = { timestamp, message, data };
    console.log(`🔍 [${timestamp}] ${message}`);
    if (data) {
      console.log('📊 Данные:', JSON.stringify(data, null, 2));
    }
    this.testResults.push(logEntry);
  }

  // API вызов с логированием
  async makeApiCall(endpoint, method = 'GET', data = null) {
    let curlCommand = `curl -s -X ${method}`;
    
    if (data) {
      curlCommand += ` -H "Content-Type: application/json" -d '${JSON.stringify(data)}'`;
    }
    
    curlCommand += ` "${API_BASE}${endpoint}"`;
    
    this.log(`🌐 API Call: ${method} ${endpoint}`);
    if (data) {
      this.log(`📤 Data:`, data);
    }
    
    try {
      const { stdout, stderr } = await execAsync(curlCommand);
      
      if (stderr) {
        this.log(`⚠️ Stderr:`, stderr);
      }
      
      const response = JSON.parse(stdout);
      this.log(`📥 Response:`, response);
      return response;
    } catch (error) {
      this.log(`❌ API Error:`, error.message);
      throw error;
    }
  }

  // Получить следующую карточку
  async getNextCard() {
    return await this.makeApiCall(`/api/spaced/next?user_id=${USER_ID}&target_lang=cb`);
  }

  // Отправить ответ
  async submitAnswer(wordId, isCorrect) {
    return await this.makeApiCall(`/api/spaced/${wordId}/response`, 'POST', {
      user_id: USER_ID,
      is_correct: isCorrect,
      response_time: 2.5,
      used_hint: false
    });
  }

  // Выучить слово (ответить правильно)
  async learnWord(card) {
    this.log(`📚 ВЫУЧИВАЕМ СЛОВО: ${card.word} (${card.translation})`);
    
    const result = await this.submitAnswer(card.word_id, true);
    
    this.log(`📊 РЕЗУЛЬТАТ ОБУЧЕНИЯ:`, {
      word: card.word,
      word_id: card.word_id,
      interval_level: result.interval_level,
      is_learned: result.is_learned,
      is_correct: result.is_correct
    });

    // Проверяем, стало ли слово выученным
    if (result.is_learned && result.interval_level === 15) {
      this.log(`🎉 СЛОВО ВЫУЧЕНО: ${card.word} (interval_level=15, is_learned=true)`);
      this.learnedWords.add(card.word_id);
      return { success: true, learned: true, result };
    } else {
      this.log(`⚠️ СЛОВО НЕ ВЫУЧЕНО: ${card.word}`, {
        interval_level: result.interval_level,
        is_learned: result.is_learned
      });
      return { success: true, learned: false, result };
    }
  }

  // Проверить, не повторяется ли выученное слово
  checkForRepetition(card) {
    if (this.learnedWords.has(card.word_id)) {
      this.log(`🚨 КРИТИЧЕСКАЯ ПРОБЛЕМА: Выученное слово повторяется!`, {
        word: card.word,
        word_id: card.word_id,
        translation: card.translation,
        is_new: card.is_new,
        is_learned: card.is_learned,
        interval_level: card.interval_level
      });
      this.repetitionDetected = true;
      return true;
    }
    return false;
  }

  // Основной тест повторения выученных слов
  async runLearnedWordsRepetitionTest() {
    this.log('🧪 === НАЧАЛО E2E ТЕСТА: Повторение выученных слов ===');
    
    try {
      const maxCards = 15; // Проверим 15 карточек
      let learnedCount = 0;
      let repetitionFound = false;

      this.log(`📝 ПЛАН: Выучить несколько слов и проверить, что они не повторяются`);
      this.log(`📊 ЦЕЛЬ: Проверить ${maxCards} карточек`);

      for (let i = 1; i <= maxCards; i++) {
        this.log(`\n📝 === КАРТОЧКА ${i}/${maxCards} ===`);
        
        // Получаем следующую карточку
        const card = await this.getNextCard();
        
        this.log(`📊 КАРТОЧКА ${i}:`, {
          word: card.word,
          translation: card.translation,
          word_id: card.word_id,
          is_new: card.is_new,
          is_learned: card.is_learned,
          interval_level: card.interval_level
        });

        // Сохраняем в историю
        this.cardHistory.push({
          index: i,
          ...card,
          timestamp: new Date().toISOString()
        });

        // КРИТИЧЕСКАЯ ПРОВЕРКА: Не повторяется ли выученное слово?
        if (this.checkForRepetition(card)) {
          repetitionFound = true;
          this.log(`❌ ТЕСТ ПРОВАЛЕН: Обнаружено повторение выученного слова на карточке ${i}`);
          break;
        }

        // Пытаемся выучить слово
        const learnResult = await this.learnWord(card);
        
        if (learnResult.learned) {
          learnedCount++;
          this.log(`📈 ПРОГРЕСС: Выучено слов: ${learnedCount}`);
        }

        // Небольшая пауза между карточками
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Финальная проверка
      this.log(`\n📋 === ФИНАЛЬНЫЕ РЕЗУЛЬТАТЫ ===`);
      this.log(`📊 СТАТИСТИКА:`, {
        total_cards_checked: this.cardHistory.length,
        learned_words_count: learnedCount,
        learned_word_ids: Array.from(this.learnedWords),
        repetition_detected: this.repetitionDetected
      });

      if (this.repetitionDetected) {
        this.log(`❌ ТЕСТ ПРОВАЛЕН: Обнаружено повторение выученных слов`);
        return { success: false, repetition_detected: true };
      } else {
        this.log(`✅ ТЕСТ ПРОШЕЛ: Выученные слова не повторяются`);
        return { success: true, repetition_detected: false };
      }

    } catch (error) {
      this.log('❌ КРИТИЧЕСКАЯ ОШИБКА В ТЕСТЕ', { error: error.message, stack: error.stack });
      throw error;
    }
  }

  // Дополнительный тест: Принудительная проверка конкретного выученного слова
  async testSpecificLearnedWord() {
    this.log('\n🔍 === ДОПОЛНИТЕЛЬНЫЙ ТЕСТ: Принудительная проверка ===');
    
    if (this.learnedWords.size === 0) {
      this.log('⚠️ Нет выученных слов для проверки');
      return { success: true, message: 'no_learned_words' };
    }

    const learnedWordId = Array.from(this.learnedWords)[0];
    this.log(`🎯 ПРОВЕРЯЕМ ВЫУЧЕННОЕ СЛОВО: ${learnedWordId}`);

    // Получаем несколько карточек подряд
    for (let i = 1; i <= 10; i++) {
      const card = await this.getNextCard();
      
      if (card.word_id === learnedWordId) {
        this.log(`🚨 КРИТИЧЕСКАЯ ПРОБЛЕМА: Выученное слово появилось снова!`, {
          attempt: i,
          word: card.word,
          word_id: card.word_id,
          interval_level: card.interval_level,
          is_learned: card.is_learned
        });
        return { success: false, repetition_confirmed: true };
      }
    }

    this.log(`✅ ДОПОЛНИТЕЛЬНЫЙ ТЕСТ ПРОШЕЛ: Выученное слово не появляется`);
    return { success: true, repetition_confirmed: false };
  }

  // Сохранить результаты теста
  saveTestResults() {
    const results = {
      test_name: 'Learned Words Repetition E2E Test',
      timestamp: new Date().toISOString(),
      user_id: USER_ID,
      api_base_url: API_BASE,
      learned_words: Array.from(this.learnedWords),
      repetition_detected: this.repetitionDetected,
      card_history: this.cardHistory,
      test_log: this.testResults
    };

    console.log('\n💾 РЕЗУЛЬТАТЫ ТЕСТА (для сохранения):');
    console.log(JSON.stringify(results, null, 2));
    
    return results;
  }
}

// Запуск теста
async function runLearnedWordsRepetitionTest() {
  console.log('🚀 Запуск E2E теста повторения выученных слов');
  
  const tester = new LearnedWordsRepetitionTester();
  
  try {
    // Основной тест
    const mainResult = await tester.runLearnedWordsRepetitionTest();
    
    // Дополнительный тест
    const additionalResult = await tester.testSpecificLearnedWord();
    
    // Сохраняем результаты
    tester.saveTestResults();
    
    const finalResult = {
      main_test: mainResult,
      additional_test: additionalResult,
      overall_success: mainResult.success && additionalResult.success
    };

    if (finalResult.overall_success) {
      console.log('\n🎉 E2E ТЕСТ ПРОШЕЛ: Выученные слова не повторяются');
    } else {
      console.log('\n❌ E2E ТЕСТ ПРОВАЛЕН: Обнаружено повторение выученных слов!');
    }
    
    return finalResult;
  } catch (error) {
    console.error('\n💥 E2E ТЕСТ ЗАВЕРШИЛСЯ С ОШИБКОЙ:', error.message);
    tester.saveTestResults();
    throw error;
  }
}

// Экспорт для использования в других тестах
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    LearnedWordsRepetitionTester,
    runLearnedWordsRepetitionTest
  };
} else {
  // Запуск в браузере
  runLearnedWordsRepetitionTest();
}
