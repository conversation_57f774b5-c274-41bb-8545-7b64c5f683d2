/**
 * ПРОВЕРКА ДОСТУПНЫХ СЛОВ
 * 
 * Проверяет, есть ли слова для изучения в базе данных
 */

const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);

const API_BASE = 'http://localhost:8000';
const USER_ID = '6848235d12259195693cb594';

async function makeApiCall(endpoint, method = 'GET', data = null) {
  let curlCommand = `curl -s -X ${method}`;
  
  if (data) {
    curlCommand += ` -H "Content-Type: application/json" -d '${JSON.stringify(data)}'`;
  }
  
  curlCommand += ` "${API_BASE}${endpoint}"`;
  
  console.log(`🌐 API: ${method} ${endpoint}`);
  
  try {
    const { stdout, stderr } = await execAsync(curlCommand);
    const response = JSON.parse(stdout);
    return response;
  } catch (error) {
    console.error(`❌ API Error:`, error.message);
    throw error;
  }
}

async function checkWordsAvailable() {
  console.log('🔍 === ПРОВЕРКА ДОСТУПНЫХ СЛОВ ===\n');
  
  try {
    // Проверяем статистику пользователя
    console.log('📊 Проверяем статистику пользователя');
    const stats = await makeApiCall(`/api/spaced/api/user/statistics?user_id=${USER_ID}&target_lang=cb`);
    
    console.log(`📊 СТАТИСТИКА:`, stats);
    
    // Пытаемся получить карточку
    console.log('\n📝 Пытаемся получить карточку');
    const card = await makeApiCall(`/api/spaced/next?user_id=${USER_ID}&target_lang=cb`);
    
    console.log(`📊 КАРТОЧКА:`, card);
    
    // Проверяем предзагрузку
    console.log('\n🔄 Пытаемся получить предзагрузку');
    const preload = await makeApiCall(`/api/spaced/next?user_id=${USER_ID}&target_lang=cb&preload=true`);
    
    console.log(`📊 ПРЕДЗАГРУЗКА:`, preload);
    
    return { success: true, stats, card, preload };
    
  } catch (error) {
    console.error('💥 ОШИБКА ПРИ ПРОВЕРКЕ:', error.message);
    return { success: false, error: error.message };
  }
}

// Запуск
checkWordsAvailable()
  .then(result => {
    if (result.success) {
      console.log('\n🎉 ПРОВЕРКА ЗАВЕРШЕНА');
      
      if (result.card && result.card.word) {
        console.log('✅ Карточки доступны для изучения');
      } else {
        console.log('❌ Карточки НЕ доступны для изучения');
        console.log('Причина:', result.card?.detail || 'Неизвестно');
      }
      
      process.exit(0);
    } else {
      console.log('\n❌ ПРОВЕРКА ПРОВАЛЕНА');
      process.exit(1);
    }
  });
