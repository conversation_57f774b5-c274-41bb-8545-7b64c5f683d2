/**
 * КРИТИЧЕСКИЙ ТЕСТ: Повторное появление выученных слов
 * 
 * Воспроизводит проблему из логов:
 * 1. Слово "mama" выучено с первого раза (уровень -1 → 15)
 * 2. Слово появляется снова как новое (уровень -1)
 * 3. Слово получает уровень 16 (превышение максимума)
 * 
 * Цель: Найти и исправить проблему с фильтрацией выученных слов
 */

const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);

const API_BASE = 'http://localhost:8000';
const USER_ID = '6848235d12259195693cb594';

async function makeApiCall(endpoint, method = 'GET', data = null) {
  let curlCommand = `curl -s -X ${method}`;
  
  if (data) {
    curlCommand += ` -H "Content-Type: application/json" -d '${JSON.stringify(data)}'`;
  }
  
  curlCommand += ` "${API_BASE}${endpoint}"`;
  
  console.log(`🌐 API: ${method} ${endpoint}`);
  
  try {
    const { stdout, stderr } = await execAsync(curlCommand);
    const response = JSON.parse(stdout);
    return response;
  } catch (error) {
    console.error(`❌ API Error:`, error.message);
    throw error;
  }
}

async function getNextCard() {
  return await makeApiCall(`/api/spaced/next?user_id=${USER_ID}&target_lang=cb`);
}

async function submitAnswer(wordId, isCorrect) {
  return await makeApiCall(`/api/spaced/${wordId}/response`, 'POST', {
    user_id: USER_ID,
    is_correct: isCorrect,
    response_time: 2.5,
    used_hint: false
  });
}

async function learnedWordReappearanceTest() {
  console.log('🧪 === КРИТИЧЕСКИЙ ТЕСТ: Повторное появление выученных слов ===\n');
  
  const learnedWords = new Map(); // word_id -> details
  const seenWords = new Set();
  let problemDetected = false;
  
  try {
    console.log('📝 ПЛАН: Выучить слова и отследить их повторное появление');
    
    // Фаза 1: Выучить несколько слов и отследить их
    for (let round = 1; round <= 10; round++) {
      console.log(`\n🔄 === РАУНД ${round} ===`);
      
      // Получаем карточку
      const card = await getNextCard();
      
      console.log(`📊 КАРТОЧКА:`, {
        word: card.word,
        translation: card.translation,
        word_id: card.word_id,
        is_new: card.is_new
      });

      // КРИТИЧЕСКАЯ ПРОВЕРКА: Не появилось ли выученное слово снова?
      if (learnedWords.has(card.word_id)) {
        const learnedInfo = learnedWords.get(card.word_id);
        console.log(`🚨 КРИТИЧЕСКАЯ ПРОБЛЕМА: Выученное слово появилось снова!`);
        console.log(`   Слово: ${card.word}`);
        console.log(`   ID: ${card.word_id}`);
        console.log(`   Выучено в раунде: ${learnedInfo.round}`);
        console.log(`   Предыдущий уровень: ${learnedInfo.final_level}`);
        console.log(`   Текущее состояние: is_new=${card.is_new}`);
        
        problemDetected = true;
        
        // Попробуем ответить правильно и посмотрим, что произойдет
        console.log(`🔍 ТЕСТИРУЕМ: Отвечаем правильно на повторно появившееся слово`);
        const result = await submitAnswer(card.word_id, true);
        
        console.log(`📊 РЕЗУЛЬТАТ:`, {
          interval_level: result.interval_level,
          is_learned: result.is_learned,
          is_correct: result.is_correct
        });
        
        if (result.interval_level > 15) {
          console.log(`🚨 ПРЕВЫШЕНИЕ МАКСИМУМА: interval_level = ${result.interval_level} > 15`);
        }
        
        return {
          success: false,
          problem_detected: true,
          problem_word: card.word,
          problem_word_id: card.word_id,
          learned_in_round: learnedInfo.round,
          reappeared_in_round: round,
          final_level: result.interval_level
        };
      }

      // Отмечаем, что видели это слово
      if (seenWords.has(card.word_id)) {
        console.log(`🔄 ПОВТОРЕНИЕ: Слово "${card.word}" уже встречалось (нормально для неправильных ответов)`);
      } else {
        seenWords.add(card.word_id);
        console.log(`🆕 НОВОЕ СЛОВО: "${card.word}" встречается впервые`);
      }

      // Отвечаем правильно с первого раза
      console.log(`📝 Отвечаем ПРАВИЛЬНО на "${card.word}"`);
      const result = await submitAnswer(card.word_id, true);
      
      console.log(`📊 РЕЗУЛЬТАТ:`, {
        interval_level: result.interval_level,
        is_learned: result.is_learned,
        is_correct: result.is_correct,
        next_review: result.next_review
      });

      // Проверяем, выучено ли слово
      if (result.is_learned && result.interval_level === 15) {
        console.log(`🎉 СЛОВО ВЫУЧЕНО: "${card.word}" (уровень 15)`);
        learnedWords.set(card.word_id, {
          word: card.word,
          round: round,
          final_level: result.interval_level,
          next_review: result.next_review
        });
      } else if (result.interval_level > 15) {
        console.log(`🚨 ПРЕВЫШЕНИЕ МАКСИМУМА: "${card.word}" получил уровень ${result.interval_level}`);
        problemDetected = true;
      } else {
        console.log(`📈 ПРОГРЕСС: "${card.word}" на уровне ${result.interval_level}`);
      }

      // Небольшая пауза
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Финальная проверка
    console.log(`\n📋 === ФИНАЛЬНЫЕ РЕЗУЛЬТАТЫ ===`);
    console.log(`📊 СТАТИСТИКА:`, {
      total_rounds: 10,
      learned_words_count: learnedWords.size,
      seen_words_count: seenWords.size,
      problem_detected: problemDetected
    });

    console.log(`📚 ВЫУЧЕННЫЕ СЛОВА:`);
    for (const [wordId, info] of learnedWords) {
      console.log(`   ${info.word} (раунд ${info.round}, уровень ${info.final_level})`);
    }

    if (problemDetected) {
      console.log(`❌ ТЕСТ ПРОВАЛЕН: Обнаружены проблемы с выученными словами`);
      return { success: false, problem_detected: true };
    } else {
      console.log(`✅ ТЕСТ ПРОШЕЛ: Выученные слова не появляются повторно`);
      return { success: true, problem_detected: false };
    }

  } catch (error) {
    console.error('💥 ОШИБКА В ТЕСТЕ:', error.message);
    return { success: false, error: error.message };
  }
}

// Дополнительный тест: Принудительная проверка конкретного выученного слова
async function testSpecificWord(targetWord = 'mama') {
  console.log(`\n🎯 === ТЕСТ КОНКРЕТНОГО СЛОВА: "${targetWord}" ===`);
  
  let foundTargetWord = false;
  let targetWordDetails = null;
  
  // Ищем целевое слово в течение 20 попыток
  for (let i = 1; i <= 20; i++) {
    console.log(`\n🔍 Попытка ${i}/20:`);
    
    const card = await getNextCard();
    console.log(`   Слово: ${card.word} (${card.translation})`);
    
    if (card.word === targetWord) {
      console.log(`🎯 НАЙДЕНО ЦЕЛЕВОЕ СЛОВО: "${targetWord}"`);
      foundTargetWord = true;
      targetWordDetails = card;
      
      // Отвечаем правильно
      const result = await submitAnswer(card.word_id, true);
      console.log(`📊 РЕЗУЛЬТАТ:`, result);
      
      if (result.interval_level > 15) {
        console.log(`🚨 ПРОБЛЕМА: Уровень ${result.interval_level} превышает максимум 15`);
        return { success: false, level_exceeded: true, level: result.interval_level };
      }
      
      break;
    } else {
      // Отвечаем неправильно, чтобы не выучить
      await submitAnswer(card.word_id, false);
    }
  }
  
  if (!foundTargetWord) {
    console.log(`⚠️ Целевое слово "${targetWord}" не найдено за 20 попыток`);
    return { success: true, word_not_found: true };
  }
  
  return { success: true, word_found: true, details: targetWordDetails };
}

// Запуск тестов
async function runTest() {
  console.log('🚀 Запуск критического теста повторного появления выученных слов\n');
  
  try {
    // Основной тест
    const mainResult = await learnedWordReappearanceTest();
    
    // Дополнительный тест конкретного слова
    const specificResult = await testSpecificWord('mama');
    
    const finalResult = {
      main_test: mainResult,
      specific_test: specificResult,
      overall_success: mainResult.success && specificResult.success
    };

    console.log('\n📊 ФИНАЛЬНЫЙ РЕЗУЛЬТАТ:');
    console.log(JSON.stringify(finalResult, null, 2));
    
    if (finalResult.overall_success) {
      console.log('\n🎉 ВСЕ ТЕСТЫ ПРОШЛИ: Проблема не воспроизведена');
      process.exit(0);
    } else {
      console.log('\n❌ ТЕСТЫ ПРОВАЛЕНЫ: Проблема воспроизведена!');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('\n💥 КРИТИЧЕСКАЯ ОШИБКА:', error.message);
    process.exit(1);
  }
}

// Запуск
runTest();
