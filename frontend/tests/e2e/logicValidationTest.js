/**
 * ТЕСТ ВАЛИДАЦИИ ЛОГИКИ ИСКЛЮЧЕНИЯ СЛОВ
 * 
 * Проверяет, что наше исправление не сломало логику исключения слов в процессе изучения
 */

const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);
const { clearUserProgress } = require('./clearUserProgress');

const API_BASE = 'http://localhost:8000';
const USER_ID = '6848235d12259195693cb594';

class LogicValidator {
  constructor() {
    this.wordsInProgress = new Map(); // Слова в процессе изучения
    this.learnedWords = new Map(); // Выученные слова
  }

  log(message, data = null) {
    const timestamp = new Date().toISOString();
    console.log(`🔍 [${timestamp}] ${message}`);
    if (data) {
      console.log('📊 Данные:', JSON.stringify(data, null, 2));
    }
  }

  async makeApiCall(endpoint, method = 'GET', data = null) {
    let curlCommand = `curl -s -X ${method}`;
    
    if (data) {
      curlCommand += ` -H "Content-Type: application/json" -d '${JSON.stringify(data)}'`;
    }
    
    curlCommand += ` "${API_BASE}${endpoint}"`;
    
    this.log(`🌐 API: ${method} ${endpoint}`);
    
    try {
      const { stdout, stderr } = await execAsync(curlCommand);
      const response = JSON.parse(stdout);
      this.log(`📥 Response:`, response);
      return response;
    } catch (error) {
      this.log(`❌ API Error:`, error.message);
      throw error;
    }
  }

  async getCard(excludeCardId = null, preload = false) {
    let endpoint = `/api/spaced/next?user_id=${USER_ID}&target_lang=cb`;
    if (excludeCardId) {
      endpoint += `&exclude_card_id=${excludeCardId}`;
    }
    if (preload) {
      endpoint += `&preload=true`;
    }
    
    return await this.makeApiCall(endpoint);
  }

  async submitAnswer(wordId, isCorrect) {
    return await this.makeApiCall(`/api/spaced/${wordId}/response`, 'POST', {
      user_id: USER_ID,
      is_correct: isCorrect,
      response_time: 2.5,
      used_hint: false
    });
  }

  // Тест 1: Проверяем, что слова в процессе изучения не появляются снова
  async testWordsInProgressExclusion() {
    this.log('\n🧪 === ТЕСТ 1: Исключение слов в процессе изучения ===');
    
    // Получаем первое слово
    const card1 = await this.getCard();
    this.log(`📝 Получили первое слово: ${card1.word} (${card1.word_id})`);
    
    // Отвечаем НЕПРАВИЛЬНО (слово остается в процессе изучения)
    const answer1 = await this.submitAnswer(card1.word_id, false);
    this.log(`📝 Ответили неправильно. Уровень: ${answer1.interval_level}, выучено: ${answer1.is_learned}`);
    
    this.wordsInProgress.set(card1.word_id, {
      word: card1.word,
      level: answer1.interval_level,
      learned: answer1.is_learned
    });
    
    // Получаем следующие 5 слов и проверяем, что первое не появляется
    for (let i = 2; i <= 6; i++) {
      const card = await this.getCard();
      this.log(`📝 Получили слово ${i}: ${card.word} (${card.word_id})`);
      
      // КРИТИЧЕСКАЯ ПРОВЕРКА: Не появилось ли слово в процессе изучения?
      if (this.wordsInProgress.has(card.word_id)) {
        this.log(`🚨 ПРОБЛЕМА: Слово в процессе изучения появилось снова!`, {
          word: card.word,
          word_id: card.word_id,
          previous_state: this.wordsInProgress.get(card.word_id)
        });
        return { success: false, problem: 'words_in_progress_reappeared' };
      }
      
      // Отвечаем неправильно, чтобы слово осталось в процессе
      const answer = await this.submitAnswer(card.word_id, false);
      this.wordsInProgress.set(card.word_id, {
        word: card.word,
        level: answer.interval_level,
        learned: answer.is_learned
      });
    }
    
    this.log(`✅ Тест 1 прошел: Слова в процессе изучения не появляются снова`);
    return { success: true };
  }

  // Тест 2: Проверяем, что выученные слова не появляются в обычных запросах
  async testLearnedWordsExclusion() {
    this.log('\n🧪 === ТЕСТ 2: Исключение выученных слов ===');
    
    // Получаем слово и выучиваем его
    const card = await this.getCard();
    this.log(`📝 Получили слово для изучения: ${card.word} (${card.word_id})`);
    
    // Отвечаем правильно до выучивания
    const answer = await this.submitAnswer(card.word_id, true);
    this.log(`📝 Ответили правильно. Уровень: ${answer.interval_level}, выучено: ${answer.is_learned}`);
    
    if (answer.is_learned) {
      this.learnedWords.set(card.word_id, {
        word: card.word,
        level: answer.interval_level
      });
      
      // Получаем следующие 5 слов и проверяем, что выученное не появляется
      for (let i = 1; i <= 5; i++) {
        const nextCard = await this.getCard();
        this.log(`📝 Получили следующее слово ${i}: ${nextCard.word} (${nextCard.word_id})`);
        
        // КРИТИЧЕСКАЯ ПРОВЕРКА: Не появилось ли выученное слово?
        if (this.learnedWords.has(nextCard.word_id)) {
          this.log(`🚨 ПРОБЛЕМА: Выученное слово появилось снова!`, {
            word: nextCard.word,
            word_id: nextCard.word_id,
            learned_state: this.learnedWords.get(nextCard.word_id)
          });
          return { success: false, problem: 'learned_word_reappeared' };
        }
        
        // Отвечаем неправильно, чтобы не выучить все слова
        await this.submitAnswer(nextCard.word_id, false);
      }
    }
    
    this.log(`✅ Тест 2 прошел: Выученные слова не появляются в обычных запросах`);
    return { success: true };
  }

  // Тест 3: Проверяем предзагрузку
  async testPreloadLogic() {
    this.log('\n🧪 === ТЕСТ 3: Логика предзагрузки ===');
    
    // Получаем основную карточку
    const mainCard = await this.getCard();
    this.log(`📝 Основная карточка: ${mainCard.word} (${mainCard.word_id})`);
    
    // Предзагружаем следующую (исключая текущую)
    const preloadCard = await this.getCard(mainCard.word_id, true);
    this.log(`🔄 Предзагруженная карточка: ${preloadCard.word} (${preloadCard.word_id})`);
    
    // Проверяем, что предзагруженная карточка отличается от основной
    if (mainCard.word_id === preloadCard.word_id) {
      this.log(`🚨 ПРОБЛЕМА: Предзагруженная карточка совпадает с основной!`);
      return { success: false, problem: 'preload_same_as_main' };
    }
    
    this.log(`✅ Тест 3 прошел: Предзагрузка работает корректно`);
    return { success: true };
  }

  async runAllTests() {
    this.log('🧪 === ВАЛИДАЦИЯ ЛОГИКИ ИСКЛЮЧЕНИЯ СЛОВ ===');
    this.log('🎯 Цель: Убедиться, что исправление не сломало логику исключения');
    this.log('');
    
    // Очищаем прогресс перед тестом
    this.log('🧹 Очистка прогресса пользователя...');
    const clearResult = await clearUserProgress(USER_ID, true);
    this.log(`✅ Прогресс очищен (удалено записей: ${clearResult.deleted_count})`);
    
    try {
      // Тест 1: Слова в процессе изучения
      const test1 = await this.testWordsInProgressExclusion();
      if (!test1.success) {
        return test1;
      }
      
      // Тест 2: Выученные слова
      const test2 = await this.testLearnedWordsExclusion();
      if (!test2.success) {
        return test2;
      }
      
      // Тест 3: Предзагрузка
      const test3 = await this.testPreloadLogic();
      if (!test3.success) {
        return test3;
      }
      
      // Очищаем прогресс после теста
      this.log('\n🧹 Очистка прогресса после теста...');
      const clearResultEnd = await clearUserProgress(USER_ID, true);
      this.log(`✅ Прогресс очищен (удалено записей: ${clearResultEnd.deleted_count})`);
      
      return { success: true, all_tests_passed: true };
      
    } catch (error) {
      this.log('💥 КРИТИЧЕСКАЯ ОШИБКА:', error.message);
      
      // Очищаем прогресс даже при ошибке
      try {
        const clearResultError = await clearUserProgress(USER_ID, true);
        this.log(`✅ Прогресс очищен после ошибки (удалено записей: ${clearResultError.deleted_count})`);
      } catch (clearError) {
        this.log('❌ Не удалось очистить прогресс после ошибки');
      }
      
      return { success: false, error: error.message };
    }
  }
}

// Запуск теста
async function runLogicValidationTest() {
  console.log('🚀 Запуск теста валидации логики\n');
  
  const validator = new LogicValidator();
  
  const result = await validator.runAllTests();
  
  console.log('\n📊 ФИНАЛЬНЫЙ РЕЗУЛЬТАТ:');
  console.log(JSON.stringify(result, null, 2));
  
  if (result.success) {
    console.log('\n🎉 ВСЕ ТЕСТЫ ПРОШЛИ: Логика исключения работает корректно');
    process.exit(0);
  } else {
    console.log('\n❌ ТЕСТЫ ПРОВАЛЕНЫ: Обнаружена проблема в логике исключения');
    process.exit(1);
  }
}

// Запуск
runLogicValidationTest();
