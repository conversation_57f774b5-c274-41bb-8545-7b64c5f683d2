/**
 * ДИАГНОСТИЧЕСКИЙ ТЕСТ: Проверка состояния базы данных
 * 
 * Проверяет:
 * 1. Правильно ли сохраняется is_learned в базе данных
 * 2. Правильно ли работает фильтрация выученных слов
 * 3. Есть ли проблемы с кэшированием
 */

const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);

const API_BASE = 'http://localhost:8000';
const USER_ID = '6848235d12259195693cb594';

async function makeApiCall(endpoint, method = 'GET', data = null) {
  let curlCommand = `curl -s -X ${method}`;
  
  if (data) {
    curlCommand += ` -H "Content-Type: application/json" -d '${JSON.stringify(data)}'`;
  }
  
  curlCommand += ` "${API_BASE}${endpoint}"`;
  
  console.log(`🌐 API: ${method} ${endpoint}`);
  
  try {
    const { stdout, stderr } = await execAsync(curlCommand);
    const response = JSON.parse(stdout);
    return response;
  } catch (error) {
    console.error(`❌ API Error:`, error.message);
    throw error;
  }
}

async function getNextCard() {
  return await makeApiCall(`/api/spaced/next?user_id=${USER_ID}&target_lang=cb`);
}

async function getPreloadCard(excludeId) {
  return await makeApiCall(`/api/spaced/next?user_id=${USER_ID}&target_lang=cb&preload=true&exclude_card_id=${excludeId}`);
}

async function submitAnswer(wordId, isCorrect) {
  return await makeApiCall(`/api/spaced/${wordId}/response`, 'POST', {
    user_id: USER_ID,
    is_correct: isCorrect,
    response_time: 2.5,
    used_hint: false
  });
}

// Проверить состояние конкретного слова в базе данных
async function checkWordInDatabase(wordId) {
  console.log(`🔍 ПРОВЕРКА СЛОВА В БАЗЕ: ${wordId}`);
  
  try {
    // Делаем запрос к API для получения информации о слове
    const response = await makeApiCall(`/api/debug/word/${wordId}?user_id=${USER_ID}`);
    console.log(`📊 СОСТОЯНИЕ В БАЗЕ:`, response);
    return response;
  } catch (error) {
    console.log(`⚠️ Не удалось получить состояние слова из базы: ${error.message}`);
    return null;
  }
}

async function databaseDiagnosticTest() {
  console.log('🧪 === ДИАГНОСТИЧЕСКИЙ ТЕСТ БАЗЫ ДАННЫХ ===\n');
  
  try {
    // Шаг 1: Получаем первую карточку
    console.log('📝 ШАГ 1: Получение первой карточки');
    const card1 = await getNextCard();
    
    console.log(`📊 КАРТОЧКА 1:`, {
      word: card1.word,
      word_id: card1.word_id,
      is_new: card1.is_new
    });

    // Шаг 2: Выучиваем первую карточку
    console.log('\n📝 ШАГ 2: Выучиваем первую карточку');
    const result1 = await submitAnswer(card1.word_id, true);
    
    console.log(`📊 РЕЗУЛЬТАТ:`, {
      interval_level: result1.interval_level,
      is_learned: result1.is_learned,
      next_review: result1.next_review
    });

    // Проверяем, что ответ обработался корректно
    if (result1.interval_level === undefined || result1.is_learned === undefined) {
      console.log(`❌ ОШИБКА API: Получены undefined значения`);
      console.log(`   Полный ответ:`, result1);
      return { success: false, reason: 'api_error_undefined_values' };
    }

    if (result1.is_learned && result1.interval_level === 15) {
      console.log(`🎉 СЛОВО ВЫУЧЕНО: "${card1.word}"`);
    } else {
      console.log(`⚠️ СЛОВО НЕ ВЫУЧЕНО: "${card1.word}"`);
      console.log(`   interval_level: ${result1.interval_level}, is_learned: ${result1.is_learned}`);
      return { success: false, reason: 'word_not_learned' };
    }

    // Шаг 3: Проверяем состояние в базе данных
    console.log('\n📝 ШАГ 3: Проверка состояния в базе данных');
    const dbState = await checkWordInDatabase(card1.word_id);
    
    if (dbState) {
      console.log(`📊 БАЗА ДАННЫХ:`, {
        is_learned: dbState.is_learned,
        interval_level: dbState.interval_level,
        next_review: dbState.next_review
      });
    }

    // Шаг 4: Получаем вторую карточку (основной запрос)
    console.log('\n📝 ШАГ 4: Получение второй карточки (основной запрос)');
    const card2 = await getNextCard();
    
    console.log(`📊 КАРТОЧКА 2:`, {
      word: card2.word,
      word_id: card2.word_id,
      is_new: card2.is_new
    });

    // ПРОВЕРКА: Не появилось ли выученное слово снова?
    if (card2.word_id === card1.word_id) {
      console.log(`🚨 ПРОБЛЕМА: Выученное слово появилось в основном запросе!`);
      return { 
        success: false, 
        problem: 'learned_word_in_main_request',
        learned_word: card1.word,
        learned_word_id: card1.word_id
      };
    } else {
      console.log(`✅ ОСНОВНОЙ ЗАПРОС: Выученное слово НЕ появилось`);
    }

    // Шаг 5: Проверяем предзагрузку
    console.log('\n📝 ШАГ 5: Проверка предзагрузки');
    const preloadCard = await getPreloadCard(card2.word_id);
    
    console.log(`📊 ПРЕДЗАГРУЗКА:`, {
      word: preloadCard.word,
      word_id: preloadCard.word_id,
      is_new: preloadCard.is_new
    });

    // КРИТИЧЕСКАЯ ПРОВЕРКА: Не появилось ли выученное слово в предзагрузке?
    if (preloadCard.word_id === card1.word_id) {
      console.log(`🚨 КРИТИЧЕСКАЯ ПРОБЛЕМА: Выученное слово появилось в предзагрузке!`);
      
      // Дополнительная диагностика
      console.log('\n🔍 ДОПОЛНИТЕЛЬНАЯ ДИАГНОСТИКА:');
      console.log(`   Выученное слово: ${card1.word} (${card1.word_id})`);
      console.log(`   Исключенное слово: ${card2.word} (${card2.word_id})`);
      console.log(`   Предзагруженное слово: ${preloadCard.word} (${preloadCard.word_id})`);
      
      // Проверяем состояние в базе еще раз
      const dbStateAfter = await checkWordInDatabase(card1.word_id);
      if (dbStateAfter) {
        console.log(`📊 СОСТОЯНИЕ В БАЗЕ ПОСЛЕ:`, dbStateAfter);
      }
      
      return { 
        success: false, 
        problem: 'learned_word_in_preload',
        learned_word: card1.word,
        learned_word_id: card1.word_id,
        excluded_word_id: card2.word_id,
        db_state_before: dbState,
        db_state_after: dbStateAfter
      };
    } else {
      console.log(`✅ ПРЕДЗАГРУЗКА: Выученное слово НЕ появилось`);
    }

    // Шаг 6: Выучиваем вторую карточку и проверяем снова
    console.log('\n📝 ШАГ 6: Выучиваем вторую карточку');
    const result2 = await submitAnswer(card2.word_id, true);
    
    if (result2.is_learned && result2.interval_level === 15) {
      console.log(`🎉 ВТОРАЯ КАРТОЧКА ВЫУЧЕНА: "${card2.word}"`);
      
      // Проверяем предзагрузку после выучивания второй карточки
      console.log('\n📝 ШАГ 7: Проверка предзагрузки после выучивания второй карточки');
      const preloadCard2 = await getPreloadCard(card2.word_id);
      
      console.log(`📊 ПРЕДЗАГРУЗКА 2:`, {
        word: preloadCard2.word,
        word_id: preloadCard2.word_id,
        is_new: preloadCard2.is_new
      });

      // Проверяем, не появилось ли одно из выученных слов
      if (preloadCard2.word_id === card1.word_id || preloadCard2.word_id === card2.word_id) {
        console.log(`🚨 ПРОБЛЕМА: Выученное слово появилось в предзагрузке после выучивания второй карточки!`);
        return { 
          success: false, 
          problem: 'learned_word_in_preload_after_second',
          learned_words: [card1.word_id, card2.word_id],
          preloaded_word_id: preloadCard2.word_id
        };
      } else {
        console.log(`✅ ПРЕДЗАГРУЗКА 2: Выученные слова НЕ появились`);
      }
    }

    console.log('\n📋 === ДИАГНОСТИКА ЗАВЕРШЕНА ===');
    console.log('✅ ТЕСТ ПРОШЕЛ: Проблема не обнаружена в диагностическом тесте');
    
    return { success: true, problem: 'none' };

  } catch (error) {
    console.error('💥 ОШИБКА В ДИАГНОСТИКЕ:', error.message);
    return { success: false, error: error.message };
  }
}

// Запуск теста
async function runTest() {
  console.log('🚀 Запуск диагностического теста базы данных\n');
  
  const result = await databaseDiagnosticTest();
  
  console.log('\n📊 РЕЗУЛЬТАТ ДИАГНОСТИКИ:');
  console.log(JSON.stringify(result, null, 2));
  
  if (result.success) {
    console.log('\n🎉 ДИАГНОСТИКА ПРОШЛА: Проблема не обнаружена');
    process.exit(0);
  } else {
    console.log('\n❌ ДИАГНОСТИКА ПРОВАЛЕНА: Проблема обнаружена!');
    process.exit(1);
  }
}

// Запуск
runTest();
