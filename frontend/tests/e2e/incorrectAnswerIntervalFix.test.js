/**
 * РЕГРЕССИОННЫЙ ТЕСТ для проблемы №12:
 * Неправильный ответ не уменьшает интервал в DEBUG информации
 * 
 * ПРОБЛЕМА: При неправильном ответе на существующее слово с интервалом > 0
 * интервал не уменьшался в DEBUG информации (только is_new устанавливался в false)
 * 
 * ИСПРАВЛЕНИЕ: Добавлена логика уменьшения интервала в блоке else if (!isAnswerCorrect)
 * 
 * Дата создания: 19 июня 2025
 * Статус: ✅ ПРОХОДИТ (проблема решена)
 */

// Симулятор логики фронтенда
class FrontendLogicSimulator {
  constructor() {
    this.currentCard = null;
    this.userInput = '';
    this.isCorrect = null;
    this.usedHelpButton = false;
    this.canGoForward = false;
  }

  // Симуляция функции checkAnswer из TrainingScreen.tsx (ИСПРАВЛЕННАЯ ВЕРСИЯ)
  checkAnswer() {
    if (!this.currentCard) {
      throw new Error('No current card');
    }

    // Проверяем правильность ответа
    const correctAnswers = this.currentCard.target_word?.examples?.[0]?.correct_answers || [];
    const isAnswerCorrect = correctAnswers.some(
      answer => answer.toLowerCase() === this.userInput.trim().toLowerCase()
    );

    // Определяем, что отправлять на сервер
    const serverIsCorrect = this.usedHelpButton ? false : isAnswerCorrect;

    // UI показывает правильный ответ (зеленый инпут)
    this.isCorrect = isAnswerCorrect;
    this.canGoForward = true;

    // Логика повторения при использовании подсказки
    const shouldAdvanceToNext = isAnswerCorrect && !this.usedHelpButton;

    console.log('🔍 DEBUG checkAnswer:', {
      userInput: this.userInput.trim(),
      isAnswerCorrect,
      usedHelpButton: this.usedHelpButton,
      serverIsCorrect,
      shouldAdvanceToNext
    });

    // МГНОВЕННОЕ обновление полосок для новых слов с правильным ответом
    const isNewCard = this.currentCard.is_new ?? true;
    const isNewWordCorrect = isNewCard && isAnswerCorrect && !this.usedHelpButton;

    console.log('🔍 DEBUG: Анализ логики обновления полосок:', {
      word: this.currentCard.word,
      current_interval_level: this.currentCard.interval_level,
      current_is_new: this.currentCard.is_new,
      current_is_learned: this.currentCard.is_learned,
      isNewCard,
      isAnswerCorrect,
      usedHelpButton: this.usedHelpButton,
      isNewWordCorrect,
      willUpdateToLevel15: isNewWordCorrect,
      willUpdateAsIncorrect: this.usedHelpButton,
      willUpdateInterval: isAnswerCorrect && !isNewWordCorrect && !this.usedHelpButton,
      willDecreaseInterval: !isAnswerCorrect || this.usedHelpButton
    });

    if (isNewWordCorrect) {
      console.log('🎉 Новое слово отвечено правильно! МГНОВЕННО обновляем полоски прогресса...');
      
      // Обновляем карточку с прогрессом
      this.currentCard = {
        ...this.currentCard,
        is_new: false,
        is_learned: true,
        interval_level: 15
      };
    } else if (this.usedHelpButton) {
      // 🔧 ИСПРАВЛЕНИЕ ПРОБЛЕМЫ №1: При использовании подсказки обновляем полоски как при неправильном ответе
      console.log('🔄 Использована подсказка - обновляем полоски как при неправильном ответе');

      const currentIntervalLevel = this.currentCard.interval_level ?? -1;
      let newIntervalLevel;

      // Логика уменьшения интервала при неправильном ответе
      if (currentIntervalLevel <= 0) {
        newIntervalLevel = -1; // Сбрасываем до минимума
      } else {
        newIntervalLevel = Math.max(-1, currentIntervalLevel - 1); // Уменьшаем на 1
      }

      this.currentCard = {
        ...this.currentCard,
        is_new: false,           // 🔧 КРИТИЧЕСКОЕ: Больше не новое слово!
        is_learned: false,
        interval_level: newIntervalLevel
      };
    } else if (!isAnswerCorrect) {
      // 🔧 ИСПРАВЛЕНИЕ ПРОБЛЕМЫ №10 + №12: При неправильном ответе обновляем is_new в false И уменьшаем интервал
      console.log('❌ Неправильный ответ - обновляем is_new в false и уменьшаем интервал');

      const currentIntervalLevel = this.currentCard.interval_level ?? -1;
      let newIntervalLevel;

      // Логика уменьшения интервала при неправильном ответе (та же что и для подсказок)
      if (currentIntervalLevel <= 0) {
        newIntervalLevel = -1; // Сбрасываем до минимума
      } else {
        newIntervalLevel = Math.max(-1, currentIntervalLevel - 1); // Уменьшаем на 1
      }

      console.log('🔧 DEBUG: Обновляем currentCard после неправильного ответа:', {
        word: this.currentCard.word,
        old_interval_level: currentIntervalLevel,
        new_interval_level: newIntervalLevel,
        old_is_learned: this.currentCard.is_learned,
        new_is_learned: false,
        old_is_new: this.currentCard.is_new,
        new_is_new: false // 🔧 КРИТИЧЕСКОЕ: Больше не новое!
      });

      this.currentCard = {
        ...this.currentCard,
        is_new: false,           // 🔧 КРИТИЧЕСКОЕ: Больше не новое слово!
        is_learned: false,       // Больше не выучено
        interval_level: newIntervalLevel
      };
    } else if (isAnswerCorrect && !isNewWordCorrect) {
      // 🔧 ИСПРАВЛЕНИЕ ПРОБЛЕМЫ №11: Обычный правильный ответ на существующее слово
      console.log('✅ Правильный ответ на существующее слово - обновляем интервал');

      const currentIntervalLevel = this.currentCard.interval_level ?? -1;
      let newIntervalLevel;

      // Логика увеличения интервала при правильном ответе
      if (currentIntervalLevel >= 14) {
        newIntervalLevel = 15; // Максимальный уровень
      } else {
        newIntervalLevel = currentIntervalLevel + 1; // Увеличиваем на 1
      }

      this.currentCard = {
        ...this.currentCard,
        is_new: false,           // Больше не новое слово
        is_learned: newIntervalLevel >= 15, // Выучено если достигли максимального уровня
        interval_level: newIntervalLevel
      };
    }

    return {
      isAnswerCorrect,
      serverIsCorrect,
      shouldAdvanceToNext,
      isNewWordCorrect
    };
  }

  // Сброс состояния для новой карточки
  reset() {
    this.userInput = '';
    this.isCorrect = null;
    this.usedHelpButton = false;
    this.canGoForward = false;
  }

  // Установка новой карточки
  setCard(card) {
    this.currentCard = card;
    this.reset();
  }
}

// Основной тест
async function testIncorrectAnswerIntervalFix() {
  console.log('\n🧪 === ТЕСТ: Исправление уменьшения интервала при неправильных ответах ===\n');

  const frontend = new FrontendLogicSimulator();

  try {
    // СЦЕНАРИЙ 1: Слово с интервалом 1 → неправильный ответ → интервал 0
    console.log('📋 СЦЕНАРИЙ 1: Слово с интервалом 1 → неправильный ответ → интервал 0');
    const cardData1 = {
      word: "oo",
      target_word: {
        word: "да",
        examples: [{
          correct_answers: ["да", "oo"]
        }]
      },
      is_new: false,        // НЕ новое слово
      is_learned: false,
      interval_level: 1     // Интервал 1
    };
    frontend.setCard(cardData1);

    console.log(`📱 Карточка: "${cardData1.word}" (${cardData1.target_word?.word})`);
    console.log(`📊 Начальное состояние: interval_level=${cardData1.interval_level}`);

    frontend.userInput = "неправильно"; // Неправильный ответ
    const result1 = frontend.checkAnswer();

    console.log(`🔍 Результат: interval_level=${frontend.currentCard.interval_level}`);

    // ТЕСТ 1: Интервал должен уменьшиться с 1 до 0
    if (frontend.currentCard.interval_level === 0) {
      console.log('✅ ТЕСТ 1 ПРОЙДЕН: Интервал уменьшился с 1 до 0');
    } else {
      console.log('❌ ТЕСТ 1 ПРОВАЛЕН: Интервал не уменьшился правильно');
      throw new Error(`Ожидался интервал 0, получен ${frontend.currentCard.interval_level}`);
    }

    // СЦЕНАРИЙ 2: Слово с интервалом 0 → неправильный ответ → интервал -1
    console.log('\n📋 СЦЕНАРИЙ 2: Слово с интервалом 0 → неправильный ответ → интервал -1');
    const cardData2 = {
      word: "unsa",
      target_word: {
        word: "что",
        examples: [{
          correct_answers: ["что", "unsa"]
        }]
      },
      is_new: false,
      is_learned: false,
      interval_level: 0     // Интервал 0
    };
    frontend.setCard(cardData2);

    console.log(`📱 Карточка: "${cardData2.word}" (${cardData2.target_word?.word})`);
    console.log(`📊 Начальное состояние: interval_level=${cardData2.interval_level}`);

    frontend.userInput = "неправильно"; // Неправильный ответ
    const result2 = frontend.checkAnswer();

    console.log(`🔍 Результат: interval_level=${frontend.currentCard.interval_level}`);

    // ТЕСТ 2: Интервал должен уменьшиться с 0 до -1
    if (frontend.currentCard.interval_level === -1) {
      console.log('✅ ТЕСТ 2 ПРОЙДЕН: Интервал уменьшился с 0 до -1');
    } else {
      console.log('❌ ТЕСТ 2 ПРОВАЛЕН: Интервал не уменьшился правильно');
      throw new Error(`Ожидался интервал -1, получен ${frontend.currentCard.interval_level}`);
    }

    // СЦЕНАРИЙ 3: Слово с интервалом -1 → неправильный ответ → интервал -1 (минимум)
    console.log('\n📋 СЦЕНАРИЙ 3: Слово с интервалом -1 → неправильный ответ → интервал -1 (минимум)');
    const cardData3 = {
      word: "ikaw",
      target_word: {
        word: "ты",
        examples: [{
          correct_answers: ["ты", "ikaw"]
        }]
      },
      is_new: false,
      is_learned: false,
      interval_level: -1    // Интервал -1 (минимум)
    };
    frontend.setCard(cardData3);

    console.log(`📱 Карточка: "${cardData3.word}" (${cardData3.target_word?.word})`);
    console.log(`📊 Начальное состояние: interval_level=${cardData3.interval_level}`);

    frontend.userInput = "неправильно"; // Неправильный ответ
    const result3 = frontend.checkAnswer();

    console.log(`🔍 Результат: interval_level=${frontend.currentCard.interval_level}`);

    // ТЕСТ 3: Интервал должен остаться -1 (минимум)
    if (frontend.currentCard.interval_level === -1) {
      console.log('✅ ТЕСТ 3 ПРОЙДЕН: Интервал остался -1 (минимум)');
    } else {
      console.log('❌ ТЕСТ 3 ПРОВАЛЕН: Интервал изменился неправильно');
      throw new Error(`Ожидался интервал -1, получен ${frontend.currentCard.interval_level}`);
    }

    // СЦЕНАРИЙ 4: После неправильного ответа → правильный ответ → интервал увеличивается
    console.log('\n📋 СЦЕНАРИЙ 4: После неправильного ответа → правильный ответ → интервал увеличивается');
    
    // Используем карточку с интервалом 0 (после первого неправильного ответа)
    frontend.setCard({
      word: "oo",
      target_word: {
        word: "да",
        examples: [{
          correct_answers: ["да", "oo"]
        }]
      },
      is_new: false,
      is_learned: false,
      interval_level: 0     // После неправильного ответа
    });

    frontend.userInput = "да"; // Правильный ответ
    const result4 = frontend.checkAnswer();

    console.log(`🔍 Результат: interval_level=${frontend.currentCard.interval_level}`);

    // ТЕСТ 4: Интервал должен увеличиться с 0 до 1
    if (frontend.currentCard.interval_level === 1) {
      console.log('✅ ТЕСТ 4 ПРОЙДЕН: После правильного ответа интервал увеличился с 0 до 1');
    } else {
      console.log('❌ ТЕСТ 4 ПРОВАЛЕН: Интервал не увеличился правильно');
      throw new Error(`Ожидался интервал 1, получен ${frontend.currentCard.interval_level}`);
    }

    console.log('\n🎉 === ВСЕ ТЕСТЫ ПРОЙДЕНЫ! ПРОБЛЕМА ИСПРАВЛЕНА! ===');
    console.log('\n📝 Резюме исправления:');
    console.log('  - ✅ Неправильный ответ уменьшает интервал правильно');
    console.log('  - ✅ Интервал 1 → неправильно → интервал 0');
    console.log('  - ✅ Интервал 0 → неправильно → интервал -1');
    console.log('  - ✅ Интервал -1 → неправильно → интервал -1 (минимум)');
    console.log('  - ✅ После неправильного ответа правильный ответ увеличивает интервал');
    console.log('\n🔧 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: добавлена логика уменьшения интервала в блоке else if (!isAnswerCorrect)');
    console.log('📋 Файл: frontend/screens/TrainingScreen.tsx (строки 786-824)');
    console.log('📅 Дата решения: 19 июня 2025');
    console.log('🧪 Статус: РЕГРЕССИОННЫЙ ТЕСТ ПРОХОДИТ');

    return true;

  } catch (error) {
    console.error(`\n❌ ТЕСТ ПРОВАЛЕН: ${error.message}`);
    return false;
  }
}

// Запуск теста
if (require.main === module) {
  testIncorrectAnswerIntervalFix()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Критическая ошибка:', error);
      process.exit(1);
    });
}

module.exports = { testIncorrectAnswerIntervalFix };
