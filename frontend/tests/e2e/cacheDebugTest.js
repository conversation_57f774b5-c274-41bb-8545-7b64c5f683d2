/**
 * ТЕСТ ОТЛАДКИ КЭША: Минимальный тест для анализа логов кэша
 * 
 * Выполняет простую последовательность:
 * 1. Получить карточку
 * 2. Выучить карточку  
 * 3. Попытаться получить ту же карточку снова
 */

const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);

const API_BASE = 'http://localhost:8000';
const USER_ID = '6848235d12259195693cb594';

async function makeApiCall(endpoint, method = 'GET', data = null) {
  let curlCommand = `curl -s -X ${method}`;
  
  if (data) {
    curlCommand += ` -H "Content-Type: application/json" -d '${JSON.stringify(data)}'`;
  }
  
  curlCommand += ` "${API_BASE}${endpoint}"`;
  
  console.log(`🌐 API: ${method} ${endpoint}`);
  
  try {
    const { stdout, stderr } = await execAsync(curlCommand);
    const response = JSON.parse(stdout);
    return response;
  } catch (error) {
    console.error(`❌ API Error:`, error.message);
    throw error;
  }
}

async function cacheDebugTest() {
  console.log('🧪 === ТЕСТ ОТЛАДКИ КЭША ===\n');
  
  try {
    // Шаг 1: Получаем карточку
    console.log('📝 ШАГ 1: Получение карточки');
    const card1 = await makeApiCall(`/api/spaced/next?user_id=${USER_ID}&target_lang=cb`);
    
    console.log(`📊 КАРТОЧКА:`, {
      word: card1.word,
      word_id: card1.word_id,
      is_new: card1.is_new
    });

    // Шаг 2: Выучиваем карточку
    console.log('\n📝 ШАГ 2: Выучиваем карточку');
    const result = await makeApiCall(`/api/spaced/${card1.word_id}/response`, 'POST', {
      user_id: USER_ID,
      is_correct: true,
      response_time: 2.5,
      used_hint: false
    });
    
    console.log(`📊 РЕЗУЛЬТАТ:`, {
      interval_level: result.interval_level,
      is_learned: result.is_learned,
      next_review: result.next_review
    });

    if (result.is_learned && result.interval_level === 15) {
      console.log(`🎉 СЛОВО ВЫУЧЕНО: "${card1.word}"`);
    } else {
      console.log(`⚠️ СЛОВО НЕ ВЫУЧЕНО: "${card1.word}"`);
      return { success: false, reason: 'word_not_learned' };
    }

    // Шаг 3: Пауза для обработки кэша
    console.log('\n📝 ШАГ 3: Пауза 2 секунды для обработки кэша');
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Шаг 4: Пытаемся получить ту же карточку снова (основной запрос)
    console.log('\n📝 ШАГ 4: Попытка получить ту же карточку (основной запрос)');
    const card2 = await makeApiCall(`/api/spaced/next?user_id=${USER_ID}&target_lang=cb`);
    
    console.log(`📊 КАРТОЧКА 2:`, {
      word: card2.word,
      word_id: card2.word_id,
      is_new: card2.is_new
    });

    // ПРОВЕРКА: Не появилось ли выученное слово снова?
    if (card2.word_id === card1.word_id) {
      console.log(`🚨 ПРОБЛЕМА: Выученное слово появилось в основном запросе!`);
      return { 
        success: false, 
        problem: 'learned_word_in_main_request',
        learned_word: card1.word,
        learned_word_id: card1.word_id
      };
    } else {
      console.log(`✅ ОСНОВНОЙ ЗАПРОС: Выученное слово НЕ появилось`);
    }

    // Шаг 5: Проверяем предзагрузку
    console.log('\n📝 ШАГ 5: Проверка предзагрузки');
    const preloadCard = await makeApiCall(`/api/spaced/next?user_id=${USER_ID}&target_lang=cb&preload=true&exclude_card_id=${card2.word_id}`);
    
    console.log(`📊 ПРЕДЗАГРУЗКА:`, {
      word: preloadCard.word,
      word_id: preloadCard.word_id,
      is_new: preloadCard.is_new
    });

    // КРИТИЧЕСКАЯ ПРОВЕРКА: Не появилось ли выученное слово в предзагрузке?
    if (preloadCard.word_id === card1.word_id) {
      console.log(`🚨 КРИТИЧЕСКАЯ ПРОБЛЕМА: Выученное слово появилось в предзагрузке!`);
      
      return { 
        success: false, 
        problem: 'learned_word_in_preload',
        learned_word: card1.word,
        learned_word_id: card1.word_id,
        excluded_word_id: card2.word_id
      };
    } else {
      console.log(`✅ ПРЕДЗАГРУЗКА: Выученное слово НЕ появилось`);
    }

    console.log('\n📋 === ТЕСТ ЗАВЕРШЕН ===');
    console.log('✅ ТЕСТ ПРОШЕЛ: Проблема не обнаружена в простом тесте');
    
    return { success: true, problem: 'none' };

  } catch (error) {
    console.error('💥 ОШИБКА В ТЕСТЕ:', error.message);
    return { success: false, error: error.message };
  }
}

// Запуск теста
async function runTest() {
  console.log('🚀 Запуск теста отладки кэша\n');
  
  const result = await cacheDebugTest();
  
  console.log('\n📊 РЕЗУЛЬТАТ ТЕСТА:');
  console.log(JSON.stringify(result, null, 2));
  
  if (result.success) {
    console.log('\n🎉 ТЕСТ ПРОШЕЛ: Проблема не обнаружена');
    process.exit(0);
  } else {
    console.log('\n❌ ТЕСТ ПРОВАЛЕН: Проблема обнаружена!');
    process.exit(1);
  }
}

// Запуск
runTest();
