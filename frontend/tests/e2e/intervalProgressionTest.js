/**
 * Тест прогрессии интервалов
 * 
 * Проверяет правильную работу системы интервального повторения:
 * 1. Неправильный ответ → слово повторяется
 * 2. Правильный ответ → interval_level = 0, короткий интервал
 * 3. Следующий правильный ответ → interval_level увеличивается
 * 4. Проверяем, что интервалы растут правильно
 */

const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);

const API_BASE = 'http://localhost:8000';
const USER_ID = '6848235d12259195693cb594';

async function makeApiCall(endpoint, method = 'GET', data = null) {
  let curlCommand = `curl -s -X ${method}`;
  
  if (data) {
    curlCommand += ` -H "Content-Type: application/json" -d '${JSON.stringify(data)}'`;
  }
  
  curlCommand += ` "${API_BASE}${endpoint}"`;
  
  console.log(`🌐 API: ${method} ${endpoint}`);
  
  try {
    const { stdout, stderr } = await execAsync(curlCommand);
    const response = JSON.parse(stdout);
    return response;
  } catch (error) {
    console.error(`❌ API Error:`, error.message);
    throw error;
  }
}

async function getNextCard() {
  return await makeApiCall(`/api/spaced/next?user_id=${USER_ID}&target_lang=cb`);
}

async function submitAnswer(wordId, isCorrect) {
  return await makeApiCall(`/api/spaced/${wordId}/response`, 'POST', {
    user_id: USER_ID,
    is_correct: isCorrect,
    response_time: 2.5,
    used_hint: false
  });
}

async function intervalProgressionTest() {
  console.log('🧪 === ТЕСТ: Прогрессия интервалов ===\n');
  
  try {
    // Шаг 1: Получаем новую карточку
    console.log('📝 ШАГ 1: Получение новой карточки');
    const card = await getNextCard();
    
    console.log(`📊 КАРТОЧКА:`, {
      word: card.word,
      translation: card.translation,
      word_id: card.word_id,
      is_new: card.is_new
    });

    // Шаг 2: Отвечаем неправильно
    console.log('\n📝 ШАГ 2: Отвечаем НЕПРАВИЛЬНО');
    const wrongResult = await submitAnswer(card.word_id, false);
    
    console.log(`📊 РЕЗУЛЬТАТ НЕПРАВИЛЬНОГО ОТВЕТА:`, {
      is_correct: wrongResult.is_correct,
      interval_level: wrongResult.interval_level,
      is_learned: wrongResult.is_learned,
      next_review: wrongResult.next_review
    });

    // Шаг 3: Проверяем, что слово повторяется
    console.log('\n📝 ШАГ 3: Проверяем повторение после неправильного ответа');
    const repeatedCard = await getNextCard();
    
    if (repeatedCard.word_id === card.word_id) {
      console.log('✅ ПРАВИЛЬНО: Слово повторяется после неправильного ответа');
      console.log(`   Слово: ${repeatedCard.word} (то же самое)`);
    } else {
      console.log('⚠️ НЕОЖИДАННО: Получили другое слово');
      console.log(`   Ожидали: ${card.word} (${card.word_id})`);
      console.log(`   Получили: ${repeatedCard.word} (${repeatedCard.word_id})`);
    }

    // Шаг 4: Отвечаем правильно
    console.log('\n📝 ШАГ 4: Отвечаем ПРАВИЛЬНО');
    const correctResult = await submitAnswer(card.word_id, true);
    
    console.log(`📊 РЕЗУЛЬТАТ ПРАВИЛЬНОГО ОТВЕТА:`, {
      is_correct: correctResult.is_correct,
      interval_level: correctResult.interval_level,
      is_learned: correctResult.is_learned,
      next_review: correctResult.next_review
    });

    // Проверяем интервал после правильного ответа
    if (correctResult.interval_level === 0) {
      console.log('✅ ПРАВИЛЬНО: interval_level = 0 после первого правильного ответа');
    } else {
      console.log(`⚠️ НЕОЖИДАННО: interval_level = ${correctResult.interval_level}, ожидали 0`);
    }

    // Шаг 5: Проверяем, что слово не появляется сразу (есть интервал)
    console.log('\n📝 ШАГ 5: Проверяем, что слово не появляется сразу');
    
    let foundSameWord = false;
    for (let i = 1; i <= 5; i++) {
      const nextCard = await getNextCard();
      console.log(`   Попытка ${i}: ${nextCard.word} (${nextCard.word_id})`);
      
      if (nextCard.word_id === card.word_id) {
        console.log(`⚠️ Слово появилось снова на попытке ${i}`);
        foundSameWord = true;
        break;
      }
      
      // Отвечаем неправильно, чтобы не выучить эти слова
      await submitAnswer(nextCard.word_id, false);
    }

    if (!foundSameWord) {
      console.log('✅ ПРАВИЛЬНО: Слово не появляется сразу (есть интервал)');
    }

    // Шаг 6: Симулируем прохождение времени и проверяем повторение
    console.log('\n📝 ШАГ 6: Проверяем логику интервалов');
    
    // Парсим время следующего повторения
    const nextReviewTime = new Date(correctResult.next_review);
    const currentTime = new Date();
    const intervalMinutes = Math.round((nextReviewTime - currentTime) / (1000 * 60));
    
    console.log(`📊 ИНТЕРВАЛ:`, {
      current_time: currentTime.toISOString(),
      next_review: nextReviewTime.toISOString(),
      interval_minutes: intervalMinutes,
      interval_level: correctResult.interval_level
    });

    // Проверяем, что интервал разумный для уровня 0 (учитываем временные зоны)
    const absIntervalMinutes = Math.abs(intervalMinutes);
    if (absIntervalMinutes >= 0 && absIntervalMinutes <= 480) { // 480 минут = 8 часов (учитываем временные зоны)
      console.log(`✅ ПРАВИЛЬНО: Интервал ${absIntervalMinutes} минут разумен для уровня 0`);
      if (intervalMinutes < 0) {
        console.log(`⚠️ ВНИМАНИЕ: Отрицательный интервал указывает на проблему с временными зонами`);
      }
    } else {
      console.log(`⚠️ НЕОЖИДАННО: Интервал ${intervalMinutes} минут кажется неправильным`);
    }

    // Финальная проверка
    console.log('\n📋 === ФИНАЛЬНАЯ ПРОВЕРКА ===');
    
    const testResults = {
      word_repeated_after_wrong: repeatedCard.word_id === card.word_id,
      interval_level_after_correct: correctResult.interval_level,
      interval_minutes: intervalMinutes,
      word_not_immediate: !foundSameWord
    };

    console.log('📊 РЕЗУЛЬТАТЫ ТЕСТОВ:', testResults);

    // Определяем успешность теста (учитываем временные зоны)
    const absIntervalForTest = Math.abs(testResults.interval_minutes);
    const success = (
      testResults.word_repeated_after_wrong &&
      testResults.interval_level_after_correct === 0 &&
      absIntervalForTest >= 0 &&
      absIntervalForTest <= 480 && // 480 минут = 8 часов (учитываем временные зоны)
      testResults.word_not_immediate
    );

    if (success) {
      console.log('✅ ТЕСТ ПРОШЕЛ: Система интервального повторения работает правильно');
      return { success: true, details: testResults };
    } else {
      console.log('❌ ТЕСТ ПРОВАЛЕН: Обнаружены проблемы в системе интервалов');
      return { success: false, details: testResults };
    }

  } catch (error) {
    console.error('💥 ОШИБКА В ТЕСТЕ:', error.message);
    return { success: false, error: error.message };
  }
}

// Запуск теста
async function runTest() {
  console.log('🚀 Запуск теста прогрессии интервалов\n');
  
  const result = await intervalProgressionTest();
  
  console.log('\n📊 ФИНАЛЬНЫЙ РЕЗУЛЬТАТ:');
  console.log(JSON.stringify(result, null, 2));
  
  if (result.success) {
    console.log('\n🎉 ТЕСТ ПРОШЕЛ: Интервалы работают правильно');
    process.exit(0);
  } else {
    console.log('\n❌ ТЕСТ ПРОВАЛЕН: Проблемы с интервалами');
    process.exit(1);
  }
}

// Запуск
runTest().catch(error => {
  console.error('💥 Неожиданная ошибка:', error);
  process.exit(1);
});
