/**
 * E2E тест: Проблема наследования зеленых полосок
 *
 * 🔍 ДЕТАЛЬНЫЙ АНАЛИЗ ФРОНТЕНДА:
 * TrainingScreen.tsx использует следующую логику:
 * 1. Состояние: currentCard (Card | null) - текущая карточка
 * 2. Предзагрузка: useCardPreloader() - предзагружает следующую карточку
 * 3. Полоски: getProgressBarsInfo(card) - рассчитывает полоски на основе card.interval_level, is_new, is_learned
 * 4. Последовательность: fetchNewCard() → setCurrentCard() → getProgressBarsInfo() → отображение
 * 5. Переход: checkAnswer() → animateCardOutWithPreload() → использует предзагруженную карточку
 *
 * КРИТИЧЕСКАЯ ТОЧКА: При переходе к новой карточке состояние должно полностью очищаться
 * перед установкой новой карточки (строки 324-340 в TrainingScreen.tsx)
 *
 * Проверяет РЕАЛЬНОЕ взаимодействие frontend + backend:
 * 1. Получаем новую карточку от реального API
 * 2. Отвечаем правильно (слово становится выученным)
 * 3. Получаем следующую карточку
 * 4. КРИТИЧЕСКИ ВАЖНО: Проверяем, что новая карточка НЕ наследует зеленые полоски
 *
 * Этот тест покажет, где именно происходит наследование:
 * - В данных с сервера?
 * - В состоянии React?
 * - В логике отображения?
 */

class GreenBarsInheritanceE2ETester {
  constructor(apiBaseUrl = 'http://localhost:8000') {
    this.apiBaseUrl = apiBaseUrl;
    this.userId = '6848235d12259195693cb594'; // Используем реального тестового пользователя
    this.testResults = [];
    this.cardHistory = [];
  }

  // Логирование с детальной информацией
  log(message, data = null) {
    const timestamp = new Date().toISOString();
    const logEntry = { timestamp, message, data };
    console.log(`🔍 [${timestamp}] ${message}`);
    if (data) {
      console.log('📊 Данные:', JSON.stringify(data, null, 2));
    }
    this.testResults.push(logEntry);
  }

  // Получить следующую карточку с детальным логированием
  async getNextCard() {
    this.log('🌐 API: Запрос следующей карточки');
    
    const params = new URLSearchParams({
      user_id: this.userId,
      target_lang: 'cb'
    });

    try {
      const response = await fetch(`${this.apiBaseUrl}/api/spaced/next?${params}`);
      
      if (!response.ok) {
        throw new Error(`API Error: ${response.status} ${response.statusText}`);
      }
      
      const cardData = await response.json();
      
      this.log('✅ API: Получена карточка', {
        word_id: cardData.word_id,
        word: cardData.word,
        translation: cardData.translation,
        is_new: cardData.is_new,
        is_learned: cardData.is_learned,
        interval_level: cardData.interval_level,
        response_headers: Object.fromEntries(response.headers.entries())
      });

      // Сохраняем в историю
      this.cardHistory.push({
        ...cardData,
        timestamp: new Date().toISOString(),
        action: 'received'
      });

      return cardData;
    } catch (error) {
      this.log('❌ API: Ошибка получения карточки', { error: error.message });
      throw error;
    }
  }

  // Отправить ответ с детальным логированием
  async submitAnswer(wordId, isCorrect, usedHint = false) {
    this.log(`📝 API: Отправка ответа`, {
      word_id: wordId,
      is_correct: isCorrect,
      used_hint: usedHint
    });

    try {
      const response = await fetch(`${this.apiBaseUrl}/api/spaced/submit`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          user_id: this.userId,
          word_id: wordId,
          is_correct: isCorrect,
          response_time: 2.5,
          used_hint: usedHint
        })
      });

      if (!response.ok) {
        throw new Error(`API Error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      
      this.log('✅ API: Ответ обработан', {
        is_correct: result.is_correct,
        interval_level: result.interval_level,
        is_learned: result.is_learned,
        next_review: result.next_review
      });

      // Сохраняем в историю
      this.cardHistory.push({
        word_id: wordId,
        timestamp: new Date().toISOString(),
        action: 'answered',
        is_correct: isCorrect,
        used_hint: usedHint,
        result: result
      });

      return result;
    } catch (error) {
      this.log('❌ API: Ошибка отправки ответа', { error: error.message });
      throw error;
    }
  }

  // Симуляция логики отображения полосок (как в TrainingScreen)
  calculateProgressBars(card) {
    if (!card) {
      return { count: 1, color: '#3a3a4a', reason: 'no_card' };
    }

    const intervalLevel = card.interval_level ?? -1;
    const isNew = card.is_new ?? true;
    const isLearned = card.is_learned ?? false;

    this.log('🔧 ПОЛОСКИ: Расчет отображения', {
      word: card.word,
      intervalLevel,
      isNew,
      isLearned,
      word_id: card.word_id
    });

    if (isLearned) {
      this.log('🔧 ПОЛОСКИ: Слово выучено → 5 зелёных полосок');
      return { count: 5, color: '#4CAF50', reason: 'learned' };
    } else if (isNew) {
      this.log('🔧 ПОЛОСКИ: Новое слово → 1 оранжевая полоска');
      return { count: 1, color: '#FFA500', reason: 'new' };
    } else if (intervalLevel >= -1 && intervalLevel <= 4) {
      this.log('🔧 ПОЛОСКИ: Интервал 0-4 → 1 голубая полоска');
      return { count: 1, color: '#7fb4ff', reason: 'level_0_4' };
    } else if (intervalLevel >= 5 && intervalLevel <= 8) {
      this.log('🔧 ПОЛОСКИ: Интервал 5-8 → 2 голубые полоски');
      return { count: 2, color: '#7fb4ff', reason: 'level_5_8' };
    } else if (intervalLevel >= 9 && intervalLevel <= 11) {
      this.log('🔧 ПОЛОСКИ: Интервал 9-11 → 3 синие полоски');
      return { count: 3, color: '#4a7dff', reason: 'level_9_11' };
    } else if (intervalLevel >= 12 && intervalLevel <= 14) {
      this.log('🔧 ПОЛОСКИ: Интервал 12-14 → 4 синие полоски');
      return { count: 4, color: '#4a7dff', reason: 'level_12_14' };
    } else {
      this.log('🔧 ПОЛОСКИ: Интервал 15+ → 5 зелёных полосок');
      return { count: 5, color: '#4CAF50', reason: 'level_15_plus' };
    }
  }

  // Основной тест наследования зеленых полосок
  async runGreenBarsInheritanceTest() {
    this.log('🧪 === НАЧАЛО E2E ТЕСТА: Наследование зеленых полосок ===');
    
    try {
      // Шаг 1: Получаем первую карточку
      this.log('\n📝 ШАГ 1: Получение первой карточки');
      const firstCard = await this.getNextCard();
      const firstCardBars = this.calculateProgressBars(firstCard);
      
      this.log('📊 ПЕРВАЯ КАРТОЧКА: Полоски прогресса', firstCardBars);

      // Шаг 2: Отвечаем правильно (без подсказок)
      this.log('\n📝 ШАГ 2: Отвечаем правильно на первую карточку');
      const firstAnswer = await this.submitAnswer(firstCard.word_id, true, false);
      
      // Проверяем, стало ли слово выученным
      if (firstAnswer.is_learned && firstAnswer.interval_level === 15) {
        this.log('🎉 УСПЕХ: Первое слово стало выученным (interval_level=15, is_learned=true)');
      } else {
        this.log('⚠️ ВНИМАНИЕ: Первое слово НЕ стало выученным', {
          interval_level: firstAnswer.interval_level,
          is_learned: firstAnswer.is_learned
        });
      }

      // Шаг 3: Получаем вторую карточку
      this.log('\n📝 ШАГ 3: Получение второй карточки');
      const secondCard = await this.getNextCard();
      const secondCardBars = this.calculateProgressBars(secondCard);
      
      this.log('📊 ВТОРАЯ КАРТОЧКА: Полоски прогресса', secondCardBars);

      // КРИТИЧЕСКАЯ ПРОВЕРКА: Наследование полосок
      this.log('\n🔍 КРИТИЧЕСКАЯ ПРОВЕРКА: Наследование полосок');
      
      const inheritanceCheck = {
        first_card: {
          word: firstCard.word,
          word_id: firstCard.word_id,
          final_state: firstAnswer,
          bars: { count: 5, color: '#4CAF50', reason: 'should_be_learned' }
        },
        second_card: {
          word: secondCard.word,
          word_id: secondCard.word_id,
          is_new: secondCard.is_new,
          is_learned: secondCard.is_learned,
          interval_level: secondCard.interval_level,
          bars: secondCardBars
        },
        inheritance_detected: false,
        test_passed: false
      };

      // Проверяем наследование
      if (secondCard.word_id !== firstCard.word_id) {
        this.log('✅ Разные карточки (word_id отличается)');
        
        if (secondCard.is_new === true && secondCardBars.count === 1 && secondCardBars.color === '#FFA500') {
          this.log('✅ ТЕСТ ПРОШЕЛ: Вторая карточка показывает 1 оранжевую полоску (новое слово)');
          inheritanceCheck.test_passed = true;
        } else if (secondCardBars.count === 5 && secondCardBars.color === '#4CAF50') {
          this.log('❌ ТЕСТ ПРОВАЛЕН: Вторая карточка показывает 5 зеленых полосок (НАСЛЕДОВАНИЕ!)');
          inheritanceCheck.inheritance_detected = true;
        } else {
          this.log('⚠️ НЕОЖИДАННЫЙ РЕЗУЛЬТАТ: Вторая карточка показывает неожиданные полоски', secondCardBars);
        }
      } else {
        this.log('⚠️ ВНИМАНИЕ: Получена та же карточка (возможно, в форсированной очереди)');
      }

      this.log('\n📊 ФИНАЛЬНЫЙ РЕЗУЛЬТАТ ПРОВЕРКИ', inheritanceCheck);

      // Шаг 4: Дополнительная проверка - получаем третью карточку
      this.log('\n📝 ШАГ 4: Получение третьей карточки для дополнительной проверки');
      const thirdCard = await this.getNextCard();
      const thirdCardBars = this.calculateProgressBars(thirdCard);
      
      this.log('📊 ТРЕТЬЯ КАРТОЧКА: Полоски прогресса', thirdCardBars);

      // Финальный отчет
      this.log('\n📋 === ФИНАЛЬНЫЙ ОТЧЕТ ===');
      this.log('📊 История карточек:', this.cardHistory);
      
      return {
        success: inheritanceCheck.test_passed,
        inheritance_detected: inheritanceCheck.inheritance_detected,
        cards_tested: [firstCard, secondCard, thirdCard],
        full_log: this.testResults
      };

    } catch (error) {
      this.log('❌ КРИТИЧЕСКАЯ ОШИБКА В ТЕСТЕ', { error: error.message, stack: error.stack });
      throw error;
    }
  }

  // Сохранить результаты теста в файл
  saveTestResults(filename = 'green_bars_test_results.json') {
    const results = {
      test_name: 'Green Bars Inheritance E2E Test',
      timestamp: new Date().toISOString(),
      user_id: this.userId,
      api_base_url: this.apiBaseUrl,
      card_history: this.cardHistory,
      test_log: this.testResults
    };

    // В реальном окружении здесь был бы fs.writeFileSync
    console.log('\n💾 РЕЗУЛЬТАТЫ ТЕСТА (для сохранения):');
    console.log(JSON.stringify(results, null, 2));
    
    return results;
  }
}

// Запуск теста
async function runGreenBarsE2ETest() {
  console.log('🚀 Запуск E2E теста наследования зеленых полосок');
  
  const tester = new GreenBarsInheritanceE2ETester();
  
  try {
    const result = await tester.runGreenBarsInheritanceTest();
    tester.saveTestResults();
    
    if (result.success) {
      console.log('\n🎉 E2E ТЕСТ ПРОШЕЛ: Наследование полосок НЕ обнаружено');
    } else if (result.inheritance_detected) {
      console.log('\n❌ E2E ТЕСТ ПРОВАЛЕН: Обнаружено наследование зеленых полосок!');
    } else {
      console.log('\n⚠️ E2E ТЕСТ: Неопределенный результат');
    }
    
    return result;
  } catch (error) {
    console.error('\n💥 E2E ТЕСТ ЗАВЕРШИЛСЯ С ОШИБКОЙ:', error.message);
    tester.saveTestResults();
    throw error;
  }
}

// Экспорт для использования в других тестах
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    GreenBarsInheritanceE2ETester,
    runGreenBarsE2ETest
  };
} else {
  // Запуск в браузере
  runGreenBarsE2ETest();
}
