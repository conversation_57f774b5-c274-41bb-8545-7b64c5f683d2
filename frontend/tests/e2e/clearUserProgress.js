/**
 * ОЧИСТКА ПРОГРЕССА ПОЛЬЗОВАТЕЛЯ
 *
 * Удаляет весь прогресс пользователя для чистого тестирования
 */

const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);

const API_BASE = 'http://localhost:8000';
const USER_ID = '6848235d12259195693cb594';

/**
 * Универсальная функция очистки прогресса пользователя
 * @param {string} userId - ID пользователя для очистки (по умолчанию тестовый пользователь)
 * @param {boolean} silent - Тихий режим (без логов)
 * @returns {Promise<Object>} Результат операции
 */
async function clearUserProgress(userId = USER_ID, silent = false) {
  if (!silent) {
    console.log('🧹 === ОЧИСТКА ПРОГРЕССА ПОЛЬЗОВАТЕЛЯ ===\n');
  }

  try {
    if (!silent) {
      console.log(`🗑️ Очищаем прогресс пользователя: ${userId}`);
    }

    const curlCommand = `curl -s -X DELETE "${API_BASE}/api/spaced/debug/user/${userId}/progress"`;

    if (!silent) {
      console.log(`🌐 API: DELETE /api/spaced/debug/user/${userId}/progress`);
    }

    const { stdout, stderr } = await execAsync(curlCommand);
    const response = JSON.parse(stdout);

    if (!silent) {
      console.log(`📥 Response:`, response);
    }

    if (response.success) {
      if (!silent) {
        console.log(`✅ Прогресс пользователя очищен успешно`);
        console.log(`📊 Удалено записей: ${response.deleted_count || 'неизвестно'}`);
      }
    } else {
      if (!silent) {
        console.log(`❌ Ошибка при очистке прогресса:`, response);
      }
    }

    return response;
    
  } catch (error) {
    console.error('💥 ОШИБКА ПРИ ОЧИСТКЕ:', error.message);
    return { success: false, error: error.message };
  }
}

// Экспортируем функцию для использования в других тестах
module.exports = { clearUserProgress };

// Запуск как отдельный скрипт
if (require.main === module) {
  clearUserProgress()
    .then(result => {
      if (result.success) {
        console.log('\n🎉 ОЧИСТКА ЗАВЕРШЕНА УСПЕШНО');
        process.exit(0);
      } else {
        console.log('\n❌ ОЧИСТКА ПРОВАЛЕНА');
        process.exit(1);
      }
    });
}
