/**
 * Простой тест API для проверки проблемы с зелеными полосками
 * Использует curl для тестирования реального API
 */

const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);

const API_BASE = 'http://localhost:8000';
const USER_ID = '6848235d12259195693cb594';

async function makeApiCall(endpoint, method = 'GET', data = null) {
  let curlCommand = `curl -s -X ${method}`;
  
  if (data) {
    curlCommand += ` -H "Content-Type: application/json" -d '${JSON.stringify(data)}'`;
  }
  
  curlCommand += ` "${API_BASE}${endpoint}"`;
  
  console.log(`🌐 API Call: ${method} ${endpoint}`);
  if (data) {
    console.log(`📤 Data:`, data);
  }
  
  try {
    const { stdout, stderr } = await execAsync(curlCommand);
    
    if (stderr) {
      console.log(`⚠️ Stderr:`, stderr);
    }
    
    const response = JSON.parse(stdout);
    console.log(`📥 Response:`, response);
    return response;
  } catch (error) {
    console.error(`❌ API Error:`, error.message);
    throw error;
  }
}

async function testGreenBarsInheritance() {
  console.log('🧪 === ТЕСТ: Наследование зеленых полосок через реальный API ===\n');
  
  try {
    // Шаг 1: Получаем первую карточку
    console.log('📝 ШАГ 1: Получение первой карточки');
    const firstCard = await makeApiCall(`/api/spaced/next?user_id=${USER_ID}&target_lang=cb`);
    
    console.log('📊 ПЕРВАЯ КАРТОЧКА:');
    console.log(`   Слово: ${firstCard.word} (${firstCard.translation})`);
    console.log(`   ID: ${firstCard.word_id}`);
    console.log(`   Новое: ${firstCard.is_new}`);
    console.log(`   Выучено: ${firstCard.is_learned}`);
    console.log(`   Уровень: ${firstCard.interval_level}`);
    
    // Рассчитываем полоски для первой карточки
    const firstCardBars = calculateProgressBars(firstCard);
    console.log(`   Полоски: ${firstCardBars.count} x ${firstCardBars.color} (${firstCardBars.reason})`);
    
    // Шаг 2: Отвечаем правильно
    console.log('\n📝 ШАГ 2: Отвечаем правильно на первую карточку');
    const answerResult = await makeApiCall(`/api/spaced/${firstCard.word_id}/response`, 'POST', {
      user_id: USER_ID,
      is_correct: true,
      response_time: 2.5,
      used_hint: false
    });
    
    console.log('📊 РЕЗУЛЬТАТ ОТВЕТА:');
    console.log(`   Правильно: ${answerResult.is_correct}`);
    console.log(`   Уровень: ${answerResult.interval_level}`);
    console.log(`   Выучено: ${answerResult.is_learned}`);
    
    // Проверяем, стало ли слово выученным
    if (answerResult.is_learned && answerResult.interval_level === 15) {
      console.log('🎉 УСПЕХ: Слово стало выученным (interval_level=15, is_learned=true)');
    } else {
      console.log('⚠️ ВНИМАНИЕ: Слово НЕ стало выученным');
    }
    
    // Шаг 3: Получаем вторую карточку
    console.log('\n📝 ШАГ 3: Получение второй карточки');
    const secondCard = await makeApiCall(`/api/spaced/next?user_id=${USER_ID}&target_lang=cb`);
    
    console.log('📊 ВТОРАЯ КАРТОЧКА:');
    console.log(`   Слово: ${secondCard.word} (${secondCard.translation})`);
    console.log(`   ID: ${secondCard.word_id}`);
    console.log(`   Новое: ${secondCard.is_new}`);
    console.log(`   Выучено: ${secondCard.is_learned}`);
    console.log(`   Уровень: ${secondCard.interval_level}`);
    
    // Рассчитываем полоски для второй карточки
    const secondCardBars = calculateProgressBars(secondCard);
    console.log(`   Полоски: ${secondCardBars.count} x ${secondCardBars.color} (${secondCardBars.reason})`);
    
    // КРИТИЧЕСКАЯ ПРОВЕРКА
    console.log('\n🔍 === КРИТИЧЕСКАЯ ПРОВЕРКА: Наследование полосок ===');
    
    if (secondCard.word_id !== firstCard.word_id) {
      console.log('✅ Разные карточки (word_id отличается)');
      
      if (secondCard.is_new === true && secondCardBars.count === 1 && secondCardBars.color === '#FFA500') {
        console.log('✅ ТЕСТ ПРОШЕЛ: Вторая карточка показывает 1 оранжевую полоску');
        console.log('✅ Наследование полосок НЕ происходит');
        return { success: true, inheritance: false };
      } else if (secondCardBars.count === 5 && secondCardBars.color === '#4CAF50') {
        console.log('❌ ТЕСТ ПРОВАЛЕН: Вторая карточка показывает 5 зеленых полосок');
        console.log('❌ ОБНАРУЖЕНО НАСЛЕДОВАНИЕ ПОЛОСОК!');
        return { success: false, inheritance: true };
      } else {
        console.log('⚠️ НЕОЖИДАННЫЙ РЕЗУЛЬТАТ: Неожиданные полоски');
        return { success: false, inheritance: false, unexpected: true };
      }
    } else {
      console.log('⚠️ ВНИМАНИЕ: Получена та же карточка (возможно, в форсированной очереди)');
      return { success: false, same_card: true };
    }
    
  } catch (error) {
    console.error('💥 КРИТИЧЕСКАЯ ОШИБКА:', error.message);
    return { success: false, error: error.message };
  }
}

// Функция расчета полосок (копия из TrainingScreen)
function calculateProgressBars(card) {
  if (!card) {
    return { count: 1, color: '#3a3a4a', reason: 'no_card' };
  }

  const intervalLevel = card.interval_level ?? -1;
  const isNew = card.is_new ?? true;
  const isLearned = card.is_learned ?? false;

  if (isLearned) {
    return { count: 5, color: '#4CAF50', reason: 'learned' };
  } else if (isNew) {
    return { count: 1, color: '#FFA500', reason: 'new' };
  } else if (intervalLevel >= -1 && intervalLevel <= 4) {
    return { count: 1, color: '#7fb4ff', reason: 'level_0_4' };
  } else if (intervalLevel >= 5 && intervalLevel <= 8) {
    return { count: 2, color: '#7fb4ff', reason: 'level_5_8' };
  } else if (intervalLevel >= 9 && intervalLevel <= 11) {
    return { count: 3, color: '#4a7dff', reason: 'level_9_11' };
  } else if (intervalLevel >= 12 && intervalLevel <= 14) {
    return { count: 4, color: '#4a7dff', reason: 'level_12_14' };
  } else {
    return { count: 5, color: '#4CAF50', reason: 'level_15_plus' };
  }
}

// Запуск теста
async function runTest() {
  console.log('🚀 Запуск простого API теста\n');
  
  const result = await testGreenBarsInheritance();
  
  console.log('\n📋 === ФИНАЛЬНЫЙ РЕЗУЛЬТАТ ===');
  console.log(JSON.stringify(result, null, 2));
  
  if (result.success) {
    console.log('\n🎉 ТЕСТ ПРОШЕЛ: Проблема с наследованием НЕ обнаружена');
    process.exit(0);
  } else {
    console.log('\n❌ ТЕСТ ПРОВАЛЕН или НЕОПРЕДЕЛЕННЫЙ РЕЗУЛЬТАТ');
    process.exit(1);
  }
}

// Запуск
runTest().catch(error => {
  console.error('💥 Неожиданная ошибка:', error);
  process.exit(1);
});
