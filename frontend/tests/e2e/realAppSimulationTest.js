/**
 * РЕАЛЬНАЯ ИМИТАЦИЯ ПРИЛОЖЕНИЯ: Тест повторения выученных слов
 *
 * 🔍 ДЕТАЛЬНЫЙ АНАЛИЗ ФРОНТЕНДА:
 * TrainingScreen.tsx работает по следующей схеме:
 * 1. Загрузка: fetchNewCard() → API /api/spaced/next
 * 2. Предзагрузка: startPreloading() → API /api/spaced/next?preload=true&exclude_card_id=X
 * 3. Ответ: checkAnswer() → API /api/spaced/{cardId}/response
 * 4. Переход: animateCardOutWithPreload() → использует предзагруженную карточку
 * 5. Цикл: новая карточка → предзагрузка следующей → ответ → переход
 *
 * КРИТИЧЕСКАЯ ЛОГИКА ПРЕДЗАГРУЗКИ:
 * - useCardPreloader() управляет предзагрузкой
 * - exclude_card_id исключает текущую карточку из предзагрузки
 * - Предзагруженная карточка становится текущей при переходе
 * - Проблема: выученные слова могут попадать в предзагрузку
 *
 * Точно воспроизводит поведение Expo Go приложения:
 * 1. Основной запрос карточки
 * 2. Предзагрузка следующей карточки
 * 3. Ответ пользователя
 * 4. Предзагрузка следующей карточки
 * 5. Исключение карточек через exclude_card_id
 *
 * Цель: Воспроизвести проблему с повторением выученных слов
 */

const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);
const { clearUserProgress } = require('./clearUserProgress');

const API_BASE = 'http://localhost:8000';
const USER_ID = '6848235d12259195693cb594';

class RealAppSimulator {
  constructor() {
    this.currentCard = null;
    this.preloadedCard = null;
    this.learnedWords = new Map();
    this.cardHistory = [];
    this.problemDetected = false;
  }

  log(message, data = null) {
    const timestamp = new Date().toISOString();
    console.log(`🔍 [${timestamp}] ${message}`);
    if (data) {
      console.log('📊 Данные:', JSON.stringify(data, null, 2));
    }
  }

  async makeApiCall(endpoint, method = 'GET', data = null) {
    let curlCommand = `curl -s -X ${method}`;
    
    if (data) {
      curlCommand += ` -H "Content-Type: application/json" -d '${JSON.stringify(data)}'`;
    }
    
    curlCommand += ` "${API_BASE}${endpoint}"`;
    
    this.log(`🌐 API: ${method} ${endpoint}`);
    
    try {
      const { stdout, stderr } = await execAsync(curlCommand);
      const response = JSON.parse(stdout);
      this.log(`📥 Response:`, response);
      return response;
    } catch (error) {
      this.log(`❌ API Error:`, error.message);
      throw error;
    }
  }

  // Получить основную карточку (как в реальном приложении)
  async getMainCard(excludeCardId = null) {
    let endpoint = `/api/spaced/next?user_id=${USER_ID}&target_lang=cb`;
    if (excludeCardId) {
      endpoint += `&exclude_card_id=${excludeCardId}`;
    }
    
    this.log('📝 ОСНОВНОЙ ЗАПРОС: Получение карточки для изучения');
    return await this.makeApiCall(endpoint);
  }

  // Предзагрузить следующую карточку (как в реальном приложении)
  async preloadNextCard(excludeCardId = null) {
    let endpoint = `/api/spaced/next?user_id=${USER_ID}&target_lang=cb&preload=true`;
    if (excludeCardId) {
      endpoint += `&exclude_card_id=${excludeCardId}`;
    }
    
    this.log('🔄 ПРЕДЗАГРУЗКА: Получение следующей карточки');
    return await this.makeApiCall(endpoint);
  }

  // Отправить ответ (как в реальном приложении)
  async submitAnswer(wordId, isCorrect) {
    this.log(`📝 ОТВЕТ: ${isCorrect ? 'ПРАВИЛЬНО' : 'НЕПРАВИЛЬНО'} на слово ${wordId}`);
    return await this.makeApiCall(`/api/spaced/${wordId}/response`, 'POST', {
      user_id: USER_ID,
      is_correct: isCorrect,
      response_time: 2.5,
      used_hint: false
    });
  }

  // Проверить, не появилось ли выученное слово снова
  checkForLearnedWordReappearance(card) {
    if (this.learnedWords.has(card.word_id)) {
      const learnedInfo = this.learnedWords.get(card.word_id);
      this.log(`🚨 КРИТИЧЕСКАЯ ПРОБЛЕМА: Выученное слово появилось снова!`, {
        word: card.word,
        word_id: card.word_id,
        learned_in_round: learnedInfo.round,
        learned_level: learnedInfo.level,
        current_state: {
          is_new: card.is_new,
          is_learned: card.is_learned,
          interval_level: card.interval_level
        }
      });
      this.problemDetected = true;
      return true;
    }
    return false;
  }

  // Симуляция одного раунда (как в реальном приложении)
  async simulateRound(roundNumber) {
    this.log(`\n🔄 === РАУНД ${roundNumber} ===`);

    // Шаг 1: Получаем текущую карточку
    if (roundNumber === 1) {
      // Первый раунд - делаем основной запрос
      this.currentCard = await this.getMainCard();
      this.log(`📝 ПЕРВЫЙ РАУНД: Основной запрос`);
    } else {
      // Последующие раунды - используем предзагруженную карточку (КАК В РЕАЛЬНОМ ПРИЛОЖЕНИИ!)
      this.currentCard = this.preloadedCard;
      this.log(`🔄 РАУНД ${roundNumber}: Используем предзагруженную карточку`);
    }

    this.log(`📊 ТЕКУЩАЯ КАРТОЧКА:`, {
      word: this.currentCard.word,
      translation: this.currentCard.translation,
      word_id: this.currentCard.word_id,
      is_new: this.currentCard.is_new,
      is_learned: this.currentCard.is_learned,
      interval_level: this.currentCard.interval_level
    });

    // КРИТИЧЕСКАЯ ПРОВЕРКА: Не выученное ли это слово?
    if (this.checkForLearnedWordReappearance(this.currentCard)) {
      return { success: false, problem_detected: true, round: roundNumber };
    }

    // Сохраняем в историю
    this.cardHistory.push({
      round: roundNumber,
      action: 'current_card',
      source: roundNumber === 1 ? 'main_request' : 'preloaded',
      ...this.currentCard
    });

    // Шаг 2: Предзагружаем следующую карточку (как в реальном приложении)
    this.preloadedCard = await this.preloadNextCard(this.currentCard.word_id);

    this.log(`🔄 ПРЕДЗАГРУЖЕННАЯ КАРТОЧКА:`, {
      word: this.preloadedCard.word,
      word_id: this.preloadedCard.word_id
    });

    // КРИТИЧЕСКАЯ ПРОВЕРКА: Не выученное ли слово в предзагрузке?
    if (this.checkForLearnedWordReappearance(this.preloadedCard)) {
      this.log(`🚨 ПРОБЛЕМА В ПРЕДЗАГРУЗКЕ: Выученное слово в предзагрузке!`);
      return { success: false, problem_detected: true, round: roundNumber, problem_in: 'preload' };
    }

    // Сохраняем предзагруженную карточку в историю
    this.cardHistory.push({
      round: roundNumber,
      action: 'preloaded_card',
      ...this.preloadedCard
    });

    // Шаг 3: Отвечаем правильно на текущую карточку
    const answerResult = await this.submitAnswer(this.currentCard.word_id, true);

    this.log(`📊 РЕЗУЛЬТАТ ОТВЕТА:`, {
      interval_level: answerResult.interval_level,
      is_learned: answerResult.is_learned,
      is_correct: answerResult.is_correct,
      next_review: answerResult.next_review
    });

    // Проверяем, выучено ли слово
    if (answerResult.is_learned && answerResult.interval_level === 15) {
      this.log(`🎉 СЛОВО ВЫУЧЕНО: "${this.currentCard.word}" (уровень 15)`);
      this.learnedWords.set(this.currentCard.word_id, {
        word: this.currentCard.word,
        round: roundNumber,
        level: answerResult.interval_level
      });
    } else if (answerResult.interval_level > 15) {
      this.log(`🚨 ПРЕВЫШЕНИЕ МАКСИМУМА: "${this.currentCard.word}" получил уровень ${answerResult.interval_level}`);
      this.problemDetected = true;
      return { success: false, level_exceeded: true, level: answerResult.interval_level };
    }

    // Небольшая пауза как в реальном приложении
    await new Promise(resolve => setTimeout(resolve, 200));

    return { success: true, learned_count: this.learnedWords.size };
  }

  // Основной тест
  async runRealAppSimulation() {
    this.log('🧪 === РЕАЛЬНАЯ ИМИТАЦИЯ ПРИЛОЖЕНИЯ ===');
    this.log('🎯 Цель: Воспроизвести проблему с повторением выученных слов');
    this.log('');

    // 🧹 КРИТИЧЕСКИ ВАЖНО: Очищаем прогресс пользователя перед тестом
    this.log('🧹 Очистка прогресса пользователя перед тестом...');
    const clearResult = await clearUserProgress(USER_ID, true); // silent mode
    if (!clearResult.success) {
      this.log('❌ Не удалось очистить прогресс пользователя');
      return { success: false, error: 'Failed to clear user progress' };
    }
    this.log(`✅ Прогресс очищен (удалено записей: ${clearResult.deleted_count})`);
    this.log('');

    try {
      const maxRounds = 15;
      
      for (let round = 1; round <= maxRounds; round++) {
        const result = await this.simulateRound(round);
        
        if (!result.success) {
          this.log(`❌ ПРОБЛЕМА ОБНАРУЖЕНА В РАУНДЕ ${round}`);
          return {
            success: false,
            problem_round: round,
            learned_words_count: this.learnedWords.size,
            details: result
          };
        }
        
        this.log(`✅ Раунд ${round} завершен. Выучено слов: ${result.learned_count}`);
      }

      // Финальная проверка
      this.log(`\n📋 === ФИНАЛЬНЫЕ РЕЗУЛЬТАТЫ ===`);
      this.log(`📊 СТАТИСТИКА:`, {
        total_rounds: maxRounds,
        learned_words_count: this.learnedWords.size,
        problem_detected: this.problemDetected,
        total_cards_seen: this.cardHistory.length
      });

      this.log(`📚 ВЫУЧЕННЫЕ СЛОВА:`);
      for (const [wordId, info] of this.learnedWords) {
        this.log(`   ${info.word} (раунд ${info.round}, уровень ${info.level})`);
      }

      // 🧹 КРИТИЧЕСКИ ВАЖНО: Очищаем прогресс пользователя после теста
      this.log('\n🧹 Очистка прогресса пользователя после теста...');
      const clearResultEnd = await clearUserProgress(USER_ID, true); // silent mode
      this.log(`✅ Прогресс очищен после теста (удалено записей: ${clearResultEnd.deleted_count})`);

      if (this.problemDetected) {
        return { success: false, problem_detected: true };
      } else {
        return { success: true, problem_detected: false };
      }

    } catch (error) {
      this.log('💥 КРИТИЧЕСКАЯ ОШИБКА:', error.message);

      // 🧹 Очищаем прогресс даже при ошибке
      try {
        this.log('🧹 Очистка прогресса пользователя после ошибки...');
        const clearResultError = await clearUserProgress(USER_ID, true);
        this.log(`✅ Прогресс очищен после ошибки (удалено записей: ${clearResultError.deleted_count})`);
      } catch (clearError) {
        this.log('❌ Не удалось очистить прогресс после ошибки:', clearError.message);
      }

      return { success: false, error: error.message };
    }
  }

  // Сохранить детальные результаты
  saveDetailedResults() {
    const results = {
      test_name: 'Real App Simulation Test',
      timestamp: new Date().toISOString(),
      user_id: USER_ID,
      learned_words: Array.from(this.learnedWords.entries()),
      problem_detected: this.problemDetected,
      card_history: this.cardHistory
    };

    console.log('\n💾 ДЕТАЛЬНЫЕ РЕЗУЛЬТАТЫ:');
    console.log(JSON.stringify(results, null, 2));
    
    return results;
  }
}

// Запуск теста
async function runRealAppSimulationTest() {
  console.log('🚀 Запуск реальной имитации Expo Go приложения\n');
  
  const simulator = new RealAppSimulator();
  
  try {
    const result = await simulator.runRealAppSimulation();
    simulator.saveDetailedResults();
    
    console.log('\n📊 ФИНАЛЬНЫЙ РЕЗУЛЬТАТ:');
    console.log(JSON.stringify(result, null, 2));
    
    if (result.success) {
      console.log('\n🎉 ТЕСТ ПРОШЕЛ: Проблема не воспроизведена в реальной имитации');
      process.exit(0);
    } else {
      console.log('\n❌ ТЕСТ ПРОВАЛЕН: Проблема воспроизведена!');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('\n💥 КРИТИЧЕСКАЯ ОШИБКА:', error.message);
    simulator.saveDetailedResults();
    process.exit(1);
  }
}

// Запуск
runRealAppSimulationTest();
