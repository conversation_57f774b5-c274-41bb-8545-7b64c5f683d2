/**
 * ТЕСТ №3: РЕГРЕССИОННЫЙ ТЕСТ - Проблема исчерпания ultra_core приоритета при предзагрузке
 *
 * 🎯 ЦЕЛЬ: Регрессионный тест для проверки исправления проблемы с приоритетами A0
 *
 * 🚨 ПРОБЛЕМА (ДО ИСПРАВЛЕНИЯ):
 * Когда ultra_core слова исчерпаны, предзагрузка НЕ переключается на core приоритет,
 * в то время как основной запрос переключается. Это приводит к 404 ошибкам в предзагрузке.
 *
 * 🔧 ИСПРАВЛЕНИЕ:
 * Синхронизация логики определения приоритетов между предзагрузкой и основным запросом
 * через функцию _get_a0_priority_filter_with_learned_words()
 *
 * 📋 ПОСЛЕДОВАТЕЛЬНОСТЬ ВОСПРОИЗВЕДЕНИЯ:
 * 1. Изучить все ultra_core слова (обычно 7-8 слов)
 * 2. Попытаться предзагрузить следующую карточку
 * 3. ДО ИСПРАВЛЕНИЯ: 404 ошибка (ultra_core исчерпан, не переключается на core)
 * 4. ПОСЛЕ ИСПРАВЛЕНИЯ: Успешная предзагрузка (автоматическое переключение на core)
 *
 * 🧪 ИСПОЛЬЗОВАНИЕ:
 * - Запускать ПЕРЕД внесением изменений в логику приоритетов
 * - Запускать ПОСЛЕ исправлений для проверки регрессии
 * - Часть набора регрессионных тестов для стабильности системы
 *
 * Цель: Обеспечить стабильность системы приоритетов A0 при предзагрузке
 */

const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);
const { clearUserProgress } = require('./clearUserProgress');

const API_BASE = 'http://localhost:8000';
const USER_ID = '684c7900e9e41d7a3b5c0d62'; // ВАША УЧЕТНАЯ ЗАПИСЬ (полный ID)

class VocabularyExhaustionTester {
  constructor() {
    this.learnedWords = new Map();
    this.cardHistory = [];
    this.preloadErrors = [];
    this.problemDetected = false;
    this.vocabularySize = 0;
  }

  log(message, data = null) {
    const timestamp = new Date().toISOString();
    console.log(`🔍 [${timestamp}] ${message}`);
    if (data) {
      console.log('📊 Данные:', JSON.stringify(data, null, 2));
    }
  }

  async makeApiCall(endpoint, method = 'GET', data = null) {
    let curlCommand = `curl -s -X ${method}`;

    if (data) {
      curlCommand += ` -H "Content-Type: application/json" -d '${JSON.stringify(data)}'`;
    }

    curlCommand += ` "${API_BASE}${endpoint}"`;

    this.log(`🌐 API: ${method} ${endpoint}`);

    try {
      const { stdout, stderr } = await execAsync(curlCommand);
      const response = JSON.parse(stdout);
      this.log(`📥 Response:`, response);

      // Проверяем на 404 ошибку
      if (response.detail && response.detail.includes('No cards available')) {
        const error = new Error(`HTTP error! status: 404, body: ${JSON.stringify(response)}`);
        throw error;
      }

      return response;
    } catch (error) {
      this.log(`❌ API Error:`, error.message);
      throw error;
    }
  }

  // Получить основную карточку
  async getMainCard(excludeCardId = null) {
    let endpoint = `/api/spaced/next?user_id=${USER_ID}&target_lang=cb`;
    if (excludeCardId) {
      endpoint += `&exclude_card_id=${excludeCardId}`;
    }
    
    this.log('📝 ОСНОВНОЙ ЗАПРОС: Получение карточки для изучения');
    return await this.makeApiCall(endpoint);
  }

  // Предзагрузить следующую карточку (КРИТИЧЕСКАЯ ТОЧКА)
  async preloadNextCard(excludeCardId = null) {
    let endpoint = `/api/spaced/next?user_id=${USER_ID}&target_lang=cb&preload=true`;
    if (excludeCardId) {
      endpoint += `&exclude_card_id=${excludeCardId}`;
    }
    
    this.log('🔄 ПРЕДЗАГРУЗКА: Получение следующей карточки');
    
    try {
      const result = await this.makeApiCall(endpoint);
      return result;
    } catch (error) {
      // КРИТИЧЕСКАЯ ПРОВЕРКА: Ловим 404 ошибки
      if (error.message.includes('404') || error.message.includes('No cards available')) {
        this.log('🚨 ПРОБЛЕМА ОБНАРУЖЕНА: 404 ошибка в предзагрузке!', {
          excludeCardId,
          learnedWordsCount: this.learnedWords.size,
          error: error.message
        });
        this.preloadErrors.push({
          round: this.learnedWords.size + 1,
          excludeCardId,
          error: error.message,
          timestamp: new Date().toISOString()
        });
        this.problemDetected = true;
        throw error;
      }
      throw error;
    }
  }

  // Отправить ответ
  async submitAnswer(wordId, isCorrect) {
    this.log(`📝 ОТВЕТ: ${isCorrect ? 'ПРАВИЛЬНО' : 'НЕПРАВИЛЬНО'} на слово ${wordId}`);
    return await this.makeApiCall(`/api/spaced/${wordId}/response`, 'POST', {
      user_id: USER_ID,
      is_correct: isCorrect,
      response_time: 2.5,
      used_hint: false
    });
  }

  // Изучить все слова в словаре
  async learnAllWordsInVocabulary() {
    this.log('📚 ИЗУЧЕНИЕ ВСЕХ СЛОВ В СЛОВАРЕ');

    const learnedCards = [];
    const maxAttempts = 25; // Увеличиваем лимит

    for (let i = 0; i < maxAttempts; i++) {
      try {
        this.log(`\n📖 Карточка ${i + 1}:`);
        const card = await this.getMainCard();

        // Проверяем на дубликаты
        if (learnedCards.find(c => c.word_id === card.word_id)) {
          this.log(`🔄 Повторная карточка обнаружена: ${card.word}`);
          break;
        }

        this.log(`📝 Изучаем слово: "${card.word}" (ID: ${card.word_id})`);
        learnedCards.push(card);

        // Отвечаем правильно, чтобы слово стало выученным
        const answerResult = await this.submitAnswer(card.word_id, true);

        this.log(`📊 Результат: interval_level=${answerResult.interval_level}, is_learned=${answerResult.is_learned}`);

        // Проверяем, что слово действительно выучено
        if (answerResult.is_learned && answerResult.interval_level === 15) {
          this.log(`🎉 Слово "${card.word}" выучено!`);
        } else {
          this.log(`⚠️ Слово "${card.word}" не выучено с первого раза`);
        }

        // Небольшая пауза
        await new Promise(resolve => setTimeout(resolve, 100));

      } catch (error) {
        if (error.message.includes('404') || error.message.includes('No cards available')) {
          this.log(`📚 Словарь исчерпан на карточке ${i + 1}`);
          break;
        }
        this.log(`❌ Ошибка при изучении карточки ${i + 1}:`, error.message);
        return {
          success: false,
          error: error.message,
          learned_count: learnedCards.length
        };
      }
    }

    this.log(`\n📊 ИТОГО ИЗУЧЕНО: ${learnedCards.length} уникальных слов`);

    return {
      success: true,
      learned_count: learnedCards.length,
      total_words: learnedCards.length,
      learned_cards: learnedCards
    };
  }

  // Симуляция раунда с детальным анализом предзагрузки
  async simulateRoundWithPreloadAnalysis(roundNumber) {
    this.log(`\n🔄 === РАУНД ${roundNumber} ===`);

    // Получаем текущую карточку
    const currentCard = await this.getMainCard();
    
    this.log(`📊 ТЕКУЩАЯ КАРТОЧКА:`, {
      word: currentCard.word,
      word_id: currentCard.word_id,
      is_new: currentCard.is_new,
      is_learned: currentCard.is_learned,
      interval_level: currentCard.interval_level
    });

    // Сохраняем в историю
    this.cardHistory.push({
      round: roundNumber,
      action: 'current_card',
      ...currentCard
    });

    // КРИТИЧЕСКАЯ ПРОВЕРКА: Пытаемся предзагрузить следующую карточку
    this.log(`🔄 КРИТИЧЕСКАЯ ПРОВЕРКА: Предзагрузка с исключением ${currentCard.word_id}`);
    
    try {
      const preloadedCard = await this.preloadNextCard(currentCard.word_id);
      
      this.log(`✅ ПРЕДЗАГРУЗКА УСПЕШНА:`, {
        word: preloadedCard.word,
        word_id: preloadedCard.word_id
      });

      // Сохраняем предзагруженную карточку
      this.cardHistory.push({
        round: roundNumber,
        action: 'preloaded_card',
        ...preloadedCard
      });

    } catch (error) {
      this.log(`❌ ПРЕДЗАГРУЗКА ПРОВАЛЕНА:`, {
        round: roundNumber,
        learnedWords: this.learnedWords.size,
        error: error.message
      });
      
      return { 
        success: false, 
        problem_detected: true, 
        round: roundNumber,
        error: 'preload_failed',
        details: error.message
      };
    }

    // Отвечаем правильно на текущую карточку
    const answerResult = await this.submitAnswer(currentCard.word_id, true);

    this.log(`📊 РЕЗУЛЬТАТ ОТВЕТА:`, {
      interval_level: answerResult.interval_level,
      is_learned: answerResult.is_learned,
      is_correct: answerResult.is_correct
    });

    // Проверяем, выучено ли слово
    if (answerResult.is_learned && answerResult.interval_level === 15) {
      this.log(`🎉 СЛОВО ВЫУЧЕНО: "${currentCard.word}" (уровень 15)`);
      this.learnedWords.set(currentCard.word_id, {
        word: currentCard.word,
        round: roundNumber,
        level: answerResult.interval_level
      });
    }

    return { success: true, learned_count: this.learnedWords.size };
  }

  // Регрессионный тест для проблемы с приоритетами A0
  async testA0PriorityExhaustionRegression() {
    this.log('🎯 РЕГРЕССИОННЫЙ ТЕСТ: Исчерпание ultra_core приоритета при предзагрузке');
    this.log('🔍 Цель: Проверить автоматическое переключение ultra_core → core в предзагрузке');
    this.log('📋 Ожидаемое поведение: Предзагрузка должна переключаться на core без 404 ошибок');

    // Изучаем ultra_core слова до исчерпания
    this.log('\n📚 ЭТАП 1: Изучаем ultra_core слова до исчерпания');

    // Изучаем ТОЧНО 7 ultra_core слов (ako, ikaw, oo, Dili, unsa, asa, mama)
    const learnedCards = [];
    const expectedUltraCoreWords = ['ako', 'ikaw', 'oo', 'Dili', 'unsa', 'asa', 'mama'];
    let ultraCoreExhausted = false;
    let attempts = 0;
    const maxAttempts = 10; // Ограничиваем для безопасности

    this.log(`🎯 ЦЕЛЬ: Изучить все 7 ultra_core слов: ${expectedUltraCoreWords.join(', ')}`);
    this.log(`🚨 ОЖИДАЕМАЯ ПРОБЛЕМА: После 7-го слова предзагрузка должна вернуть 404`);

    while (!ultraCoreExhausted && attempts < maxAttempts) {
      attempts++;
      try {
        const card = await this.getMainCard();
        this.log(`📖 Карточка ${attempts}: "${card.word}" (ID: ${card.word_id})`);

        // Проверяем, что это ожидаемое ultra_core слово
        if (expectedUltraCoreWords.includes(card.word)) {
          this.log(`✅ Подтверждено: "${card.word}" - это ultra_core слово`);
        } else {
          this.log(`⚠️ Неожиданное слово: "${card.word}" - не в списке ultra_core`);
        }

        learnedCards.push(card);

        // Эмулируем время размышления пользователя (быстро для теста)
        await new Promise(resolve => setTimeout(resolve, 500));

        // Отвечаем правильно
        const answerResult = await this.submitAnswer(card.word_id, true);
        this.log(`✅ Слово "${card.word}" выучено (уровень ${answerResult.interval_level})`);

        // КРИТИЧЕСКАЯ ПРОВЕРКА: После 7-го слова предзагрузка должна дать 404
        if (attempts === 7) {
          this.log(`\n🚨 КРИТИЧЕСКИЙ МОМЕНТ: Изучили 7-е ultra_core слово "${card.word}"`);
          this.log(`🔍 Теперь предзагрузка должна искать 8-е ultra_core слово и получить 404...`);

          // ВАЖНО: Ждем немного, чтобы буфер времени сработал
          this.log(`⏰ Ждем 2 секунды для активации буфера времени...`);
          await new Promise(resolve => setTimeout(resolve, 2000));

          try {
            const preloadResult = await this.preloadNextCard(card.word_id);

            // Проверяем приоритет предзагруженного слова
            if (preloadResult && preloadResult.priority === 'core') {
              this.log(`❌ ПРОБЛЕМА НЕ ВОСПРОИЗВЕДЕНА: Предзагрузка успешно переключилась на core: "${preloadResult.word}"`);
              this.log(`🎉 ИСПРАВЛЕНИЕ РАБОТАЕТ! Предзагрузка корректно переключается ultra_core → core`);

              return {
                success: true,
                problem_detected: false,
                issue: 'problem_not_reproduced_fix_works',
                learned_ultra_core_count: 7,
                last_ultra_core_word: card.word,
                next_preloaded_word: preloadResult.word,
                next_priority: preloadResult.priority,
                message: 'ИСПРАВЛЕНИЕ РАБОТАЕТ: Предзагрузка корректно переключается на core'
              };
            } else {
              this.log(`⚠️ Предзагрузка успешна, но приоритет неожиданный: "${preloadResult.word}" (${preloadResult.priority})`);

              return {
                success: true,
                problem_detected: false,
                issue: 'unexpected_priority_but_works',
                learned_ultra_core_count: 7,
                last_ultra_core_word: card.word,
                next_preloaded_word: preloadResult.word,
                next_priority: preloadResult.priority,
                message: 'Предзагрузка работает, но с неожиданным приоритетом'
              };
            }

          } catch (preloadError) {
            if (preloadError.message.includes('404') || preloadError.message.includes('No cards available')) {
              this.log(`🎯 ПРОБЛЕМА ВОСПРОИЗВЕДЕНА! Предзагрузка вернула 404 после 7-го ultra_core слова!`);
              this.log(`🚨 РЕГРЕССИЯ: Предзагрузка НЕ переключается на core приоритет!`);

              // Проверяем, что основной запрос переключается на core
              this.log(`🔍 Проверяем основной запрос (должен переключиться на core)...`);
              try {
                const nextMainCard = await this.getMainCard();
                this.log(`✅ Основной запрос работает: "${nextMainCard.word}" (priority: ${nextMainCard.priority})`);

                return {
                  success: false,
                  problem_detected: true,
                  issue: 'ultra_core_exhausted_preload_404_after_7th',
                  learned_ultra_core_count: 7,
                  last_ultra_core_word: card.word,
                  next_core_word: nextMainCard.word,
                  next_core_priority: nextMainCard.priority,
                  error_details: preloadError.message,
                  diagnosis: 'Предзагрузка не переключается на core, основной запрос переключается'
                };

              } catch (mainError) {
                this.log(`❌ Основной запрос тоже не работает:`, mainError.message);
                return {
                  success: false,
                  problem_detected: true,
                  issue: 'both_preload_and_main_failed_after_7th',
                  learned_ultra_core_count: 7,
                  error_details: `Preload: ${preloadError.message}, Main: ${mainError.message}`,
                  diagnosis: 'И предзагрузка, и основной запрос не работают'
                };
              }
            } else {
              this.log(`❌ Неожиданная ошибка предзагрузки:`, preloadError.message);
              return { success: false, error: 'unexpected_preload_error', details: preloadError.message };
            }
          }
        }

        // Для остальных слов просто проверяем предзагрузку
        if (attempts < 7) {
          try {
            const preloadResult = await this.preloadNextCard(card.word_id);
            this.log(`✅ Предзагрузка ${attempts}/7: "${preloadResult.word}"`);
          } catch (preloadError) {
            this.log(`❌ Неожиданная ошибка предзагрузки на ${attempts}/7:`, preloadError.message);
            return { success: false, error: `preload_error_at_${attempts}`, details: preloadError.message };
          }
        }

        // Эмулируем время перехода к следующей карточке
        await new Promise(resolve => setTimeout(resolve, 300));

      } catch (error) {
        this.log(`❌ Ошибка при изучении карточки ${attempts}:`, error.message);
        return { success: false, error: `Failed to learn card ${attempts}`, details: error.message };
      }
    }

    if (!ultraCoreExhausted && attempts >= 7) {
      this.log(`⚠️ Изучили ${attempts} слов, но проблема не воспроизведена`);
      this.log(`🤔 Возможно, исправление уже работает или порядок слов изменился`);
      return {
        success: true,
        problem_detected: false,
        ultra_core_not_exhausted: true,
        learned_cards: attempts,
        message: 'Проблема не воспроизведена - возможно, уже исправлена'
      };
    }

    // Если мы дошли сюда, значит проблема не воспроизведена
    this.log(`\n✅ ТЕСТ ПРОЙДЕН: Ultra_core исчерпание не вызвало проблем с предзагрузкой`);
    return {
      success: true,
      problem_detected: false,
      learned_ultra_core_count: learnedCards.length,
      message: 'Предзагрузка корректно переключается между приоритетами'
    };
  }

  // Дополнительный тест для проверки перехода core → extended
  async testCoreToExtendedTransition() {
    this.log('\n🔍 ДОПОЛНИТЕЛЬНЫЙ ТЕСТ: Переход core → extended');
    this.log('🎯 Цель: Проверить переход с core на extended приоритет');

    // Изучаем еще 6 core слов после ultra_core
    const coreWordsToLearn = 6;
    let coreWordsLearned = 0;

    for (let i = 0; i < coreWordsToLearn; i++) {
      try {
        const card = await this.getMainCard();
        this.log(`📖 Core слово ${i + 1}/6: "${card.word}"`);

        await new Promise(resolve => setTimeout(resolve, 300));
        const answerResult = await this.submitAnswer(card.word_id, true);
        this.log(`✅ Core слово "${card.word}" выучено`);
        coreWordsLearned++;

        // После 6-го core слова проверяем переход на extended
        if (i === 5) {
          this.log(`\n🚨 КРИТИЧЕСКИЙ МОМЕНТ: Изучили все core слова`);
          this.log(`🔍 Предзагрузка должна переключиться на extended...`);

          try {
            const preloadResult = await this.preloadNextCard(card.word_id);
            this.log(`✅ Предзагрузка успешна: "${preloadResult.word}" (должно быть extended)`);

            return {
              success: true,
              core_to_extended_works: true,
              core_words_learned: coreWordsLearned,
              next_extended_word: preloadResult.word
            };

          } catch (preloadError) {
            if (preloadError.message.includes('404')) {
              this.log(`🚨 ПРОБЛЕМА: Предзагрузка не переключилась на extended!`);
              return {
                success: false,
                problem_detected: true,
                issue: 'core_to_extended_transition_failed',
                core_words_learned: coreWordsLearned,
                error_details: preloadError.message
              };
            } else {
              throw preloadError;
            }
          }
        }

        await new Promise(resolve => setTimeout(resolve, 300));

      } catch (error) {
        this.log(`❌ Ошибка при изучении core слова ${i + 1}:`, error.message);
        return { success: false, error: `Failed to learn core word ${i + 1}`, details: error.message };
      }
    }

    return {
      success: true,
      core_words_learned: coreWordsLearned,
      message: 'Core слова изучены, но переход на extended не протестирован'
    };

    let currentCard;
    try {
      currentCard = await this.getMainCard();
      this.log(`📝 7-я карточка: "${currentCard.word}" (ID: ${currentCard.word_id})`);
    } catch (error) {
      this.log('❌ Основной запрос провалился на 7-й карточке:', error.message);
      return { success: false, error: 'main_request_failed_on_7th_card', details: error.message };
    }

    // ЭТАП 3: Первый запрос предзагрузки (может вернуть дублирующуюся карточку)
    this.log('\n🔍 ЭТАП 3: Первый запрос предзагрузки');

    let firstPreloadCard;
    try {
      firstPreloadCard = await this.preloadNextCard(currentCard.word_id);
      this.log(`🔄 Первая предзагрузка: "${firstPreloadCard.word}" (ID: ${firstPreloadCard.word_id})`);

      // Проверяем дублирование
      if (firstPreloadCard.word_id === currentCard.word_id) {
        this.log('🚨 ДУБЛИРОВАНИЕ ОБНАРУЖЕНО! Первый запрос вернул ту же карточку');

        // ЭТАП 4: Второй запрос альтернативной карточки (КРИТИЧЕСКАЯ ТОЧКА)
        this.log('\n🔍 ЭТАП 4: Второй запрос альтернативной карточки (как в cardPreloader.ts)');

        try {
          const alternativeCard = await this.preloadNextCard(currentCard.word_id);
          this.log(`✅ Альтернативная карточка найдена: "${alternativeCard.word}" (ID: ${alternativeCard.word_id})`);

          return {
            success: true,
            problem_detected: false,
            duplication_handled: true,
            learned_cards: learnedCards.length
          };

        } catch (alternativeError) {
          if (alternativeError.message.includes('404') || alternativeError.message.includes('No cards available')) {
            this.log('🎯 ПРОБЛЕМА ВОСПРОИЗВЕДЕНА! Второй запрос вернул 404');
            this.log('🔍 Это точно соответствует логике cardPreloader.ts');

            return {
              success: false,
              problem_detected: true,
              issue: 'alternative_card_404_after_duplication',
              learned_cards_count: learnedCards.length,
              current_card: currentCard,
              first_preload_card: firstPreloadCard,
              error_details: alternativeError.message
            };
          } else {
            this.log('❌ Неожиданная ошибка во втором запросе:', alternativeError.message);
            return { success: false, error: 'unexpected_alternative_error', details: alternativeError.message };
          }
        }

      } else {
        this.log('✅ Дублирования нет - первый запрос вернул другую карточку');
        return {
          success: true,
          problem_detected: false,
          no_duplication: true,
          learned_cards: learnedCards.length
        };
      }

    } catch (firstPreloadError) {
      if (firstPreloadError.message.includes('404') || firstPreloadError.message.includes('No cards available')) {
        this.log('🎯 ПРОБЛЕМА ВОСПРОИЗВЕДЕНА! Первый запрос предзагрузки вернул 404');

        return {
          success: false,
          problem_detected: true,
          issue: 'first_preload_404',
          learned_cards_count: learnedCards.length,
          current_card: currentCard,
          error_details: firstPreloadError.message
        };
      } else {
        this.log('❌ Неожиданная ошибка в первом запросе предзагрузки:', firstPreloadError.message);
        return { success: false, error: 'unexpected_first_preload_error', details: firstPreloadError.message };
      }
    }
  }

  // Эмуляция точной последовательности TrainingScreen
  async simulateTrainingScreenFlow() {
    this.log('🎯 ЭМУЛЯЦИЯ TRAININGSCREEN: Точная последовательность как в реальном приложении');
    this.log('🎯 ЦЕЛЬ: Воспроизвести проблему на 7-й карточке (6 слов в прогрессе)');

    const cardHistory = [];
    let currentCard = null;
    let preloadedCard = null;

    // Ограничиваем до 10 карточек, чтобы точно воспроизвести проблему на 7-й
    for (let cardNumber = 1; cardNumber <= 10; cardNumber++) {
      this.log(`\n📖 === КАРТОЧКА ${cardNumber} ===`);

      try {
        // ЭТАП 1: Получаем основную карточку (как fetchNewCard в TrainingScreen)
        this.log('🔍 ЭТАП 1: Основной запрос (fetchNewCard)');
        currentCard = await this.getMainCard();

        this.log(`📝 Получена карточка: "${currentCard.word}" (ID: ${currentCard.word_id})`);
        cardHistory.push({
          cardNumber,
          word: currentCard.word,
          word_id: currentCard.word_id,
          action: 'main_request'
        });

        // ЭТАП 2: Запускаем предзагрузку (как startPreloading в TrainingScreen)
        this.log('🔍 ЭТАП 2: Предзагрузка следующей карточки (startPreloading)');

        try {
          preloadedCard = await this.preloadNextCard(currentCard.word_id);
          this.log(`🔄 Предзагружена карточка: "${preloadedCard.word}" (ID: ${preloadedCard.word_id})`);

          // Проверяем дублирование (как в TrainingScreen)
          if (preloadedCard.word_id === currentCard.word_id) {
            this.log('⚠️ ДУБЛИРОВАНИЕ: Предзагруженная карточка та же, что и текущая!');
            return {
              success: false,
              problem_detected: true,
              issue: 'preload_duplication',
              card_number: cardNumber,
              current_card: currentCard,
              preloaded_card: preloadedCard
            };
          }

        } catch (preloadError) {
          if (preloadError.message.includes('404') || preloadError.message.includes('No cards available')) {
            this.log('🚨 ПРОБЛЕМА ОБНАРУЖЕНА: Предзагрузка вернула 404!');
            return {
              success: false,
              problem_detected: true,
              issue: 'preload_404_error',
              card_number: cardNumber,
              current_card: currentCard,
              learned_cards_count: cardHistory.length - 1,
              error_details: preloadError.message
            };
          } else {
            this.log('❌ НЕОЖИДАННАЯ ОШИБКА в предзагрузке:', preloadError.message);
            throw preloadError;
          }
        }

        // ЭТАП 3: Отвечаем на текущую карточку (как checkAnswer в TrainingScreen)
        this.log('🔍 ЭТАП 3: Ответ на карточку (checkAnswer)');
        const answerResult = await this.submitAnswer(currentCard.word_id, true);

        this.log(`📊 Результат: interval_level=${answerResult.interval_level}, is_learned=${answerResult.is_learned}`);

        // Проверяем, что слово выучено
        if (answerResult.is_learned && answerResult.interval_level === 15) {
          this.log(`🎉 Слово "${currentCard.word}" выучено!`);

          // КРИТИЧЕСКАЯ ПРОВЕРКА: Анализируем состояние после изучения
          this.log(`📊 АНАЛИЗ СОСТОЯНИЯ ПОСЛЕ КАРТОЧКИ ${cardNumber}:`);
          this.log(`   - Выучено слов: ${cardNumber}`);
          this.log(`   - Осталось в словаре: ${15 - cardNumber} слов`);

          // Если это 6-я карточка, делаем детальный анализ
          if (cardNumber === 6) {
            this.log('🚨 КРИТИЧЕСКАЯ ТОЧКА: 6 слов выучено - следующая предзагрузка должна упасть!');
            this.log('🔍 Ожидаем 404 ошибку на следующей предзагрузке...');
          }
        }

        // ЭТАП 4: Переход к предзагруженной карточке (как animateCardOutWithPreload)
        this.log('🔍 ЭТАП 4: Переход к предзагруженной карточке (animateCardOutWithPreload)');

        if (preloadedCard) {
          this.log(`✅ Используем предзагруженную карточку: "${preloadedCard.word}"`);
          // В реальном приложении предзагруженная карточка становится текущей
          // Мы эмулируем это, устанавливая её как текущую для следующей итерации
        }

        // Небольшая пауза как в реальном приложении
        await new Promise(resolve => setTimeout(resolve, 100));

      } catch (error) {
        if (error.message.includes('404') || error.message.includes('No cards available')) {
          this.log(`📚 Основной запрос вернул 404 на карточке ${cardNumber} - словарь исчерпан`);
          break;
        }
        throw error;
      }
    }

    this.log(`\n📊 ИТОГО: Обработано ${cardHistory.length} карточек без проблем`);
    return {
      success: true,
      problem_detected: false,
      cards_processed: cardHistory.length,
      card_history: cardHistory
    };
  }

  // Основной регрессионный тест
  async runVocabularyExhaustionTest() {
    this.log('🧪 === ТЕСТ №3: РЕГРЕССИОННЫЙ ТЕСТ A0 ПРИОРИТЕТОВ ===');
    this.log('🎯 Цель: Проверить автоматическое переключение ultra_core → core в предзагрузке');
    this.log('🔍 Регрессионный тест для стабильности системы приоритетов');
    this.log('');

    // 🧹 Очищаем прогресс пользователя
    this.log('🧹 Очистка прогресса пользователя перед тестом...');
    const clearResult = await clearUserProgress(USER_ID, true);
    if (!clearResult.success) {
      this.log('❌ Не удалось очистить прогресс пользователя');
      return { success: false, error: 'Failed to clear user progress' };
    }
    this.log(`✅ Прогресс очищен (удалено записей: ${clearResult.deleted_count})`);

    try {
      // Запускаем регрессионный тест исчерпания ultra_core приоритета
      const result = await this.testA0PriorityExhaustionRegression();

      if (result.problem_detected) {
        this.log('\n🚨 РЕГРЕССИЯ ОБНАРУЖЕНА!');
        this.log(`📍 Изучено ultra_core слов: ${result.learned_ultra_core_count}`);
        this.log(`🔍 Проблема: ${result.issue}`);
        this.log(`🚨 КРИТИЧНО: Предзагрузка НЕ переключается на core приоритет!`);

        return result;
      } else {
        this.log('\n✅ РЕГРЕССИОННЫЙ ТЕСТ ПРОЙДЕН!');
        this.log('🎉 Предзагрузка корректно переключается между приоритетами A0');
        return result;
      }

      // Финальная проверка
      this.log(`\n📋 === ФИНАЛЬНЫЕ РЕЗУЛЬТАТЫ ===`);
      this.log(`📊 СТАТИСТИКА:`, {
        total_rounds: maxRounds,
        learned_words_count: this.learnedWords.size,
        vocabulary_size: this.vocabularySize,
        problem_detected: this.problemDetected,
        preload_errors_count: this.preloadErrors.length
      });

      // Очищаем прогресс после теста
      this.log('\n🧹 Очистка прогресса пользователя после теста...');
      const clearResultEnd = await clearUserProgress(USER_ID, true);
      this.log(`✅ Прогресс очищен после теста (удалено записей: ${clearResultEnd.deleted_count})`);

      if (this.problemDetected) {
        return { 
          success: false, 
          problem_detected: true,
          vocabulary_size: this.vocabularySize,
          preload_errors: this.preloadErrors
        };
      } else {
        return { 
          success: true, 
          problem_detected: false,
          vocabulary_size: this.vocabularySize
        };
      }

    } catch (error) {
      this.log('💥 КРИТИЧЕСКАЯ ОШИБКА:', error.message);

      // Очищаем прогресс даже при ошибке
      try {
        await clearUserProgress(USER_ID, true);
      } catch (clearError) {
        this.log('❌ Не удалось очистить прогресс после ошибки:', clearError.message);
      }

      return { success: false, error: error.message };
    }
  }
}

// Запуск теста
async function runVocabularyExhaustionTest() {
  console.log('🚀 Запуск Теста №3: Проблема исчерпания словаря при предзагрузке\n');
  
  const tester = new VocabularyExhaustionTester();
  
  try {
    const result = await tester.runVocabularyExhaustionTest();
    
    console.log('\n📊 ФИНАЛЬНЫЙ РЕЗУЛЬТАТ:');
    console.log(JSON.stringify(result, null, 2));
    
    if (result.success) {
      console.log('\n🎉 ТЕСТ ПРОШЕЛ: Проблема не воспроизведена');
      process.exit(0);
    } else {
      console.log('\n❌ ТЕСТ ПРОВАЛЕН: Проблема воспроизведена!');
      if (result.preload_errors && result.preload_errors.length > 0) {
        console.log('\n🚨 ОШИБКИ ПРЕДЗАГРУЗКИ:');
        result.preload_errors.forEach((error, index) => {
          console.log(`${index + 1}. Раунд ${error.round}: ${error.error}`);
        });
      }
      process.exit(1);
    }
    
  } catch (error) {
    console.error('\n💥 КРИТИЧЕСКАЯ ОШИБКА:', error.message);
    process.exit(1);
  }
}

// Запуск
runVocabularyExhaustionTest();
