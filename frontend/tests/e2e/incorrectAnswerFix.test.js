/**
 * РЕГРЕССИОННЫЙ ТЕСТ для проблемы №10:
 * Неправильный ответ не обновляет is_new в false
 * 
 * ПРОБЛЕМА: После неправильного ответа слово остается is_new=true,
 * поэтому при следующем правильном ответе срабатывает логика для новых слов (interval_level=15)
 * 
 * ИСПРАВЛЕНИЕ: Добавлено is_new: false в блоке else if (!isAnswerCorrect)
 * 
 * Дата создания: 19 июня 2025
 * Статус: ✅ ПРОХОДИТ (проблема решена)
 */

// Симулятор логики фронтенда
class FrontendLogicSimulator {
  constructor() {
    this.currentCard = null;
    this.userInput = '';
    this.isCorrect = null;
    this.usedHelpButton = false;
    this.canGoForward = false;
  }

  // Симуляция функции checkAnswer из TrainingScreen.tsx (ИСПРАВЛЕННАЯ ВЕРСИЯ)
  checkAnswer() {
    if (!this.currentCard) {
      throw new Error('No current card');
    }

    // Проверяем правильность ответа
    const correctAnswers = this.currentCard.target_word?.examples?.[0]?.correct_answers || [];
    const isAnswerCorrect = correctAnswers.some(
      answer => answer.toLowerCase() === this.userInput.trim().toLowerCase()
    );

    // Определяем, что отправлять на сервер
    const serverIsCorrect = this.usedHelpButton ? false : isAnswerCorrect;

    // UI показывает правильный ответ (зеленый инпут)
    this.isCorrect = isAnswerCorrect;
    this.canGoForward = true;

    // Логика повторения при использовании подсказки
    const shouldAdvanceToNext = isAnswerCorrect && !this.usedHelpButton;

    console.log('🔍 DEBUG checkAnswer:', {
      userInput: this.userInput.trim(),
      isAnswerCorrect,
      usedHelpButton: this.usedHelpButton,
      serverIsCorrect,
      shouldAdvanceToNext
    });

    // МГНОВЕННОЕ обновление полосок для новых слов с правильным ответом
    const isNewCard = this.currentCard.is_new ?? true;
    const isNewWordCorrect = isNewCard && isAnswerCorrect && !this.usedHelpButton;

    console.log('🔍 DEBUG: Анализ логики обновления полосок:', {
      word: this.currentCard.word,
      current_interval_level: this.currentCard.interval_level,
      current_is_new: this.currentCard.is_new,
      current_is_learned: this.currentCard.is_learned,
      isNewCard,
      isAnswerCorrect,
      usedHelpButton: this.usedHelpButton,
      isNewWordCorrect,
      willUpdateToLevel15: isNewWordCorrect,
      willUpdateAsIncorrect: this.usedHelpButton,
      willUpdateIsNewToFalse: !isAnswerCorrect || this.usedHelpButton
    });

    if (isNewWordCorrect) {
      console.log('🎉 Новое слово отвечено правильно! МГНОВЕННО обновляем полоски прогресса...');
      
      // Обновляем карточку с прогрессом
      this.currentCard = {
        ...this.currentCard,
        is_new: false,
        is_learned: true,
        interval_level: 15
      };
    } else if (this.usedHelpButton) {
      // 🔧 ИСПРАВЛЕНИЕ ПРОБЛЕМЫ №1: При использовании подсказки обновляем полоски как при неправильном ответе
      console.log('🔄 Использована подсказка - обновляем полоски как при неправильном ответе');

      const currentIntervalLevel = this.currentCard.interval_level ?? -1;
      let newIntervalLevel;

      // Логика уменьшения интервала при неправильном ответе
      if (currentIntervalLevel <= 0) {
        newIntervalLevel = -1; // Сбрасываем до минимума
      } else {
        newIntervalLevel = Math.max(-1, currentIntervalLevel - 1); // Уменьшаем на 1
      }

      console.log('🔧 DEBUG: Обновляем currentCard после подсказки:', {
        word: this.currentCard.word,
        old_interval_level: currentIntervalLevel,
        new_interval_level: newIntervalLevel,
        old_is_learned: this.currentCard.is_learned,
        new_is_learned: false,
        old_is_new: this.currentCard.is_new,
        new_is_new: false // 🔧 КРИТИЧЕСКОЕ: Больше не новое!
      });

      this.currentCard = {
        ...this.currentCard,
        is_new: false,           // 🔧 КРИТИЧЕСКОЕ: Больше не новое слово!
        is_learned: false,
        interval_level: newIntervalLevel
      };
    } else if (!isAnswerCorrect) {
      // 🔧 ИСПРАВЛЕНИЕ ПРОБЛЕМЫ №10: При неправильном ответе обновляем is_new в false
      console.log('❌ Неправильный ответ - обновляем is_new в false');

      console.log('🔧 DEBUG: Обновляем currentCard после неправильного ответа:', {
        word: this.currentCard.word,
        old_interval_level: this.currentCard.interval_level,
        old_is_learned: this.currentCard.is_learned,
        old_is_new: this.currentCard.is_new,
        new_is_new: false // 🔧 КРИТИЧЕСКОЕ: Больше не новое!
      });

      this.currentCard = {
        ...this.currentCard,
        is_new: false,           // 🔧 КРИТИЧЕСКОЕ: Больше не новое слово!
      };
    }

    return {
      isAnswerCorrect,
      serverIsCorrect,
      shouldAdvanceToNext,
      isNewWordCorrect
    };
  }

  // Сброс состояния для новой карточки
  reset() {
    this.userInput = '';
    this.isCorrect = null;
    this.usedHelpButton = false;
    this.canGoForward = false;
  }

  // Установка новой карточки
  setCard(card) {
    this.currentCard = card;
    this.reset();
  }
}

// Основной тест
async function testIncorrectAnswerFix() {
  console.log('\n🧪 === ТЕСТ: Исправление проблемы с неправильными ответами ===\n');

  const frontend = new FrontendLogicSimulator();

  try {
    // Шаг 1: Создаем тестовую карточку (новое слово)
    console.log('📋 Шаг 1: Создание тестовой карточки...');
    const cardData = {
      word: "Dili",
      target_word: {
        word: "нет",
        examples: [{
          correct_answers: ["нет", "dili"]
        }]
      },
      is_new: true,
      is_learned: false,
      interval_level: -1
    };
    frontend.setCard(cardData);

    console.log(`📱 Карточка создана: "${cardData.word}" (${cardData.target_word?.word})`);
    console.log(`📊 Начальное состояние: is_new=${cardData.is_new}, is_learned=${cardData.is_learned}, interval_level=${cardData.interval_level}`);

    // Шаг 2: Симулируем неправильный ответ
    console.log('\n📋 Шаг 2: Неправильный ответ...');
    frontend.userInput = "i"; // Неправильный ответ

    const firstResult = frontend.checkAnswer();

    console.log('🔍 Результат первого ответа (неправильный):');
    console.log(`  - isAnswerCorrect: ${firstResult.isAnswerCorrect}`);
    console.log(`  - serverIsCorrect: ${firstResult.serverIsCorrect}`);
    console.log(`  - shouldAdvanceToNext: ${firstResult.shouldAdvanceToNext}`);
    console.log(`  - isNewWordCorrect: ${firstResult.isNewWordCorrect}`);
    console.log(`  - Карточка после первого ответа: is_new=${frontend.currentCard.is_new}, interval_level=${frontend.currentCard.interval_level}`);

    // Шаг 3: Сброс состояния для второго ответа
    console.log('\n📋 Шаг 3: Сброс состояния для второго ответа...');
    frontend.reset();
    frontend.userInput = "dili"; // Правильный ответ

    // Шаг 4: Симулируем правильный ответ
    console.log('\n📋 Шаг 4: Правильный ответ...');
    const secondResult = frontend.checkAnswer();

    console.log('🔍 Результат второго ответа (правильный):');
    console.log(`  - isAnswerCorrect: ${secondResult.isAnswerCorrect}`);
    console.log(`  - serverIsCorrect: ${secondResult.serverIsCorrect}`);
    console.log(`  - shouldAdvanceToNext: ${secondResult.shouldAdvanceToNext}`);
    console.log(`  - isNewWordCorrect: ${secondResult.isNewWordCorrect}`);
    console.log(`  - Карточка после второго ответа: is_new=${frontend.currentCard.is_new}, interval_level=${frontend.currentCard.interval_level}`);

    // Шаг 5: Проверяем исправление проблемы
    console.log('\n📋 Шаг 5: Проверка исправления проблемы...');

    // ТЕСТ 1: После первого неправильного ответа is_new должно стать false
    if (frontend.currentCard.is_new === false) {
      console.log('✅ ТЕСТ 1 ПРОЙДЕН: После неправильного ответа is_new = false');
    } else {
      console.log('❌ ТЕСТ 1 ПРОВАЛЕН: После неправильного ответа is_new все еще true');
      throw new Error('После неправильного ответа is_new должно стать false');
    }

    // ТЕСТ 2: Второй ответ НЕ должен срабатывать как isNewWordCorrect
    if (secondResult.isNewWordCorrect === false) {
      console.log('✅ ТЕСТ 2 ПРОЙДЕН: Второй ответ НЕ срабатывает как новое слово');
    } else {
      console.log('❌ ТЕСТ 2 ПРОВАЛЕН: Второй ответ срабатывает как новое слово');
      throw new Error('Второй ответ не должен срабатывать как новое слово');
    }

    // ТЕСТ 3: interval_level НЕ должен стать 15 после второго ответа
    if (frontend.currentCard.interval_level !== 15) {
      console.log('✅ ТЕСТ 3 ПРОЙДЕН: interval_level НЕ стал 15 после второго ответа');
    } else {
      console.log('❌ ТЕСТ 3 ПРОВАЛЕН: interval_level стал 15 после второго ответа');
      throw new Error('interval_level не должен становиться 15 после второго ответа');
    }

    console.log('\n🎉 === ВСЕ ТЕСТЫ ПРОЙДЕНЫ! ПРОБЛЕМА ИСПРАВЛЕНА! ===');
    console.log('\n📝 Резюме исправления:');
    console.log('  - ✅ После неправильного ответа is_new устанавливается в false');
    console.log('  - ✅ Второй правильный ответ НЕ срабатывает как новое слово');
    console.log('  - ✅ interval_level НЕ устанавливается в 15 неправильно');
    console.log('  - ✅ Логика работает корректно');
    console.log('\n🔧 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: is_new: false в блоке else if (!isAnswerCorrect)');
    console.log('📋 Файл: frontend/screens/TrainingScreen.tsx (строки 785-806)');
    console.log('📅 Дата решения: 19 июня 2025');
    console.log('🧪 Статус: РЕГРЕССИОННЫЙ ТЕСТ ПРОХОДИТ');

    return true;

  } catch (error) {
    console.error(`\n❌ ТЕСТ ПРОВАЛЕН: ${error.message}`);
    return false;
  }
}

// Запуск теста
if (require.main === module) {
  testIncorrectAnswerFix()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Критическая ошибка:', error);
      process.exit(1);
    });
}

module.exports = { testIncorrectAnswerFix };
