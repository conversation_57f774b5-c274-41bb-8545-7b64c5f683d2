/**
 * Тест retry сервиса для отправки ответов на карточки
 * 
 * Этот тест проверяет:
 * 1. Успешную отправку при работающем сервере
 * 2. Добавление в очередь при ошибке сети
 * 3. Повторные попытки отправки
 * 4. Экспоненциальный backoff
 */

// Мокаем AsyncStorage для тестов
const mockAsyncStorage = {
  storage: {},
  getItem: jest.fn((key) => Promise.resolve(mockAsyncStorage.storage[key] || null)),
  setItem: jest.fn((key, value) => {
    mockAsyncStorage.storage[key] = value;
    return Promise.resolve();
  }),
  removeItem: jest.fn((key) => {
    delete mockAsyncStorage.storage[key];
    return Promise.resolve();
  }),
  clear: jest.fn(() => {
    mockAsyncStorage.storage = {};
    return Promise.resolve();
  })
};

// Мокаем fetch
global.fetch = jest.fn();

// Мокаем logger
const mockLog = {
  success: jest.fn(),
  warning: jest.fn(),
  info: jest.fn(),
  error: jest.fn()
};

jest.mock('@react-native-async-storage/async-storage', () => mockAsyncStorage);
jest.mock('../utils/logger', () => ({
  log: mockLog,
  LogCategory: {
    API: 'API'
  }
}));

// Импортируем retry сервис после моков
const RetryService = require('../services/retryService').default;

describe('RetryService', () => {
  let retryService;

  beforeEach(() => {
    jest.clearAllMocks();
    mockAsyncStorage.clear();
    
    // Создаем новый экземпляр с быстрыми настройками для тестов
    retryService = new (require('../services/retryService').default.constructor)({
      maxAttempts: 3,
      baseDelay: 100, // 100ms для быстрых тестов
      maxDelay: 1000,
      backoffMultiplier: 2
    });
  });

  afterEach(() => {
    jest.clearAllTimers();
  });

  test('успешная отправка при работающем сервере', async () => {
    // Мокаем успешный ответ
    const mockResponse = {
      ok: true,
      json: () => Promise.resolve({ success: true, interval_level: 15 })
    };
    fetch.mockResolvedValueOnce(mockResponse);

    const result = await retryService.sendWithRetry(
      'http://test.com/api/response',
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ test: 'data' })
      },
      {
        cardId: 'test-card-id',
        userId: 'test-user-id',
        isCorrect: true,
        responseTime: 2.5
      }
    );

    expect(result).toBe(mockResponse);
    expect(fetch).toHaveBeenCalledTimes(1);
    expect(mockLog.success).toHaveBeenCalledWith(
      'API',
      'Ответ успешно отправлен на сервер',
      { cardId: 'test-card-id', isCorrect: true }
    );
  });

  test('добавление в очередь при ошибке сети', async () => {
    // Мокаем ошибку сети
    fetch.mockRejectedValueOnce(new Error('Network error'));

    const result = await retryService.sendWithRetry(
      'http://test.com/api/response',
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ test: 'data' })
      },
      {
        cardId: 'test-card-id',
        userId: 'test-user-id',
        isCorrect: true,
        responseTime: 2.5
      }
    );

    expect(result).toBeNull();
    expect(fetch).toHaveBeenCalledTimes(1);
    expect(mockLog.warning).toHaveBeenCalledWith(
      'API',
      'Ошибка отправки, добавляем в очередь повторов',
      { error: 'Network error', cardId: 'test-card-id' }
    );
    expect(mockLog.info).toHaveBeenCalledWith(
      'API',
      'Запрос добавлен в очередь повторов',
      expect.objectContaining({
        cardId: 'test-card-id',
        queueSize: 1
      })
    );
  });

  test('получение статистики очереди', async () => {
    // Добавляем запрос в очередь
    fetch.mockRejectedValueOnce(new Error('Network error'));
    
    await retryService.sendWithRetry(
      'http://test.com/api/response',
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ test: 'data' })
      },
      {
        cardId: 'test-card-id',
        userId: 'test-user-id',
        isCorrect: true,
        responseTime: 2.5
      }
    );

    const stats = await retryService.getQueueStats();
    expect(stats.totalRequests).toBe(1);
    expect(stats.oldestRequest).toBeDefined();
    expect(typeof stats.oldestRequest).toBe('number');
  });

  test('очистка очереди', async () => {
    // Добавляем запрос в очередь
    fetch.mockRejectedValueOnce(new Error('Network error'));
    
    await retryService.sendWithRetry(
      'http://test.com/api/response',
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ test: 'data' })
      },
      {
        cardId: 'test-card-id',
        userId: 'test-user-id',
        isCorrect: true,
        responseTime: 2.5
      }
    );

    // Проверяем, что запрос в очереди
    let stats = await retryService.getQueueStats();
    expect(stats.totalRequests).toBe(1);

    // Очищаем очередь
    await retryService.clearQueue();

    // Проверяем, что очередь пуста
    stats = await retryService.getQueueStats();
    expect(stats.totalRequests).toBe(0);
  });
});

console.log('🧪 Retry Service Test готов к запуску');
console.log('📝 Для запуска используйте: npm test retryService.test.js');
