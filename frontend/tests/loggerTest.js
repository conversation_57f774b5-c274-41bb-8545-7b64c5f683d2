/**
 * Демонстрация новой системы логирования
 */

console.log('🧪 === ДЕМОНСТРАЦИЯ НОВОЙ СИСТЕМЫ ЛОГИРОВАНИЯ ===\n');

// Симуляция форматирования времени
function formatTime() {
  const now = new Date();
  return now.toTimeString().split(' ')[0] + '.' + now.getMilliseconds().toString().padStart(3, '0');
}

// Симуляция логов
function demoLog(level, category, message, data) {
  const time = formatTime();
  const icons = {
    'INFO': 'ℹ️',
    'SUCCESS': '✅',
    'WARNING': '⚠️',
    'ERROR': '❌',
    'TIMING': '⏱️'
  };

  const categoryIcons = {
    'CARD_LOADING': '📱',
    'PRELOADER': '🚀',
    'USER_INPUT': '👤',
    'API': '🌐'
  };

  const icon = icons[level];
  const categoryIcon = categoryIcons[category];

  console.log(`${icon} [${time}] ${categoryIcon} ${category}: ${message}`);
  if (data) {
    console.log('   📋 Data:', data);
  }
}

// Демонстрация разных типов логов
console.log('');
console.log('🔸 === ЗАГРУЗКА КАРТОЧЕК ===');
console.log('');

demoLog('INFO', 'CARD_LOADING', 'Начинаем загрузку карточки для пользователя 3fcb316b', { language: 'ru' });

setTimeout(() => {
  demoLog('SUCCESS', 'CARD_LOADING', 'Карточка загружена: "что" (what)', { loadTime: '1250ms' });
}, 1000);

setTimeout(() => {
  console.log('');
  console.log('🔸 === ПРЕДЗАГРУЗКА ===');
  console.log('');

  demoLog('INFO', 'PRELOADER', 'Начинаем предзагрузку для 3fcb316b', { language: 'ru' });

  setTimeout(() => {
    demoLog('SUCCESS', 'PRELOADER', 'Предзагружена карточка: "нет"', { loadTime: '2100ms' });
  }, 2000);

  setTimeout(() => {
    demoLog('SUCCESS', 'PRELOADER', 'МГНОВЕННЫЙ переход к предзагруженной карточке: "нет"');
  }, 2500);
}, 2000);

setTimeout(() => {
  console.log('');
  console.log('🔸 === ПОЛЬЗОВАТЕЛЬСКИЙ ВВОД ===');
  console.log('');

  demoLog('SUCCESS', 'USER_INPUT', 'Ответ на "что": ПРАВИЛЬНО', { responseTime: '3.2s' });
  demoLog('WARNING', 'USER_INPUT', 'Ответ на "где": НЕПРАВИЛЬНО (с подсказкой)', { responseTime: '2.8s' });
}, 5000);

setTimeout(() => {
  console.log('');
  console.log('🔸 === API ЗАПРОСЫ ===');
  console.log('');

  demoLog('INFO', 'API', 'Запрос к /api/spaced/next', { user_id: '123', preload: true });

  setTimeout(() => {
    demoLog('SUCCESS', 'API', 'Ответ от /api/spaced/next (200)', { responseTime: '1850ms' });
  }, 1500);
}, 7000);

setTimeout(() => {
  console.log('');
  console.log('🔸 === ТАЙМИНГИ ===');
  console.log('');

  demoLog('TIMING', 'API', 'Общее время загрузки карточки: 3420ms', { network: '1850ms', processing: '1570ms' });
  demoLog('TIMING', 'API', 'Анимация перехода: 432ms');

  setTimeout(() => {
    console.log('');
    console.log('🎉 === ДЕМОНСТРАЦИЯ ЗАВЕРШЕНА ===');
    console.log('');
    console.log('✅ Преимущества новой системы логирования:');
    console.log('   • Единообразное форматирование с временными метками');
    console.log('   • Категоризация по типам (карточки, предзагрузка, API, etc.)');
    console.log('   • Цветовые индикаторы через эмодзи');
    console.log('   • Структурированные данные');
    console.log('   • Разделители между логическими блоками');
    console.log('   • Легко отключается в продакшене');
    console.log('');
  }, 2000);
}, 10000);

console.log('✅ Демонстрация запущена! Логи будут появляться в течение 15 секунд...\n');
