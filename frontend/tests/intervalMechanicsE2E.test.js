/**
 * E2E тест механики интервалов с симуляцией реального пользователя
 * 
 * Проверяет:
 * 1. Неправильный ответ → форсированная очередь
 * 2. Правильный ответ → переход в интервальную очередь
 * 3. Предзагрузка не возвращает недавно отвеченные слова
 * 4. Интервальные переходы работают корректно
 */

// Мокаем API с реалистичным поведением
class MockSpacedRepetitionAPI {
  constructor() {
    this.userProgress = new Map(); // word_id -> progress
    this.words = [
      { _id: 'word1', word: 'ako', translation: 'я', language: 'cb' },
      { _id: 'word2', word: 'ikaw', translation: 'ты', language: 'cb' },
      { _id: 'word3', word: 'asa', translation: 'где', language: 'cb' },
      { _id: 'word4', word: 'unsa', translation: 'что', language: 'cb' },
      { _id: 'word5', word: 'oo', translation: 'да', language: 'cb' }
    ];
    this.currentTime = Date.now();
    this.intervals = [0.5, 5, 10, 60, 1440]; // минуты
  }

  // Симулируем прохождение времени
  advanceTime(minutes) {
    this.currentTime += minutes * 60 * 1000;
    console.log(`⏰ Время продвинуто на ${minutes} минут`);
  }

  // Получить следующую карточку
  async getNextCard(userId, nativeLang, targetLang, preload = false) {
    console.log(`\n🔍 API: getNextCard (preload=${preload})`);
    
    const now = this.currentTime;
    const recentThreshold = now - (2 * 60 * 1000); // 2 минуты назад

    // 1. Проверяем форсированную очередь (только если не предзагрузка)
    if (!preload) {
      for (const [wordId, progress] of this.userProgress) {
        if (progress.force_review) {
          const word = this.words.find(w => w._id === wordId);
          console.log(`✅ Возвращаем из форсированной очереди: ${word.word}`);
          return this.formatCard(word, progress);
        }
      }
    } else {
      console.log(`🚀 Пропускаем форсированную очередь для предзагрузки`);
    }

    // 2. Проверяем активную очередь (исключаем недавно отвеченные)
    for (const [wordId, progress] of this.userProgress) {
      if (
        progress.interval_level >= 0 && 
        !progress.force_review && 
        !progress.is_learned &&
        progress.next_review <= now &&
        progress.last_reviewed <= recentThreshold
      ) {
        const word = this.words.find(w => w._id === wordId);
        console.log(`✅ Возвращаем из активной очереди: ${word.word} (исключение работает)`);
        return this.formatCard(word, progress);
      }
    }

    // 3. Возвращаем новое слово (только если не предзагрузка или нет активных слов)
    for (const word of this.words) {
      if (!this.userProgress.has(word._id)) {
        console.log(`✅ Возвращаем новое слово: ${word.word}`);

        // Создаем прогресс для нового слова (НЕ добавляем в прогресс при предзагрузке)
        if (!preload) {
          this.userProgress.set(word._id, {
            interval_level: -1,
            correct_answers: 0,
            incorrect_answers: 0,
            last_reviewed: now,
            next_review: now,
            is_learned: false,
            force_review: false
          });
        }

        return this.formatCard(word, {
          interval_level: -1,
          correct_answers: 0,
          incorrect_answers: 0,
          last_reviewed: now,
          next_review: now,
          is_learned: false,
          force_review: false
        });
      }
    }

    console.log(`❌ Нет доступных слов`);
    return null;
  }

  // Обработать ответ пользователя
  async processAnswer(cardId, isCorrect) {
    console.log(`\n📝 API: processAnswer (${cardId}, ${isCorrect ? 'CORRECT' : 'INCORRECT'})`);
    
    const progress = this.userProgress.get(cardId);
    if (!progress) {
      throw new Error(`Progress not found for card ${cardId}`);
    }

    const now = this.currentTime;
    const currentLevel = progress.interval_level;

    if (isCorrect) {
      progress.correct_answers++;
      
      // Новое слово с первого раза правильно → выучено
      if (currentLevel === -1 && progress.correct_answers === 1 && progress.incorrect_answers === 0) {
        progress.interval_level = this.intervals.length - 1;
        progress.is_learned = true;
        progress.next_review = now + (365 * 24 * 60 * 60 * 1000); // год
        console.log(`🎉 Новое слово выучено с первого раза!`);
      } else {
        // Обычный переход на следующий уровень
        const newLevel = currentLevel + 1;
        progress.interval_level = newLevel;
        
        if (newLevel < this.intervals.length) {
          const intervalMinutes = this.intervals[newLevel];
          progress.next_review = now + (intervalMinutes * 60 * 1000);
          console.log(`📈 Уровень: ${currentLevel} → ${newLevel}, интервал: ${intervalMinutes} мин`);
        } else {
          progress.is_learned = true;
          progress.next_review = now + (365 * 24 * 60 * 60 * 1000);
          console.log(`🎓 Слово выучено! Уровень: ${newLevel}`);
        }
      }
      
      progress.force_review = false;
    } else {
      progress.incorrect_answers++;
      progress.interval_level = Math.max(-1, currentLevel - 1);
      progress.next_review = now;
      progress.force_review = true;
      console.log(`📉 Неправильный ответ, уровень: ${currentLevel} → ${progress.interval_level}`);
    }

    progress.last_reviewed = now;
    
    return {
      interval_level: progress.interval_level,
      is_correct: isCorrect,
      is_learned: progress.is_learned,
      next_review: new Date(progress.next_review).toISOString()
    };
  }

  formatCard(word, progress) {
    return {
      word_id: word._id,
      word: word.word,
      translation: word.translation,
      language: word.language,
      is_new: progress.interval_level === -1,
      is_forced_review: progress.force_review,
      interval_level: progress.interval_level
    };
  }

  // Получить состояние прогресса для отладки
  getProgressState() {
    const state = {};
    for (const [wordId, progress] of this.userProgress) {
      const word = this.words.find(w => w._id === wordId);
      state[word.word] = {
        interval_level: progress.interval_level,
        force_review: progress.force_review,
        next_review: new Date(progress.next_review).toLocaleTimeString(),
        last_reviewed: new Date(progress.last_reviewed).toLocaleTimeString()
      };
    }
    return state;
  }
}

// Мокаем предзагрузчик
class MockCardPreloader {
  constructor(api) {
    this.api = api;
    this.cache = null;
    this.isLoading = false;
  }

  async startPreloading(userId, nativeLang, targetLang) {
    if (this.isLoading) return;
    
    this.isLoading = true;
    console.log(`🚀 PRELOADER: Начинаем предзагрузку`);
    
    try {
      const card = await this.api.getNextCard(userId, nativeLang, targetLang, true);
      this.cache = card;
      this.isLoading = false;
      
      if (card) {
        console.log(`✅ PRELOADER: Предзагружена карточка: ${card.word}`);
      } else {
        console.log(`❌ PRELOADER: Карточка не найдена`);
      }
    } catch (error) {
      this.isLoading = false;
      console.log(`❌ PRELOADER: Ошибка: ${error.message}`);
    }
  }

  getPreloadedCard() {
    if (this.cache && !this.isLoading) {
      const card = this.cache;
      this.cache = null;
      console.log(`📦 PRELOADER: Используем предзагруженную карточку: ${card.word}`);
      return card;
    }
    return null;
  }

  clear() {
    this.cache = null;
    this.isLoading = false;
  }
}

// Симулятор пользователя
class UserSimulator {
  constructor() {
    this.api = new MockSpacedRepetitionAPI();
    this.preloader = new MockCardPreloader(this.api);
    this.currentCard = null;
    this.userId = 'test_user';
    this.nativeLang = 'ru';
    this.targetLang = 'cb';
  }

  async loadNextCard() {
    // Пытаемся использовать предзагруженную карточку
    let card = this.preloader.getPreloadedCard();
    
    if (!card) {
      console.log(`📱 Загружаем карточку обычным способом`);
      card = await this.api.getNextCard(this.userId, this.nativeLang, this.targetLang, false);
    }
    
    this.currentCard = card;
    
    // Запускаем предзагрузку следующей карточки
    if (card) {
      await this.preloader.startPreloading(this.userId, this.nativeLang, this.targetLang);
    }
    
    return card;
  }

  async answerCard(isCorrect) {
    if (!this.currentCard) {
      throw new Error('No current card to answer');
    }

    const result = await this.api.processAnswer(this.currentCard.word_id, isCorrect);
    
    // Запускаем предзагрузку после ответа
    await this.preloader.startPreloading(this.userId, this.nativeLang, this.targetLang);
    
    return result;
  }

  advanceTime(minutes) {
    this.api.advanceTime(minutes);
  }

  getProgressState() {
    return this.api.getProgressState();
  }
}

// Основной тест
async function runIntervalMechanicsTest() {
  console.log('🧪 Запуск E2E теста механики интервалов');
  console.log('=' * 60);

  const user = new UserSimulator();
  let testsPassed = 0;
  let totalTests = 0;

  function assert(condition, message) {
    totalTests++;
    if (condition) {
      console.log(`✅ ${message}`);
      testsPassed++;
    } else {
      console.log(`❌ ${message}`);
    }
  }

  try {
    // Тест 1: Получение нового слова
    console.log('\n1️⃣ Тест: Получение нового слова');
    const card1 = await user.loadNextCard();
    assert(card1 && card1.is_new, 'Получено новое слово');
    assert(card1.word === 'ako', 'Получено ожидаемое слово "ako"');

    // Тест 2: Неправильный ответ → форсированная очередь
    console.log('\n2️⃣ Тест: Неправильный ответ');
    await user.answerCard(false);
    
    const card2 = await user.loadNextCard();
    assert(card2.word === 'ako', 'То же слово вернулось после неправильного ответа');
    assert(card2.is_forced_review, 'Слово в форсированной очереди');

    // Тест 3: Правильный ответ → переход в интервальную очередь
    console.log('\n3️⃣ Тест: Правильный ответ');
    await user.answerCard(true);
    
    const card3 = await user.loadNextCard();
    assert(card3.word !== 'ako', 'Получено другое слово после правильного ответа');
    assert(card3.word === 'ikaw', 'Получено следующее новое слово');

    // Тест 4: Проверка исключения недавно отвеченных слов
    console.log('\n4️⃣ Тест: Исключение недавно отвеченных слов');
    console.log('Состояние прогресса:', user.getProgressState());
    
    // Продвигаем время на 1.5 минуты (меньше порога в 2 минуты)
    user.advanceTime(1.5);
    
    const card4 = await user.loadNextCard();
    assert(card4.word !== 'ako', 'Слово "ako" исключено (< 2 минут)');

    // Тест 5: Слово появляется после истечения времени исключения
    console.log('\n5️⃣ Тест: Слово появляется после истечения времени');
    user.advanceTime(1); // Общее время: 2.5 минуты
    
    const card5 = await user.loadNextCard();
    assert(card5.word === 'ako', 'Слово "ako" появилось после истечения времени исключения');

    // Тест 6: Цикл интервалов
    console.log('\n6️⃣ Тест: Цикл интервалов');
    await user.answerCard(true); // ako: 0 → 1 (5 минут)
    
    const card6 = await user.loadNextCard();
    assert(card6.word !== 'ako', 'Слово исключено после правильного ответа');
    
    // Продвигаем время на 6 минут
    user.advanceTime(6);
    
    const card7 = await user.loadNextCard();
    assert(card7.word === 'ako', 'Слово появилось через 5+ минут');

    console.log('\n📊 Финальное состояние прогресса:');
    console.log(user.getProgressState());

  } catch (error) {
    console.error('❌ Ошибка в тесте:', error);
  }

  console.log(`\n🏁 Тест завершен: ${testsPassed}/${totalTests} тестов пройдено`);
  
  if (testsPassed === totalTests) {
    console.log('🎉 Все тесты пройдены успешно!');
  } else {
    console.log('⚠️ Некоторые тесты не пройдены');
  }
}

// Тест с реальным API
class RealAPITester {
  constructor(apiBaseUrl = 'http://localhost:8000') {
    this.apiBaseUrl = apiBaseUrl;
    this.userId = 'test_user_' + Date.now();
  }

  async getNextCard(preload = false) {
    const params = new URLSearchParams({
      user_id: this.userId,
      native_lang: 'ru',
      target_lang: 'cb',
      preload: preload.toString()
    });

    const response = await fetch(`${this.apiBaseUrl}/api/spaced/next?${params}`);
    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`);
    }
    return await response.json();
  }

  async submitAnswer(cardId, isCorrect) {
    const response = await fetch(`${this.apiBaseUrl}/api/spaced/${cardId}/response`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ is_correct: isCorrect })
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`);
    }
    return await response.json();
  }

  async runRealAPITest() {
    console.log('\n🌐 Тест с реальным API');
    console.log('=' * 40);

    try {
      // 1. Получаем новое слово
      console.log('\n1️⃣ Получаем новое слово');
      const card1 = await this.getNextCard(false);
      console.log(`✅ Получено: ${card1.word} (${card1.translation})`);

      // 2. Предзагружаем следующее
      console.log('\n2️⃣ Предзагружаем следующее слово');
      const preloaded1 = await this.getNextCard(true);
      console.log(`🚀 Предзагружено: ${preloaded1.word} (${preloaded1.translation})`);

      // 3. Отвечаем неправильно
      console.log('\n3️⃣ Отвечаем неправильно');
      await this.submitAnswer(card1.word_id, false);
      console.log(`❌ Ответили неправильно на: ${card1.word}`);

      // 4. Проверяем, что то же слово вернулось
      console.log('\n4️⃣ Проверяем форсированную очередь');
      const card2 = await this.getNextCard(false);
      if (card2.word === card1.word) {
        console.log(`✅ Слово вернулось в форсированную очередь: ${card2.word}`);
      } else {
        console.log(`❌ ОШИБКА: Ожидалось ${card1.word}, получено ${card2.word}`);
      }

      // 5. Отвечаем правильно
      console.log('\n5️⃣ Отвечаем правильно');
      const result = await this.submitAnswer(card2.word_id, true);
      console.log(`✅ Правильный ответ, interval_level: ${result.interval_level}`);

      // 6. Проверяем исключение недавно отвеченных слов
      console.log('\n6️⃣ Проверяем исключение недавно отвеченных слов');
      const card3 = await this.getNextCard(false);
      if (card3.word !== card1.word) {
        console.log(`✅ Недавно отвеченное слово исключено: получено ${card3.word} вместо ${card1.word}`);
      } else {
        console.log(`❌ ОШИБКА: Недавно отвеченное слово НЕ исключено!`);
      }

      // 7. Проверяем предзагрузку
      console.log('\n7️⃣ Проверяем предзагрузку');
      const preloaded2 = await this.getNextCard(true);
      if (preloaded2.word !== card1.word) {
        console.log(`✅ Предзагрузка тоже исключает недавно отвеченные слова: ${preloaded2.word}`);
      } else {
        console.log(`❌ ОШИБКА: Предзагрузка НЕ исключает недавно отвеченные слова!`);
      }

      console.log('\n🎉 Тест с реальным API завершен успешно!');

    } catch (error) {
      console.error('\n❌ Ошибка в тесте с реальным API:', error.message);
    }
  }
}

// Запуск тестов
async function runAllTests() {
  // Мок-тест
  await runIntervalMechanicsTest();

  // Тест с реальным API (если доступен)
  try {
    const realTester = new RealAPITester();
    await realTester.runRealAPITest();
  } catch (error) {
    console.log('\n⚠️ Тест с реальным API пропущен (API недоступен)');
  }
}

// Запуск теста
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runIntervalMechanicsTest,
    UserSimulator,
    MockSpacedRepetitionAPI,
    RealAPITester,
    runAllTests
  };
} else {
  runAllTests();
}
