/**
 * Тесты логики предзагрузки карточек
 * Симулируют различные сценарии взаимодействия пользователя
 */

// Мокаем API и сервисы
const mockAPI = {
  // Симуляция задержки сети
  networkDelay: 1000,
  
  // Счетчик запросов для отслеживания
  requestCount: 0,
  
  // Мокаем получение следующей карточки
  async getNextCard(userId, nativeLang, targetLang, preload = false) {
    this.requestCount++;
    const requestId = this.requestCount;
    
    console.log(`🌐 [API REQUEST ${requestId}] Запрос карточки: preload=${preload}, delay=${this.networkDelay}ms`);
    
    // Симулируем задержку сети
    await new Promise(resolve => setTimeout(resolve, this.networkDelay));
    
    // Возвращаем разные карточки в зависимости от preload
    const cards = [
      { id: 'card1', word: 'где', translation: 'where', is_forced_review: true },
      { id: 'card2', word: 'ты', translation: 'you', is_forced_review: false },
      { id: 'card3', word: 'да', translation: 'yes', is_forced_review: false },
      { id: 'card4', word: 'нет', translation: 'no', is_forced_review: false }
    ];
    
    if (preload) {
      // При предзагрузке пропускаем форсированную очередь
      const nonForcedCards = cards.filter(c => !c.is_forced_review);
      const card = nonForcedCards[Math.min(this.requestCount - 1, nonForcedCards.length - 1)];
      console.log(`✅ [API RESPONSE ${requestId}] Предзагрузка: ${card.word} (${card.translation})`);
      return card;
    } else {
      // Обычная загрузка - сначала форсированные
      const card = cards[Math.min(this.requestCount - 1, cards.length - 1)];
      console.log(`✅ [API RESPONSE ${requestId}] Обычная загрузка: ${card.word} (${card.translation})`);
      return card;
    }
  },
  
  // Мокаем отправку ответа
  async submitAnswer(cardId, isCorrect) {
    console.log(`📤 [API] Отправка ответа: cardId=${cardId}, correct=${isCorrect}`);
    await new Promise(resolve => setTimeout(resolve, 200)); // Быстрый ответ
    return { success: true, is_correct: isCorrect };
  }
};

// Мокаем предзагрузчик
class MockPreloader {
  constructor() {
    this.cache = null;
    this.isLoading = false;
    this.loadingPromise = null;
  }
  
  async startPreloading(userId, nativeLang, targetLang) {
    if (this.isLoading) {
            return;
    }
    
        this.isLoading = true;
    
    try {
      this.loadingPromise = mockAPI.getNextCard(userId, nativeLang, targetLang, true);
      const card = await this.loadingPromise;
      
      this.cache = card;
      this.isLoading = false;
      console.log(`[PRELOADER] ✅ Карточка предзагружена: ${card.word}`);
      return card;
    } catch (error) {
      this.isLoading = false;
      console.log(`[PRELOADER] ❌ Ошибка предзагрузки: ${error.message}`);
      throw error;
    }
  }
  
  getPreloadedCard() {
    if (this.cache && !this.isLoading) {
      const card = this.cache;
      this.cache = null; // Очищаем кэш
      console.log(`[PRELOADER] 📦 Используем предзагруженную карточку: ${card.word}`);
      return card;
    }
    
    if (this.isLoading) {
          } else {
          }
    return null;
  }
  
  async waitForPreload(timeoutMs = 2000) {
    if (this.loadingPromise) {
      console.log(`[PRELOADER] ⏳ Ожидаем завершения предзагрузки (${timeoutMs}ms)...`);
      try {
        const timeoutPromise = new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Timeout')), timeoutMs)
        );
        
        const card = await Promise.race([this.loadingPromise, timeoutPromise]);
        console.log(`[PRELOADER] ✅ Дождались предзагрузку: ${card.word}`);
        return this.getPreloadedCard();
      } catch (error) {
        if (error.message === 'Timeout') {
          console.log(`[PRELOADER] ⏰ Таймаут ожидания (${timeoutMs}ms)`);
        }
        return null;
      }
    }
    return this.getPreloadedCard();
  }
  
  clear() {
    this.cache = null;
    this.isLoading = false;
    this.loadingPromise = null;
      }
}

// Симуляция пользовательского взаимодействия
class UserSimulator {
  constructor() {
    this.preloader = new MockPreloader();
    this.currentCard = null;
    this.isLoading = false;
  }
  
  async loadFirstCard() {
    console.log('\n🎯 === ЗАГРУЗКА ПЕРВОЙ КАРТОЧКИ ===');
    this.isLoading = true;
    
    // Загружаем первую карточку
    this.currentCard = await mockAPI.getNextCard('user1', 'en', 'ru', false);
    console.log(`📱 Показана карточка: ${this.currentCard.word} (${this.currentCard.translation})`);
    
    // Сразу начинаем предзагрузку следующей
    console.log('🚀 Сразу начинаем предзагрузку следующей карточки...');
    this.preloader.startPreloading('user1', 'en', 'ru').catch(console.error);
    
    this.isLoading = false;
    return this.currentCard;
  }
  
  async answerCorrectly(thinkingTimeMs = 2000) {
    console.log(`\n✅ === ПРАВИЛЬНЫЙ ОТВЕТ (думал ${thinkingTimeMs}ms) ===`);
    
    // Симулируем время размышления
    await new Promise(resolve => setTimeout(resolve, thinkingTimeMs));
    
    // Отправляем ответ на сервер
    await mockAPI.submitAnswer(this.currentCard.id, true);
    
    // Запускаем новую предзагрузку для следующей карточки
    console.log('🚀 Запускаем предзагрузку следующей карточки...');
    setTimeout(() => {
      this.preloader.startPreloading('user1', 'en', 'ru').catch(console.error);
    }, 100);
    
    // Пытаемся использовать предзагруженную карточку
    let nextCard = this.preloader.getPreloadedCard();
    
    if (!nextCard) {
      // Если нет готовой, ждем с таймаутом
      console.log('⏳ Предзагруженной карточки нет, ждем...');
      nextCard = await this.preloader.waitForPreload(2000);
    }
    
    if (nextCard) {
      console.log(`🎯 МГНОВЕННЫЙ переход к карточке: ${nextCard.word}`);
      this.currentCard = nextCard;
      return { transitionTime: 'instant', card: nextCard };
    } else {
      console.log('📥 Предзагрузка не успела, загружаем обычным способом...');
      const startTime = Date.now();
      this.currentCard = await mockAPI.getNextCard('user1', 'en', 'ru', false);
      const transitionTime = Date.now() - startTime;
      console.log(`📱 Карточка загружена за ${transitionTime}ms: ${this.currentCard.word}`);
      return { transitionTime, card: this.currentCard };
    }
  }
  
  async answerIncorrectly(thinkingTimeMs = 1500) {
    console.log(`\n❌ === НЕПРАВИЛЬНЫЙ ОТВЕТ (думал ${thinkingTimeMs}ms) ===`);
    
    // Симулируем время размышления
    await new Promise(resolve => setTimeout(resolve, thinkingTimeMs));
    
    // Отправляем неправильный ответ
    await mockAPI.submitAnswer(this.currentCard.id, false);
    
    // При неправильном ответе показываем ту же карточку
    console.log(`🔄 Повторяем ту же карточку: ${this.currentCard.word}`);
    
    // Очищаем предзагрузку и начинаем новую
    this.preloader.clear();
    console.log('🚀 Начинаем новую предзагрузку...');
    this.preloader.startPreloading('user1', 'en', 'ru').catch(console.error);
    
    return { transitionTime: 'instant', card: this.currentCard };
  }
}

// Тестовые сценарии
async function runTests() {
  console.log('🧪 === НАЧИНАЕМ ТЕСТИРОВАНИЕ ЛОГИКИ ПРЕДЗАГРУЗКИ ===\n');

  // Сбрасываем настройки для реалистичного тестирования
  mockAPI.networkDelay = 1000; // Реалистичная задержка сети
  mockAPI.requestCount = 0; // Сбрасываем счетчик

  // ТЕСТ 1: Правильный ответ
  console.log('📋 ТЕСТ 1: Правильный ответ');
  console.log('Сценарий: Пользователь отвечает правильно с первого раза');
  const user1 = new UserSimulator();

  const card1 = await user1.loadFirstCard();
  console.log(`🎯 Текущая карточка: ${card1.word} (${card1.translation})`);

  await new Promise(resolve => setTimeout(resolve, 1000)); // Даем время предзагрузке

  const result1 = await user1.answerCorrectly(2000); // Реалистичное время ответа
  console.log(`✅ Результат: переход за ${result1.transitionTime}, новая карточка: ${result1.card.word}`);
  console.log(`🔍 Проверка: ${card1.word} → ${result1.card.word} (должны быть разные карточки)`);

  // ТЕСТ 2: Неправильный ответ
  console.log('\n📋 ТЕСТ 2: Неправильный ответ');
  console.log('Сценарий: Пользователь отвечает неправильно');
  mockAPI.requestCount = 0; // Сбрасываем для чистого теста
  const user2 = new UserSimulator();

  const card2 = await user2.loadFirstCard();
  console.log(`🎯 Текущая карточка: ${card2.word} (${card2.translation})`);

  await new Promise(resolve => setTimeout(resolve, 1000));

  const result2 = await user2.answerIncorrectly(2000);
  console.log(`❌ Результат: переход за ${result2.transitionTime}, карточка: ${result2.card.word}`);
  console.log(`🔍 Проверка: ${card2.word} → ${result2.card.word} (должна быть та же карточка)`);

  // ТЕСТ 3: Правильный → Неправильный
  console.log('\n📋 ТЕСТ 3: Правильный → Неправильный ответ');
  console.log('Сценарий: Сначала правильно, потом неправильно');
  mockAPI.requestCount = 0;
  const user3 = new UserSimulator();

  const card3_1 = await user3.loadFirstCard();
  console.log(`🎯 Первая карточка: ${card3_1.word} (${card3_1.translation})`);

  await new Promise(resolve => setTimeout(resolve, 1000));

  const result3_1 = await user3.answerCorrectly(2000);
  console.log(`✅ Правильный ответ: переход за ${result3_1.transitionTime}, карточка: ${result3_1.card.word}`);
  console.log(`🔍 Проверка: ${card3_1.word} → ${result3_1.card.word} (должны быть разные)`);

  await new Promise(resolve => setTimeout(resolve, 1000));

  const result3_2 = await user3.answerIncorrectly(2000);
  console.log(`❌ Неправильный ответ: переход за ${result3_2.transitionTime}, карточка: ${result3_2.card.word}`);
  console.log(`🔍 Проверка: ${result3_1.card.word} → ${result3_2.card.word} (должна быть та же)`);

  // ТЕСТ 4: Неправильный → Правильный
  console.log('\n📋 ТЕСТ 4: Неправильный → Правильный ответ');
  console.log('Сценарий: Сначала неправильно, потом правильно');
  mockAPI.requestCount = 0;
  const user4 = new UserSimulator();

  const card4_1 = await user4.loadFirstCard();
  console.log(`🎯 Первая карточка: ${card4_1.word} (${card4_1.translation})`);

  await new Promise(resolve => setTimeout(resolve, 1000));

  const result4_1 = await user4.answerIncorrectly(2000);
  console.log(`❌ Неправильный ответ: переход за ${result4_1.transitionTime}, карточка: ${result4_1.card.word}`);
  console.log(`🔍 Проверка: ${card4_1.word} → ${result4_1.card.word} (должна быть та же)`);

  await new Promise(resolve => setTimeout(resolve, 1000));

  const result4_2 = await user4.answerCorrectly(2000);
  console.log(`✅ Правильный ответ: переход за ${result4_2.transitionTime}, карточка: ${result4_2.card.word}`);
  console.log(`🔍 Проверка: ${result4_1.card.word} → ${result4_2.card.word} (должны быть разные)`);

  // Итоговый анализ
  console.log('\n🎉 === ИТОГОВЫЙ АНАЛИЗ ===');
  console.log('✅ Тест 1: Правильный ответ - переход должен быть мгновенным');
  console.log('✅ Тест 2: Неправильный ответ - должна повториться та же карточка');
  console.log('✅ Тест 3: Правильный→Неправильный - проверка смены карточек');
  console.log('✅ Тест 4: Неправильный→Правильный - проверка логики повторения');

  console.log('\n🎯 === ТЕСТИРОВАНИЕ ЗАВЕРШЕНО ===');
}

// Запускаем тесты
runTests().catch(console.error);
