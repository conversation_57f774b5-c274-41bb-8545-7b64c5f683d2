/**
 * Демонстрация работы retry сервиса
 * 
 * Этот скрипт показывает, как retry сервис обрабатывает:
 * 1. Успешные запросы
 * 2. Временные сбои сети (ngrok отвалился)
 * 3. Восстановление соединения
 * 4. Автоматическую повторную отправку
 */

// Мокаем React Native окружение для Node.js
global.setInterval = setInterval;
global.setTimeout = setTimeout;
global.clearInterval = clearInterval;
global.clearTimeout = clearTimeout;

// Мокаем AsyncStorage
const mockStorage = {};
const mockAsyncStorage = {
  getItem: (key) => Promise.resolve(mockStorage[key] || null),
  setItem: (key, value) => {
    mockStorage[key] = value;
    return Promise.resolve();
  },
  removeItem: (key) => {
    delete mockStorage[key];
    return Promise.resolve();
  }
};

// Мокаем logger
const mockLog = {
  success: (category, message, data) => console.log(`✅ [${category}] ${message}`, data || ''),
  warning: (category, message, data) => console.log(`⚠️ [${category}] ${message}`, data || ''),
  info: (category, message, data) => console.log(`ℹ️ [${category}] ${message}`, data || ''),
  error: (category, message, data) => console.log(`❌ [${category}] ${message}`, data || '')
};

// Подменяем модули
require.cache[require.resolve('@react-native-async-storage/async-storage')] = {
  exports: { default: mockAsyncStorage }
};

require.cache[require.resolve('../utils/logger')] = {
  exports: {
    log: mockLog,
    LogCategory: { API: 'API' }
  }
};

// Импортируем RetryService
const RetryService = require('../services/retryService').default;

// Создаем экземпляр с быстрыми настройками для демо
const retryService = new (RetryService.constructor)({
  maxAttempts: 5,
  baseDelay: 2000, // 2 секунды
  maxDelay: 10000, // 10 секунд
  backoffMultiplier: 2
});

// Симулируем различные сценарии
async function demonstrateRetryService() {
  console.log('🚀 Демонстрация Retry Service');
  console.log('=' .repeat(50));

  // Сценарий 1: Успешная отправка
  console.log('\n📤 Сценарий 1: Успешная отправка');
  global.fetch = jest.fn().mockResolvedValue({
    ok: true,
    json: () => Promise.resolve({ success: true, interval_level: 15 })
  });

  await retryService.sendWithRetry(
    'https://test-api.com/response',
    {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ user_id: 'user123', is_correct: true })
    },
    {
      cardId: 'card-001',
      userId: 'user123',
      isCorrect: true,
      responseTime: 2.5
    }
  );

  // Сценарий 2: Ошибка ngrok (503)
  console.log('\n🚫 Сценарий 2: Ошибка ngrok (503 Gateway Error)');
  global.fetch = jest.fn().mockRejectedValue(
    new Error('HTTP error! status: 503, body: ngrok gateway error\nThe server returned an invalid or incomplete HTTP response.\n\nERR_NGROK_3004')
  );

  await retryService.sendWithRetry(
    'https://test-api.com/response',
    {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ user_id: 'user123', is_correct: true })
    },
    {
      cardId: 'card-002',
      userId: 'user123',
      isCorrect: true,
      responseTime: 3.2
    }
  );

  // Показываем статистику очереди
  console.log('\n📊 Статистика очереди после ошибок:');
  const stats = await retryService.getQueueStats();
  console.log(`   Запросов в очереди: ${stats.totalRequests}`);
  if (stats.oldestRequest) {
    const ageMinutes = Math.floor((Date.now() - stats.oldestRequest) / (1000 * 60));
    console.log(`   Самый старый запрос: ${ageMinutes} минут назад`);
  }

  // Сценарий 3: Восстановление соединения
  console.log('\n🔄 Сценарий 3: Восстановление соединения через 5 секунд...');
  setTimeout(() => {
    console.log('🌐 Соединение восстановлено! Мокаем успешные ответы...');
    global.fetch = jest.fn().mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ success: true, interval_level: 10 })
    });
  }, 5000);

  // Ждем обработки очереди
  console.log('⏳ Ожидаем автоматической обработки очереди...');
  
  // Проверяем статистику через 15 секунд
  setTimeout(async () => {
    console.log('\n📊 Финальная статистика очереди:');
    const finalStats = await retryService.getQueueStats();
    console.log(`   Запросов в очереди: ${finalStats.totalRequests}`);
    
    if (finalStats.totalRequests === 0) {
      console.log('🎉 Все запросы успешно отправлены!');
    } else {
      console.log('⏳ Некоторые запросы все еще в очереди...');
    }
    
    console.log('\n✅ Демонстрация завершена');
    process.exit(0);
  }, 15000);
}

// Запускаем демонстрацию
demonstrateRetryService().catch(error => {
  console.error('❌ Ошибка в демонстрации:', error);
  process.exit(1);
});

console.log('\n💡 Этот скрипт демонстрирует автоматическую обработку сбоев сети');
console.log('   Пользователь не увидит никаких ошибок - все обрабатывается в фоне');
