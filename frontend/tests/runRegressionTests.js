#!/usr/bin/env node
/**
 * Раннер для регрессионных тестов
 * Запускает все тесты, которые проверяют исправленные проблемы
 */

async function runRegressionTests() {
  console.log('🧪 === ЗАПУСК РЕГРЕССИОННЫХ ТЕСТОВ ===\n');
  
  let totalTests = 0;
  let passedTests = 0;
  
  // 🧪 РЕГРЕССИОННЫЙ ТЕСТ №1: Проблема с зелеными полосками после подсказки
  console.log('🧪 Регрессионный тест №1: Проблема с зелеными полосками после подсказки');
  console.log('📋 Проблема №9 из problems.md');
  console.log('📅 Дата решения: 19 июня 2025\n');
  
  totalTests++;
  try {
    const { testHintButtonFix } = require('./e2e/hintButtonFix.test.js');
    const success = await testHintButtonFix();
    if (success) {
      console.log('✅ РЕГРЕССИОННЫЙ ТЕСТ №1 ПРОЙДЕН\n');
      passedTests++;
    } else {
      console.log('❌ РЕГРЕССИОННЫЙ ТЕСТ №1 ПРОВАЛЕН\n');
    }
  } catch (error) {
    console.log(`❌ Ошибка в регрессионном тесте №1: ${error.message}\n`);
  }
  
  // 🧪 РЕГРЕССИОННЫЙ ТЕСТ №2: Проблема с зелеными полосками после неправильного ответа
  console.log('🧪 Регрессионный тест №2: Проблема с зелеными полосками после неправильного ответа');
  console.log('📋 Проблема №10 из problems.md');
  console.log('📅 Дата решения: 19 июня 2025\n');

  totalTests++;
  try {
    const { testIncorrectAnswerFix } = require('./e2e/incorrectAnswerFix.test.js');
    const success = await testIncorrectAnswerFix();
    if (success) {
      console.log('✅ РЕГРЕССИОННЫЙ ТЕСТ №2 ПРОЙДЕН\n');
      passedTests++;
    } else {
      console.log('❌ РЕГРЕССИОННЫЙ ТЕСТ №2 ПРОВАЛЕН\n');
    }
  } catch (error) {
    console.log(`❌ Ошибка в регрессионном тесте №2: ${error.message}\n`);
  }

  // 🧪 РЕГРЕССИОННЫЙ ТЕСТ №3: DEBUG информация об интервале не обновляется
  console.log('🧪 Регрессионный тест №3: DEBUG информация об интервале не обновляется');
  console.log('📋 Проблема №11 из problems.md');
  console.log('📅 Дата решения: 19 июня 2025\n');

  totalTests++;
  try {
    const { testIntervalUpdateFix } = require('./e2e/intervalUpdateFix.test.js');
    const success = await testIntervalUpdateFix();
    if (success) {
      console.log('✅ РЕГРЕССИОННЫЙ ТЕСТ №3 ПРОЙДЕН\n');
      passedTests++;
    } else {
      console.log('❌ РЕГРЕССИОННЫЙ ТЕСТ №3 ПРОВАЛЕН\n');
    }
  } catch (error) {
    console.log(`❌ Ошибка в регрессионном тесте №3: ${error.message}\n`);
  }
  
  // Итоговый отчет
  console.log('📊 === ИТОГОВЫЙ ОТЧЕТ РЕГРЕССИОННЫХ ТЕСТОВ ===');
  console.log(`📋 Всего тестов: ${totalTests}`);
  console.log(`✅ Пройдено: ${passedTests}`);
  console.log(`❌ Провалено: ${totalTests - passedTests}`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 ВСЕ РЕГРЕССИОННЫЕ ТЕСТЫ ПРОЙДЕНЫ!');
    console.log('✅ Исправленные проблемы работают корректно');
    console.log('✅ Нет регрессий в функциональности');
    return true;
  } else {
    console.log('\n⚠️ НЕКОТОРЫЕ РЕГРЕССИОННЫЕ ТЕСТЫ НЕ ПРОЙДЕНЫ!');
    console.log('❌ Возможны регрессии в исправленной функциональности');
    console.log('🔧 Требуется дополнительная проверка и исправление');
    return false;
  }
}

async function main() {
  try {
    const success = await runRegressionTests();
    process.exit(success ? 0 : 1);
  } catch (error) {
    console.error('💥 Критическая ошибка в регрессионных тестах:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { runRegressionTests };
