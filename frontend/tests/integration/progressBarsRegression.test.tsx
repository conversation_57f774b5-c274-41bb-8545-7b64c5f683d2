/**
 * Критический тест: Проблема с зелеными полосками прогресса
 * 
 * Проверяет сценарий:
 * 1. Пользователь отвечает правильно на новое слово
 * 2. Слово становится выученным (interval_level = 15, is_learned = true)
 * 3. Следующая карточка НЕ должна наследовать зеленые полоски
 * 4. Новая карточка должна показывать 1 оранжевую полоску
 */

import { jest } from '@jest/globals';

// Мокаем API сервис
const mockApiService = {
  getNextCard: jest.fn(),
  submitAnswer: jest.fn(),
};

// Мокаем логгер
const mockLogger = {
  log: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
};

// Функция расчета полосок прогресса (копия из TrainingScreen)
const getProgressBarsInfo = (card: any) => {
  if (!card) {
    return { count: 1, color: '#3a3a4a' };
  }

  const intervalLevel = card.interval_level ?? -1;
  const isNew = card.is_new ?? true;
  const isLearned = card.is_learned ?? false;

  if (isLearned) {
    return { count: 5, color: '#4CAF50' }; // 5 зелёных полосок для выученных слов
  } else if (isNew) {
    return { count: 1, color: '#FFA500' }; // Оранжевая только для новых слов
  } else if (intervalLevel >= -1 && intervalLevel <= 4) {
    return { count: 1, color: '#7fb4ff' }; // Голубая для интервала меньше 1 дня
  } else if (intervalLevel >= 5 && intervalLevel <= 8) {
    return { count: 2, color: '#7fb4ff' }; // 2 полоски для 1 день - 1 месяц
  } else if (intervalLevel >= 9 && intervalLevel <= 11) {
    return { count: 3, color: '#4a7dff' }; // 3 полоски для 1-6 месяцев
  } else if (intervalLevel >= 12 && intervalLevel <= 14) {
    return { count: 4, color: '#4a7dff' }; // 4 полоски для 6-12 месяцев
  } else {
    return { count: 5, color: '#4CAF50' }; // 5 полосок зелёная для более 1 года
  }
};

describe('Progress Bars Regression Test - Green Bars Issue', () => {
  
  beforeEach(() => {
    // Очищаем моки перед каждым тестом
    jest.clearAllMocks();
    console.log('\n🧪 === НАЧАЛО ТЕСТА ===');
  });

  afterEach(() => {
    console.log('🧪 === КОНЕЦ ТЕСТА ===\n');
  });

  it('КРИТИЧЕСКИЙ: Новая карточка НЕ должна наследовать зеленые полоски от выученной', () => {
    console.log('🎯 ТЕСТ: Проверяем наследование полосок прогресса');

    // 1. Первая карточка - новое слово
    const firstCard = {
      word_id: 'word-1',
      word: 'tubig',
      translation: 'вода',
      is_new: true,
      is_learned: false,
      interval_level: -1
    };

    console.log('📝 Шаг 1: Получили новую карточку');
    const firstCardProgress = getProgressBarsInfo(firstCard);
    
    expect(firstCardProgress.count).toBe(1);
    expect(firstCardProgress.color).toBe('#FFA500');
    console.log('✅ Новая карточка показывает 1 оранжевую полоску');

    // 2. Пользователь отвечает правильно - слово становится выученным
    const learnedCard = {
      ...firstCard,
      is_new: false,
      is_learned: true,
      interval_level: 15
    };

    console.log('📝 Шаг 2: Пользователь ответил правильно, слово выучено');
    const learnedCardProgress = getProgressBarsInfo(learnedCard);
    
    expect(learnedCardProgress.count).toBe(5);
    expect(learnedCardProgress.color).toBe('#4CAF50');
    console.log('✅ Выученная карточка показывает 5 зеленых полосок');

    // 3. Получаем следующую карточку - она должна быть новой
    const secondCard = {
      word_id: 'word-2',
      word: 'mama',
      translation: 'мама',
      is_new: true,
      is_learned: false,
      interval_level: -1
    };

    console.log('📝 Шаг 3: Получили следующую новую карточку');
    const secondCardProgress = getProgressBarsInfo(secondCard);
    
    // КРИТИЧЕСКАЯ ПРОВЕРКА: Новая карточка НЕ должна наследовать зеленые полоски
    expect(secondCardProgress.count).toBe(1);
    expect(secondCardProgress.color).toBe('#FFA500');
    expect(secondCardProgress).not.toEqual(learnedCardProgress);
    
    console.log('✅ КРИТИЧЕСКИЙ ТЕСТ ПРОШЕЛ: Новая карточка показывает 1 оранжевую полоску');
    console.log('✅ Наследование полосок НЕ происходит');
  });

  it('СЦЕНАРИЙ: Полный цикл ответов на несколько карточек', () => {
    console.log('🎯 ТЕСТ: Полный цикл ответов на карточки');

    const testCards = [
      {
        word_id: 'word-1',
        word: 'tubig',
        translation: 'вода',
        is_new: true,
        is_learned: false,
        interval_level: -1
      },
      {
        word_id: 'word-2', 
        word: 'mama',
        translation: 'мама',
        is_new: true,
        is_learned: false,
        interval_level: -1
      },
      {
        word_id: 'word-3',
        word: 'asa',
        translation: 'где',
        is_new: true,
        is_learned: false,
        interval_level: -1
      }
    ];

    const results: Array<{
      cardIndex: number;
      word: string;
      initialProgress: { count: number; color: string };
      finalProgress: { count: number; color: string };
    }> = [];

    testCards.forEach((card, index) => {
      console.log(`📝 Карточка ${index + 1}: ${card.word} (${card.translation})`);
      
      // Проверяем начальное состояние
      const initialProgress = getProgressBarsInfo(card);
      expect(initialProgress.count).toBe(1);
      expect(initialProgress.color).toBe('#FFA500');
      
      // Симулируем правильный ответ
      const answeredCard = {
        ...card,
        is_new: false,
        is_learned: true,
        interval_level: 15
      };
      
      const finalProgress = getProgressBarsInfo(answeredCard);
      expect(finalProgress.count).toBe(5);
      expect(finalProgress.color).toBe('#4CAF50');
      
      results.push({
        cardIndex: index,
        word: card.word,
        initialProgress,
        finalProgress
      });
      
      console.log(`✅ Карточка ${index + 1}: 1 оранжевая → 5 зеленых`);
    });

    // Проверяем, что все карточки ведут себя одинаково
    results.forEach((result, index) => {
      expect(result.initialProgress.count).toBe(1);
      expect(result.initialProgress.color).toBe('#FFA500');
      expect(result.finalProgress.count).toBe(5);
      expect(result.finalProgress.color).toBe('#4CAF50');
    });

    console.log('✅ Все карточки ведут себя консистентно');
  });

  it('ГРАНИЧНЫЕ СЛУЧАИ: Различные уровни интервалов', () => {
    console.log('🎯 ТЕСТ: Граничные случаи уровней интервалов');

    const testCases = [
      { level: -1, expected: { count: 1, color: '#FFA500' }, description: 'Новое слово' },
      { level: 0, expected: { count: 1, color: '#7fb4ff' }, description: 'Уровень 0' },
      { level: 4, expected: { count: 1, color: '#7fb4ff' }, description: 'Уровень 4' },
      { level: 5, expected: { count: 2, color: '#7fb4ff' }, description: 'Уровень 5' },
      { level: 8, expected: { count: 2, color: '#7fb4ff' }, description: 'Уровень 8' },
      { level: 9, expected: { count: 3, color: '#4a7dff' }, description: 'Уровень 9' },
      { level: 11, expected: { count: 3, color: '#4a7dff' }, description: 'Уровень 11' },
      { level: 12, expected: { count: 4, color: '#4a7dff' }, description: 'Уровень 12' },
      { level: 14, expected: { count: 4, color: '#4a7dff' }, description: 'Уровень 14' },
      { level: 15, expected: { count: 5, color: '#4CAF50' }, description: 'Выученное слово' },
    ];

    testCases.forEach(({ level, expected, description }) => {
      const card = {
        word_id: `test-${level}`,
        word: 'test',
        translation: 'тест',
        is_new: level === -1,
        is_learned: level === 15,
        interval_level: level
      };

      const result = getProgressBarsInfo(card);
      
      expect(result.count).toBe(expected.count);
      expect(result.color).toBe(expected.color);
      
      console.log(`✅ ${description} (уровень ${level}): ${result.count} полосок ${result.color}`);
    });
  });

});
