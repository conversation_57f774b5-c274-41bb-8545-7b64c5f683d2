# Балансировка сфер фона - Золотая середина

## Проблема
Сферы на фоне стали совсем не видны после уменьшения прозрачности до 0.08-0.12.

## Решение
Найдена золотая середина - увеличена прозрачность до 0.14-0.18, но сохранены приглушенные цвета.

## Изменения прозрачности

### Было (слишком темно):
```tsx
['rgba(120, 80, 160, 0.12)', 'rgba(120, 80, 160, 0)']  // 0.12 - почти не видно
['rgba(100, 100, 120, 0.08)', 'rgba(100, 100, 120, 0)'] // 0.08 - совсем не видно
```

### Стало (сбалансированно):
```tsx
['rgba(130, 90, 180, 0.18)', 'rgba(130, 90, 180, 0)']   // 0.18 - хорошо видно
['rgba(120, 120, 140, 0.14)', 'rgba(120, 120, 140, 0)'] // 0.14 - заметно
```

## Новая палитра сфер

### Основные цвета (0.16-0.18):
- **Фиолетовый**: `rgba(130, 90, 180, 0.18)` - более заметный
- **Бирюзовый**: `rgba(90, 140, 150, 0.18)` - хорошо видимый
- **Синий**: `rgba(100, 130, 160, 0.18)` - сбалансированный

### Теплые цвета (0.16-0.17):
- **Желтый**: `rgba(160, 150, 90, 0.16)` - приглушенный, но видимый
- **Розовый**: `rgba(150, 100, 130, 0.16)` - мягкий
- **Оранжевый**: `rgba(160, 130, 90, 0.17)` - теплый

### Нейтральные цвета (0.14-0.15):
- **Серо-синий**: `rgba(120, 120, 140, 0.14)` - тонкий
- **Серо-коричневый**: `rgba(130, 120, 120, 0.14)` - едва заметный
- **Зеленый**: `rgba(100, 150, 130, 0.15)` - природный

## Применение по темам

### Training Theme
- Фиолетовый (0.18) - основной акцент
- Желтый (0.16) - теплый контраст
- Бирюзовый (0.18) - холодный баланс
- Розовый (0.16) - мягкий акцент

### Profile Theme  
- Синий (0.18) - профессиональный
- Фиолетовый (0.16) - креативный
- Зеленый (0.15) - спокойный

### Minimal Theme
- Серо-синий (0.12) - минимальный, но заметный

## Принципы балансировки

### Видимость
- ✅ **0.14-0.18**: Хорошо заметны, создают атмосферу
- ❌ **0.08-0.12**: Слишком темные, почти не видны
- ❌ **0.25-0.30**: Слишком яркие, отвлекают

### Цветовая температура
- **Холодные** (синий, бирюзовый, фиолетовый): 0.16-0.18
- **Теплые** (желтый, оранжевый, розовый): 0.16-0.17  
- **Нейтральные** (серые): 0.14-0.15

### Контрастность
- Достаточно заметны на черном фоне
- Не конкурируют с контентом
- Создают глубину и атмосферу

## Результат

### Визуальный эффект
- 🌟 **Заметные, но не навязчивые** сферы
- 🎨 **Приглушенная палитра** без ярких неонов
- 🌫️ **Атмосферный фон** с глубиной
- ⚖️ **Идеальный баланс** между видимостью и сдержанностью

### Технические характеристики
- Прозрачность: 0.14-0.18 (вместо 0.08-0.12)
- Цвета: Приглушенные, но насыщенные
- Рандомизация: 10 различных цветовых схем
- Темы: Индивидуальные палитры для каждого экрана

Теперь сферы хорошо видны, создают красивую атмосферу, но не отвлекают от контента! ✨
