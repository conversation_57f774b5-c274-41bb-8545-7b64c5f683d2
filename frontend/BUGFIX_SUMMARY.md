# Исправление ошибки: "Value is a string, expected a number"

## Проблема
```
Warning: Error: Exception in HostFunction: Value is a string, expected a number
```

Ошибка возникала в компоненте `GlassmorphismCard` при использовании Canvas с Skia.

## Причины ошибки

### 1. Передача строки 'auto' в Canvas
- **Проблема**: Canvas компонент ожидает числовые значения для width/height
- **Причина**: Мы передавали `height="auto"` в Canvas операции
- **Решение**: Добавлена переменная `canvasHeight` с числовым значением

### 2. Неправильные названия предустановок
- **Проблема**: Использовались несуществующие предустановки `glassmorphismPresets.profileCard`
- **Причина**: Конфликт между названиями в разных файлах
- **Решение**: Использованы правильные названия из `components/GlassmorphismCard.tsx`

## Исправления

### ✅ 1. Исправлен GlassmorphismCard.tsx
```tsx
// Было:
clip={rrect(rect(0, 0, cardWidth, height), borderRadius, borderRadius)}

// Стало:
const canvasHeight = height === 'auto' ? 1000 : height;
clip={rrect(rect(0, 0, cardWidth, canvasHeight), borderRadius, borderRadius)}
```

### ✅ 2. Исправлены названия предустановок

**App.tsx:**
```tsx
// Было:
<GlassmorphismCard {...glassmorphismPresets.profileCard}>

// Стало:
<GlassmorphismCard {...glassmorphismPresets.accent}>
```

**ProfileScreen.tsx:**
```tsx
// Было:
<GlassmorphismCard {...glassmorphismPresets.profileCard}>

// Стало:
<GlassmorphismCard {...glassmorphismPresets.accent}>
<GlassmorphismCard {...glassmorphismPresets.minimal}>
```

## Доступные предустановки

В `components/GlassmorphismCard.tsx`:
- ✅ `training` - основная карточка тренировки
- ✅ `profile` - карточка профиля (не используется)
- ✅ `minimal` - минимальная карточка
- ✅ `accent` - акцентная карточка

## Логика обработки высоты

```tsx
export const GlassmorphismCard = ({
  height = 'auto', // По умолчанию адаптивная высота
  // ...
}) => {
  // Для React Native View
  const cardHeight = height === 'auto' ? undefined : height;
  
  // Для Canvas (нужно число)
  const canvasHeight = height === 'auto' ? 1000 : height;
  
  return (
    <View style={{ height: cardHeight }}>
      <Canvas>
        <BackdropBlur clip={rrect(rect(0, 0, cardWidth, canvasHeight), ...)} />
      </Canvas>
    </View>
  );
};
```

## Результат

- ✅ Ошибка "Value is a string, expected a number" исправлена
- ✅ Карточки корректно отображаются с адаптивной высотой
- ✅ Canvas операции работают с числовыми значениями
- ✅ Все предустановки используют правильные названия

## Тестирование

Проверьте следующие экраны:
1. **HomeScreen** - карточки приветствия
2. **ProfileScreen** - карточки информации и настроек
3. **TrainingScreen** - основная карточка тренировки

Все должны отображаться без ошибок с красивыми glassmorphism эффектами.
