// Конфигурация API
// При смене хоста/порта изменяйте эти константы:
const NGROK_URL = 'https://068aa24a7c82.ngrok-free.app';
const LOCAL_HOST = '***********';
const PORT = '8000';

// Выбираем URL в зависимости от окружения
export const API_BASE_URL = NGROK_URL; // Используем ngrok для доступа с телефона
// export const API_BASE_URL = `http://${LOCAL_HOST}:${PORT}`; // Используем локальный хост для разработки

export const API_URL = API_BASE_URL;
export const API_ENDPOINT = `${API_BASE_URL}/api`;

// Экспорт для обратной совместимости
export default {
  API_HOST: new URL(API_BASE_URL).hostname,
  API_PORT: new URL(API_BASE_URL).port || (API_BASE_URL.startsWith('https') ? '443' : '80'),
  API_BASE_URL,
  API_URL,
  API_ENDPOINT
};
