import React, { createContext, useContext, useCallback } from 'react';

interface TrainingContextType {
  onTrainingComplete: (() => void) | null;
  setOnTrainingComplete: (callback: (() => void) | null) => void;
}

const TrainingContext = createContext<TrainingContextType>({
  onTrainingComplete: null,
  setOnTrainingComplete: () => {},
});

export const useTraining = () => {
  const context = useContext(TrainingContext);
  if (!context) {
    throw new Error('useTraining must be used within a TrainingProvider');
  }
  return context;
};

interface TrainingProviderProps {
  children: React.ReactNode;
}

export const TrainingProvider: React.FC<TrainingProviderProps> = ({ children }) => {
  const [onTrainingComplete, setOnTrainingComplete] = React.useState<(() => void) | null>(null);

  const handleSetOnTrainingComplete = useCallback((callback: (() => void) | null) => {
    setOnTrainingComplete(() => callback);
  }, []);

  const value = {
    onTrainingComplete,
    setOnTrainingComplete: handleSetOnTrainingComplete,
  };

  return (
    <TrainingContext.Provider value={value}>
      {children}
    </TrainingContext.Provider>
  );
};
