import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { API_URL } from '../config/api';
import { log, LogCategory } from '../utils/logger';

// Интерфейс для статистики пользователя
interface UserStatistics {
  learned_words: number;
  total_words_in_progress: number;
  total_words_available: number;
  active_words: number;
  passive_words: number;
  forced_words: number;
  progress_percentage: number;
  target_language: string | null;
}

export type User = {
  _id: string;
  email: string;
  username?: string;
  settings?: {
    native_language: string;
    target_language: string;  // Язык текущей сессии изучения (TODO: переименовать в session_language)
    daily_goal?: number;
    language_levels?: { [languageCode: string]: string };  // Уровни по каждому языку
    onboarding_completed?: boolean;  // Флаг завершения онбординга
  };
  learning_languages?: string[];  // Изучаемые языки
  createdAt: string;
  updatedAt: string;
};

type LoginResponse = {
  success: boolean;
  error?: string;
  user?: User;
};

type AuthContextType = {
  user: User | null;
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  register: (email: string, password: string, username?: string) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<void>;
  updateUser: (userData: Partial<User>) => Promise<void>;
  refreshUser: () => Promise<void>;
  needsOnboarding: () => boolean;
  isLoading: boolean;
  error: string | null;
  isInitializing: boolean;
  setError: (error: string | null) => void;
  // Предзагруженная статистика
  preloadedStatistics: UserStatistics | null;
  refreshStatistics: () => Promise<void>;
};

const USER_STORAGE_KEY = '@WordMaster:user';
const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [preloadedStatistics, setPreloadedStatistics] = useState<UserStatistics | null>(null);

  const updateUser = async (userData: Partial<User>): Promise<void> => {
    if (!user) return;

    try {
      setIsLoading(true);
      
      // Получаем токен авторизации
      const token = await AsyncStorage.getItem('auth_token');
      if (!token) {
        throw new Error('Токен авторизации не найден');
      }

      // Отправляем запрос на сервер
      const response = await fetch(`${API_URL}/api/users/me`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(userData)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Ошибка при обновлении данных');
      }

      // Получаем обновленные данные пользователя
      const updatedUserData = await response.json();
      
      // Обновляем данные в AsyncStorage
      await AsyncStorage.setItem(USER_STORAGE_KEY, JSON.stringify(updatedUserData));
      
      // Обновляем состояние
      setUser(updatedUserData);
      
      // Уведомление об успешном обновлении теперь показывается в UI компонентах

    } catch (error) {
      console.error('Ошибка при обновлении пользователя:', error);
      // Ошибки теперь обрабатываются в UI компонентах
      const errorMessage = error instanceof Error ? error.message : 'Неизвестная ошибка';
      console.error('Не удалось обновить данные:', errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Функция для загрузки статистики
  const fetchStatistics = async (targetLanguage?: string): Promise<UserStatistics | null> => {
    try {
      const token = await AsyncStorage.getItem('auth_token');
      if (!token) {
        return null;
      }

      const url = new URL(`${API_URL}/api/spaced/api/user/statistics`);
      if (targetLanguage) {
        url.searchParams.append('target_language', targetLanguage);
      }

      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching statistics:', error);
      return null;
    }
  };

  const refreshStatistics = async (): Promise<void> => {
    if (!user) return;

    const targetLanguage = user.settings?.target_language || 'en';
    const statistics = await fetchStatistics(targetLanguage);
    if (statistics) {
      setPreloadedStatistics(statistics);
    }
  };

  const refreshUser = async (): Promise<void> => {
    try {
      const token = await AsyncStorage.getItem('auth_token');
      if (!token) {
        console.log('No token found, cannot refresh user data');
        return;
      }

      console.log('Refreshing user data...');
      const userData = await fetchUserData(token);
      if (userData) {
        // Принудительно очищаем старый кэш
        await AsyncStorage.removeItem(USER_STORAGE_KEY);
        await AsyncStorage.setItem(USER_STORAGE_KEY, JSON.stringify(userData));
        setUser(userData);
        console.log('User data refreshed successfully:', userData.settings);

        // Предзагружаем статистику после обновления пользователя
        const targetLanguage = userData.settings?.target_language || 'en';
        const statistics = await fetchStatistics(targetLanguage);
        if (statistics) {
          setPreloadedStatistics(statistics);
        }
      }
    } catch (error) {
      console.error('Error refreshing user data:', error);
      // Не выбрасываем ошибку, чтобы не нарушить работу приложения
    }
  };

  // Проверяем, авторизован ли пользователь при загрузке приложения
  useEffect(() => {
    const loadUser = async () => {
      try {
        // Пытаемся получить токен из хранилища
        const token = await AsyncStorage.getItem('auth_token');

        if (token) {
          // Если есть токен, загружаем данные пользователя с сервера
          const userData = await fetchUserData(token);
          if (userData) {
            await AsyncStorage.setItem(USER_STORAGE_KEY, JSON.stringify(userData));
            setUser(userData);

            // Предзагружаем статистику
            const targetLanguage = userData.settings?.target_language || 'en';
            const statistics = await fetchStatistics(targetLanguage);
            if (statistics) {
              setPreloadedStatistics(statistics);
            }
          }
        } else {
          // Если токена нет, проверяем, есть ли сохраненные данные пользователя
          const savedUserData = await AsyncStorage.getItem(USER_STORAGE_KEY);
          if (savedUserData) {
            const parsedUser = JSON.parse(savedUserData);
            setUser(parsedUser);

            // Загружаем статистику для сохраненного пользователя
            const token = await AsyncStorage.getItem('auth_token');
            if (token) {
              const targetLanguage = parsedUser.settings?.target_language || 'en';
              const statistics = await fetchStatistics(targetLanguage);
              if (statistics) {
                setPreloadedStatistics(statistics);
              }
            }
          }
        }
      } catch (error) {
        console.error('Ошибка при загрузке пользователя:', error);
        // В случае ошибки очищаем невалидные данные
        await AsyncStorage.removeItem('auth_token');
        await AsyncStorage.removeItem(USER_STORAGE_KEY);
        setUser(null);
      } finally {
        setIsInitializing(false);
      }
    };

    loadUser();
  }, []);

  // Дополнительный эффект для загрузки статистики при изменении пользователя
  useEffect(() => {
    const loadStatisticsForUser = async () => {
      if (user && !preloadedStatistics) {
        console.log('🔄 Загружаем статистику для пользователя...');
        const targetLanguage = user.settings?.target_language || 'en';
        const statistics = await fetchStatistics(targetLanguage);
        if (statistics) {
          setPreloadedStatistics(statistics);
          console.log('✅ Статистика загружена');
        }
      }
    };

    loadStatisticsForUser();
  }, [user, preloadedStatistics]);

  // Функция для загрузки данных пользователя
  const fetchUserData = async (token: string) => {
    try {
      log.apiRequest('/api/users/me', { hasToken: !!token });
      const response = await fetch(`${API_URL}/api/users/me`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      log.apiResponse('/api/users/me', response.status, 0);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response:', errorText);
        throw new Error('Не удалось загрузить данные пользователя');
      }

      const userData = await response.json();
      log.success(LogCategory.AUTH, 'Данные пользователя получены', { userId: userData._id });
      return userData;
    } catch (error) {
      log.error(LogCategory.AUTH, `Ошибка при загрузке данных пользователя: ${error.message}`);
      throw error;
    }
  };

  const login = async (email: string, password: string): Promise<{ success: boolean; error?: string }> => {
    setIsLoading(true);
    setError(null);

    try {
      log.authLogin(email);
      // Отправляем запрос на вход
      const loginUrl = `${API_URL}/api/token`;
      
      // Создаем FormData и добавляем поля
      const formData = new URLSearchParams();
      formData.append('username', email);
      formData.append('password', password);
      formData.append('grant_type', 'password');
      formData.append('scope', '');
      formData.append('client_id', '');
      formData.append('client_secret', '');
      
      log.debug(LogCategory.AUTH, 'Отправляем данные формы входа');
      
      let response;
      try {
        response = await fetch(loginUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: formData.toString(),
        });
      } catch (fetchError) {
        console.error('Network error during login:', fetchError);
        // Проверяем доступность бэкенда
        try {
          const healthCheck = await fetch(`${API_URL}/health`);
          console.log('Backend health check status:', healthCheck.status);
        } catch (healthError) {
          console.error('Backend is not reachable:', healthError);
          throw new Error('Сервер не доступен. Пожалуйста, проверьте подключение к интернету и запущен ли бэкенд.');
        }
        throw new Error('Ошибка подключения к серверу. Пожалуйста, попробуйте снова.');
      }

      log.apiResponse('/api/token', response.status, 0);
      
      if (!response.ok) {
        let errorMessage = 'Не удалось войти';
        
        try {
          const errorText = await response.text();
          console.error('Login error response:', errorText);
          
          if (response.status === 401) {
            errorMessage = 'Неверный email или пароль';
          } else if (response.status === 422) {
            errorMessage = 'Ошибка валидации данных';
            try {
              const errorData = JSON.parse(errorText);
              if (Array.isArray(errorData.detail)) {
                errorMessage = errorData.detail.map((e: any) => e.msg || e.message).join(', ');
              } else {
                errorMessage = errorData.detail || errorMessage;
              }
            } catch (e) {
              console.error('Error parsing error response:', e);
            }
          } else {
            errorMessage = `Ошибка сервера: ${response.status}`;
          }
        } catch (e) {
          console.error('Error processing error response:', e);
          errorMessage = 'Произошла непредвиденная ошибка';
        }
        
        throw new Error(errorMessage);
      }

      const responseData = await response.json();
      log.success(LogCategory.AUTH, 'Токен получен успешно');
      const { access_token } = responseData;

      // Получаем данные пользователя
      const userResponse = await fetch(`${API_URL}/api/users/me`, {
        headers: {
          'Authorization': `Bearer ${access_token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!userResponse.ok) {
        throw new Error('Не удалось загрузить данные пользователя');
      }

      const userData = await userResponse.json();
      log.authLoginSuccess(userData._id);

      // Сохраняем токен и данные пользователя
      await AsyncStorage.setItem('auth_token', access_token);
      await AsyncStorage.setItem(USER_STORAGE_KEY, JSON.stringify(userData));

      setUser(userData);

      // Предзагружаем статистику после логина
      const targetLanguage = userData.settings?.target_language || 'en';
      const statistics = await fetchStatistics(targetLanguage);
      if (statistics) {
        setPreloadedStatistics(statistics);
      }

      return { success: true };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Ошибка при входе';
      console.error('Login error:', errorMessage);
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (email: string, password: string, username?: string): Promise<{ success: boolean; error?: string }> => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('Attempting registration with email:', email);

      const registerUrl = `${API_URL}/api/register`;
      console.log('Register URL:', registerUrl);

      const requestBody = {
        email,
        password,
        username: username || email.split('@')[0], // Используем часть email как username по умолчанию
        settings: {
          native_language: 'en', // Изменили дефолт с 'ru' на 'en'
          target_language: 'en',  // Язык текущей сессии изучения
          daily_goal: 10,
          // НЕ устанавливаем language_levels и learning_languages для онбординга
        }
      };

      console.log('Sending registration data:', { ...requestBody, password: '[HIDDEN]' });

      let response;
      try {
        response = await fetch(registerUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestBody),
        });
      } catch (fetchError) {
        console.error('Network error during registration:', fetchError);
        // Проверяем доступность бэкенда
        try {
          const healthCheck = await fetch(`${API_URL}/health`);
          console.log('Backend health check status:', healthCheck.status);
        } catch (healthError) {
          console.error('Backend is not reachable:', healthError);
          throw new Error('Сервер не доступен. Пожалуйста, проверьте подключение к интернету и запущен ли бэкенд.');
        }
        throw new Error('Ошибка подключения к серверу. Пожалуйста, попробуйте снова.');
      }

      console.log('Registration response status:', response.status);

      if (!response.ok) {
        let errorMessage = 'Не удалось зарегистрироваться';

        try {
          const errorText = await response.text();
          console.error('Registration error response:', errorText);

          if (response.status === 400) {
            errorMessage = 'Пользователь с таким email уже существует';
          } else if (response.status === 422) {
            errorMessage = 'Ошибка валидации данных';
            try {
              const errorData = JSON.parse(errorText);
              if (Array.isArray(errorData.detail)) {
                errorMessage = errorData.detail.map((e: any) => e.msg || e.message).join(', ');
              } else {
                errorMessage = errorData.detail || errorMessage;
              }
            } catch (e) {
              console.error('Error parsing error response:', e);
            }
          } else {
            errorMessage = `Ошибка сервера: ${response.status}`;
          }
        } catch (e) {
          console.error('Error processing error response:', e);
          errorMessage = 'Произошла непредвиденная ошибка';
        }

        throw new Error(errorMessage);
      }

      const responseData = await response.json();
      console.log('Registration successful, received data:', responseData);

      // После успешной регистрации автоматически входим в систему
      const loginResult = await login(email, password);

      if (loginResult.success) {
        return { success: true };
      } else {
        return { success: false, error: 'Регистрация прошла успешно, но не удалось войти в систему. Попробуйте войти вручную.' };
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Ошибка при регистрации';
      console.error('Registration error:', errorMessage);
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  };

  const needsOnboarding = (): boolean => {
    if (!user) {
      return false;
    }

    // Проверяем флаг завершения онбординга
    // Если флаг не установлен (undefined или false), нужен онбординг
    const onboardingCompleted = user.settings?.onboarding_completed === true;

    return !onboardingCompleted;
  };

  const logout = async (): Promise<void> => {
    try {
      // Удаляем токен и данные пользователя из хранилища
      await AsyncStorage.removeItem('auth_token');
      await AsyncStorage.removeItem(USER_STORAGE_KEY);
      setUser(null);

      // Здесь можно добавить вызов API для выхода, если это поддерживается бэкендом
      /*
      const token = await AsyncStorage.getItem('auth_token');
      if (token) {
        await fetch(`${API_URL}/auth/logout`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }).catch(console.error);
      }
      */
    } catch (error) {
      console.error('Ошибка при выходе:', error);
      // В любом случае продолжаем выход, даже если возникла ошибка
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        login,
        register,
        logout,
        updateUser,
        refreshUser,
        needsOnboarding,
        isLoading,
        error,
        isInitializing,
        setError,
        preloadedStatistics,
        refreshStatistics,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
