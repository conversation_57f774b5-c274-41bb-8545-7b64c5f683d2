import React, { createContext, useContext, useState, ReactNode } from 'react';

interface LanguageSessionContextType {
  showLanguageSessionModal: boolean;
  openLanguageSessionModal: () => void;
  closeLanguageSessionModal: () => void;
}

const LanguageSessionContext = createContext<LanguageSessionContextType | undefined>(undefined);

export const useLanguageSession = () => {
  const context = useContext(LanguageSessionContext);
  if (!context) {
    throw new Error('useLanguageSession must be used within a LanguageSessionProvider');
  }
  return context;
};

interface LanguageSessionProviderProps {
  children: ReactNode;
}

export const LanguageSessionProvider: React.FC<LanguageSessionProviderProps> = ({ children }) => {
  const [showLanguageSessionModal, setShowLanguageSessionModal] = useState(false);

  const openLanguageSessionModal = () => setShowLanguageSessionModal(true);
  const closeLanguageSessionModal = () => setShowLanguageSessionModal(false);

  return (
    <LanguageSessionContext.Provider
      value={{
        showLanguageSessionModal,
        openLanguageSessionModal,
        closeLanguageSessionModal,
      }}
    >
      {children}
    </LanguageSessionContext.Provider>
  );
};
