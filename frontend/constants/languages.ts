export interface LanguageOption {
  code: string;
  flag: string;
  name: string;
  speakers: number; // количество носителей в миллионах
}

// Языки отсортированы по популярности (количество носителей)
export const ALL_LANGUAGES: LanguageOption[] = [
  { code: 'en', flag: '🇺🇸', name: 'English', speakers: 1500 },
  { code: 'zh', flag: '🇨🇳', name: '中文', speakers: 1100 },
  { code: 'hi', flag: '🇮🇳', name: 'हिन्दी', speakers: 600 },
  { code: 'es', flag: '🇪🇸', name: '<PERSON><PERSON><PERSON>ño<PERSON>', speakers: 500 },
  { code: 'pa', flag: '🇮🇳', name: 'ਪੰਜਾਬੀ', speakers: 100 },
  { code: 'mr', flag: '🇮🇳', name: 'मराठी', speakers: 83 },
  { code: 'ar', flag: '🇸🇦', name: 'العربية', speakers: 400 },
  { code: 'bn', flag: '🇧🇩', name: 'বাংলা', speakers: 300 },
  { code: 'fr', flag: '🇫🇷', name: 'Français', speakers: 280 },
  { code: 'ru', flag: '🇷🇺', name: 'Русский', speakers: 260 },
  { code: 'pt', flag: '🇵🇹', name: 'Português', speakers: 260 },
  { code: 'ur', flag: '🇵🇰', name: 'اردو', speakers: 230 },
  { code: 'id', flag: '🇮🇩', name: 'Bahasa Indonesia', speakers: 230 },
  { code: 'ja', flag: '🇯🇵', name: '日本語', speakers: 125 },
  { code: 'fa', flag: '🇮🇷', name: 'فارسی', speakers: 110 },
  { code: 'de', flag: '🇩🇪', name: 'Deutsch', speakers: 100 },
  { code: 'tr', flag: '🇹🇷', name: 'Türkçe', speakers: 80 },
  { code: 'ko', flag: '🇰🇷', name: '한국어', speakers: 77 },
  { code: 'vi', flag: '🇻🇳', name: 'Tiếng Việt', speakers: 76 },
  { code: 'it', flag: '🇮🇹', name: 'Italiano', speakers: 65 },
  { code: 'th', flag: '🇹🇭', name: 'ไทย', speakers: 60 },
  { code: 'pl', flag: '🇵🇱', name: 'Polski', speakers: 45 },
  { code: 'uk', flag: '🇺🇦', name: 'Українська', speakers: 40 },
  { code: 'my', flag: '🇲🇲', name: 'မြန်မာ', speakers: 33 },
  { code: 'ms', flag: '🇲🇾', name: 'Bahasa Malaysia', speakers: 33 },
  { code: 'uz', flag: '🇺🇿', name: 'Oʻzbekcha', speakers: 32 },
  { code: 'ne', flag: '🇳🇵', name: 'नेपाली', speakers: 25 },
  { code: 'nl', flag: '🇳🇱', name: 'Nederlands', speakers: 24 },
  { code: 'ro', flag: '🇷🇴', name: 'Română', speakers: 24 },
  { code: 'tl', flag: '🇵🇭', name: 'Tagalog', speakers: 22 },
  { code: 'cb', flag: '🇵🇭', name: 'Cebuano', speakers: 20 },
  { code: 'kk', flag: '🇰🇿', name: 'Қазақша', speakers: 13 },
  { code: 'sv', flag: '🇸🇪', name: 'Svenska', speakers: 10 },
  { code: 'da', flag: '🇩🇰', name: 'Dansk', speakers: 6 },
  { code: 'fi', flag: '🇫🇮', name: 'Suomi', speakers: 5.5 },
  { code: 'no', flag: '🇳🇴', name: 'Norsk', speakers: 5 },
  { code: 'ka', flag: '🇬🇪', name: 'ქართული', speakers: 4 },
];

// Создаем мапу для быстрого поиска языка по коду
export const LANGUAGE_MAP: { [key: string]: LanguageOption } = ALL_LANGUAGES.reduce(
  (acc, lang) => {
    acc[lang.code] = lang;
    return acc;
  },
  {} as { [key: string]: LanguageOption }
);

// Функция для получения языка по коду
export const getLanguageByCode = (code: string): LanguageOption | undefined => {
  return LANGUAGE_MAP[code];
};

// Функция для получения флага по коду языка
export const getLanguageFlag = (code: string): string => {
  return LANGUAGE_MAP[code]?.flag || '🌐';
};

// Функция для получения названия по коду языка
export const getLanguageName = (code: string): string => {
  return LANGUAGE_MAP[code]?.name || code.toUpperCase();
};

// Функция для получения количества носителей
export const getLanguageSpeakers = (code: string): number => {
  return LANGUAGE_MAP[code]?.speakers || 0;
};

// Языки для выбора родного языка (включая русский)
export const NATIVE_LANGUAGE_OPTIONS = [
  { label: 'Русский', value: 'ru', flag: '🇷🇺' },
  { label: 'English', value: 'en', flag: '🇺🇸' },
  { label: 'Español', value: 'es', flag: '🇪🇸' },
  { label: 'Deutsch', value: 'de', flag: '🇩🇪' },
  { label: 'Français', value: 'fr', flag: '🇫🇷' },
  { label: '中文', value: 'zh', flag: '🇨🇳' },
  { label: 'हिन्दी', value: 'hi', flag: '🇮🇳' },
  { label: 'Cebuano', value: 'cb', flag: '🇵🇭' },
  { label: 'Tagalog', value: 'tl', flag: '🇵🇭' },
  { label: 'Bahasa Indonesia', value: 'id', flag: '🇮🇩' },
  { label: 'Bahasa Malaysia', value: 'ms', flag: '🇲🇾' },
  { label: 'Українська', value: 'uk', flag: '🇺🇦' },
  { label: 'ქართული', value: 'ka', flag: '🇬🇪' },
];
