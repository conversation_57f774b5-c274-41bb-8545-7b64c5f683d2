/**
 * 🎨 СТИЛИ КНОПКИ "НАЧАТЬ ТРЕНИРОВКУ":
 * - Градиент: строки 196 (LinearGradient colors)
 * - Основные стили кнопки: строки 436-450 (mainButton, customButton, buttonGradient)
 * - Стили текста и иконки: строки 456-477 (buttonText, buttonIconText)
 *
 * 📊 ГРАДИЕНТ ИЗ ПРОГРЕСС-БАРА: ['#7fb4ff', '#4a7dff'] - синий градиент из счетчика карточек
 */

import React, { useCallback } from 'react';
import { NavigationContainer, useFocusEffect } from '@react-navigation/native';
import {
  createNativeStackNavigator,
  NativeStackNavigationOptions,
  NativeStackScreenProps
} from '@react-navigation/native-stack';
import {
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ActivityIndicator
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Provider as PaperProvider, DefaultTheme } from 'react-native-paper';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { Ionicons } from '@expo/vector-icons';

// Инициализация i18n
import './src/i18n';

// Импортируем хуки для переводов
import { useAppTranslation } from './src/i18n/hooks';

// Импортируем новые компоненты дизайна
import { AnimatedGradientBackground, backgroundThemes } from './components/AnimatedGradientBackground';
import { GlassmorphismCard, glassmorphismPresets } from './components/GlassmorphismCard';
import { GlassButton } from './components/GlassButton';
import { LanguageSessionButton } from './components/LanguageSessionButton';
import { ProfileButton } from './components/ProfileButton';
import { SVGGradientText } from './components/SVGGradientText';

// Настраиваем тему для react-native-paper
const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: '#4a4e69',
    accent: '#f72585',
    background: '#1a1a2e',
    surface: '#16213e',
    text: '#ffffff',
  },
};

// Импортируем экраны
import TrainingScreen from './screens/TrainingScreen';
import LoginScreen from './screens/LoginScreen';
import RegisterScreen from './screens/RegisterScreen';
import ProfileScreen from './screens/ProfileScreen';
import AddLanguageScreen from './screens/AddLanguageScreen';
import NativeLanguageScreen from './screens/NativeLanguageScreen';

// Импортируем контекст аутентификации
import { AuthProvider, useAuth } from './contexts/AuthContext';

// Импортируем контекст языковой сессии
import { LanguageSessionProvider } from './contexts/LanguageSessionContext';
import { TrainingProvider, useTraining } from './contexts/TrainingContext';
import { LanguageSessionModal } from './components/LanguageSessionModal';
import { UserStatistics } from './components/UserStatistics';

// Импортируем хуки
import { useUserStatistics } from './hooks/useUserStatistics';

// Определяем типы для навигации
type RootStackParamList = {
  Login: undefined;
  Home: undefined;
  Training: undefined;
  Register: undefined;
  Profile: undefined;
  AddLanguage: undefined;
  NativeLanguage: undefined;
  OnboardingFlow: undefined;
};

export type { RootStackParamList }; // Экспортируем тип для использования в других файлах

type RootStackScreenProps<T extends keyof RootStackParamList> = NativeStackScreenProps<RootStackParamList, T>;

const Stack = createNativeStackNavigator<RootStackParamList>();

// Компонент для кнопок в навигации
const HeaderRightButtons: React.FC = () => (
  <View style={{ flexDirection: 'row', alignItems: 'center' }}>
    <LanguageSessionButton />
    <ProfileButton />
  </View>
);

// Опции для навигации
const screenOptions: NativeStackNavigationOptions = {
  headerStyle: {
    backgroundColor: '#1a1a2e',
  },
  headerTintColor: '#fff',
  headerTitleStyle: {
    fontWeight: '600',
  },
  animation: 'fade',
};

// Главный экран с кнопками
const HomeScreen: React.FC<RootStackScreenProps<'Home'>> = ({ navigation }) => {
  const { user, logout } = useAuth();
  const { t } = useAppTranslation('common');
  const { setOnTrainingComplete } = useTraining();

  // Получаем предзагруженную статистику из AuthContext
  const { preloadedStatistics, refreshStatistics } = useAuth();

  // Функция для обновления статистики после завершения тренировки
  const handleTrainingComplete = useCallback(() => {
    if (refreshStatistics) {
      console.log('📊 Обновление статистики после завершения тренировки...');
      refreshStatistics()
        .then(() => {
          console.log('✅ Статистика успешно обновлена');
        })
        .catch((error) => {
          console.error('❌ Ошибка обновления статистики:', error);
        });
    }
  }, [refreshStatistics]);



  const handleLogout = async () => {
    // Простой выход без подтверждения (можно добавить кастомное подтверждение позже)
    await logout();
    navigation.navigate('Home');
  };

  const handleStartTraining = () => {
    // Регистрируем callback для обновления статистики после завершения тренировки
    setOnTrainingComplete(handleTrainingComplete);
    navigation.navigate('Training');
  };

  return (
    <View style={styles.container}>
      {/* Animated Gradient Background */}
      <AnimatedGradientBackground {...backgroundThemes.minimal} />

      <SafeAreaView style={styles.safeArea}>
        <View style={styles.content}>
          <View style={styles.titleContainer}>
            <View style={styles.titleWrapper}>
              <SVGGradientText
                fontSize={32}
                fontWeight="bold"
                colors={['#87CEFD', '#9370DB']} // Тот же градиент как в профиле
                width={280}
                height={50}
                textAlign="middle"
                style={styles.title}
              >
                Words Master
              </SVGGradientText>
            </View>
          </View>

          {user ? (
            <View style={styles.userInfo}>
              <Text style={styles.welcomeText}>
                {t('welcome.greeting').replace('{{name}}', user.username || user.email)}
              </Text>
              <Text style={styles.subtitle}>{t('welcome.subtitle')}</Text>
            </View>
          ) : (
            <View style={styles.infoSection}>
              <Text style={styles.subtitle}>{t('welcome.description')}</Text>
            </View>
          )}

          {user ? (
            <>
              <TouchableOpacity
                onPress={handleStartTraining}
                style={styles.modernButton3}
                activeOpacity={0.8}
              >
                <LinearGradient
                  colors={['#87CEFD', '#B19CD9', '#D8BFD8']} // Небесно-голубой → мягкий лавандовый → пастельный пурпурный
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                  style={styles.modernGradient}
                >
                  <View style={styles.buttonContent}>
                    <Text style={styles.buttonText}>{t('buttons.startTraining')}</Text>
                    <Text style={styles.buttonIconText}>▶</Text>
                  </View>
                </LinearGradient>
              </TouchableOpacity>

              {/* Статистика пользователя */}
              {preloadedStatistics ? (
                <UserStatistics statistics={preloadedStatistics} />
              ) : (
                <View style={styles.statisticsContainer}>
                  <Text style={styles.loadingText}>{t('common:loading')}</Text>
                </View>
              )}
            </>
          ) : (
            <View style={styles.authSection}>
              <Text style={styles.authPromptText}>
                {t('auth.loginPrompt')}
              </Text>

              <GlassButton
                variant="primary"
                size="large"
                fullWidth
                onPress={() => navigation.navigate('Login')}
                style={styles.authButton}
              >
                {t('auth.login')}
              </GlassButton>

              <TouchableOpacity
                onPress={() => navigation.navigate('Register')}
                style={styles.registerLink}
              >
                <Text style={styles.registerLinkText}>
                  {t('auth.noAccount')}
                </Text>
              </TouchableOpacity>
            </View>
          )}


        </View>
      </SafeAreaView>
    </View>
  );
};

// Компонент для отображения загрузки
const LoadingScreen = () => {
  const { t } = useAppTranslation('common');

  return (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color="#87CEFD" />
      <Text style={styles.loadingText}>{t('buttons.loading')}</Text>
    </View>
  );
};

// Навигация приложения
const AppNavigator: React.FC = () => {
  const { user, isInitializing, needsOnboarding } = useAuth();
  const { t } = useAppTranslation('common');

  if (isInitializing) {
    return <LoadingScreen />;
  }

  const shouldShowOnboarding = user && needsOnboarding();

  return (
    <Stack.Navigator screenOptions={screenOptions}>
        {shouldShowOnboarding ? (
          // Онбординг для новых пользователей - начинаем с выбора родного языка
          <Stack.Screen
            name="NativeLanguage"
            component={NativeLanguageScreen}
            options={{
              title: t('navigation.welcome'),
              headerBackTitle: t('navigation.back'),
            }}
          />
        ) : (
          <Stack.Screen
            name="Home"
            component={HomeScreen}
            options={{
              title: 'Words Master',
              headerRight: () => <HeaderRightButtons />,
              headerLeft: () => null, // No back button on home screen
              headerStyle: {
                backgroundColor: '#1a1a2e',
                paddingTop: 10, // Дополнительный отступ для SafeArea
              },
            }}
          />
        )}

        {user ? (
          // Защищенные маршруты для всех авторизованных пользователей
          <>
            {!shouldShowOnboarding && (
              <>
                <Stack.Screen
                  name="Training"
                  component={TrainingScreen}
                  options={{
                    title: t('navigation.training'),
                    headerRight: () => <HeaderRightButtons />
                  }}
                />
                <Stack.Screen
                  name="Profile"
                  component={ProfileScreen}
                  options={{
                    title: t('navigation.profile'),
                    headerBackTitle: t('navigation.back'),
                    headerRight: () => <HeaderRightButtons />
                  }}
                />
              </>
            )}
            <Stack.Screen
              name="AddLanguage"
              component={AddLanguageScreen}
              options={{
                title: shouldShowOnboarding ? t('navigation.chooseLanguage') : t('navigation.addLanguage'),
                headerBackTitle: t('navigation.back'),
              }}
            />
          </>
        ) : (
          // Публичные маршруты
          <>
            <Stack.Screen
              name="Login"
              component={LoginScreen}
              options={{
                title: t('navigation.authorization'),
                headerBackTitle: t('navigation.back'),
              }}
            />
            <Stack.Screen
              name="Register"
              component={RegisterScreen}
              options={{
                title: t('navigation.registration'),
                headerBackTitle: t('navigation.back'),
              }}
            />
          </>
        )}
      </Stack.Navigator>
  );
};

// Основной компонент приложения
const App: React.FC = () => {
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <PaperProvider theme={theme}>
        <AuthProvider>
          <TrainingProvider>
            <LanguageSessionProvider>
              <NavigationContainer>
                <AppNavigator />
                <LanguageSessionModal />
              </NavigationContainer>
            </LanguageSessionProvider>
          </TrainingProvider>
        </AuthProvider>
      </PaperProvider>
    </GestureHandlerRootView>
  );
};

export default App;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000', // Fallback цвет
  },
  safeArea: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1a1a2e',
  },
  loadingText: {
    marginTop: 10,
    color: '#fff',
    fontSize: 16,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    padding: 20,
  },
  titleContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 30,
    width: '100%',
  },
  titleWrapper: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 280,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
    alignSelf: 'center', // Центрируем SVG компонент
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    lineHeight: 24,
  },
  userInfo: {
    marginBottom: 30,
    alignItems: 'center',
  },
  infoSection: {
    marginBottom: 30,
    alignItems: 'center',
  },
  welcomeText: {
    fontSize: 20,
    color: '#fff',
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  mainButton: {
    marginBottom: 35, // Увеличенный отступ между кнопкой тренировки и статистикой
  },
  customButton: {
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  buttonGradient: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  // 🎨 ФИНАЛЬНЫЙ СТИЛЬ: Мягкий градиент с ярким пурпурным свечением
  modernButton3: {
    marginBottom: 35,
    borderRadius: 20,
    // Усиленное пурпурное свечение
    shadowColor: '#8A2BE2', // Более яркий пурпурный
    shadowOffset: { width: 0, height: 18 }, // Еще больше вниз
    shadowOpacity: 0.8, // Максимальная видимость
    shadowRadius: 35, // Очень большой радиус
    elevation: 25,
  },
  modernGradient: {
    paddingVertical: 20,
    paddingHorizontal: 28,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#fff',
    marginRight: 8,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  buttonIcon: {
    marginLeft: 4,
    marginTop: 2,
  },
  buttonIconText: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    marginLeft: 8,
    marginTop: 1,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  authSection: {
    alignItems: 'center',
    width: '100%',
  },
  authPromptText: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 24,
  },
  authButton: {
    marginBottom: 16,
  },
  registerLink: {
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  registerLinkText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.6)',
    textAlign: 'center',
    textDecorationLine: 'underline',
  },
  buttonsContainer: {
    flexDirection: 'row',
    marginTop: 10,
    justifyContent: 'space-between',
    gap: 15,
  },
  sideButton: {
    flex: 1,
  },
  statisticsContainer: {
    marginBottom: 20,
    alignItems: 'center',
  },
});
