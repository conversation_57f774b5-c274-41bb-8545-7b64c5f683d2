import AsyncStorage from '@react-native-async-storage/async-storage';
import { log, LogCategory } from '../utils/logger';

interface PendingRequest {
  id: string;
  url: string;
  method: string;
  headers: Record<string, string>;
  body: string;
  timestamp: number;
  attempts: number;
  maxAttempts: number;
  cardId: string;
  userId: string;
  isCorrect: boolean;
  responseTime: number;
}

interface RetryConfig {
  maxAttempts: number;
  baseDelay: number; // в миллисекундах
  maxDelay: number;
  backoffMultiplier: number;
}

const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxAttempts: 5,
  baseDelay: 1000, // 1 секунда
  maxDelay: 30000, // 30 секунд
  backoffMultiplier: 2
};

const STORAGE_KEY = 'pending_card_responses';

class RetryService {
  private config: RetryConfig;
  private isProcessing = false;

  constructor(config: RetryConfig = DEFAULT_RETRY_CONFIG) {
    this.config = config;
    this.startPeriodicRetry();
  }

  /**
   * Отправляет запрос с автоматическими повторными попытками
   */
  async sendWithRetry(
    url: string,
    options: {
      method: string;
      headers: Record<string, string>;
      body: string;
    },
    metadata: {
      cardId: string;
      userId: string;
      isCorrect: boolean;
      responseTime: number;
    }
  ): Promise<Response | null> {
    const requestId = this.generateRequestId();
    
    try {
      // Пытаемся отправить сразу
      const response = await fetch(url, options);
      
      if (response.ok) {
        log.success(LogCategory.API, 'Ответ успешно отправлен на сервер', {
          cardId: metadata.cardId,
          isCorrect: metadata.isCorrect
        });
        return response;
      } else {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      log.warning(LogCategory.API, 'Ошибка отправки, добавляем в очередь повторов', {
        error: error.message,
        cardId: metadata.cardId
      });

      // Сохраняем запрос для повторной отправки
      await this.addToPendingQueue({
        id: requestId,
        url,
        method: options.method,
        headers: options.headers,
        body: options.body,
        timestamp: Date.now(),
        attempts: 0,
        maxAttempts: this.config.maxAttempts,
        ...metadata
      });

      // Сразу пытаемся обработать очередь
      this.processQueue();
      
      return null;
    }
  }

  /**
   * Добавляет запрос в очередь ожидающих повторной отправки
   */
  private async addToPendingQueue(request: PendingRequest): Promise<void> {
    try {
      const existingQueue = await this.getPendingQueue();
      existingQueue.push(request);
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(existingQueue));
      
      log.info(LogCategory.API, 'Запрос добавлен в очередь повторов', {
        requestId: request.id,
        cardId: request.cardId,
        queueSize: existingQueue.length
      });
    } catch (error) {
      log.error(LogCategory.API, 'Ошибка сохранения в очередь повторов', { error: error.message });
    }
  }

  /**
   * Получает текущую очередь ожидающих запросов
   */
  private async getPendingQueue(): Promise<PendingRequest[]> {
    try {
      const stored = await AsyncStorage.getItem(STORAGE_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      log.error(LogCategory.API, 'Ошибка чтения очереди повторов', { error: error.message });
      return [];
    }
  }

  /**
   * Сохраняет обновленную очередь
   */
  private async savePendingQueue(queue: PendingRequest[]): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(queue));
    } catch (error) {
      log.error(LogCategory.API, 'Ошибка сохранения очереди повторов', { error: error.message });
    }
  }

  /**
   * Обрабатывает очередь ожидающих запросов
   */
  async processQueue(): Promise<void> {
    if (this.isProcessing) {
      return;
    }

    this.isProcessing = true;

    try {
      const queue = await this.getPendingQueue();
      
      if (queue.length === 0) {
        return;
      }

      log.info(LogCategory.API, 'Начинаем обработку очереди повторов', { queueSize: queue.length });

      const updatedQueue: PendingRequest[] = [];

      for (const request of queue) {
        const shouldRetry = await this.processRequest(request);
        
        if (shouldRetry) {
          // Увеличиваем счетчик попыток и добавляем обратно в очередь
          request.attempts++;
          updatedQueue.push(request);
        }
        // Если shouldRetry = false, запрос либо успешно отправлен, либо исчерпал попытки
      }

      await this.savePendingQueue(updatedQueue);
      
      if (updatedQueue.length > 0) {
        log.info(LogCategory.API, 'Очередь повторов обновлена', { 
          remainingRequests: updatedQueue.length 
        });
      } else {
        log.success(LogCategory.API, 'Все запросы из очереди повторов успешно отправлены');
      }

    } catch (error) {
      log.error(LogCategory.API, 'Ошибка обработки очереди повторов', { error: error.message });
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Обрабатывает один запрос из очереди
   * @returns true если нужно повторить попытку, false если запрос завершен (успешно или исчерпаны попытки)
   */
  private async processRequest(request: PendingRequest): Promise<boolean> {
    if (request.attempts >= request.maxAttempts) {
      log.error(LogCategory.API, 'Исчерпаны попытки отправки запроса', {
        requestId: request.id,
        cardId: request.cardId,
        attempts: request.attempts,
        maxAttempts: request.maxAttempts
      });
      return false;
    }

    // Вычисляем задержку с экспоненциальным backoff
    const delay = Math.min(
      this.config.baseDelay * Math.pow(this.config.backoffMultiplier, request.attempts),
      this.config.maxDelay
    );

    // Проверяем, прошло ли достаточно времени с последней попытки
    const timeSinceLastAttempt = Date.now() - request.timestamp;
    if (timeSinceLastAttempt < delay) {
      return true; // Еще рано для повторной попытки
    }

    try {
      log.info(LogCategory.API, 'Повторная попытка отправки запроса', {
        requestId: request.id,
        cardId: request.cardId,
        attempt: request.attempts + 1,
        maxAttempts: request.maxAttempts,
        delay: delay
      });

      const response = await fetch(request.url, {
        method: request.method,
        headers: request.headers,
        body: request.body
      });

      if (response.ok) {
        log.success(LogCategory.API, 'Запрос успешно отправлен при повторной попытке', {
          requestId: request.id,
          cardId: request.cardId,
          attempt: request.attempts + 1
        });
        return false; // Успешно отправлен, убираем из очереди
      } else {
        const errorText = await response.text();
        log.warning(LogCategory.API, 'Повторная попытка неуспешна', {
          requestId: request.id,
          cardId: request.cardId,
          status: response.status,
          error: errorText
        });
        
        // Обновляем timestamp для следующей попытки
        request.timestamp = Date.now();
        return true; // Нужна еще одна попытка
      }
    } catch (error) {
      log.warning(LogCategory.API, 'Ошибка при повторной попытке', {
        requestId: request.id,
        cardId: request.cardId,
        error: error.message
      });
      
      // Обновляем timestamp для следующей попытки
      request.timestamp = Date.now();
      return true; // Нужна еще одна попытка
    }
  }

  /**
   * Запускает периодическую обработку очереди
   */
  private startPeriodicRetry(): void {
    // Проверяем очередь каждые 10 секунд
    setInterval(() => {
      this.processQueue();
    }, 10000);

    // Также обрабатываем очередь при запуске
    setTimeout(() => {
      this.processQueue();
    }, 1000);
  }

  /**
   * Генерирует уникальный ID для запроса
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Получает статистику очереди повторов
   */
  async getQueueStats(): Promise<{ totalRequests: number; oldestRequest?: number }> {
    const queue = await this.getPendingQueue();
    const oldestRequest = queue.length > 0 ? Math.min(...queue.map(r => r.timestamp)) : undefined;
    
    return {
      totalRequests: queue.length,
      oldestRequest
    };
  }

  /**
   * Очищает очередь повторов (для отладки)
   */
  async clearQueue(): Promise<void> {
    await AsyncStorage.removeItem(STORAGE_KEY);
    log.info(LogCategory.API, 'Очередь повторов очищена');
  }
}

// Создаем единственный экземпляр сервиса
export const retryService = new RetryService();

export default retryService;
