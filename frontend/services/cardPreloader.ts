/**
 * Сервис предзагрузки карточек для оптимизации производительности.
 *
 * 📖 ПОЛНАЯ ДОКУМЕНТАЦИЯ: backend/docs/SPACED_REPETITION_SYSTEM.md
 *
 * Реализует идею из promts/2.md:
 * - Загружает следующую карточку в фоне, пока пользователь работает с текущей
 * - Если ответ правильный - показывает предзагруженную карточку
 * - Если неправильный - повторяет текущую карточку (форсированная очередь)
 *
 * 🔑 КЛЮЧЕВЫЕ ОСОБЕННОСТИ:
 * - Двухуровневая фильтрация дублирующихся карточек
 * - Использует флаг preload=true для НЕ создания записей прогресса
 * - Альтернативные карточки тоже запрашиваются с preload=true
 * - Кэширование с автоматической очисткой устаревших данных
 *
 * ⚠️ ВАЖНО: Предзагрузка НЕ должна влиять на прогресс пользователя!
 */

import { API_ENDPOINT } from '../config/api';
import { Card } from './api';

// Импортируем функцию обработки данных карточки
import { processCardData } from './cardDataProcessor';
import { log, LogCategory } from '../utils/logger';

interface PreloadedCard {
  card: Card | null;
  isLoading: boolean;
  error: string | null;
  timestamp: number;
}

class CardPreloader {
  private preloadedCard: PreloadedCard = {
    card: null,
    isLoading: false,
    error: null,
    timestamp: 0
  };

  private preloadPromise: Promise<Card | null> | null = null;
  private maxCacheAge = 5 * 60 * 1000; // 5 минут

  /**
   * Начинает предзагрузку следующей карточки в фоне
   */
  async startPreloading(userId: string, nativeLang: string, targetLang: string, currentCardId?: string): Promise<void> {
    const startTime = Date.now();

    log.preloadStart(userId, targetLang);

    // Если уже загружаем, не начинаем новую загрузку
    if (this.preloadedCard.isLoading) {
      log.warning(LogCategory.PRELOADER, 'Уже идет загрузка, пропускаем');
      return;
    }

    this.preloadedCard.isLoading = true;
    this.preloadedCard.error = null;

    try {
      // Создаем промис для загрузки
      this.preloadPromise = this.fetchNextCard(userId, nativeLang, targetLang, currentCardId);

      // Загружаем карточку
      const card = await this.preloadPromise;
      const loadTime = Date.now() - startTime;

      if (card) {
        // 🎯 ВАШЕ РЕШЕНИЕ: Проверяем, не вернулась ли та же карточка
        const cardId = card.word_id || card.id || card._id;

        // Отладочные логи
        log.debug(LogCategory.PRELOADER, 'Проверка дублирования карточки', {
          currentCardId: currentCardId,
          preloadedCardId: cardId,
          preloadedWord: card.target_word?.word || card.word || 'unknown',
          isDuplicate: currentCardId && cardId === currentCardId
        });

        if (currentCardId && cardId === currentCardId) {
          log.debug(LogCategory.PRELOADER, '🚨 ДУБЛИРОВАНИЕ: Предзагрузка вернула ту же карточку!', {
            current: currentCardId,
            preloaded: cardId
          });
          log.debug(LogCategory.PRELOADER, '🔄 АЛЬТЕРНАТИВА: Запрашиваем альтернативную карточку');

          // Запрашиваем еще одну карточку (следующую) с флагом предзагрузки
          const alternativeCard = await this.fetchRawData(userId, nativeLang, targetLang, currentCardId);

          if (!alternativeCard) {
            log.debug(LogCategory.PRELOADER, '❌ АЛЬТЕРНАТИВА: Альтернативная карточка не найдена (null)');
          } else {
            log.debug(LogCategory.PRELOADER, `✅ АЛЬТЕРНАТИВА: Получена карточка "${alternativeCard?.word}"`);
          }

          const processedAlternativeCard = alternativeCard ? await processCardData({
            data: alternativeCard,
            userId,
            nativeLang,
            targetLang
          }) : null;

          if (processedAlternativeCard) {
            const altCardId = processedAlternativeCard.word_id || processedAlternativeCard.id || processedAlternativeCard._id;
            log.debug(LogCategory.PRELOADER, `🔍 АЛЬТЕРНАТИВА: Обработана карточка "${processedAlternativeCard.word}" (ID: ${altCardId})`);

            if (altCardId === currentCardId) {
              // Если и вторая карточка та же - не кэшируем ничего
              log.debug(LogCategory.PRELOADER, '🚨 ДВОЙНОЕ ДУБЛИРОВАНИЕ: Даже альтернативная карточка та же!');
              this.preloadedCard = {
                card: null,
                isLoading: false,
                error: 'Все доступные карточки - текущая',
                timestamp: Date.now()
              };
            } else {
              // Кэшируем альтернативную карточку
              log.debug(LogCategory.PRELOADER, `✅ АЛЬТЕРНАТИВА: Кэшируем карточку "${processedAlternativeCard.word}"`);
              this.preloadedCard = {
                card: processedAlternativeCard,
                isLoading: false,
                error: null,
                timestamp: Date.now()
              };
              log.preloadSuccess(processedAlternativeCard.target_word?.word || processedAlternativeCard.word || 'unknown', loadTime, 'альтернативная');
            }
          } else {
            // Альтернативная карточка не найдена
            log.debug(LogCategory.PRELOADER, '❌ АЛЬТЕРНАТИВА: Не удалось обработать альтернативную карточку');
            this.preloadedCard = {
              card: null,
              isLoading: false,
              error: 'Альтернативная карточка не найдена',
              timestamp: Date.now()
            };
            log.debug(LogCategory.PRELOADER, 'Альтернативная карточка не найдена');
          }
        } else {
          // Карточка отличается от текущей - кэшируем как обычно
          this.preloadedCard = {
            card,
            isLoading: false,
            error: null,
            timestamp: Date.now()
          };
          log.preloadSuccess(card.target_word?.word || card.word || 'unknown', loadTime);
        }
      } else {
        this.preloadedCard = {
          card: null,
          isLoading: false,
          error: 'Карточка не найдена',
          timestamp: Date.now()
        };
        log.warning(LogCategory.PRELOADER, `Карточка не найдена за ${loadTime}ms`);
      }
    } catch (error) {
      const loadTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Неизвестная ошибка';
      log.error(LogCategory.PRELOADER, `Ошибка предзагрузки за ${loadTime}ms: ${errorMessage}`);
      this.preloadedCard = {
        card: null,
        isLoading: false,
        error: errorMessage,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Получает предзагруженную карточку, если она доступна
   */
  getPreloadedCard(): Card | null {
    const now = Date.now();

    // Проверяем, не устарела ли карточка
    if (this.preloadedCard.timestamp &&
        now - this.preloadedCard.timestamp > this.maxCacheAge) {
      log.warning(LogCategory.PRELOADER, 'Предзагруженная карточка устарела, очищаем кэш');
      this.clearPreloadedCard();
      return null;
    }

    if (this.preloadedCard.card && !this.preloadedCard.isLoading) {
      const cardWord = this.preloadedCard.card.target_word?.word || this.preloadedCard.card.word || 'unknown';
      log.preloadUsed(cardWord);

      // Возвращаем карточку и очищаем кэш
      const card = this.preloadedCard.card;
      this.clearPreloadedCard();
      return card;
    }

    if (this.preloadedCard.isLoading) {
      const loadingTime = this.preloadedCard.timestamp ? now - this.preloadedCard.timestamp : 0;
      log.info(LogCategory.PRELOADER, `Предзагрузка еще идет (${loadingTime}ms)`);
    } else {
      log.preloadMissed();
    }

    return null;
  }

  /**
   * Ожидает завершения текущей предзагрузки с таймаутом
   */
  async waitForPreload(timeoutMs: number = 1000): Promise<Card | null> {
    if (this.preloadPromise) {
      console.log(`[PRELOADER] ⏳ Ожидаем завершения предзагрузки (таймаут: ${timeoutMs}ms)...`);
      try {
        // Создаем промис с таймаутом
        const timeoutPromise = new Promise<null>((_, reject) => {
          setTimeout(() => reject(new Error('Timeout')), timeoutMs);
        });

        const card = await Promise.race([this.preloadPromise, timeoutPromise]);
        this.preloadPromise = null;
                return card;
      } catch (error) {
        if (error.message === 'Timeout') {
          console.log(`[PRELOADER] ⏰ Таймаут ожидания предзагрузки (${timeoutMs}ms)`);
        } else {
          console.error('[PRELOADER] ❌ Ошибка при ожидании предзагрузки:', error);
        }
        this.preloadPromise = null;
        return null;
      }
    }
    return this.getPreloadedCard();
  }

  /**
   * Очищает предзагруженную карточку
   */
  clearPreloadedCard(): void {
    this.preloadedCard = {
      card: null,
      isLoading: false,
      error: null,
      timestamp: 0
    };
    this.preloadPromise = null;
  }

  /**
   * Полная очистка всего кэша предзагрузчика
   */
  clearAllCache(): void {
    this.clearPreloadedCard();
    console.log('[PRELOADER] 🧹 Весь кэш предзагрузчика очищен');
  }

  /**
   * Проверяет, идет ли сейчас загрузка
   */
  isLoading(): boolean {
    return this.preloadedCard.isLoading;
  }

  /**
   * Получает статус предзагрузки
   */
  getStatus(): PreloadedCard {
    return { ...this.preloadedCard };
  }

  /**
   * Загружает следующую карточку с сервера, используя ту же логику что и fetchNewCard
   */
  private async fetchNextCard(userId: string, nativeLang: string, targetLang: string, currentCardId?: string): Promise<Card | null> {
    const requestStart = Date.now();

    try {
      log.debug(LogCategory.PRELOADER, 'Начинаем загрузку карточки');

      // 🚀 Используем ту же логику что и в fetchRawCardData из cardDataProcessor
      const rawApiData = await this.fetchRawData(userId, nativeLang, targetLang, currentCardId);

      // Если нет данных (все карточки изучены), возвращаем null
      if (!rawApiData) {
                return null;
      }

      // 🚀 Используем общую логику обработки данных
      const processedCard = await processCardData({
        data: rawApiData,
        userId,
        nativeLang,
        targetLang
      });

      const totalTime = Date.now() - requestStart;
      log.timing('Общее время предзагрузки с обработкой', totalTime, {
        word: processedCard?.word || 'unknown'
      });

      return processedCard;
    } catch (error) {
      const totalTime = Date.now() - requestStart;
      console.error(`[PRELOADER] ❌ Ошибка загрузки карточки за ${totalTime}ms:`, error);
      throw error;
    }
  }

  /**
   * Получает сырые данные с API, используя ту же логику что и fetchRawCardData
   */
  private async fetchRawData(userId: string, nativeLang: string, targetLang: string, currentCardId?: string): Promise<any> {
    // 🔍 ДЕТАЛЬНЫЕ ЛОГИ: Начало запроса
    log.debug(LogCategory.PRELOADER, `🔍 ЗАПРОС: Начинаем fetchRawData для пользователя ${userId.slice(-8)}`);
    log.debug(LogCategory.PRELOADER, `📋 ПАРАМЕТРЫ: nativeLang=${nativeLang}, targetLang=${targetLang}`);

    const params = new URLSearchParams({
      user_id: userId,
      native_lang: nativeLang,
      target_lang: targetLang,
      preload: 'true'  // 🚀 КЛЮЧЕВОЕ ИЗМЕНЕНИЕ: указываем, что это предзагрузка
    });

    // 🎯 НОВОЕ: Исключаем текущую карточку из выборки
    if (currentCardId) {
      params.append('exclude_card_id', currentCardId);
      log.debug(LogCategory.PRELOADER, `🚫 ИСКЛЮЧЕНИЕ: Исключаем карточку ${currentCardId}`);
    } else {
      log.debug(LogCategory.PRELOADER, `⚠️ ВНИМАНИЕ: currentCardId пустой, не исключаем карточки`);
    }

    const url = `${API_ENDPOINT}/spaced/next?${params.toString()}`;
    log.debug(LogCategory.PRELOADER, `🌐 URL: ${url}`);
    log.apiRequest('/api/spaced/next', { preload: true });

    const fetchStart = Date.now();
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    const fetchTime = Date.now() - fetchStart;
    log.apiResponse('/api/spaced/next', response.status, fetchTime);

    if (!response.ok) {
      const errorText = await response.text();

      // 🔍 ДЕТАЛЬНЫЕ ЛОГИ: Ошибка запроса
      log.debug(LogCategory.PRELOADER, `❌ ОШИБКА: HTTP ${response.status}`);
      log.debug(LogCategory.PRELOADER, `📋 ТЕЛО ОШИБКИ: ${errorText}`);

      // Проверяем если это ошибка "нет карточек" - возвращаем null
      if (response.status === 404 && errorText.includes('No cards available for review')) {
        log.debug(LogCategory.PRELOADER, `🚨 КРИТИЧЕСКАЯ ОШИБКА: Нет доступных карточек для предзагрузки`);
        return null;
      }
      if (response.status === 500 && errorText.includes('No cards available for review')) {
        log.debug(LogCategory.PRELOADER, `🚨 КРИТИЧЕСКАЯ ОШИБКА: Нет доступных карточек для предзагрузки (500)`);
        return null;
      }
      throw new Error(`HTTP error! status: ${response.status}, body: ${errorText}`);
    }

    const parseStart = Date.now();
    const data = await response.json();
    const parseTime = Date.now() - parseStart;

    // 🔍 ДЕТАЛЬНЫЕ ЛОГИ: Успешный ответ
    log.debug(LogCategory.PRELOADER, `✅ УСПЕХ: Получены данные карточки`);
    log.debug(LogCategory.PRELOADER, `📋 ДАННЫЕ: word="${data?.word}", word_id="${data?.word_id}"`);

    return data;
  }
}

// Экспортируем синглтон
export const cardPreloader = new CardPreloader();

/**
 * Хук для использования предзагрузчика в React компонентах
 */
export const useCardPreloader = () => {
  return {
    startPreloading: cardPreloader.startPreloading.bind(cardPreloader),
    getPreloadedCard: cardPreloader.getPreloadedCard.bind(cardPreloader),
    waitForPreload: cardPreloader.waitForPreload.bind(cardPreloader),
    clearPreloadedCard: cardPreloader.clearPreloadedCard.bind(cardPreloader),
    clearAllCache: cardPreloader.clearAllCache.bind(cardPreloader),
    isLoading: cardPreloader.isLoading.bind(cardPreloader),
    getStatus: cardPreloader.getStatus.bind(cardPreloader),
  };
};
