# Retry Service - Автоматическая повторная отправка ответов

## Проблема

При использовании ngrok для разработки иногда происходят сбои соединения (503 Gateway Error), из-за которых ответы пользователя на карточки не доходят до сервера. Это приводит к потере данных о прогрессе обучения.

## Решение

Реализован `RetryService` - сервис автоматической повторной отправки запросов с следующими возможностями:

### Основные функции

1. **Немедленная попытка отправки** - сначала пытается отправить запрос обычным способом
2. **Автоматическое сохранение при ошибке** - если запрос не удался, сохраняет его в локальную очередь
3. **Периодические повторы** - каждые 10 секунд проверяет очередь и пытается отправить неудавшиеся запросы
4. **Экспоненциальный backoff** - увеличивает интервал между попытками (1с → 2с → 4с → 8с → 16с)
5. **Принудительная обработка** - при активации приложения сразу обрабатывает очередь

### Настройки по умолчанию

```typescript
{
  maxAttempts: 5,        // Максимум 5 попыток
  baseDelay: 1000,       // Начальная задержка 1 секунда
  maxDelay: 30000,       // Максимальная задержка 30 секунд
  backoffMultiplier: 2   // Удваиваем задержку каждый раз
}
```

### Использование

```typescript
import retryService from '../services/retryService';

// Отправка с автоматическими повторами
const response = await retryService.sendWithRetry(
  url,
  {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  },
  {
    cardId: 'card-123',
    userId: 'user-456',
    isCorrect: true,
    responseTime: 2.5
  }
);

if (response) {
  // Запрос успешно отправлен
  const data = await response.json();
} else {
  // Запрос добавлен в очередь повторов
  // Будет отправлен автоматически позже
}
```

### Логирование

Сервис подробно логирует все операции:

- ✅ Успешные отправки
- ⚠️ Добавление в очередь при ошибках
- ℹ️ Повторные попытки
- ❌ Исчерпание попыток

### Хранение данных

Неотправленные запросы сохраняются в `AsyncStorage` под ключом `pending_card_responses` и автоматически восстанавливаются при перезапуске приложения.

### Прозрачность для пользователя

Пользователь **не видит никаких ошибок** - UI продолжает работать нормально, а данные отправляются в фоне. Это обеспечивает плавный пользовательский опыт даже при нестабильном соединении.

### Интеграция

Сервис интегрирован в `TrainingScreen.tsx` и автоматически обрабатывает все ответы на карточки. Никаких дополнительных действий от разработчика не требуется.

### Тестирование

Для тестирования доступны:

- `frontend/tests/retryService.test.js` - юнит-тесты
- `frontend/tests/retryServiceDemo.js` - демонстрация работы

### Мониторинг

Для отладки доступны методы:

```typescript
// Статистика очереди
const stats = await retryService.getQueueStats();
console.log(`Запросов в очереди: ${stats.totalRequests}`);

// Принудительная обработка
await retryService.processQueue();

// Очистка очереди (только для отладки)
await retryService.clearQueue();
```

## Результат

Теперь при сбоях ngrok пользователи не теряют свой прогресс - все ответы автоматически сохраняются и отправляются при восстановлении соединения.
