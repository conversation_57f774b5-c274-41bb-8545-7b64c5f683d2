import AsyncStorage from '@react-native-async-storage/async-storage';

export interface Example {
  sentence: string;
  correct_answers: string[];
  translations?: string[];
  word_info?: {
    type: string;
    form?: string;
  };
}

export interface Word {
  _id: string;
  word: string;
  text?: string;  // Добавляем необязательное поле text
  language: string;
  concept_id: string;
  level: string;
  priority?: 'ultra_core' | 'core' | 'extended'; // Приоритет для A0 уровня
  translations: string[];
  examples: Example[];
  tags: string[];
  created_at: string;
  updated_at: string;
  form?: string;
  transcription?: string;
}

export interface CardProgress {
  ease_factor: number;
  interval: number;
  repetitions: number;
  review_date: string;
  next_review: string;
}

export interface Card {
  _id: string;
  native_word: Word;
  target_word: Word;
  progress?: CardProgress;
  id?: string;
  word?: string;
  translation?: string;
  translationWord?: string;
  english?: string;
  level?: string;
  examples?: Example[];
  type?: string;
  form?: string;
  transcription?: string;
}

import { API_ENDPOINT } from '../config/api';
import { log, LogCategory } from '../utils/logger';

const API_BASE_URL = API_ENDPOINT;

export const fetchRandomCard = async (level?: string, nativeLang: string = 'en', targetLang: string = 'en'): Promise<Card> => {
  try {
    const params = new URLSearchParams();
    if (level) params.append('level', level);
    params.append('native_lang', nativeLang);
    params.append('target_lang', targetLang);
    
    const url = `${API_BASE_URL}/cards/random?${params.toString()}`;
    
    log.apiRequest('/cards/random', { level, nativeLang, targetLang });
    const response = await fetch(url);
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || 'Failed to fetch card');
    }
    
    return await response.json();
  } catch (error) {
    log.error(LogCategory.API, `Error fetching random card: ${error.message}`);
    throw error;
  }
};

export const fetchCardByConceptId = async (conceptId: string): Promise<Card> => {
  try {
    const response = await fetch(`${API_BASE_URL}/cards/${conceptId}`);
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || 'Failed to fetch card');
    }
    
    return await response.json();
  } catch (error) {
    log.error(LogCategory.API, `Error fetching card with concept_id ${conceptId}: ${error.message}`);
    throw error;
  }
};

export const submitCardResponse = async (cardId: string, rating: number): Promise<CardProgress> => {
  try {
    const token = await AsyncStorage.getItem('userToken');
    const response = await fetch(`${API_BASE_URL}/cards/${cardId}/review`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ rating })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || 'Failed to submit card response');
    }

    return await response.json();
  } catch (error) {
    log.error('API', `Error submitting card response: ${error.message}`);
    throw error;
  }
};
