/**
 * Общая логика обработки данных карточек.
 * Используется как в основной загрузке, так и в предзагрузке.
 */

import { Card } from './api';
import { API_URL } from '../config/api';
import { log, LogCategory } from '../utils/logger';

export interface ProcessCardDataParams {
  data: any;
  userId: string;
  nativeLang: string;
  targetLang: string;
}

/**
 * Обрабатывает сырые данные карточки с API и возвращает готовую структуру Card
 */
export async function processCardData({
  data,
  userId,
  nativeLang,
  targetLang
}: ProcessCardDataParams): Promise<Card> {
  const processStart = Date.now();

  if (!data || !data.word_id) {
    throw new Error('Invalid card data received from spaced repetition API');
  }

  log.debug(LogCategory.CARD_LOADING, 'Обрабатываем данные карточки', {
    word: data.word,
    language: data.language
  });
  
  // Основное слово (то, что изучаем)
  const mainWord = data.word || '';
  const mainExamples = data.examples || [];
  const mainPartOfSpeech = data.part_of_speech || '';
  const mainPronunciation = data.ipa_pronunciation || '';
  const mainLanguage = data.language || targetLang;
  
  // Перевод (уже приходит с backend!)
  const translationWord = data.translation || '';
  const translationExamples = data.translation_examples || [];
  const translationLanguage = data.translation_language || nativeLang;
  
  // Данные получены, обрабатываем
  
  // 🚀 ОПТИМИЗАЦИЯ: Определяем, какое слово основное, а какое перевод
  let targetWord, nativeWord, targetExamples, nativeExamples;
  
  if (mainLanguage === targetLang) {
    // Основное слово на изучаемом языке, перевод на родном
    targetWord = mainWord;
    targetExamples = mainExamples;
    nativeWord = translationWord || mainWord; // fallback
    nativeExamples = translationExamples || mainExamples;
  } else {
    // Основное слово на родном языке, изучаем перевод
    nativeWord = mainWord;
    nativeExamples = mainExamples;
    targetWord = translationWord || mainWord; // fallback
    targetExamples = translationExamples || mainExamples;
  }
  
  // Данные обработаны, создаем карточку
  
  // 🚀 ОПТИМИЗАЦИЯ: Создаем структуру карточки с оптимизированными данными
  const cardData: Card = {
    id: data.word_id,
    word: targetWord,  // Изучаемое слово
    translation: nativeWord,  // Перевод на родной язык
    translationWord: nativeWord,  // Дублируем для совместимости
    english: targetWord,  // Дублируем для совместимости (может быть не английский)
    progress: 0,  // Начинаем с 0% прогресса
    level: data.level || 'A1',
    priority: data.priority, // Приоритет для A0 уровня
    examples: targetExamples.map((ex: any) => ({
      sentence: ex.sentence || '',
      translation: ex.translation || ''
    })),
    type: 'word',  // Тип карточки
    form: mainPartOfSpeech || 'verb',  // Форма слова (глагол, существительное и т.д.)
    transcription: mainPronunciation,  // Транскрипция
    // Поля интервального повторения
    is_new: data.is_new,
    is_forced_review: data.is_forced_review,
    interval_level: data.interval_level ?? (data.is_new ? -1 : 0), // Временное решение: -1 для новых, 0 для остальных
    concept_id: data.concept_id,
    part_of_speech: data.part_of_speech,
    ipa_pronunciation: data.ipa_pronunciation,
    // Дополнительные поля
    target_word: {
      _id: data.word_id,
      word: targetWord,
      text: targetWord,
      language: targetLang,
      examples: targetExamples,
      part_of_speech: mainPartOfSpeech,
      ipa_pronunciation: mainPronunciation,
      level: data.level || 'A1'
    },
    native_word: {
      _id: `${data.word_id}_${nativeLang}`,
      word: nativeWord,
      text: nativeWord,
      language: nativeLang,
      examples: nativeExamples,
      part_of_speech: data.part_of_speech || ''
    }
  };
  
  const processTime = Date.now() - processStart;
  log.timing('Обработка данных карточки', processTime, {
    word: cardData.word,
    translation: cardData.translation
  });
  
  return cardData;
}

/**
 * Получает сырые данные карточки с API
 */
export async function fetchRawCardData({
  userId,
  nativeLang,
  targetLang
}: {
  userId: string;
  nativeLang: string;
  targetLang: string;
}): Promise<any> {
  const apiRequestStart = Date.now();
  
  const response = await fetch(
    `${API_URL}/api/spaced/next?user_id=${userId}&native_lang=${nativeLang}&target_lang=${targetLang}`,
    {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );
  
  const apiRequestTime = Date.now() - apiRequestStart;
  log.apiResponse('/api/spaced/next', response.status, apiRequestTime);
  
  if (!response.ok) {
    const errorText = await response.text();
    // Проверяем если это ошибка "нет карточек"
    if (response.status === 500 && errorText.includes('No cards available for review')) {
      // Возвращаем null чтобы показать экран завершения
      return null;
    }
    throw new Error(`HTTP error! status: ${response.status}, body: ${errorText}`);
  }
  
  const data = await response.json();
  
  return data;
}
