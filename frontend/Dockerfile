# Используем официальный Node.js образ
FROM node:18-slim

# Устанавливаем рабочую директорию
WORKDIR /app

# Устанавливаем системные зависимости
RUN apt-get update && apt-get install -y \
    git \
    && rm -rf /var/lib/apt/lists/*

# Копируем package.json и package-lock.json
COPY package*.json ./

# Устанавливаем зависимости
RUN npm install

# Устанавливаем Expo CLI глобально
RUN npm install -g @expo/cli

# Копируем код приложения
COPY . .

# Создаем пользователя для безопасности
RUN useradd --create-home --shell /bin/bash app \
    && chown -R app:app /app
USER app

# Открываем порты
EXPOSE 19006 8081

# Команда по умолчанию
CMD ["npx", "expo", "start", "--web", "--port", "19006"]
