/**
 * Централизованная система логирования для приложения
 * Обеспечивает единообразное форматирование и категоризацию логов
 */

// Типы логов
export enum LogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  SUCCESS = 'SUCCESS',
  WARNING = 'WARNING',
  ERROR = 'ERROR',
  TIMING = 'TIMING'
}

// Категории логов
export enum LogCategory {
  CARD_LOADING = 'CARD_LOADING',
  PRELOADER = 'PRELOADER',
  USER_INPUT = 'USER_INPUT',
  API = 'API',
  NAVIGATION = 'NAVIGATION',
  ANIMATION = 'ANIMATION',
  PROGRESS = 'PROGRESS',
  AUTH = 'AUTH'
}

// Цвета для разных типов логов (эмодзи для мобильных логов)
const LOG_ICONS = {
  [LogLevel.DEBUG]: '🔍',
  [LogLevel.INFO]: 'ℹ️',
  [LogLevel.SUCCESS]: '✅',
  [LogLevel.WARNING]: '⚠️',
  [LogLevel.ERROR]: '❌',
  [LogLevel.TIMING]: '⏱️'
};

const CATEGORY_ICONS = {
  [LogCategory.CARD_LOADING]: '📱',
  [LogCategory.PRELOADER]: '🚀',
  [LogCategory.USER_INPUT]: '👤',
  [LogCategory.API]: '🌐',
  [LogCategory.NAVIGATION]: '🧭',
  [LogCategory.ANIMATION]: '🎬',
  [LogCategory.PROGRESS]: '📊',
  [LogCategory.AUTH]: '🔐'
};

class Logger {
  private isDevelopment = __DEV__;
  
  /**
   * Форматирует время в читаемый вид
   */
  private formatTime(): string {
    const now = new Date();
    return now.toTimeString().split(' ')[0] + '.' + now.getMilliseconds().toString().padStart(3, '0');
  }

  /**
   * Основной метод логирования
   */
  private log(
    level: LogLevel,
    category: LogCategory,
    message: string,
    data?: any,
    addSeparator: boolean = false
  ): void {
    if (!this.isDevelopment) return;

    const time = this.formatTime();
    const icon = LOG_ICONS[level];
    const categoryIcon = CATEGORY_ICONS[category];
    
    // Формируем основное сообщение
    const formattedMessage = `${icon} [${time}] ${categoryIcon} ${category}: ${message}`;
    
    // Добавляем разделитель перед логом если нужно
    if (addSeparator) {
      console.log('');
    }
    
    // Выводим основное сообщение
    console.log(formattedMessage);
    
    // Выводим дополнительные данные если есть
    if (data !== undefined) {
      console.log('   📋 Data:', data);
    }
  }

  // === МЕТОДЫ ДЛЯ ЗАГРУЗКИ КАРТОЧЕК ===
  
  cardLoadStart(userId: string, language: string): void {
    this.log(LogLevel.INFO, LogCategory.CARD_LOADING, 
      `Начинаем загрузку карточки для пользователя ${userId.slice(-8)}`, 
      { language }, true);
  }

  cardLoadSuccess(word: string, translation: string, loadTime: number): void {
    this.log(LogLevel.SUCCESS, LogCategory.CARD_LOADING, 
      `Карточка загружена: "${word}" (${translation})`,
      { loadTime: `${loadTime}ms` });
  }

  cardLoadError(error: string): void {
    this.log(LogLevel.ERROR, LogCategory.CARD_LOADING, 
      `Ошибка загрузки карточки: ${error}`);
  }

  // === МЕТОДЫ ДЛЯ ПРЕДЗАГРУЗКИ ===
  
  preloadStart(userId: string, language: string): void {
    this.log(LogLevel.INFO, LogCategory.PRELOADER, 
      `Начинаем предзагрузку для ${userId.slice(-8)}`, 
      { language }, true);
  }

  preloadSuccess(word: string, loadTime: number, type?: string): void {
    const typeText = type ? ` (${type})` : '';
    this.log(LogLevel.SUCCESS, LogCategory.PRELOADER,
      `Предзагружена карточка: "${word}"${typeText}`,
      { loadTime: `${loadTime}ms` });
  }

  preloadUsed(word: string): void {
    this.log(LogLevel.SUCCESS, LogCategory.PRELOADER, 
      `МГНОВЕННЫЙ переход к предзагруженной карточке: "${word}"`);
  }

  preloadMissed(): void {
    this.log(LogLevel.WARNING, LogCategory.PRELOADER, 
      'Предзагруженной карточки нет, загружаем обычным способом');
  }

  // === МЕТОДЫ ДЛЯ ПОЛЬЗОВАТЕЛЬСКОГО ВВОДА ===
  
  userAnswer(word: string, isCorrect: boolean, responseTime: number, usedHelp: boolean): void {
    const level = isCorrect ? LogLevel.SUCCESS : LogLevel.WARNING;
    const result = isCorrect ? 'ПРАВИЛЬНО' : 'НЕПРАВИЛЬНО';
    const helpText = usedHelp ? ' (с подсказкой)' : '';
    
    this.log(level, LogCategory.USER_INPUT, 
      `Ответ на "${word}": ${result}${helpText}`,
      { responseTime: `${responseTime.toFixed(1)}s` }, true);
  }

  // === МЕТОДЫ ДЛЯ API ===
  
  apiRequest(endpoint: string, params?: any): void {
    this.log(LogLevel.INFO, LogCategory.API, 
      `Запрос к ${endpoint}`,
      params);
  }

  apiResponse(endpoint: string, status: number, responseTime: number): void {
    const level = status >= 200 && status < 300 ? LogLevel.SUCCESS : LogLevel.ERROR;
    this.log(level, LogCategory.API, 
      `Ответ от ${endpoint} (${status})`,
      { responseTime: `${responseTime}ms` });
  }

  // === МЕТОДЫ ДЛЯ НАВИГАЦИИ ===
  
  navigationChange(from: string, to: string): void {
    this.log(LogLevel.INFO, LogCategory.NAVIGATION, 
      `Переход: ${from} → ${to}`, undefined, true);
  }

  // === МЕТОДЫ ДЛЯ АНИМАЦИЙ ===
  
  animationStart(type: string): void {
    this.log(LogLevel.INFO, LogCategory.ANIMATION, 
      `Начинаем анимацию: ${type}`);
  }

  animationComplete(type: string, duration: number): void {
    this.log(LogLevel.SUCCESS, LogCategory.ANIMATION, 
      `Анимация завершена: ${type}`,
      { duration: `${duration}ms` });
  }

  // === МЕТОДЫ ДЛЯ ПРОГРЕССА ===

  progressUpdate(word: string, newLevel: number, isLearned: boolean): void {
    this.log(LogLevel.INFO, LogCategory.PROGRESS,
      `Обновлен прогресс для "${word}"`,
      { level: newLevel, learned: isLearned }, true);
  }

  // === МЕТОДЫ ДЛЯ АУТЕНТИФИКАЦИИ ===

  authLogin(email: string): void {
    this.log(LogLevel.INFO, LogCategory.AUTH,
      `Попытка входа: ${email}`, undefined, true);
  }

  authLoginSuccess(userId: string): void {
    this.log(LogLevel.SUCCESS, LogCategory.AUTH,
      `Успешный вход пользователя ${userId.slice(-8)}`);
  }

  authLoginError(error: string): void {
    this.log(LogLevel.ERROR, LogCategory.AUTH,
      `Ошибка входа: ${error}`);
  }

  authRegister(email: string): void {
    this.log(LogLevel.INFO, LogCategory.AUTH,
      `Попытка регистрации: ${email}`, undefined, true);
  }

  authRegisterSuccess(userId: string): void {
    this.log(LogLevel.SUCCESS, LogCategory.AUTH,
      `Успешная регистрация пользователя ${userId.slice(-8)}`);
  }

  authLogout(): void {
    this.log(LogLevel.INFO, LogCategory.AUTH,
      'Выход из системы', undefined, true);
  }

  // === МЕТОДЫ ДЛЯ ТАЙМИНГОВ ===
  
  timing(operation: string, duration: number, details?: any): void {
    this.log(LogLevel.TIMING, LogCategory.API, 
      `${operation}: ${duration}ms`,
      details);
  }

  // === ОБЩИЕ МЕТОДЫ ===
  
  debug(category: LogCategory, message: string, data?: any): void {
    this.log(LogLevel.DEBUG, category, message, data);
  }

  info(category: LogCategory, message: string, data?: any): void {
    this.log(LogLevel.INFO, category, message, data);
  }

  success(category: LogCategory, message: string, data?: any): void {
    this.log(LogLevel.SUCCESS, category, message, data);
  }

  warning(category: LogCategory, message: string, data?: any): void {
    this.log(LogLevel.WARNING, category, message, data);
  }

  error(category: LogCategory, message: string, data?: any): void {
    this.log(LogLevel.ERROR, category, message, data);
  }

  // === РАЗДЕЛИТЕЛИ ===
  
  separator(title?: string): void {
    if (!this.isDevelopment) return;
    
    console.log('');
    if (title) {
      console.log(`🔸 === ${title.toUpperCase()} ===`);
    } else {
      console.log('─'.repeat(50));
    }
    console.log('');
  }
}

// Экспортируем единственный экземпляр логгера
export const logger = new Logger();

// Экспортируем удобные алиасы для часто используемых методов
export const log = {
  // Карточки
  cardStart: (userId: string, lang: string) => logger.cardLoadStart(userId, lang),
  cardSuccess: (word: string, translation: string, time: number) => logger.cardLoadSuccess(word, translation, time),
  cardError: (error: string) => logger.cardLoadError(error),
  
  // Предзагрузка
  preloadStart: (userId: string, lang: string) => logger.preloadStart(userId, lang),
  preloadSuccess: (word: string, time: number, type?: string) => logger.preloadSuccess(word, time, type),
  preloadUsed: (word: string) => logger.preloadUsed(word),
  preloadMissed: () => logger.preloadMissed(),
  
  // Пользователь
  userAnswer: (word: string, correct: boolean, time: number, help: boolean) => 
    logger.userAnswer(word, correct, time, help),
  
  // API
  apiRequest: (endpoint: string, params?: any) => logger.apiRequest(endpoint, params),
  apiResponse: (endpoint: string, status: number, time: number) => 
    logger.apiResponse(endpoint, status, time),
  
  // Тайминги
  timing: (operation: string, duration: number, details?: any) =>
    logger.timing(operation, duration, details),

  // Анимации
  animationStart: (type: string) => logger.animationStart(type),
  animationComplete: (type: string, duration: number) => logger.animationComplete(type, duration),

  // Аутентификация
  authLogin: (email: string) => logger.authLogin(email),
  authLoginSuccess: (userId: string) => logger.authLoginSuccess(userId),
  authLoginError: (error: string) => logger.authLoginError(error),
  authRegister: (email: string) => logger.authRegister(email),
  authRegisterSuccess: (userId: string) => logger.authRegisterSuccess(userId),
  authLogout: () => logger.authLogout(),

  // Разделители
  separator: (title?: string) => logger.separator(title),

  // Общие методы
  debug: (category: LogCategory, message: string, data?: any) => logger.debug(category, message, data),
  info: (category: LogCategory, message: string, data?: any) => logger.info(category, message, data),
  success: (category: LogCategory, message: string, data?: any) => logger.success(category, message, data),
  warning: (category: LogCategory, message: string, data?: any) => logger.warning(category, message, data),
  error: (category: LogCategory, message: string, data?: any) => logger.error(category, message, data)
};
