/**
 * Утилита для поиска и замены всех оставшихся старых логов
 */

const fs = require('fs');
const path = require('path');

// Паттерны старых логов, которые нужно заменить или удалить
const OLD_LOG_PATTERNS = [
  // Логи фокуса
  /console\.log\('🎯.*focusing input.*'\);?/g,
  /console\.log\('✅ Input focused.*'\);?/g,
  /console\.log\('❌ Input ref is null.*'\);?/g,
  
  // Логи обработки карточек
  /console\.log\('🔄 \[CARD PROCESSOR\].*'\);?/g,
  /console\.log\('🚀 \[OPTIMIZATION\].*'\);?/g,
  /console\.log\('🌐 \[RAW FETCH\].*'\);?/g,
  
  // Логи предзагрузки
  /console\.log\('\[PRELOADER\].*'\);?/g,
  
  // Логи анимации
  /console\.log\('⏰ \[TIMING\].*'\);?/g,
  
  // Общие отладочные логи
  /console\.log\('Starting.*process.*'\);?/g,
  /console\.log\('.*Card changed.*'\);?/g,
  /console\.log\('.*Input.*'\);?/g,
];

// Функция для очистки файла от старых логов
function cleanupFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let originalContent = content;
    let changesCount = 0;
    
    // Применяем все паттерны
    OLD_LOG_PATTERNS.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches) {
        changesCount += matches.length;
        content = content.replace(pattern, '// Лог удален автоматически');
      }
    });
    
    // Удаляем пустые строки после замены
    content = content.replace(/\/\/ Лог удален автоматически\n\s*\n/g, '');
    content = content.replace(/\/\/ Лог удален автоматически\n/g, '');
    
    if (changesCount > 0) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ ${filePath}: удалено ${changesCount} старых логов`);
      return changesCount;
    }
    
    return 0;
  } catch (error) {
    console.error(`❌ Ошибка обработки ${filePath}:`, error.message);
    return 0;
  }
}

// Функция для рекурсивного поиска файлов
function findFiles(dir, extensions = ['.ts', '.tsx', '.js', '.jsx']) {
  const files = [];
  
  function walk(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Пропускаем node_modules и другие служебные папки
        if (!['node_modules', '.git', '.expo', 'dist', 'build'].includes(item)) {
          walk(fullPath);
        }
      } else if (stat.isFile()) {
        const ext = path.extname(fullPath);
        if (extensions.includes(ext)) {
          files.push(fullPath);
        }
      }
    }
  }
  
  walk(dir);
  return files;
}

// Основная функция
function cleanupAllLogs() {
  console.log('🧹 === ОЧИСТКА СТАРЫХ ЛОГОВ ===\n');
  
  const frontendDir = path.join(__dirname, '..');
  const files = findFiles(frontendDir);
  
  console.log(`📁 Найдено ${files.length} файлов для проверки\n`);
  
  let totalChanges = 0;
  let processedFiles = 0;
  
  files.forEach(file => {
    const changes = cleanupFile(file);
    if (changes > 0) {
      processedFiles++;
      totalChanges += changes;
    }
  });
  
  console.log('\n🎉 === РЕЗУЛЬТАТ ===');
  console.log(`✅ Обработано файлов: ${processedFiles}`);
  console.log(`🗑️ Удалено старых логов: ${totalChanges}`);
  
  if (totalChanges > 0) {
    console.log('\n📋 Рекомендации:');
    console.log('1. Проверьте изменения в файлах');
    console.log('2. Запустите приложение для проверки');
    console.log('3. При необходимости добавьте новые логи через logger.ts');
  } else {
    console.log('\n🎊 Все логи уже чистые!');
  }
}

// Запускаем очистку
if (require.main === module) {
  cleanupAllLogs();
}

module.exports = { cleanupAllLogs, cleanupFile };
