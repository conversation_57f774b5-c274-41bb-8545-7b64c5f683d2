{"name": "word-master-design", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@expo/cli": "^0.24.14", "@expo/ngrok": "^4.1.3", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-masked-view/masked-view": "0.3.2", "@react-native-picker/picker": "^2.11.0", "@react-navigation/native": "^7.1.10", "@react-navigation/native-stack": "^7.3.14", "@react-navigation/stack": "^7.3.3", "@shopify/react-native-skia": "v2.0.0-next.4", "axios": "^1.9.0", "expo": "~53.0.9", "expo-blur": "^14.1.4", "expo-haptics": "~14.1.4", "expo-linear-gradient": "~14.1.5", "expo-secure-store": "^14.2.3", "expo-status-bar": "~2.2.3", "i18next": "^25.2.1", "react": "^19.0.0", "react-i18next": "^15.5.3", "react-native": "^0.79.5", "react-native-animatable": "^1.4.0", "react-native-country-flags": "^1.1.0", "react-native-gesture-handler": "^2.25.0", "react-native-linear-gradient": "^2.8.3", "react-native-paper": "^5.14.5", "react-native-radial-gradient": "^1.2.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "react-native-svg": "^15.11.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.0", "@types/react": "~19.0.10", "@types/react-native": "^0.72.8", "jest": "^30.0.1", "jest-environment-node": "^30.0.1", "typescript": "~5.8.3"}, "private": true, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}