# Word Master - Экран тренировки

Демонстрация дизайна экрана тренировки для приложения Word Master.

## Запуск проекта

1. Установите зависимости:
```bash
npm install
```

2. Запустите проект:
```bash
npx expo start
```

3. Выберите платформу:
- Нажмите `i` для iOS симулятора
- Нажмите `a` для Android эмулятора
- Используйте Expo Go на телефоне для просмотра на реальном устройстве

## Особенности дизайна

- **Тёмная тема** с градиентным фоном
- **Стекломорфизм** для карточек и панелей
- **Многоцветные градиенты** (фиолетовые, зелёные, оранжевые)
- **Анимации** при появлении элементов и взаимодействии
- **Интерактивные элементы**:
  - Поле ввода с визуальной обратной связью
  - Кнопка подсказки
  - Кнопка проверки с градиентом
  - Анимированная обратная связь (правильно/неправильно)

## Структура экрана

1. **Шапка с прогрессом**:
   - Уровень и серия дней (streak)
   - Прогресс дня с градиентным индикатором
   - Общее количество изученных слов

2. **Основная карточка**:
   - Английское предложение с пропуском
   - Русский перевод
   - Поле ввода для ответа
   - Кнопки действий

3. **Информационная панель**:
   - Текущий интервал повторения
   - Количество повторений
   - Точность ответов