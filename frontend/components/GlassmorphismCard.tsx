import React, { useMemo } from 'react';
import { View, StyleSheet, ViewStyle, Dimensions } from 'react-native';
import { Canvas, BackdropBlur, Rect, rrect, rect, Circle, RadialGradient, vec, Group, Blur } from '@shopify/react-native-skia';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');



// Props interface
interface GlassmorphismCardProps {
  children: React.ReactNode;
  style?: ViewStyle;
  blurIntensity?: number;
  backgroundColor?: string;
  borderColor?: string;
  borderWidth?: number;
  borderRadius?: number;
  width?: number | string;
  height?: number | 'auto'; // Добавляем 'auto' для адаптивной высоты
  withFogEffect?: boolean;
  fogColor?: string[];
  fogPosition?: { x: number; y: number };
  fogRadius?: number;
}

export const GlassmorphismCard: React.FC<GlassmorphismCardProps> = ({
  children,
  style,
  blurIntensity = 20,
  backgroundColor = 'rgba(20, 20, 35, 0.25)',
  borderColor = 'rgba(255, 255, 255, 0.08)',
  borderWidth = 1,
  borderRadius = 14,
  width = '100%',
  height = 'auto',
  withFogEffect = true,
  fogColor = [
    'rgba(120, 200, 255, 0.4)',
    'rgba(120, 200, 255, 0.25)',
    'rgba(120, 200, 255, 0.1)',
    'rgba(120, 200, 255, 0.02)',
    'rgba(120, 200, 255, 0)'
  ],
  fogPosition = { x: -50, y: 450 },
  fogRadius = 350
}) => {
  const cardWidth = typeof width === 'string' ? screenWidth - 16 : width;
  const cardHeight = height === 'auto' ? undefined : height;
  // Для Canvas нужна числовая высота, используем большое значение для auto
  const canvasHeight = height === 'auto' ? 1000 : height;

  return (
    <View style={[
      {
        borderRadius,
        overflow: 'hidden',
        backgroundColor,
        borderWidth,
        borderColor,
        width,
        height: cardHeight,
        minHeight: height === 'auto' ? 100 : undefined // Минимальная высота для auto
      },
      style
    ]}>
      {/* Glassmorphism background with blur effect */}
      <View style={StyleSheet.absoluteFill}>
        <Canvas style={{ flex: 1 }}>
          <BackdropBlur
            blur={blurIntensity}
            clip={rrect(rect(0, 0, cardWidth, canvasHeight), borderRadius, borderRadius)}
          >
            <Rect
              x={0}
              y={0}
              width={screenWidth}
              height={canvasHeight}
              color={backgroundColor}
            />
            
            {/* Optional fog effect */}
            {withFogEffect && (
              <Group>
                <Circle cx={fogPosition.x} cy={fogPosition.y} r={fogRadius}>
                  <RadialGradient
                    c={vec(fogPosition.x, fogPosition.y)}
                    r={fogRadius}
                    colors={fogColor}
                    positions={[0, 0.2, 0.5, 0.8, 1]}
                  />
                </Circle>
              </Group>
            )}
            
            <Blur blur={15} />
          </BackdropBlur>
        </Canvas>
      </View>
      
      {/* Content */}
      <View style={styles.content}>
        {children}
      </View>
    </View>
  );
};

// Предустановленные стили для разных типов glassmorphism карточек
export const glassmorphismPresets = {
  // Основная карточка тренировки
  training: {
    blurIntensity: 20,
    backgroundColor: 'rgba(20, 20, 35, 0.25)',
    borderColor: 'rgba(255, 255, 255, 0.08)',
    borderWidth: 1,
    borderRadius: 14,
    withFogEffect: true,
    fogColor: [
      'rgba(120, 200, 255, 0.4)',
      'rgba(120, 200, 255, 0.25)',
      'rgba(120, 200, 255, 0.1)',
      'rgba(120, 200, 255, 0.02)',
      'rgba(120, 200, 255, 0)'
    ]
  },
  
  // Карточка профиля
  profile: {
    blurIntensity: 15,
    backgroundColor: 'rgba(30, 30, 50, 0.3)',
    borderColor: 'rgba(255, 255, 255, 0.12)',
    borderWidth: 1,
    borderRadius: 16,
    withFogEffect: true,
    fogColor: [
      'rgba(150, 100, 255, 0.3)',
      'rgba(150, 100, 255, 0.2)',
      'rgba(150, 100, 255, 0.1)',
      'rgba(150, 100, 255, 0.05)',
      'rgba(150, 100, 255, 0)'
    ]
  },
  
  // Минимальная карточка
  minimal: {
    blurIntensity: 10,
    backgroundColor: 'rgba(40, 40, 60, 0.2)',
    borderColor: 'rgba(255, 255, 255, 0.06)',
    borderWidth: 1,
    borderRadius: 12,
    withFogEffect: false
  },
  
  // Карточка с акцентом
  accent: {
    blurIntensity: 25,
    backgroundColor: 'rgba(50, 20, 80, 0.3)',
    borderColor: 'rgba(170, 70, 255, 0.2)',
    borderWidth: 1.5,
    borderRadius: 18,
    withFogEffect: true,
    fogColor: [
      'rgba(170, 70, 255, 0.4)',
      'rgba(170, 70, 255, 0.25)',
      'rgba(170, 70, 255, 0.1)',
      'rgba(170, 70, 255, 0.02)',
      'rgba(170, 70, 255, 0)'
    ]
  }
};

const styles = StyleSheet.create({
  content: {
    position: 'relative',
    zIndex: 1,
    paddingTop: 44,
    paddingHorizontal: 32,
    paddingBottom: 24,
  }
});

// Экспорт типов
export type { GlassmorphismCardProps };
