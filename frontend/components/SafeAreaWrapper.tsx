import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface SafeAreaWrapperProps {
  children: React.ReactNode;
  style?: ViewStyle;
  edges?: ('top' | 'bottom' | 'left' | 'right')[];
  backgroundColor?: string;
  // Дополнительные отступы для кастомизации
  additionalBottomPadding?: number;
  additionalTopPadding?: number;
  // Минимальные отступы (если системные отступы меньше)
  minBottomPadding?: number;
  minTopPadding?: number;
  // Флаг для модальных окон (не применяет фон)
  isModal?: boolean;
}

export const SafeAreaWrapper: React.FC<SafeAreaWrapperProps> = ({
  children,
  style,
  edges = ['top', 'bottom', 'left', 'right'],
  backgroundColor = 'transparent',
  additionalBottomPadding = 0,
  additionalTopPadding = 0,
  minBottomPadding = 20, // Минимальный отступ снизу 20px
  minTopPadding = 0,
  isModal = false,
}) => {
  const insets = useSafeAreaInsets();

  // Вычисляем отступы с учетом минимальных значений
  const topPadding = edges.includes('top') 
    ? Math.max(insets.top + additionalTopPadding, minTopPadding)
    : additionalTopPadding;
    
  const bottomPadding = edges.includes('bottom') 
    ? Math.max(insets.bottom + additionalBottomPadding, minBottomPadding)
    : Math.max(additionalBottomPadding, minBottomPadding);
    
  const leftPadding = edges.includes('left') 
    ? insets.left 
    : 0;
    
  const rightPadding = edges.includes('right') 
    ? insets.right 
    : 0;

  const safeAreaStyle: ViewStyle = {
    paddingTop: topPadding,
    paddingBottom: bottomPadding,
    paddingLeft: leftPadding,
    paddingRight: rightPadding,
    // Не применяем backgroundColor для модальных окон
    ...(isModal ? {} : { backgroundColor }),
  };

  return (
    <View style={[styles.container, safeAreaStyle, style]}>
      {children}
    </View>
  );
};

// Предустановленные варианты для разных типов экранов
export const SafeAreaWrapperPresets = {
  // Для главного экрана с кнопками внизу
  homeScreen: {
    edges: ['top', 'bottom', 'left', 'right'] as const,
    minBottomPadding: 30, // Больше места для кнопок
    additionalBottomPadding: 10,
  },
  
  // Для экрана тренировки
  trainingScreen: {
    edges: ['top', 'bottom', 'left', 'right'] as const,
    minBottomPadding: 25, // Место для кнопок ввода
    additionalBottomPadding: 5,
  },
  
  // Для модальных окон
  modal: {
    edges: ['bottom', 'left', 'right'] as const,
    minBottomPadding: 20,
    isModal: true,
  },
  
  // Для экранов с формами
  form: {
    edges: ['top', 'bottom', 'left', 'right'] as const,
    minBottomPadding: 25, // Место для кнопок формы
    additionalBottomPadding: 10,
  },
  
  // Для экранов профиля
  profile: {
    edges: ['top', 'bottom', 'left', 'right'] as const,
    minBottomPadding: 20,
  },
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
