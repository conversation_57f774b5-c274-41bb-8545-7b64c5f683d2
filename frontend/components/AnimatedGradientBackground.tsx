import React, { useMemo } from 'react';
import { View, Dimensions } from 'react-native';
import { Canvas, RadialGradient, Rect, vec, Circle } from '@shopify/react-native-skia';

// Screen dimensions
const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
const baseScreenSize = Math.min(screenWidth, screenHeight);

// Функция для генерации более заметных цветов для сфер
const generateRandomSphereColors = (): string[] => {
  const visibleColorPairs = [
    ['rgba(140, 100, 200, 0.25)', 'rgba(140, 100, 200, 0)'],   // Заметный фиолетовый
    ['rgba(180, 170, 100, 0.23)', 'rgba(180, 170, 100, 0)'],  // Заметный желтый
    ['rgba(100, 160, 170, 0.25)', 'rgba(100, 160, 170, 0)'],  // Заметный бирюзовый
    ['rgba(170, 110, 150, 0.23)', 'rgba(170, 110, 150, 0)'],  // Заметный розовый
    ['rgba(110, 150, 180, 0.25)', 'rgba(110, 150, 180, 0)'],  // Заметный синий
    ['rgba(110, 170, 150, 0.23)', 'rgba(110, 170, 150, 0)'],  // Заметный зеленый
    ['rgba(180, 150, 100, 0.24)', 'rgba(180, 150, 100, 0)'],  // Заметный оранжевый
    ['rgba(170, 110, 160, 0.23)', 'rgba(170, 110, 160, 0)'],  // Заметный розовый
    ['rgba(140, 140, 160, 0.20)', 'rgba(140, 140, 160, 0)'],  // Серо-синий
    ['rgba(150, 140, 140, 0.20)', 'rgba(150, 140, 140, 0)']   // Серо-коричневый
  ];

  const randomIndex = Math.floor(Math.random() * visibleColorPairs.length);
  return visibleColorPairs[randomIndex];
};

// Sphere configuration type
interface Sphere {
  id: string;
  cx: number;
  cy: number;
  r: number;
  colors: string[];
}

// Default sphere configuration for training theme (более заметные цвета)
const defaultSpheres: Sphere[] = [
  {
    id: 'sphere-1',
    cx: screenWidth * 0.15,
    cy: screenHeight * 0.2,
    r: baseScreenSize * 1.2,
    colors: ['rgba(140, 100, 200, 0.25)', 'rgba(140, 100, 200, 0)']
  },
  {
    id: 'sphere-2',
    cx: screenWidth * 0.85,
    cy: screenHeight * 0.3,
    r: baseScreenSize * 0.7,
    colors: ['rgba(180, 170, 100, 0.23)', 'rgba(180, 170, 100, 0)']
  },
  {
    id: 'sphere-3',
    cx: screenWidth * 0.15,
    cy: screenHeight * 0.6,
    r: baseScreenSize * 1.8,
    colors: ['rgba(100, 160, 170, 0.25)', 'rgba(100, 160, 170, 0)']
  },
  {
    id: 'sphere-4',
    cx: screenWidth * 0.95,
    cy: screenHeight * 0.75,
    r: baseScreenSize * 1.0,
    colors: ['rgba(170, 110, 150, 0.23)', 'rgba(170, 110, 150, 0)']
  }
];

// Props interface
interface AnimatedGradientBackgroundProps {
  spheres?: Sphere[];
  backgroundColor?: string;
  overlayColor?: string;
  overlayOpacity?: number;
}

export const AnimatedGradientBackground: React.FC<AnimatedGradientBackgroundProps> = ({
  spheres = defaultSpheres,
  backgroundColor = '#000000',
  overlayColor = 'rgba(0, 0, 0, 0.5)',
  overlayOpacity = 0.5
}) => {
  // Генерируем рандомные цвета для каждой сферы
  const spheresWithRandomColors = useMemo(() =>
    spheres.map(sphere => ({
      ...sphere,
      colors: generateRandomSphereColors()
    })), [spheres]);

  return (
    <View style={{
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor
    }}>
      <Canvas style={{ flex: 1 }}>
        {spheresWithRandomColors.map((sphere) => (
          <Circle
            key={sphere.id}
            cx={sphere.cx}
            cy={sphere.cy}
            r={sphere.r}
          >
            <RadialGradient
              c={vec(sphere.cx, sphere.cy)}
              r={sphere.r}
              colors={sphere.colors}
            />
          </Circle>
        ))}
        {/* Semi-transparent overlay */}
        <Rect
          x={0}
          y={0}
          width={screenWidth}
          height={screenHeight}
          color={overlayColor}
          blendMode="srcOver"
        />
      </Canvas>
    </View>
  );
};

// Предустановленные темы для разных экранов (все с 4 рандомными сферами)
export const backgroundThemes = {
  training: {
    spheres: defaultSpheres,
    backgroundColor: '#000000',
    overlayColor: 'rgba(0, 0, 0, 0.5)'
  },

  profile: {
    spheres: defaultSpheres, // Используем те же 4 сферы с рандомными цветами
    backgroundColor: '#000000',
    overlayColor: 'rgba(0, 0, 0, 0.5)'
  },

  minimal: {
    spheres: defaultSpheres, // Используем те же 4 сферы с рандомными цветами
    backgroundColor: '#000000',
    overlayColor: 'rgba(0, 0, 0, 0.5)'
  }
};

// Экспорт типов для использования в других компонентах
export type { Sphere, AnimatedGradientBackgroundProps };
