import React from 'react';
import { StyleSheet, View, Dimensions } from 'react-native';
import { Canvas, RadialGradient, Rect, vec } from '@shopify/react-native-skia';

const { width, height } = Dimensions.get('window');

export const RadialGradientBackground = () => {
  return (
    <View style={styles.container}>
      <Canvas style={styles.canvas}>
        <Rect x={0} y={0} width={width} height={height}>
          <RadialGradient
            c={vec(width * 0.2, height * 0.2)} // Позиция первого градиента в левом верхнем углу
            r={Math.max(width, height) * 0.8} // Увеличиваем радиус для большей видимости
            colors={['rgba(157, 78, 221, 0.8)', 'rgba(157, 78, 221, 0.2)']} // Более яркие цвета
          />
          <RadialGradient
            c={vec(width * 0.8, height * 0.8)} // Позиция второго градиента в правом нижнем углу
            r={Math.max(width, height) * 0.8} // Увеличиваем радиус
            colors={['rgba(100, 108, 255, 0.8)', 'rgba(100, 108, 255, 0.2)']} // Более яркие цвета
          />
        </Rect>
      </Canvas>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    zIndex: -1,
  },
  canvas: {
    flex: 1,
  },
});
