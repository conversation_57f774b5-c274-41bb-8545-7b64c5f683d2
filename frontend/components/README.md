# Компоненты дизайна

Этот документ описывает переиспользуемые компоненты дизайна, вынесенные из TrainingScreen для использования в других частях приложения.

## AnimatedGradientBackground

Компонент анимированного градиентного фона с движущимися сферами.

### Использование

```tsx
import { AnimatedGradientBackground, backgroundThemes } from '../components/AnimatedGradientBackground';

// Использование предустановленной темы
<AnimatedGradientBackground {...backgroundThemes.training} />

// Кастомная конфигурация
<AnimatedGradientBackground 
  spheres={customSpheres}
  backgroundColor="#000000"
  overlayColor="rgba(0, 0, 0, 0.5)"
/>
```

### Предустановленные темы

- **training** - Основная тема для экрана тренировки (4 сферы: фиолетовая, желтая, голубая, розовая)
- **profile** - Тема для экрана профиля (3 сферы: синяя, фиолетовая, зеленая)
- **minimal** - Минимальная тема (1 сфера)

### Пропсы

- `spheres?: Sphere[]` - Массив сфер для отображения
- `backgroundColor?: string` - Цвет фона (по умолчанию: '#000000')
- `overlayColor?: string` - Цвет полупрозрачного слоя
- `overlayOpacity?: number` - Прозрачность слоя

## GlassmorphismCard

Компонент карточки с эффектом glassmorphism (стеклянный эффект с размытием).

### Использование

```tsx
import { GlassmorphismCard, glassmorphismPresets } from '../components/GlassmorphismCard';

// Использование предустановки
<GlassmorphismCard {...glassmorphismPresets.training}>
  <Text>Содержимое карточки</Text>
</GlassmorphismCard>

// Кастомная конфигурация
<GlassmorphismCard 
  blurIntensity={20}
  backgroundColor="rgba(20, 20, 35, 0.25)"
  borderColor="rgba(255, 255, 255, 0.08)"
  borderRadius={14}
  withFogEffect={true}
>
  <Text>Содержимое</Text>
</GlassmorphismCard>
```

### Предустановленные стили

- **training** - Основная карточка тренировки с голубым туманом
- **profile** - Карточка профиля с фиолетовым туманом
- **minimal** - Минимальная карточка без тумана
- **accent** - Акцентная карточка с фиолетовым туманом
- **modal** - Стиль для модальных окон
- **button** - Стиль для кнопок

### Пропсы

- `children: React.ReactNode` - Содержимое карточки
- `style?: ViewStyle` - Дополнительные стили
- `blurIntensity?: number` - Интенсивность размытия (по умолчанию: 20)
- `backgroundColor?: string` - Цвет фона
- `borderColor?: string` - Цвет границы
- `borderWidth?: number` - Толщина границы
- `borderRadius?: number` - Радиус скругления
- `width?: number | string` - Ширина карточки
- `height?: number` - Высота карточки
- `withFogEffect?: boolean` - Включить эффект тумана
- `fogColor?: string[]` - Цвета для эффекта тумана
- `fogPosition?: { x: number; y: number }` - Позиция тумана
- `fogRadius?: number` - Радиус тумана

## Система тем

### Цвета

Обновленная система цветов включает:

```tsx
import { colors } from '../theme/colors';

// Градиентные сферы
colors.gradientSpheres.purple.primary // 'rgba(170, 70, 255, 0.3)'
colors.gradientSpheres.yellow.primary // 'rgba(255, 220, 50, 0.3)'

// Glassmorphism цвета
colors.glassmorphism.background.primary // 'rgba(20, 20, 35, 0.25)'
colors.glassmorphism.border.primary // 'rgba(255, 255, 255, 0.08)'
colors.glassmorphism.fog.blue // Массив цветов для голубого тумана
```

### Стили Glassmorphism

```tsx
import { glassmorphismStyles, createGlassmorphismStyle } from '../theme/glassmorphism';

// Готовые стили
<View style={glassmorphismStyles.container}>

// Создание кастомного стиля
const customStyle = createGlassmorphismStyle('accent', {
  borderRadius: 20,
  padding: 24
});
```

## Примеры использования

### Экран с анимированным фоном

```tsx
function MyScreen() {
  return (
    <View style={{ flex: 1 }}>
      <AnimatedGradientBackground {...backgroundThemes.profile} />
      <SafeAreaView style={{ flex: 1 }}>
        {/* Содержимое экрана */}
      </SafeAreaView>
    </View>
  );
}
```

### Карточка с glassmorphism эффектом

```tsx
function InfoCard({ title, children }) {
  return (
    <GlassmorphismCard {...glassmorphismPresets.profile}>
      <Text style={styles.title}>{title}</Text>
      {children}
    </GlassmorphismCard>
  );
}
```

### Кнопка с glassmorphism эффектом

```tsx
function GlassButton({ onPress, children }) {
  return (
    <TouchableOpacity onPress={onPress}>
      <GlassmorphismCard {...glassmorphismPresets.button}>
        {children}
      </GlassmorphismCard>
    </TouchableOpacity>
  );
}
```

## Рефакторинг TrainingScreen

В результате рефакторинга TrainingScreen:

1. **Уменьшен размер файла** с 1669 до ~1600 строк
2. **Вынесен анимированный фон** в отдельный компонент
3. **Вынесен glassmorphism эффект** в переиспользуемый компонент
4. **Создана система тем** для цветов и стилей
5. **Улучшена переиспользуемость** дизайна

### Что было вынесено

- Конфигурация сфер градиентного фона
- Canvas код для отрисовки сфер
- BackdropBlur и эффекты размытия
- Цвета и стили glassmorphism
- Система отступов и радиусов

### Что осталось в TrainingScreen

- Логика тренировки
- Состояния и анимации карточек
- Обработка пользовательского ввода
- Специфичные стили для элементов тренировки
