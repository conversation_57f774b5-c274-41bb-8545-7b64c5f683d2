import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView, Dimensions } from 'react-native';
import { GlassmorphismCard } from './GlassmorphismCard';
import { colors } from '../theme/colors';
import { ALL_LANGUAGES, LanguageOption } from '../constants/languages';

const { width: screenWidth } = Dimensions.get('window');



interface AddLanguageModalProps {
  visible: boolean;
  onClose: () => void;
  onSelectLanguage: (languageCode: string) => void;
  excludeLanguages: string[]; // Языки, которые уже изучаются
}



export const AddLanguageModal: React.FC<AddLanguageModalProps> = ({
  visible,
  onClose,
  onSelectLanguage,
  excludeLanguages
}) => {
  if (!visible) return null;

  // Фильтруем языки, исключая уже изучаемые
  const availableLanguages = ALL_LANGUAGES.filter(
    lang => !excludeLanguages.includes(lang.code)
  );

  const handleLanguageSelect = (languageCode: string) => {
    console.log('AddLanguageModal: Language selected:', languageCode);
    onSelectLanguage(languageCode);
    onClose();
  };

  return (
    <View style={styles.overlay}>
      <TouchableOpacity 
        style={styles.backdrop} 
        activeOpacity={1} 
        onPress={onClose}
      />
      
      <View style={styles.container}>
        <GlassmorphismCard
          backgroundColor="rgba(20, 20, 35, 0.95)"
          borderColor="rgba(255, 255, 255, 0.15)"
          borderWidth={1}
          borderRadius={20}
          blurIntensity={25}
          withFogEffect={true}
          fogColor={[
            'rgba(120, 200, 255, 0.3)',
            'rgba(120, 200, 255, 0.2)',
            'rgba(120, 200, 255, 0.1)',
            'rgba(120, 200, 255, 0.05)',
            'rgba(120, 200, 255, 0)'
          ]}
          style={styles.modalCard}
        >
          <Text style={styles.title}>Добавить новый язык</Text>
          
          <ScrollView 
            style={styles.scrollView}
            showsVerticalScrollIndicator={false}
          >
            {availableLanguages.length > 0 ? (
              availableLanguages.map((language) => (
                <TouchableOpacity
                  key={language.code}
                  onPress={() => handleLanguageSelect(language.code)}
                  style={styles.languageItem}
                  activeOpacity={0.7}
                >
                  <View style={styles.languageContent}>
                    <Text style={styles.languageFlag}>{language.flag}</Text>
                    <Text style={styles.languageName}>{language.name}</Text>
                  </View>
                  <View style={styles.addIcon}>
                    <Text style={styles.addIconText}>+</Text>
                  </View>
                </TouchableOpacity>
              ))
            ) : (
              <View style={styles.emptyState}>
                <Text style={styles.emptyText}>
                  Все доступные языки уже добавлены! 🎉
                </Text>
              </View>
            )}
          </ScrollView>
          
          <TouchableOpacity 
            style={styles.cancelButton}
            onPress={onClose}
            activeOpacity={0.8}
          >
            <Text style={styles.cancelText}>Отмена</Text>
          </TouchableOpacity>
        </GlassmorphismCard>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  container: {
    width: screenWidth - 40,
    maxHeight: '70%',
    zIndex: 1001,
  },
  modalCard: {
    width: '100%',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.text.primary,
    textAlign: 'center',
    marginBottom: 20,
  },
  scrollView: {
    maxHeight: 350,
  },
  languageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 14,
    paddingHorizontal: 16,
    marginVertical: 2,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.08)',
  },
  languageContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  languageFlag: {
    fontSize: 24,
    marginRight: 12,
  },
  languageName: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text.secondary,
  },
  addIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(120, 200, 255, 0.2)',
    borderWidth: 1,
    borderColor: 'rgba(120, 200, 255, 0.4)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  addIconText: {
    color: colors.text.primary,
    fontSize: 16,
    fontWeight: 'bold',
  },
  emptyState: {
    paddingVertical: 40,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  cancelButton: {
    marginTop: 20,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    alignSelf: 'center',
  },
  cancelText: {
    color: colors.text.secondary,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
});
