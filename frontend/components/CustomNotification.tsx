import React, { useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Animated, Dimensions } from 'react-native';
import { GlassmorphismCard } from './GlassmorphismCard';
import { colors } from '../theme/colors';

const { width: screenWidth } = Dimensions.get('window');

interface CustomNotificationProps {
  message: string;
  visible: boolean;
  onHide: () => void;
  duration?: number;
  type?: 'success' | 'error' | 'info';
}

export const CustomNotification: React.FC<CustomNotificationProps> = ({
  message,
  visible,
  onHide,
  duration = 1000, // 1 секунда по умолчанию
  type = 'success',
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const progressAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (visible) {
      // Показываем уведомление
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start();

      // Автоматически скрываем через заданное время
      const timer = setTimeout(() => {
        hideNotification();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [visible]);

  const hideNotification = () => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 0.8,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onHide();
    });
  };

  if (!visible) return null;

  const getIconForType = () => {
    switch (type) {
      case 'success':
        return '✨'; // Более красивая иконка
      case 'error':
        return '⚠️';
      case 'info':
        return '💡';
      default:
        return '✨';
    }
  };

  const getColorsForType = () => {
    switch (type) {
      case 'success':
        return {
          background: 'rgba(20, 20, 35, 0.95)',
          border: 'rgba(120, 200, 255, 0.3)',
          icon: '#87CEFD',
          fog: [
            'rgba(120, 200, 255, 0.3)',
            'rgba(120, 200, 255, 0.2)',
            'rgba(120, 200, 255, 0.1)',
            'rgba(120, 200, 255, 0.05)',
            'rgba(120, 200, 255, 0)'
          ]
        };
      case 'error':
        return {
          background: 'rgba(20, 20, 35, 0.95)',
          border: 'rgba(255, 120, 120, 0.3)',
          icon: '#FF7878',
          fog: [
            'rgba(255, 120, 120, 0.3)',
            'rgba(255, 120, 120, 0.2)',
            'rgba(255, 120, 120, 0.1)',
            'rgba(255, 120, 120, 0.05)',
            'rgba(255, 120, 120, 0)'
          ]
        };
      default:
        return {
          background: 'rgba(20, 20, 35, 0.95)',
          border: 'rgba(120, 200, 255, 0.3)',
          icon: '#87CEFD',
          fog: [
            'rgba(120, 200, 255, 0.3)',
            'rgba(120, 200, 255, 0.2)',
            'rgba(120, 200, 255, 0.1)',
            'rgba(120, 200, 255, 0.05)',
            'rgba(120, 200, 255, 0)'
          ]
        };
    }
  };

  const typeColors = getColorsForType();

  return (
    <View style={styles.overlay}>
      <Animated.View
        style={[
          styles.container,
          {
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        <GlassmorphismCard
          backgroundColor={typeColors.background}
          borderColor={typeColors.border}
          borderWidth={1}
          borderRadius={20}
          blurIntensity={25}
          withFogEffect={true}
          fogColor={typeColors.fog}
          style={styles.notificationCard}
        >
          <View style={styles.content}>
            <Text style={styles.icon}>
              {getIconForType()}
            </Text>
            <Text style={styles.message}>{message}</Text>
          </View>
        </GlassmorphismCard>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 9999,
    backgroundColor: 'rgba(0, 0, 0, 0.4)', // Чуть больше затемнения
  },
  container: {
    width: screenWidth - 80,
    maxWidth: 280,
  },
  notificationCard: {
    width: '100%',
  },
  content: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 24,
    paddingHorizontal: 24,
  },
  icon: {
    fontSize: 32,
    marginBottom: 12,
  },
  message: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text.primary,
    textAlign: 'center',
    lineHeight: 22,
  },
});
