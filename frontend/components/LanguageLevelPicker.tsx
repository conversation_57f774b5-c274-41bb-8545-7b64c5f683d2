import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView, Dimensions } from 'react-native';
import { GlassmorphismCard } from './GlassmorphismCard';
import { colors } from '../theme/colors';

const { width: screenWidth } = Dimensions.get('window');

interface LanguageLevelOption {
  label: string;
  value: string;
  description: string;
}

interface LanguageLevelPickerProps {
  selectedValue: string;
  onSelect: (value: string) => void;
  onClose: () => void;
  title: string;
}

const LANGUAGE_LEVEL_OPTIONS: LanguageLevelOption[] = [
  { 
    label: 'A0', 
    value: 'A0', 
    description: 'Топ-100 самых базовых слов' 
  },
  { 
    label: 'A1', 
    value: 'A1', 
    description: 'Начальный уровень' 
  },
  { 
    label: 'A2', 
    value: 'A2', 
    description: 'Элементарный уровень' 
  },
  { 
    label: 'B1', 
    value: 'B1', 
    description: 'Средний уровень' 
  },
  { 
    label: 'B2', 
    value: 'B2', 
    description: 'Выше среднего' 
  },
  { 
    label: 'C1', 
    value: 'C1', 
    description: 'Продвинутый уровень' 
  },
  { 
    label: 'C2', 
    value: 'C2', 
    description: 'Профессиональный уровень' 
  },
];

export const LanguageLevelPicker: React.FC<LanguageLevelPickerProps> = ({
  selectedValue,
  onSelect,
  onClose,
  title
}) => {
  const handleSelect = (value: string) => {
    onSelect(value);
    onClose();
  };

  return (
    <View style={styles.overlay}>
      <TouchableOpacity 
        style={styles.backdrop} 
        activeOpacity={1} 
        onPress={onClose}
      />
      
      <View style={styles.container}>
        <GlassmorphismCard
          backgroundColor="rgba(20, 20, 35, 0.95)"
          borderColor="rgba(255, 255, 255, 0.15)"
          borderWidth={1}
          borderRadius={20}
          blurIntensity={25}
          withFogEffect={true}
          fogColor={[
            'rgba(120, 200, 255, 0.3)',
            'rgba(120, 200, 255, 0.2)',
            'rgba(120, 200, 255, 0.1)',
            'rgba(120, 200, 255, 0.05)',
            'rgba(120, 200, 255, 0)'
          ]}
          style={styles.pickerCard}
        >
          <Text style={styles.title}>{title}</Text>
          
          <ScrollView 
            style={styles.scrollView}
            showsVerticalScrollIndicator={false}
          >
            {LANGUAGE_LEVEL_OPTIONS.map((option) => (
              <TouchableOpacity
                key={option.value}
                onPress={() => handleSelect(option.value)}
                style={[
                  styles.levelItem,
                  selectedValue === option.value && styles.selectedItem
                ]}
                activeOpacity={0.7}
              >
                <View style={styles.levelContent}>
                  <View style={styles.levelHeader}>
                    <Text style={[
                      styles.levelLabel,
                      selectedValue === option.value && styles.selectedLabel
                    ]}>
                      {option.label}
                    </Text>
                    {selectedValue === option.value && (
                      <View style={styles.checkmark}>
                        <Text style={styles.checkmarkText}>✓</Text>
                      </View>
                    )}
                  </View>
                  <Text style={styles.levelDescription}>
                    {option.description}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </ScrollView>
          
          <TouchableOpacity 
            style={styles.cancelButton}
            onPress={onClose}
            activeOpacity={0.8}
          >
            <Text style={styles.cancelText}>Отмена</Text>
          </TouchableOpacity>
        </GlassmorphismCard>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  container: {
    width: screenWidth - 40,
    maxHeight: '70%',
    zIndex: 1001,
  },
  pickerCard: {
    width: '100%',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.text.primary,
    textAlign: 'center',
    marginBottom: 20,
  },
  scrollView: {
    maxHeight: 350,
  },
  levelItem: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    marginVertical: 2,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.08)',
  },
  selectedItem: {
    backgroundColor: 'rgba(120, 200, 255, 0.15)',
    borderColor: 'rgba(120, 200, 255, 0.3)',
  },
  levelContent: {
    flex: 1,
  },
  levelHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  levelLabel: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.secondary,
  },
  selectedLabel: {
    color: colors.text.primary,
    fontWeight: 'bold',
  },
  levelDescription: {
    fontSize: 14,
    color: colors.text.tertiary,
    lineHeight: 18,
  },
  checkmark: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(120, 200, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkmarkText: {
    color: colors.text.primary,
    fontSize: 14,
    fontWeight: 'bold',
  },
  cancelButton: {
    marginTop: 20,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    alignSelf: 'center',
  },
  cancelText: {
    color: colors.text.secondary,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
});
