import React from 'react';
import { View, StyleSheet, ViewStyle, TextStyle } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { SVGGradientText } from './SVGGradientText';

interface SectionHeaderProps {
  title: string;
  style?: ViewStyle;
  textStyle?: TextStyle;
  colors?: string[];
  lineColors?: string[];
}

export const SectionHeader: React.FC<SectionHeaderProps> = ({
  title,
  style,
  textStyle,
  colors = [
    'rgba(135, 206, 250, 1)', // Нежно-голубой небесный
    'rgba(147, 112, 219, 0.9)' // Легкий пастельный фиолетовый
  ],
  lineColors = [
    'rgba(135, 206, 250, 0.6)', // Нежно-голубой для линии
    'rgba(147, 112, 219, 0.4)', // Легкий фиолетовый для линии
    'rgba(147, 112, 219, 0.1)', // Затухание
    'transparent'
  ]
}) => {
  return (
    <View style={[styles.container, style]}>
      <View style={styles.textContainer}>
        <SVGGradientText
          fontSize={18}
          fontWeight="600"
          style={[styles.title, textStyle]}
          colors={colors}
          width={Math.max(250, title.length * 12)}
          height={25}
        >
          {title}
        </SVGGradientText>
      </View>
      
      {/* Градиентная линия под заголовком */}
      <LinearGradient
        colors={lineColors}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.gradientLine}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
    marginTop: 5, // Уменьшили отступ сверху от края карточки
  },
  textContainer: {
    alignItems: 'flex-start', // Выравнивание по левому краю
    marginBottom: 15, // Увеличили расстояние от текста до линии
    marginLeft: 0, // Убираем отступ слева
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  gradientLine: {
    height: 2,
    borderRadius: 1,
    marginHorizontal: 0, // Линия от края до края карточки
  },
});
