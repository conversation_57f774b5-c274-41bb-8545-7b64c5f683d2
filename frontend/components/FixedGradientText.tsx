import React from 'react';
import { Text, StyleSheet, TextStyle, View, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import MaskedView from '@react-native-masked-view/masked-view';

interface FixedGradientTextProps {
  children: React.ReactNode;
  colors?: string[];
  style?: TextStyle;
  fontSize?: number;
  fontWeight?: 'normal' | 'bold' | '600';
}

export const FixedGradientText: React.FC<FixedGradientTextProps> = ({
  children,
  colors = [
    '#87CEFD', // Нежно-голубой небесный
    '#9370DB'  // Легкий пастельный фиолетовый
  ],
  style,
  fontSize = 18,
  fontWeight = '600',
}) => {
  const textStyle = {
    fontSize,
    fontWeight,
    backgroundColor: 'transparent',
    ...style,
  };

  // Вычисляем примерные размеры текста
  const textLength = String(children).length;
  const estimatedWidth = textLength * fontSize * 0.6;
  const estimatedHeight = fontSize * 1.2;

  return (
    <View style={[styles.wrapper, { width: estimatedWidth, height: estimatedHeight }]}>
      <MaskedView
        style={[styles.container, { width: estimatedWidth, height: estimatedHeight }]}
        maskElement={
          <View style={[styles.maskContainer, { width: estimatedWidth, height: estimatedHeight }]}>
            <Text style={[styles.maskText, textStyle]}>
              {children}
            </Text>
          </View>
        }
      >
        <LinearGradient
          colors={colors}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={[styles.gradient, { width: estimatedWidth, height: estimatedHeight }]}
        />
      </MaskedView>
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    alignSelf: 'center',
  },
  container: {
    overflow: 'hidden',
  },
  maskContainer: {
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
  },
  maskText: {
    backgroundColor: 'transparent',
    color: 'black', // КРИТИЧНО: должен быть черным для маски
    textAlign: 'center',
  },
  gradient: {
    position: 'absolute',
    top: 0,
    left: 0,
  },
});
