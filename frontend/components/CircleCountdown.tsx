import React, { useEffect, useRef, useState } from "react";
import {
  Canvas,
  Circle,
} from "@shopify/react-native-skia";
import { View, StyleSheet, Animated } from "react-native";
import { colors } from "../theme/colors";
import CircleCountdownFallback from "./CircleCountdownFallback";

const SIZE = 40; // Размер соответствует высоте кнопок навигации
const RADIUS = SIZE / 2 - 3;
const STROKE = 2; // Тонкая обводка

interface CircleCountdownProps {
  timeLeft: number; // Оставшееся время в миллисекундах
  totalDuration: number; // Общая длительность в миллисекундах
  isActive: boolean; // Контролирует, активен ли таймер
}

export default function CircleCountdown({
  timeLeft,
  totalDuration,
  isActive
}: CircleCountdownProps) {
  // Состояние для отслеживания ошибок Skia
  const [hasSkiaError, setHasSkiaError] = useState(false);

  // Вычисляем прогресс на основе оставшегося времени
  const progressValue = isActive && totalDuration > 0 ? timeLeft / totalDuration : 0;

  // Пульсирующий эффект для React Native анимации
  const pulseAnim = useRef(new Animated.Value(1)).current;

  // Анимация прогресса
  const progressAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    // Плавно анимируем прогресс
    Animated.timing(progressAnim, {
      toValue: progressValue,
      duration: 100, // Быстрая анимация для синхронизации
      useNativeDriver: false, // Нужно для интерполяции углов
    }).start();
  }, [progressValue]);

  useEffect(() => {
    if (isActive) {
      // Запускаем пульсирующую анимацию (более деликатную)
      const pulseAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.05, // Уменьшили пульсацию
            duration: 1000, // Увеличили длительность для плавности
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      );
      pulseAnimation.start();

      return () => pulseAnimation.stop();
    } else {
      pulseAnim.setValue(1);
    }
  }, [isActive]);

  // Вычисляем угол для анимации
  // progressValue: 1 (полное время) → 0 (время закончилось)
  // angle: 2π (полный круг) → 0 (нет круга)
  const angle = Math.PI * 2 * progressValue;



  if (!isActive) {
    return null; // Не показываем таймер если он неактивен
  }

  // Параметры start/end в Circle не работают в этой версии Skia
  // Используем fallback версию, которая работает надежно
  return (
    <CircleCountdownFallback
      timeLeft={timeLeft}
      totalDuration={totalDuration}
      isActive={isActive}
    />
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    width: SIZE,
    height: SIZE,
  },
});
