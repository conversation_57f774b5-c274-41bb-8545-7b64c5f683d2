import React from 'react';
import { View, StyleSheet, ViewStyle, StatusBar, Platform } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

interface DarkSafeAreaViewProps {
  children: React.ReactNode;
  style?: ViewStyle;
  backgroundColor?: string;
  statusBarStyle?: 'light-content' | 'dark-content';
}

/**
 * Компонент для создания темного фона на всем экране, включая системные области
 * Решает проблему белых полосок в safe area на некоторых устройствах
 */
export const DarkSafeAreaView: React.FC<DarkSafeAreaViewProps> = ({
  children,
  style,
  backgroundColor = '#1a1a2e',
  statusBarStyle = 'light-content',
}) => {
  return (
    <View style={[styles.container, { backgroundColor }]}>
      <StatusBar 
        barStyle={statusBarStyle} 
        backgroundColor={backgroundColor} 
        translucent={true}
      />
      <SafeAreaView style={[styles.safeArea, style]} edges={['top', 'bottom', 'left', 'right']}>
        {children}
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
});
