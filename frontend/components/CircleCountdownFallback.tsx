import React, { useEffect, useRef } from "react";
import { View, StyleSheet, Animated } from "react-native";
import Svg, { Circle } from 'react-native-svg';
import { colors } from "../theme/colors";

const SIZE = 40; // Размер соответствует высоте кнопок навигации

interface CircleCountdownProps {
  timeLeft: number; // Оставшееся время в миллисекундах
  totalDuration: number; // Общая длительность в миллисекундах
  isActive: boolean; // Контролирует, активен ли таймер
}

export default function CircleCountdownFallback({
  timeLeft,
  totalDuration,
  isActive
}: CircleCountdownProps) {
  // Вычисляем прогресс на основе оставшегося времени
  const progressValue = isActive && totalDuration > 0 ? timeLeft / totalDuration : 0;

  // Пульсирующий эффект для React Native анимации
  const pulseAnim = useRef(new Animated.Value(1)).current;
  
  // Анимация прогресса
  const progressAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    // Плавно анимируем прогресс
    Animated.timing(progressAnim, {
      toValue: progressValue,
      duration: 100, // Быстрая анимация для синхронизации
      useNativeDriver: false, // Нужно для интерполяции углов
    }).start();
  }, [progressValue]);

  useEffect(() => {
    if (isActive) {
      // Запускаем пульсирующую анимацию (более деликатную)
      const pulseAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.05, // Уменьшили пульсацию
            duration: 1000, // Увеличили длительность для плавности
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      );
      pulseAnimation.start();

      return () => pulseAnimation.stop();
    } else {
      pulseAnim.setValue(1);
    }
  }, [isActive]);

  if (!isActive) {
    return null; // Не показываем таймер если он неактивен
  }

  // SVG круговой прогресс - голубой "тает" по часовой стрелке от 12 часов
  const radius = (SIZE - 8) / 2;
  const circumference = 2 * Math.PI * radius;



  return (
    <Animated.View style={[styles.container, { transform: [{ scale: pulseAnim }] }]}>
      <Svg width={SIZE} height={SIZE} style={styles.svg}>
        {/* Серый фоновый круг - всегда полный */}
        <Circle
          cx={SIZE / 2}
          cy={SIZE / 2}
          r={radius}
          stroke="rgba(255, 255, 255, 0.15)"
          strokeWidth="2"
          fill="transparent"
        />

        {/* Голубой прогресс - "тает" по часовой стрелке от 12 часов */}
        <Circle
          cx={SIZE / 2}
          cy={SIZE / 2}
          r={radius}
          stroke="rgba(127, 180, 255, 0.6)"
          strokeWidth="2"
          fill="transparent"
          strokeDasharray={`${circumference * progressValue} ${circumference}`}
          strokeDashoffset={0}
          strokeLinecap="round"
          transform={`rotate(-90 ${SIZE / 2} ${SIZE / 2})`}
        />
      </Svg>

      {/* Центральная точка */}
      <View style={styles.centerDot} />
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    width: SIZE,
    height: SIZE,
    position: 'relative',
  },
  svg: {
    position: 'absolute',
  },
  centerDot: {
    width: 3,
    height: 3,
    borderRadius: 1.5,
    backgroundColor: 'rgba(127, 180, 255, 0.7)', // Соответствует новому цвету прогресса
    zIndex: 10,
  },
});
