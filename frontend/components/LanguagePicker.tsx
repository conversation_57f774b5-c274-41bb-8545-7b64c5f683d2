import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView, Dimensions } from 'react-native';
import { GlassmorphismCard } from './GlassmorphismCard';
import { colors } from '../theme/colors';

const { width: screenWidth } = Dimensions.get('window');

interface LanguageOption {
  label: string;
  value: string;
  flag: string;
}

interface LanguagePickerProps {
  options: LanguageOption[];
  selectedValue: string;
  onSelect: (value: string) => void;
  onClose: () => void;
  title: string;
}

export const LanguagePicker: React.FC<LanguagePickerProps> = ({
  options,
  selectedValue,
  onSelect,
  onClose,
  title
}) => {
  const handleSelect = (value: string) => {
    onSelect(value);
    onClose();
  };

  return (
    <View style={styles.overlay}>
      <TouchableOpacity 
        style={styles.backdrop} 
        activeOpacity={1} 
        onPress={onClose}
      />
      
      <View style={styles.container}>
        <GlassmorphismCard
          backgroundColor="rgba(20, 20, 35, 0.95)"
          borderColor="rgba(255, 255, 255, 0.15)"
          borderWidth={1}
          borderRadius={20}
          blurIntensity={25}
          withFogEffect={true}
          fogColor={[
            'rgba(120, 200, 255, 0.3)',
            'rgba(120, 200, 255, 0.2)',
            'rgba(120, 200, 255, 0.1)',
            'rgba(120, 200, 255, 0.05)',
            'rgba(120, 200, 255, 0)'
          ]}
          style={styles.pickerCard}
        >
          <Text style={styles.title}>{title}</Text>
          
          <ScrollView 
            style={styles.scrollView}
            showsVerticalScrollIndicator={false}
          >
            {options.map((option) => (
              <TouchableOpacity
                key={option.value}
                onPress={() => handleSelect(option.value)}
                style={[
                  styles.languageItem,
                  selectedValue === option.value && styles.selectedItem
                ]}
                activeOpacity={0.7}
              >
                <View style={styles.languageContent}>
                  <Text style={styles.flag}>{option.flag}</Text>
                  <Text style={[
                    styles.languageLabel,
                    selectedValue === option.value && styles.selectedLabel
                  ]}>
                    {option.label}
                  </Text>
                </View>
                
                {selectedValue === option.value && (
                  <View style={styles.checkmark}>
                    <Text style={styles.checkmarkText}>✓</Text>
                  </View>
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
          
          <TouchableOpacity 
            style={styles.cancelButton}
            onPress={onClose}
            activeOpacity={0.8}
          >
            <Text style={styles.cancelText}>Отмена</Text>
          </TouchableOpacity>
        </GlassmorphismCard>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  container: {
    width: screenWidth - 40,
    maxHeight: '70%',
    zIndex: 1001,
  },
  pickerCard: {
    width: '100%',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.text.primary,
    textAlign: 'center',
    marginBottom: 20,
  },
  scrollView: {
    maxHeight: 300,
  },
  languageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 20,
    marginVertical: 2,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.08)',
  },
  selectedItem: {
    backgroundColor: 'rgba(120, 200, 255, 0.15)',
    borderColor: 'rgba(120, 200, 255, 0.3)',
  },
  languageContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  flag: {
    fontSize: 24,
    marginRight: 12,
  },
  languageLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text.secondary,
  },
  selectedLabel: {
    color: colors.text.primary,
    fontWeight: '600',
  },
  checkmark: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(120, 200, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkmarkText: {
    color: colors.text.primary,
    fontSize: 14,
    fontWeight: 'bold',
  },
  cancelButton: {
    marginTop: 20,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    alignSelf: 'center',
  },
  cancelText: {
    color: colors.text.secondary,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
});
