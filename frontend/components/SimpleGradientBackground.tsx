import React from 'react';
import { Text, StyleSheet, TextStyle, View } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

interface SimpleGradientBackgroundProps {
  children: React.ReactNode;
  colors?: string[];
  style?: TextStyle;
  fontSize?: number;
  fontWeight?: 'normal' | 'bold' | '600';
}

export const SimpleGradientBackground: React.FC<SimpleGradientBackgroundProps> = ({
  children,
  colors = [
    '#87CEFD', // Нежно-голубой небесный
    '#9370DB'  // Легкий пастельный фиолетовый
  ],
  style,
  fontSize = 18,
  fontWeight = '600',
}) => {
  const textStyle = {
    fontSize,
    fontWeight,
    ...style,
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={colors}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.gradient}
      >
        <Text style={[styles.text, textStyle]}>
          {children}
        </Text>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignSelf: 'center',
  },
  gradient: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 12,
  },
  text: {
    color: 'white',
    backgroundColor: 'transparent',
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
});
