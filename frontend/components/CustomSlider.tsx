import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Dimensions } from 'react-native';
import { PanGestureHandler } from 'react-native-gesture-handler';
import { GlassmorphismCard } from './GlassmorphismCard';
import { colors } from '../theme/colors';

// Импортируем хуки для переводов
import { useAppTranslation } from '../src/i18n/hooks';
import Animated, {
  useAnimatedGestureHandler,
  useAnimatedStyle,
  useSharedValue,
  runOnJS,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';

const { width: screenWidth } = Dimensions.get('window');

interface CustomSliderProps {
  value: number;
  onValueChange: (value: number) => void;
  onClose: () => void;
  title: string;
  minValue?: number;
  maxValue?: number;
}

export const CustomSlider: React.FC<CustomSliderProps> = ({
  value,
  onValueChange,
  onClose,
  title,
  minValue = 5,
  maxValue = 100,
}) => {
  const [currentValue, setCurrentValue] = useState(value);
  const { t } = useAppTranslation('common');
  const sliderWidth = screenWidth - 120; // Ширина слайдера
  const thumbSize = 24;
  
  // Вычисляем начальную позицию ползунка
  const initialPosition = ((value - minValue) / (maxValue - minValue)) * sliderWidth;
  const translateX = useSharedValue(initialPosition);

  const gestureHandler = useAnimatedGestureHandler({
    onStart: (_, context) => {
      context.startX = translateX.value;
    },
    onActive: (event, context) => {
      const newX = Math.max(0, Math.min(sliderWidth, context.startX + event.translationX));
      translateX.value = newX;
      
      // Вычисляем новое значение
      const newValue = Math.round(
        minValue + (newX / sliderWidth) * (maxValue - minValue)
      );
      runOnJS(setCurrentValue)(newValue);
    },
  });

  const thumbStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: translateX.value }],
    };
  });

  const progressStyle = useAnimatedStyle(() => {
    return {
      width: translateX.value + thumbSize / 2,
    };
  });

  const handleSave = () => {
    onValueChange(currentValue);
    onClose();
  };

  return (
    <View style={styles.overlay}>
      <TouchableOpacity 
        style={styles.backdrop} 
        activeOpacity={1} 
        onPress={onClose}
      />
      
      <View style={styles.container}>
        <GlassmorphismCard
          backgroundColor="rgba(20, 20, 35, 0.95)"
          borderColor="rgba(255, 255, 255, 0.15)"
          borderWidth={1}
          borderRadius={20}
          blurIntensity={25}
          withFogEffect={true}
          fogColor={[
            'rgba(120, 200, 255, 0.3)',
            'rgba(120, 200, 255, 0.2)',
            'rgba(120, 200, 255, 0.1)',
            'rgba(120, 200, 255, 0.05)',
            'rgba(120, 200, 255, 0)'
          ]}
          style={styles.sliderCard}
        >
          <Text style={styles.title}>{title}</Text>
          
          <View style={styles.valueDisplay}>
            <Text style={styles.currentValue}>{currentValue}</Text>
            <Text style={styles.valueUnit}>{t('cardsPerDay')}</Text>
          </View>
          
          <View style={styles.sliderContainer}>
            <View style={styles.sliderTrack}>
              {/* Прогресс-бар */}
              <Animated.View style={[styles.sliderProgress, progressStyle]} />
              
              {/* Ползунок */}
              <PanGestureHandler onGestureEvent={gestureHandler}>
                <Animated.View style={[styles.sliderThumb, thumbStyle]}>
                  <View style={styles.thumbInner} />
                </Animated.View>
              </PanGestureHandler>
            </View>
            
            {/* Подписи минимум и максимум */}
            <View style={styles.sliderLabels}>
              <Text style={styles.sliderLabel}>{minValue}</Text>
              <Text style={styles.sliderLabel}>{maxValue}</Text>
            </View>
          </View>
          
          <View style={styles.buttonContainer}>
            <TouchableOpacity 
              style={styles.cancelButton}
              onPress={onClose}
              activeOpacity={0.8}
            >
              <Text style={styles.cancelText}>{t('buttons.cancel')}</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.saveButton}
              onPress={handleSave}
              activeOpacity={0.8}
            >
              <Text style={styles.saveText}>{t('buttons.save')}</Text>
            </TouchableOpacity>
          </View>
        </GlassmorphismCard>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  container: {
    width: screenWidth - 40,
    zIndex: 1001,
  },
  sliderCard: {
    width: '100%',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.text.primary,
    textAlign: 'center',
    marginBottom: 30,
  },
  valueDisplay: {
    alignItems: 'center',
    marginBottom: 40,
  },
  currentValue: {
    fontSize: 48,
    fontWeight: 'bold',
    color: colors.text.primary,
    textShadowColor: 'rgba(120, 200, 255, 0.5)',
    textShadowOffset: { width: 0, height: 0 },
    textShadowRadius: 10,
  },
  valueUnit: {
    fontSize: 16,
    color: colors.text.secondary,
    marginTop: 5,
  },
  sliderContainer: {
    marginBottom: 40,
  },
  sliderTrack: {
    height: 6,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 3,
    position: 'relative',
    marginHorizontal: 12,
  },
  sliderProgress: {
    height: 6,
    backgroundColor: 'rgba(120, 200, 255, 0.8)',
    borderRadius: 3,
    position: 'absolute',
    top: 0,
    left: 0,
  },
  sliderThumb: {
    position: 'absolute',
    top: -9,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(120, 200, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: 'rgba(120, 200, 255, 0.8)',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 1,
    shadowRadius: 8,
    elevation: 8,
  },
  thumbInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: colors.text.primary,
  },
  sliderLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
    marginHorizontal: 12,
  },
  sliderLabel: {
    fontSize: 14,
    color: colors.text.tertiary,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 15,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  saveButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(120, 200, 255, 0.8)',
    borderWidth: 1,
    borderColor: 'rgba(120, 200, 255, 0.9)',
  },
  cancelText: {
    color: colors.text.secondary,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  saveText: {
    color: colors.text.primary,
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
});
