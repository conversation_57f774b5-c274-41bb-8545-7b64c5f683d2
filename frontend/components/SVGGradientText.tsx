import React from 'react';
import { View, StyleSheet, TextStyle } from 'react-native';
import Svg, { Text as SvgText, Defs, LinearGradient, Stop } from 'react-native-svg';

interface SVGGradientTextProps {
  children: string;
  colors?: string[];
  style?: TextStyle;
  fontSize?: number;
  fontWeight?: 'normal' | 'bold' | '600';
  width?: number;
  height?: number;
  textAlign?: 'start' | 'middle' | 'end';
}

export const SVGGradientText: React.FC<SVGGradientTextProps> = ({
  children,
  colors = [
    '#87CEFD', // Нежно-голубой небесный
    '#9370DB'  // Легкий пастельный фиолетовый
  ],
  style,
  fontSize = 18,
  fontWeight = '600',
  width,
  height,
  textAlign = 'start',
}) => {
  // Вычисляем размеры
  const textWidth = width || Math.max(200, children.length * fontSize * 0.6);
  const textHeight = height || fontSize * 1.5;

  // Определяем вес шрифта для SVG
  const svgFontWeight = fontWeight === 'bold' ? 'bold' : fontWeight === '600' ? '600' : 'normal';

  // Определяем позицию X в зависимости от выравнивания
  const getXPosition = () => {
    switch (textAlign) {
      case 'middle': return textWidth / 2;
      case 'end': return textWidth;
      default: return 0;
    }
  };

  return (
    <View style={[styles.container, style, { width: textWidth, height: textHeight }]}>
      <Svg width={textWidth} height={textHeight} viewBox={`0 0 ${textWidth} ${textHeight}`}>
        <Defs>
          <LinearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <Stop offset="0%" stopColor={colors[0]} />
            <Stop offset="100%" stopColor={colors[1]} />
          </LinearGradient>
        </Defs>
        <SvgText
          x={getXPosition()}
          y={textHeight / 2 + fontSize / 3}
          fontSize={fontSize}
          fontWeight={svgFontWeight}
          textAnchor={textAlign}
          fill="url(#textGradient)"
        >
          {children}
        </SvgText>
      </Svg>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignSelf: 'flex-start', // Выравнивание по левому краю
  },
});
