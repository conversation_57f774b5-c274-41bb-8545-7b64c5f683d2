import React from 'react';
import { TouchableOpacity, Text, StyleSheet } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../App';

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

export const ProfileButton: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();

  const handlePress = () => {
    // Use reset to ensure back button always goes to Home
    navigation.reset({
      index: 1,
      routes: [
        { name: 'Home' },
        { name: 'Profile' }
      ],
    });
  };

  return (
    <TouchableOpacity 
      style={styles.profileButton}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <Text style={styles.profileIcon}>👤</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  profileButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileIcon: {
    fontSize: 16,
  },
});
