import React from 'react';
import { Text, StyleSheet, TextStyle } from 'react-native';

interface SimpleColorTextProps {
  children: React.ReactNode;
  style?: TextStyle;
  fontSize?: number;
  fontWeight?: 'normal' | 'bold' | '600';
  color?: string;
}

export const SimpleColorText: React.FC<SimpleColorTextProps> = ({
  children,
  style,
  fontSize = 18,
  fontWeight = '600',
  color = '#87CEFD', // Нежно-голубой как основной цвет
}) => {
  const textStyle = {
    fontSize,
    fontWeight,
    color,
    textShadowColor: 'rgba(147, 112, 219, 0.6)', // Фиолетовая тень для эффекта градиента
    textShadowOffset: { width: 1, height: 0 },
    textShadowRadius: 3,
    ...style,
  };

  return (
    <Text style={[styles.text, textStyle]}>
      {children}
    </Text>
  );
};

const styles = StyleSheet.create({
  text: {
    backgroundColor: 'transparent',
  },
});
