import React from 'react';
import { TouchableOpacity, Text, StyleSheet } from 'react-native';
import { useAuth } from '../contexts/AuthContext';
import { useLanguageSession } from '../contexts/LanguageSessionContext';
import { getLanguageByCode } from '../constants/languages';

// Простая кнопка для открытия модального окна выбора языка сессии
export const LanguageSessionButton: React.FC = () => {
  const { user } = useAuth();
  const { openLanguageSessionModal } = useLanguageSession();

  // Получаем текущий язык сессии
  const currentLanguage = user?.settings?.target_language || 'en';
  
  // Получаем флаг текущего языка
  const currentLanguageData = getLanguageByCode(currentLanguage);
  const currentFlag = currentLanguageData?.flag || '🌐';

  return (
    <TouchableOpacity
      style={styles.flagButton}
      onPress={openLanguageSessionModal}
      activeOpacity={0.7}
    >
      <Text style={styles.flagText}>{currentFlag}</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  flagButton: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  flagText: {
    fontSize: 22,
  },
});
