import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Dimensions, ScrollView } from 'react-native';
import { GlassmorphismCard } from './GlassmorphismCard';
import { CustomNotification } from './CustomNotification';
import { useAuth } from '../contexts/AuthContext';
import { useLanguageSession } from '../contexts/LanguageSessionContext';
import { useNotification } from '../hooks/useNotification';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../App';
import { colors } from '../theme/colors';
import { getLanguageByCode } from '../constants/languages';
import { useAppTranslation } from '../src/i18n/hooks';

const { width: screenWidth } = Dimensions.get('window');

interface LanguageOption {
  code: string;
  flag: string;
  name: string;
  level: string;
}

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

export const LanguageSessionModal: React.FC = () => {
  const { user, updateUser } = useAuth();
  const { showLanguageSessionModal, closeLanguageSessionModal } = useLanguageSession();
  const { notification, showSuccess, showError, hideNotification } = useNotification();
  const navigation = useNavigation<NavigationProp>();
  const { t } = useAppTranslation('common');

  if (!showLanguageSessionModal) return null;

  // Получаем текущий язык сессии
  const currentLanguage = user?.settings?.target_language || 'en';

  // Получаем все изучаемые языки с уровнями
  const getLanguageOptions = (): LanguageOption[] => {
    const learningLanguages = user?.learning_languages || ['en'];
    const languageLevels = user?.settings?.language_levels || { en: 'A0' };

    return learningLanguages.map(langCode => {
      const language = getLanguageByCode(langCode);
      return {
        code: langCode,
        flag: language?.flag || '🌐',
        name: language?.name || langCode.toUpperCase(),
        level: languageLevels[langCode] || 'A0'
      };
    });
  };

  const languageOptions = getLanguageOptions();

  const handleLanguageSelect = async (languageCode: string) => {
    if (!user?.settings) return;

    try {
      const updates = {
        settings: {
          ...user.settings,
          target_language: languageCode
        }
      };

      await updateUser(updates);
      closeLanguageSessionModal();

      const selectedLang = languageOptions.find(lang => lang.code === languageCode);
      showSuccess(`${t('success.updated')} ${selectedLang?.name || languageCode}`);
    } catch (error) {
      console.error('Ошибка при смене языка сессии:', error);
      showError(t('errors.unknownError'));
    }
  };

  const handleShowAddLanguage = () => {
    closeLanguageSessionModal();
    navigation.navigate('AddLanguage');
  };

  return (
    <View style={styles.overlay}>
      <TouchableOpacity
        style={styles.backdrop}
        activeOpacity={1}
        onPress={closeLanguageSessionModal}
      />

      <View style={styles.pickerContainer}>
        <GlassmorphismCard
          backgroundColor="rgba(20, 20, 35, 0.95)"
          borderColor="rgba(255, 255, 255, 0.15)"
          borderWidth={1}
          borderRadius={20}
          blurIntensity={25}
          withFogEffect={true}
          fogColor={[
            'rgba(120, 200, 255, 0.3)',
            'rgba(120, 200, 255, 0.2)',
            'rgba(120, 200, 255, 0.1)',
            'rgba(120, 200, 255, 0.05)',
            'rgba(120, 200, 255, 0)'
          ]}
          style={styles.pickerCard}
        >
          <Text style={styles.pickerTitle}>{t('chooseLanguageToLearn')}</Text>

          <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
            {languageOptions.map((option) => (
              <TouchableOpacity
                key={option.code}
                onPress={() => handleLanguageSelect(option.code)}
                style={[
                  styles.languageItem,
                  currentLanguage === option.code && styles.selectedItem
                ]}
                activeOpacity={0.7}
              >
                <View style={styles.languageContent}>
                  <Text style={styles.languageFlag}>{option.flag}</Text>
                  <View style={styles.languageInfo}>
                    <Text style={[
                      styles.languageName,
                      currentLanguage === option.code && styles.selectedText
                    ]}>
                      {option.name}
                    </Text>
                    <Text style={styles.languageLevel}>
                      {t('level')}: {option.level}
                    </Text>
                  </View>
                  {currentLanguage === option.code && (
                    <View style={styles.checkmark}>
                      <Text style={styles.checkmarkText}>✓</Text>
                    </View>
                  )}
                </View>
              </TouchableOpacity>
            ))}

            {/* Кнопка добавления нового языка */}
            <TouchableOpacity
              onPress={handleShowAddLanguage}
              style={styles.addLanguageButton}
              activeOpacity={0.7}
            >
              <View style={styles.addLanguageContent}>
                <Text style={styles.addLanguageIcon}>➕</Text>
                <Text style={styles.addLanguageText}>{t('navigation.addLanguage')}</Text>
              </View>
            </TouchableOpacity>
          </ScrollView>

          <TouchableOpacity
            style={styles.cancelButton}
            onPress={closeLanguageSessionModal}
            activeOpacity={0.8}
          >
            <Text style={styles.cancelText}>{t('buttons.cancel')}</Text>
          </TouchableOpacity>
        </GlassmorphismCard>
      </View>

      {/* Кастомные уведомления */}
      <CustomNotification
        message={notification.message}
        visible={notification.visible}
        type={notification.type}
        onHide={hideNotification}
        duration={1000}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  pickerContainer: {
    width: screenWidth - 40,
    maxHeight: 400,
    zIndex: 1001,
  },
  pickerCard: {
    width: '100%',
  },
  scrollView: {
    maxHeight: 250,
  },
  pickerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
    textAlign: 'center',
    marginBottom: 20,
  },
  languageItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginVertical: 2,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.08)',
  },
  selectedItem: {
    backgroundColor: 'rgba(120, 200, 255, 0.15)',
    borderColor: 'rgba(120, 200, 255, 0.3)',
  },
  languageContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  languageFlag: {
    fontSize: 24,
    marginRight: 12,
  },
  languageInfo: {
    flex: 1,
  },
  languageName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.secondary,
    marginBottom: 2,
  },
  selectedText: {
    color: colors.text.primary,
  },
  languageLevel: {
    fontSize: 12,
    color: colors.text.tertiary,
  },
  checkmark: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: 'rgba(120, 200, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkmarkText: {
    color: colors.text.primary,
    fontSize: 12,
    fontWeight: 'bold',
  },
  addLanguageButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginVertical: 8,
    borderRadius: 12,
    backgroundColor: 'rgba(120, 200, 255, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(120, 200, 255, 0.3)',
    borderStyle: 'dashed',
  },
  addLanguageContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  addLanguageIcon: {
    fontSize: 16,
    marginRight: 8,
  },
  addLanguageText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text.secondary,
  },
  cancelButton: {
    marginTop: 16,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    alignSelf: 'center',
  },
  cancelText: {
    color: colors.text.secondary,
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
});
