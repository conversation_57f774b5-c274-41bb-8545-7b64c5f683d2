import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { GlassmorphismCard, glassmorphismPresets } from './GlassmorphismCard';
import { useAppTranslation } from '../src/i18n/hooks';

interface UserStatisticsProps {
  statistics: {
    learned_words: number;
    total_words_in_progress: number;
    total_words_available: number;
    active_words: number;
    passive_words: number;
    forced_words: number;
    progress_percentage: number;
    target_language: string | null;
  };
}

export const UserStatistics: React.FC<UserStatisticsProps> = ({ statistics }) => {
  const { t } = useAppTranslation('common');



  return (
    <View style={styles.container}>
      <GlassmorphismCard
        {...glassmorphismPresets.profile}
        style={styles.statisticsCard}
        width="100%"
        height="auto"
      >
        <View style={styles.statisticsContent}>
          {/* Основная статистика */}
          <View style={styles.mainStatsRow}>
            <View style={styles.mainStatItem}>
              <Text style={styles.mainStatNumber}>
                {statistics.learned_words}
              </Text>
              <Text style={styles.mainStatLabel}>
                {t('statistics.learnedWords', 'Выучено слов')}
              </Text>
            </View>
            <View style={styles.mainStatDivider} />
            <View style={styles.mainStatItem}>
              <Text style={styles.mainStatNumber}>
                {statistics.total_words_available}
              </Text>
              <Text style={styles.mainStatLabel}>
                {t('statistics.totalAvailable', 'Всего доступно')}
              </Text>
            </View>
          </View>

          {/* Прогресс-бар */}
          <View style={styles.progressSection}>
            <View style={styles.progressHeader}>
              <Text style={styles.progressLabel}>
                {t('statistics.progress', 'Прогресс изучения')}
              </Text>
              <Text style={styles.progressPercentage}>
                {statistics.progress_percentage}%
              </Text>
            </View>
            <View style={styles.progressBarContainer}>
              <View style={styles.progressBarBackground}>
                <LinearGradient
                  colors={['#87CEFD', '#9370DB']}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                  style={[
                    styles.progressBarFill,
                    { width: `${Math.min(statistics.progress_percentage, 100)}%` }
                  ]}
                />
              </View>
              <Text style={styles.progressText}>
                {statistics.learned_words} из {statistics.total_words_available}
              </Text>
            </View>
          </View>

          {/* Детальная статистика очередей */}
          <View style={styles.queuesSection}>
            <Text style={styles.queuesSectionTitle}>
              {t('statistics.queues', 'Очереди изучения')}
            </Text>
            <View style={styles.queuesRow}>
              <View style={styles.queueItem}>
                <View style={[styles.queueIndicator, styles.activeQueue]} />
                <Text style={styles.queueNumber}>{statistics.active_words}</Text>
                <Text style={styles.queueLabel}>
                  {t('statistics.activeQueue', 'Готово к тренировке')}
                </Text>
              </View>
              <View style={styles.queueItem}>
                <View style={[styles.queueIndicator, styles.passiveQueue]} />
                <Text style={styles.queueNumber}>{statistics.passive_words}</Text>
                <Text style={styles.queueLabel}>
                  {t('statistics.passiveQueue', 'Ожидают интервала')}
                </Text>
              </View>
              <View style={styles.queueItem}>
                <View style={[styles.queueIndicator, styles.forcedQueue]} />
                <Text style={styles.queueNumber}>{statistics.forced_words}</Text>
                <Text style={styles.queueLabel}>
                  {t('statistics.forcedQueue', 'Требуют повторения')}
                </Text>
              </View>
            </View>
          </View>
        </View>
      </GlassmorphismCard>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
    width: '100%',
  },
  statisticsCard: {
    padding: 0,
  },
  statisticsContent: {
    padding: 20,
  },
  statisticsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#fff',
    textAlign: 'center',
    marginBottom: 20,
  },
  mainStatsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  mainStatItem: {
    flex: 1,
    alignItems: 'center',
  },
  mainStatDivider: {
    width: 1,
    height: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    marginHorizontal: 20,
  },
  mainStatNumber: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#87CEFD',
    marginBottom: 4,
  },
  mainStatLabel: {
    fontSize: 13,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    lineHeight: 16,
  },
  progressSection: {
    marginBottom: 24,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  progressLabel: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: '500',
  },
  progressPercentage: {
    fontSize: 16,
    color: '#87CEFD',
    fontWeight: '600',
  },
  progressBarContainer: {
    alignItems: 'center',
  },
  progressBarBackground: {
    width: '100%',
    height: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 8,
  },
  progressBarFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.6)',
  },
  queuesSection: {
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
    paddingTop: 20,
  },
  queuesSectionTitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: '500',
    marginBottom: 16,
    textAlign: 'center',
  },
  queuesRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  queueItem: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  queueIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginBottom: 8,
  },
  activeQueue: {
    backgroundColor: '#4CAF50', // Зеленый для активной очереди
  },
  passiveQueue: {
    backgroundColor: '#FF9800', // Оранжевый для пассивной очереди
  },
  forcedQueue: {
    backgroundColor: '#F44336', // Красный для форсированной очереди
  },
  queueNumber: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
    marginBottom: 4,
  },
  queueLabel: {
    fontSize: 10,
    color: 'rgba(255, 255, 255, 0.6)',
    textAlign: 'center',
    lineHeight: 12,
  },
});
