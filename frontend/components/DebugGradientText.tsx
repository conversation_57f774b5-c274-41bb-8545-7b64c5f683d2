import React from 'react';
import { Text, StyleSheet, TextStyle, View } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import MaskedView from '@react-native-masked-view/masked-view';

interface DebugGradientTextProps {
  children: React.ReactNode;
  colors?: string[];
  style?: TextStyle;
  fontSize?: number;
  fontWeight?: 'normal' | 'bold' | '600';
}

export const DebugGradientText: React.FC<DebugGradientTextProps> = ({
  children,
  colors = [
    '#87CEFD', // Нежно-голубой небесный
    '#9370DB'  // Легкий пастельный фиолетовый
  ],
  style,
  fontSize = 18,
  fontWeight = '600',
}) => {
  const textStyle = {
    fontSize,
    fontWeight,
    backgroundColor: 'transparent',
    ...style,
  };

  return (
    <View style={styles.debugWrapper}>
      {/* Показываем обычный текст для сравнения */}
      <Text style={[styles.debugText, textStyle]}>
        DEBUG: {children}
      </Text>
      
      {/* Градиентный текст */}
      <MaskedView
        style={styles.container}
        maskElement={
          <View style={styles.maskContainer}>
            <Text style={[styles.maskText, textStyle]}>
              {children}
            </Text>
          </View>
        }
      >
        <LinearGradient
          colors={colors}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.gradient}
        />
      </MaskedView>
      
      {/* Показываем градиент без маски для проверки */}
      <LinearGradient
        colors={colors}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.debugGradient}
      >
        <Text style={[styles.debugGradientText, textStyle]}>
          GRADIENT: {children}
        </Text>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  debugWrapper: {
    alignSelf: 'flex-start',
    borderWidth: 2,
    borderColor: 'red',
    padding: 5,
  },
  container: {
    alignSelf: 'flex-start',
    borderWidth: 1,
    borderColor: 'yellow',
  },
  maskContainer: {
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
  },
  maskText: {
    backgroundColor: 'transparent',
    color: 'black', // Цвет для маски - ДОЛЖЕН БЫТЬ ЧЕРНЫМ
    fontWeight: '600',
    textAlign: 'center',
  },
  gradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  debugText: {
    color: 'white',
    backgroundColor: 'blue',
  },
  debugGradient: {
    padding: 5,
    borderRadius: 5,
  },
  debugGradientText: {
    color: 'white',
    backgroundColor: 'transparent',
  },
});
