import React, { useMemo } from 'react';
import { TouchableOpacity, Text, StyleSheet, ViewStyle, TextStyle, View, Dimensions } from 'react-native';
import { Canvas, RadialGradient, Rect, vec, Circle, LinearGradient as SkiaLinearGradient, RoundedRect, BackdropBlur, Blur } from '@shopify/react-native-skia';

// Типы кнопок
type ButtonVariant = 'primary' | 'secondary' | 'accent' | 'danger' | 'success';
type ButtonSize = 'small' | 'medium' | 'large';

interface GlassButtonProps {
  children: React.ReactNode;
  onPress: () => void;
  variant?: ButtonVariant;
  size?: ButtonSize;
  disabled?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  fullWidth?: boolean;
}

// Простые современные конфигурации кнопок
const buttonConfigs = {
  primary: {
    backgroundColor: 'rgba(30, 35, 50, 0.9)',
    borderColor: 'rgba(70, 130, 255, 0.3)',
    textColor: '#ffffff',
    gradientStart: 'rgba(70, 130, 255, 0.15)',
    gradientEnd: 'rgba(70, 130, 255, 0.05)'
  },
  secondary: {
    backgroundColor: 'rgba(40, 40, 50, 0.9)',
    borderColor: 'rgba(255, 255, 255, 0.15)',
    textColor: '#ffffff',
    gradientStart: 'rgba(255, 255, 255, 0.08)',
    gradientEnd: 'rgba(255, 255, 255, 0.02)'
  },
  accent: {
    backgroundColor: 'rgba(35, 25, 45, 0.9)',
    borderColor: 'rgba(170, 70, 255, 0.3)',
    textColor: '#ffffff',
    gradientStart: 'rgba(170, 70, 255, 0.15)',
    gradientEnd: 'rgba(170, 70, 255, 0.05)'
  },
  danger: {
    backgroundColor: 'rgba(45, 25, 25, 0.9)',
    borderColor: 'rgba(255, 70, 70, 0.3)',
    textColor: '#ffffff',
    gradientStart: 'rgba(255, 70, 70, 0.15)',
    gradientEnd: 'rgba(255, 70, 70, 0.05)'
  },
  success: {
    backgroundColor: 'rgba(25, 45, 35, 0.9)',
    borderColor: 'rgba(70, 255, 150, 0.3)',
    textColor: '#ffffff',
    gradientStart: 'rgba(70, 255, 150, 0.15)',
    gradientEnd: 'rgba(70, 255, 150, 0.05)'
  }
};

// Размеры кнопок (все одинаковой высоты)
const sizeConfigs = {
  small: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    fontSize: 14,
    borderRadius: 12,
    minHeight: 48
  },
  medium: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    fontSize: 16,
    borderRadius: 12,
    minHeight: 48
  },
  large: {
    paddingVertical: 12,
    paddingHorizontal: 32,
    fontSize: 18,
    borderRadius: 12,
    minHeight: 48
  }
};

export const GlassButton: React.FC<GlassButtonProps> = ({
  children,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  style,
  textStyle,
  fullWidth = false
}) => {
  const config = buttonConfigs[variant];
  const sizeConfig = sizeConfigs[size];
  const { width: screenWidth } = Dimensions.get('window');

  const buttonStyle = [
    styles.button,
    {
      paddingVertical: sizeConfig.paddingVertical,
      paddingHorizontal: sizeConfig.paddingHorizontal,
      borderRadius: sizeConfig.borderRadius,
      minHeight: sizeConfig.minHeight,
      width: fullWidth ? '100%' : 'auto',
      opacity: disabled ? 0.5 : 1,
      backgroundColor: config.backgroundColor,
      borderWidth: 1,
      borderColor: config.borderColor,
      overflow: 'hidden'
    },
    style
  ];

  const textStyles = [
    styles.text,
    {
      fontSize: sizeConfig.fontSize,
      color: config.textColor
    },
    textStyle
  ];

  const buttonWidth = fullWidth ? screenWidth - 40 : 200;

  return (
    <TouchableOpacity
      onPress={onPress}
      disabled={disabled}
      activeOpacity={0.8}
      style={buttonStyle}
    >
      {/* Простой современный фон */}
      <View style={StyleSheet.absoluteFill}>
        <Canvas style={{ flex: 1 }}>
          {/* Основной фон */}
          <RoundedRect
            x={0}
            y={0}
            width={buttonWidth}
            height={sizeConfig.minHeight}
            r={sizeConfig.borderRadius}
            color={config.backgroundColor}
          />

          {/* Тонкий градиентный overlay */}
          <RoundedRect
            x={0}
            y={0}
            width={buttonWidth}
            height={sizeConfig.minHeight}
            r={sizeConfig.borderRadius}
          >
            <SkiaLinearGradient
              start={vec(0, 0)}
              end={vec(0, sizeConfig.minHeight)}
              colors={[
                config.gradientStart,
                config.gradientEnd
              ]}
            />
          </RoundedRect>
        </Canvas>
      </View>

      {/* Контент кнопки */}
      <View style={styles.content}>
        {typeof children === 'string' ? (
          <Text style={textStyles}>{children || ''}</Text>
        ) : children ? (
          children
        ) : (
          <Text style={textStyles}></Text>
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    alignSelf: 'center',
    overflow: 'hidden',
  },
  cardContainer: {
    margin: 0,
  },
  content: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    zIndex: 2,
  },
  text: {
    fontWeight: '600',
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
});

// Экспорт типов
export type { GlassButtonProps, ButtonVariant, ButtonSize };
