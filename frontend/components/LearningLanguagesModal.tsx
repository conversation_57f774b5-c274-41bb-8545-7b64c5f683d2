import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Alert, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { colors } from '../theme/colors';
import { getLanguageByCode } from '../constants/languages';

const { width: screenWidth } = Dimensions.get('window');

// Импортируем хуки для переводов
import { useAppTranslation } from '../src/i18n/hooks';

interface LearningLanguagesModalProps {
  visible: boolean;
  onClose: () => void;
  learningLanguages: string[];
  languageLevels: { [key: string]: string };
  onLevelChange: (languageCode: string, level: string) => void;
  onAddLanguage: () => void;
  onRemoveLanguage: (languageCode: string) => void;
}

const LANGUAGE_LEVELS = [
  { value: 'A0', label: 'A0', description: 'Топ-100 самых важных слов' },
  { value: 'A1', label: 'A1', description: 'Начальный уровень' },
  { value: 'A2', label: 'A2', description: 'Элементарный уровень' },
  { value: 'B1', label: 'B1', description: 'Средний уровень' },
  { value: 'B2', label: 'B2', description: 'Выше среднего' },
  { value: 'C1', label: 'C1', description: 'Продвинутый уровень' },
  { value: 'C2', label: 'C2', description: 'Профессиональный уровень' },
];

// Компонент модального окна как overlay (без Modal)
const LearningLanguagesOverlay: React.FC<{
  onClose: () => void;
  learningLanguages: string[];
  languageLevels: { [key: string]: string };
  onLevelChange: (languageCode: string, level: string) => void;
  onAddLanguage: () => void;
  handleRemoveLanguage: (languageCode: string) => void;
  t: any;
}> = ({
  onClose,
  learningLanguages,
  languageLevels,
  onLevelChange,
  onAddLanguage,
  handleRemoveLanguage,
  t
}) => {
  return (
    <View style={styles.overlay}>
      <TouchableOpacity
        style={styles.backdrop}
        activeOpacity={1}
        onPress={onClose}
      />

      <View style={styles.modalContainer}>
        <LinearGradient
          colors={['rgba(20, 20, 35, 0.95)', 'rgba(16, 21, 62, 0.95)']}
          style={styles.modal}
        >
          <View style={styles.header}>
            <Text style={styles.title}>{t('fields.learningLanguages')}</Text>
          </View>

          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            {learningLanguages.map((langCode) => {
              const language = getLanguageByCode(langCode) || { flag: '🌐', name: langCode.toUpperCase() };
              const currentLevel = languageLevels[langCode] || 'A0';

              return (
                <View key={langCode} style={styles.languageCard}>
                  <View style={styles.languageHeader}>
                    <View style={styles.languageInfo}>
                      <Text style={styles.languageFlag}>{language.flag}</Text>
                      <Text style={styles.languageName}>{language.name}</Text>
                    </View>
                    {learningLanguages.length > 1 && (
                      <TouchableOpacity
                        style={styles.removeButton}
                        onPress={() => handleRemoveLanguage(langCode)}
                        activeOpacity={0.7}
                      >
                        <Text style={styles.removeButtonText}>✕</Text>
                      </TouchableOpacity>
                    )}
                  </View>

                  <Text style={styles.levelLabel}>{t('currentLevel')}:</Text>
                  <View style={styles.levelButtons}>
                    {LANGUAGE_LEVELS.map((level) => (
                      <TouchableOpacity
                        key={level.value}
                        style={[
                          styles.levelButton,
                          currentLevel === level.value && styles.levelButtonActive
                        ]}
                        onPress={() => onLevelChange(langCode, level.value)}
                        activeOpacity={0.7}
                      >
                        <Text style={[
                          styles.levelButtonText,
                          currentLevel === level.value && styles.levelButtonTextActive
                        ]}>
                          {level.label}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>
              );
            })}

            <TouchableOpacity
              style={styles.addLanguageButton}
              onPress={onAddLanguage}
              activeOpacity={0.7}
            >
              <Text style={styles.addLanguageIcon}>➕</Text>
              <Text style={styles.addLanguageText}>{t('common:navigation.addLanguage')}</Text>
            </TouchableOpacity>
          </ScrollView>
        </LinearGradient>
      </View>
    </View>
  );
};

export const LearningLanguagesModal: React.FC<LearningLanguagesModalProps> = ({
  visible,
  onClose,
  learningLanguages,
  languageLevels,
  onLevelChange,
  onAddLanguage,
  onRemoveLanguage,
}) => {
  const { t } = useAppTranslation('profile');
  const handleRemoveLanguage = (languageCode: string) => {
    const language = getLanguageByCode(languageCode);
    const languageName = language?.name || languageCode.toUpperCase();

    Alert.alert(
      t('removeLanguage.title'),
      t('removeLanguage.message').replace('{{language}}', languageName),
      [
        {
          text: t('common:buttons.cancel'),
          style: 'cancel',
        },
        {
          text: t('common:buttons.delete'),
          style: 'destructive',
          onPress: () => onRemoveLanguage(languageCode),
        },
      ]
    );
  };
  // Используем overlay вместо Modal
  if (!visible) return null;

  return (
    <LearningLanguagesOverlay
      onClose={onClose}
      learningLanguages={learningLanguages}
      languageLevels={languageLevels}
      onLevelChange={onLevelChange}
      onAddLanguage={onAddLanguage}
      handleRemoveLanguage={handleRemoveLanguage}
      t={t}
    />
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContainer: {
    width: screenWidth - 40,
    maxWidth: 400,
    maxHeight: '80%',
    zIndex: 1001,
  },
  modal: {
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
    overflow: 'hidden',
  },
  header: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.text.primary,
    textAlign: 'center',
  },
  content: {
    padding: 20,
  },
  languageCard: {
    marginBottom: 20,
    padding: 16,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.08)',
  },
  languageHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  languageInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  languageFlag: {
    fontSize: 24,
    marginRight: 12,
  },
  languageName: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
  },
  levelLabel: {
    fontSize: 14,
    color: colors.text.secondary,
    marginBottom: 8,
  },
  levelButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  levelButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  levelButtonActive: {
    backgroundColor: 'rgba(120, 200, 255, 0.3)',
    borderColor: 'rgba(120, 200, 255, 0.5)',
  },
  levelButtonText: {
    fontSize: 14,
    color: colors.text.secondary,
    fontWeight: '500',
  },
  levelButtonTextActive: {
    color: colors.text.primary,
    fontWeight: '600',
  },
  addLanguageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    backgroundColor: 'rgba(120, 200, 255, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(120, 200, 255, 0.3)',
    borderStyle: 'dashed',
    marginTop: 10,
  },
  addLanguageIcon: {
    fontSize: 18,
    marginRight: 8,
  },
  addLanguageText: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text.secondary,
  },
  removeButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 71, 87, 0.2)',
    borderWidth: 1,
    borderColor: 'rgba(255, 71, 87, 0.4)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  removeButtonText: {
    fontSize: 12,
    color: '#ff4757',
    fontWeight: 'bold',
  },
});
