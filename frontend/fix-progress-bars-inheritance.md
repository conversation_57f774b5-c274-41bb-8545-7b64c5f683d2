# Исправление наследования состояния полосок прогресса

## Проблема
После правильного ответа на первую карточку, вторая карточка показывала 5 зеленых полосок, хотя была новой и по ней еще не было ответов.

## Анализ проблемы

### Временная линия из логов:
```
19:40:19 - Ответ на "asa": ПРАВИЛЬНО
19:40:19 - 🎉 Новое слово отвечено правильно! МГНОВЕННО обновляем полоски прогресса...
19:40:20 - Переход к карточке "Dili"
ПРОБЛЕМА: Карточка "Dili" наследует состояние от "asa"
```

### Корень проблемы:
1. **Мгновенное обновление** полосок для "asa" изменяло `currentCard`
2. **При переходе** к "Dili" новая карточка **наследовала** измененное состояние
3. **Результат**: "Dili" показывала полоски как выученная карточка

## Техническая причина

### БЫЛО (неправильно):
```typescript
// Мгновенное обновление currentCard
if (isNewWordCorrect) {
  setCurrentCard(prev => ({
    ...prev,
    is_new: false,           // ← ЭТО ВЛИЯЛО НА СЛЕДУЮЩУЮ КАРТОЧКУ!
    is_learned: true,        // ← И ЭТО ТОЖЕ!
    interval_level: 15,      // ← И ЭТО!
  }));
}

// При переходе к следующей карточке
setCurrentCard(newCard); // ← newCard наследовала состояние!
```

### Проблема наследования:
- `setCurrentCard` изменял состояние текущей карточки
- При переходе новая карточка получала измененное состояние
- Полоски прогресса отображались неправильно

## Решение

### 1. Убрали мгновенное изменение currentCard:
```typescript
// СТАЛО (правильно):
if (isNewWordCorrect) {
  console.log('🎉 Новое слово отвечено правильно! МГНОВЕННО обновляем полоски прогресса...');
  // НЕ обновляем currentCard здесь - это может повлиять на следующую карточку
  // Обновление будет происходить в асинхронной функции с данными сервера
}
```

### 2. Добавили явный сброс состояния:
```typescript
// 🚀 ВАЖНО: Создаем копию карточки с правильным начальным состоянием
const cleanCard = newCard ? {
  ...newCard,
  // Убеждаемся, что новая карточка имеет правильное начальное состояние
  is_new: newCard.is_new ?? true, // Новые карточки должны быть помечены как новые
  is_learned: newCard.is_learned ?? false, // По умолчанию не выучены
  interval_level: newCard.interval_level ?? -1, // Начальный уровень
} : null;

setCurrentCard(cleanCard);
```

## Результат исправления

### ✅ Правильное отображение полосок:
- Новые карточки показывают правильное начальное состояние
- Нет наследования состояния от предыдущих карточек
- Полоски прогресса отображаются корректно

### ✅ Сохранение функциональности:
- Мгновенная проверка ответов работает
- Асинхронная синхронизация с сервером сохранена
- Данные прогресса обновляются правильно

### ✅ Стабильность:
- Каждая карточка имеет независимое состояние
- Нет побочных эффектов между карточками
- Предсказуемое поведение полосок прогресса

## Логика полосок прогресса

### Новая карточка (правильно):
- `is_new: true` → показывает пустые полоски
- `is_learned: false` → не выучена
- `interval_level: -1` → начальный уровень

### После правильного ответа (асинхронно):
- Сервер возвращает обновленные данные
- `is_new: false`, `is_learned: true`, `interval_level: 15`
- Полоски обновляются корректно

Это исправление устраняет визуальную ошибку и обеспечивает правильное отображение прогресса для каждой карточки.
