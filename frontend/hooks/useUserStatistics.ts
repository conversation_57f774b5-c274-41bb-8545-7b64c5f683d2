import { useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuth } from '../contexts/AuthContext';
import { API_BASE_URL } from '../config/api';

interface UserStatistics {
  learned_words: number;
  total_words_in_progress: number;
  total_words_available: number;
  active_words: number;
  passive_words: number;
  forced_words: number;
  progress_percentage: number;
  target_language: string | null;
}

export const useUserStatistics = (targetLanguage?: string) => {
  const [statistics, setStatistics] = useState<UserStatistics | null>(null);
  const [loading, setLoading] = useState(true); // Начинаем с true для немедленной загрузки
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  const fetchStatistics = async () => {
    if (!user) {
      return;
    }

    // Получаем токен из AsyncStorage
    const token = await AsyncStorage.getItem('auth_token');
    if (!token) {
      return;
    }

    // Показываем загрузку при каждом запросе
    setLoading(true);
    setError(null);

    try {
      const url = new URL(`${API_BASE_URL}/api/spaced/api/user/statistics`);
      if (targetLanguage) {
        url.searchParams.append('target_language', targetLanguage);
      }

      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setStatistics(data);
    } catch (err) {
      console.error('Error fetching user statistics:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStatistics();
  }, [user, targetLanguage]);

  return {
    statistics,
    loading,
    error,
    refetch: fetchStatistics,
    refresh: fetchStatistics, // Алиас для удобства
  };
};
