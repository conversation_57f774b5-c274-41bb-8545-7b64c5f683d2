import { useState } from 'react';

interface NotificationState {
  visible: boolean;
  message: string;
  type: 'success' | 'error' | 'info';
}

export const useNotification = () => {
  const [notification, setNotification] = useState<NotificationState>({
    visible: false,
    message: '',
    type: 'success',
  });

  const showNotification = (
    message: string, 
    type: 'success' | 'error' | 'info' = 'success'
  ) => {
    setNotification({
      visible: true,
      message,
      type,
    });
  };

  const hideNotification = () => {
    setNotification(prev => ({
      ...prev,
      visible: false,
    }));
  };

  const showSuccess = (message: string) => showNotification(message, 'success');
  const showError = (message: string) => showNotification(message, 'error');
  const showInfo = (message: string) => showNotification(message, 'info');

  return {
    notification,
    showNotification,
    hideNotification,
    showSuccess,
    showError,
    showInfo,
  };
};
