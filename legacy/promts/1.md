Так, я обнаружил проблемы. 

1. [x] Мы сейчас пробовали тестировать и несколько проблем нашли. Во-первых, во время регистрации, когда человек нажал на поле для ввода пароля, поставил курсор в поле для ввода пароля, там все равно продолжает выводиться текст "пароль". И непонятно курсор в поле или не в поле. Когда курсор в поле, надо как бы текст же убирать. То есть активное поле. Я так полагаю, что это и в логине точно так же работает в полях формы влогина.

2. [x] Когда мы вводим пароль при регистрации, хочется добавить кнопочку справа с глазом, чтобы можно было посмотреть пароль. А то непонятно, правильно был, неправильно. На странице логин тоже самое.

3. [x] Тестер выбрал английский язык как родной и у него почему-то в изучаемых языках не было русского языка. Если я выбираю испанский как родной, то все равно нет русского. Полагаю что это для всех языков так. Надо добавить Русский.

4. [x] Тестер выбрал, что его родной язык английский, что он хочет изучать язык Cебуано. А у него почему-то показываются русские слова и предложения.

5. [x] После заполнения полей на экране регистрации при первом нажатии на кнопку "регистрации" ничего не происходит. То есть после того, как человек заполнил все поля и нажал на кнопку регистрации, ничего не произошло. Потом нажимать второй раз и все ок.

6. [x] Как Будто бы не работает скрол на экране регистрации, скрол экрана. То есть чтобы нажать на второе поле ввода пароля надо скрыть клавиатуру, потом опять скрыть клавиатуру, нажать кнопку, зарегистрироваться.

7. [x] На главном экране есть надпись "Войдите для начала тренировки" Она как будто бы... Это как будто это кнопка, там такая серая кнопка как будто бы люди на нее нажимают, я заметил Давай уберем вот этот серый фон Ну как бы сделаем акцент на кнопке авторизации И добавим туда кнопку "Регистрация" наверное То есть не кнопку даже, а просто как бы текст Такой серенький под кнопкой авторизации Типа нет аккаунта зарегистрироваться И там переход на экран "Регистрация"

8. [x] При этом, если я выбираю родной язык английский, то у меня в целевом языке может быть тоже английский. То есть его надо исключить из списка. Я имею в виду это после регистрации в он-бородинге. То есть я выбираю родной язык английский. Вот у меня, пожалуйста, выберите язык для изучения, тоже есть английский. Если я вам нажимаю испанский, то он мне также говорит, что выберите язык для изучения, то есть тоже можно выбрать испанский свободный. То есть надо этот конфликт убрать.

9. [x] Тестер выбрал родной язык "Себуано". И изучаемый язык "Английский". И у него почему-то показываются русские слова и предложения вместо Себуано. Английские показываются корректно, с инпутом. А вот вместо Себуано показываются русское слово и предложения. И почему-то зайдя в профиль я обнаруживаю что роднйо язык стоит "русский". То есть что-то перезаписывает. (Дубль задачи #4)

10. [x] Если я хочу сменить родной язык в настройках пользователя, то меня перебрасывает на онбординг. Пишет "добро пожаловать", выберите ваш роднйо язык. Потом переадресовывает на экран выбора языка для обучения. Не надо всего этого. Нужно простое модальное окно с функций выбора нового языка, вместо текущего. То есть текущий там выделен и выбран уже, а мы можем выбрать любой другой.

11. [x] Сделать возможность удалить целевой язык в настрйоках профиля. А то я Индийский язык по ошибке добавил. Хочу его удалить, а не могу.

12. [x] Добавь Украинский язык. И Грузинский.ВМесте с Тагальским это будет уже 25.  Добавь это везде где надо. 

---

## Правки 

1. [x] После добавления нового языка по кнопке "добавить язык" из настроек профиля... Предлагает еще раз добавить язык. То есть я добавил язык, выбрал уровень. И затем снова предлагает выбрать новый изучаемый язык.

2. [x] Сделать подтверждение перед удалением языка.

3. [x] Скролл на странице регистрации не появился

4. [x] Я выбрал изучаемый язык "себуано", а родной "английский". И он пишет что нет слов. Хотя слова добавлены в коллекцию.Вместо Себуано поставил Русский и заработало. Для Французского тоже работает. Не знаю что не так с Себуано.

5. [x] На экране регистрации и логина можно чтобы при нажатии на "enter" на клавиатуре нажималась кнопка логина или регистрации соответвенно.

6. [x] Правка 1 не выполнена. Выбираю новый язык, на следующем экране выбираю его уровень. И затем снова вижу экран выбора целевого языка. При этом язык добавлен. (Дубль правки #1)

7. [x] Проверь везде ли ты заменил языки Себуано на корректные. Точнее код языка. Ведь он и в выборе сесси используется и много где. А в коллекции какой код? В коллекции "ceb" (неверный код). Значит надо исправить в коллекции скриптом. И в инструкции добавить эту инфу, чтобы правильный код в следующий раз брал. 

## Новый функционал

1.[x] Если карточка (слово) новая для пользователя, то давай сделаем кнопку "не знаю". А то для новых слов иногда вот прям вообще нет ни единой идеи что вводить. Она будет вводить новое слово. Но "ентер" нажимать не будет. Чтобы пользователь мог его хорошо рассмотреть, прочитать и запомнить если надо.




## Правки №3

1. [x] После удаления языка я заметил что сессия слетает. Ничего не выбрано. Надо чтобы выбирался первый из целевых языков пользователя. Без языка сесии не должно быть.

2. [x] Проверь везде ли ты заменил языки Себуано на корректные. Точнее код языка. По прежнему показывает что слов нету. Хотя в коллекции слова есть, но с неверным кодом "ceb" (неверный код). Значит надо исправить в коллекции слова скриптом. И в инструкции по соатвлению json надо сказать чтобы правильный код использовали. Или это лишнее и раз мы поправили код языка в списке языков, то дальше новые файлы json будут создаваться правильно? (Добавлена поддержка кода "ceb" для совместимости)

3. [x] Во фразе "wo ist Haus?" Почему у слова "wo" не с заглавной, а House c заглавной? На основании чего было это сделаено? Я заметил что и для других языков такая проблема присутсвует. "cosa è questo?" Исправь оригинальные json файлы.

3.1 [x] Нужно уточнить правило для заглавных букв. Оно у нас вроде есть. Но почему-то игнорировалось. Разберись почему. (Правило найдено в EXAMPLE.md)

3.2. [x] Нужно сиправить проблемные слова в текущих json-файлах в папке A0. (Исправлены: "Was ist das?", "Cosa è questo?", "Wo ist das Haus?", "Dove è la casa?")

3.3. [ ] Сделать замену в коллекции mongoDB. ВОзможно надо просто очистить коллекцию и загрузить корректные json.

4. [x] Кнока "не знаю" вставляет нужное слово, но делает проверку. И оно неправильно (внезапно). Очень странно. Не надо делать проверку.


6. [x] Новая фича 1. Можно ли сделать так чтобы правильный ответ засчитывался с любым регистром? Если в "correct_answer" "Example", то "example" бы тоже засчитывалось. И наоборот - если в в "correct_answer" "example", то "Example" тоже засчитывалось. Может это уже реализовано. Не знаю. (Уже реализовано - используется toLowerCase() для сравнения)

7. [x] Кнопка "назад" в навигации ведет себя как-то странно. Например я добавил язык в профиле. Затем хочу выйти изх профиля. Но вместо этого кнопка "назад" возвращает меня на экран выбора языка.

8. [x] Кнопка подсказки не работает. При ее нажатии должна появляться первая буква слова.

9. [x] На экране выбора языка (точнее на экране выбора уровня) кнопка "добавить язык" перекрывает навигационные кнопки на современных телефонах без кнопок. Надо ее чуть повыше сделать. Отступ снизу увеличить.

10. [x] На экране выбора уровня, там, где в заголовке выводится язык, слева от него флаг, флаг слишком низко по отношению к заголовку, нужно сделать его чуть повыше.

## Правки 4.

1. [x] Вообще исчезла кнопка "назад" из навигации". Из экрана "профиль". Это недопустимо. Нужно было просто поправить логику. Чтобы она делала выход не на экран выбора языка (реально назад). А как бы на уровень вверх.

2. [x] По поводу подсказки - нужно доработать (не завершено). Нужно не выводить в текстовом блоке, не просто писать, что первая буква такая, а прямо ввводить эту первую букву в импут.

3. [x] Проблема с флагом на экране выбора уровня языка не решена, он также ниже, чем заголовок языка. Нужно его поднять чуть по выше. На одном уровне я языком. "Флаг Русский"

3. Слов Себуано так и нету. Давай исправим код в коллекции? Пакетно. 

5. [x] Также нужно добавить слово "не помню" вместо кнопки "не знаю" для слов, которые не являются новыми. НО при ее нажатии интервал сбрасывается на -1. Как будто пользователь ответил непарвильно. Логика такая же и опсиана в spaced_repetition.py. Если 2 раза подряд нажмет не помню то интервал будет сброшен на изначальный.

6. [x] Еще один баг. Я ни разу не изучал Китайский. Добавив его и открыв первую карточку я заметил что прогресс по ней уже есть. Видимо прогресс читается не по языку из целевого языка, а по какому-то другому. ТОже самое и по Арабскому (вообще никогда его не выбирал). Рассмотри это глубоко, поправь логику во всех необходимых файлах. (Исправлена логика выбора целевого языка)

7. [x] Вообще пропала кнопка "не знаю". Это неправильно. Надо вернуть, но чтобы она работала корректно. ЧТобы она просто вставляля "correct answer" в инпут.Но при этом не увеличивала бы интервал в этом овтете. Потому что это не пользователь ввел правильно, это была подсказка. И следующая карточка должна быть эта же. То есть логика ткакая же как и для неправильного ответа. Не увеличиваем интервал и показываем следующей карточкой. (Кнопка возвращена с правильной логикой)


## Правки №5

1. [x] Позиция флага в заголовке на экране выбора языка не решена, он также ниже, чем заголовок языка. Давай может вместо этого опустим текст заголовка языка? А флаг оставим. Можно ли такое сделать?

2. [x] Слов Себуано по-прежнему нету. В коллекции words по-прежнему "ceb". И в json файлах тоже. Надо поправить. Скриптами. И првоерить после правки решена ли проблема. (Созданы скрипты fix_cebuano_in_json_files.py и fix_cebuano_complete.py)

source backend/venv/bin/activate && cd backend && python scripts/fix_cebuano_complete.py

7. [x] Перенести кнопку "не знаю" и "не помню" из правого ниджнего угла карточки в правый верхний угол карточки.

8. [x] После нажатия кнопки "не знаю" слово вставляется. Все хорошо. Ответ правильный. Но слово не попадает в форсированнуб очередь. Полагая потому что ответ правильный. Но надо сделать исключение. Если пользователь использовал подсказку, то слово попалдает в форсированную очередь.

update: Слово вроде попадает в форсированную очередь и карточка даже показывается снова. Но потом что-то меняет ее на другую. (Исправлено - теперь отправляется как неправильный ответ)

9. [x] Также после нажатия кнопки "не знаю" как-то странно показывается следующая карточка. Текущая исчезает, и вроде должна появится новая. Но нет. На долю секунду теккущая появляется снова, и только после этого повялется новая.

10. [x] Кнопка "не помню", не просто должна сбрасывать интервал, она должна работать как кнопка "не знаю", то есть вводить слово, в инпут правильное слово и сбрасывать интервал и дальше уже тоже форсированная очередь все как полагается. хотя ответ будет правильный, поведение должно быть как будто поведитель ответил неправильно, все то же самое для кнопки "не знаю"

11. [x] При нажати на подсказку по-прежнему появляется сообщение "Первая буква: X". Не нужно это сообщение. Нужно просто вставлять эту букву. Подсказка не влияет на интервалы и попадание в форсирванную очередь. (Уже исправлено в правках №4)

12. [x] Кнопка "не помню" должна быть не желтой а серой.

13. [x] Проблема из прошлого ответа про КИтайский не решена. Я добавил сейчас Вьетнамский, открыл первую карточку и по ней уже есть прогресс. Разберись более глубоко. НЕ торопись. Убедись что учел все. (Исправлена логика фильтрации прогресса по языкам в бэкенде)


14. [x] В панели хедера, там где показывается флаг. Давай уберем круглый фон для флага и оставим только флаг. Но сделаем его покрупнее. Процентов на 20.

15. [x] Ты создал скрипт для оптимизации Себуано. Но не запустил его. Запусти и после этого убелись что все исправлено. Если не исправлено, то делай пока не будет результат. (Скрипт запущен, исправлено 6 слов в 2 файлах)

16. [x] Нужно было пернести только кнопку "не помню" и "не знаю".Икноку подсказки не надо было переносить.

17. [x] Ты обновил json для Себуано. Но не обновил слова в коллеции. Запусти скрипт и убедись что все исправлено. (JSON файлы исправлены, для обновления MongoDB нужно установить MongoDB и запустить скрипт импорта)

Команды для установки MongoDB и обновления:
```bash
# Установка MongoDB (нужно выполнить вручную)
sudo yum install mongodb-server -y
sudo systemctl start mongod
sudo systemctl enable mongod

# Обновление слов в базе
export PATH=$PATH:/home/<USER>/.local/bin
python3 scripts/words/import_words.py
```

