import React, { useState } from 'react';
import { StyleSheet, View, Text, ScrollView, Dimensions, Pressable, useColorScheme } from 'react-native';
import {
  Canvas,
  Circle,
  RadialGradient,
  vec,
  RoundedRect,
  LinearGradient,
  Group,
  BackdropBlur,
  rrect,
  rect,
} from '@shopify/react-native-skia';
import { SafeAreaView } from 'react-native-safe-area-context';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
// Use the smaller dimension as a base for consistent scaling
const baseScreenSize = Math.min(screenWidth, screenHeight);

const themes = {
  light: {
    background: '#FFFFFF',
    titleText: '#000000',
    buttonTextColor: '#000000',
    buttonBackgroundColor: 'rgba(220, 220, 220, 0.35)', // More transparent grey
    buttonBorderColor: 'rgba(0, 0, 0, 0.2)',
    buttonShadowColor: 'rgba(0, 0, 0, 0.2)',
    spheres: [
      { // Blue - Adjusted position further
        id: 'light-sphere-1',
        cxRatio: 0.25, // Moved further right
        cyRatio: 0.25, // Moved further down
        rRatio: 1.0,
        colors: ['rgba(191, 212, 240, 0.9)', 'rgba(191, 212, 240, 0)'],
      },
      { // Pink - Adjusted position further
        id: 'light-sphere-2',
        cxRatio: 0.95, // Moved further right
        cyRatio: 0.90, // Moved further down
        rRatio: 1.0,
        colors: ['rgba(238, 153, 184, 0.75)', 'rgba(238, 153, 184, 0)'],
      },
    ],
    glassFill: ['rgba(255,255,255,0.6)', 'rgba(255,255,255,0.3)', 'rgba(255,255,255,0.5)'],
    glassStroke: 'rgba(200,200,200,0.5)',
    glassText: '#000000',
  },
  dark: {
    background: '#000000',
    titleText: '#FFFFFF',
    buttonTextColor: '#FFFFFF',
    buttonBackgroundColor: 'rgba(6, 4, 4, 0.15)', // More transparent white
    buttonBorderColor: 'rgba(255, 255, 255, 0.3)',
    buttonShadowColor: 'rgba(255, 255, 255, 0.15)',
    spheres: [
      { // Violet - средняя насыщенность
        id: 'dark-sphere-1',
        cxRatio: 0.15, cyRatio: 0.2, rRatio: 1.2,
        colors: ['rgba(170, 70, 255, 0.08)', 'rgba(170, 70, 255, 0)'],
      },
      { // Yellow - средняя насыщенность
        id: 'dark-sphere-2',
        cxRatio: 0.85, cyRatio: 0.3, rRatio: 0.7,
        colors: ['rgba(255, 220, 50, 0.09)', 'rgba(255, 220, 50, 0)'],
      },
      { // Teal - средняя насыщенность
        id: 'dark-sphere-3',
        cxRatio: 0.15, cyRatio: 0.6, rRatio: 1.8,
        colors: ['rgba(0, 200, 200, 0.08)', 'rgba(0, 200, 200, 0)'],
      },
      { // Pink - средняя насыщенность
        id: 'dark-sphere-4',
        cxRatio: 0.95, // Extreme Bottom-right
        cyRatio: 0.75, // Moved up from 0.95
        rRatio: 1.0,   // Large
        colors: ['rgba(220, 100, 170, 0.09)', 'rgba(220, 100, 170, 0)'],
      }
    ],
    glassFill: ['rgba(255,255,255,0.2)', 'rgba(255,255,255,0.05)', 'rgba(255,255,255,0.15)'],
    glassStroke: 'rgba(255,255,255,0.25)',
    glassText: '#FFFFFF',
  },
};

export default function HomeScreen() {
  const systemTheme = useColorScheme();
  const [themeKey, setThemeKey] = useState<'light' | 'dark'>(systemTheme || 'dark');

  const toggleTheme = () => {
    setThemeKey(prevTheme => (prevTheme === 'light' ? 'dark' : 'light'));
  };

  const currentTheme = themes[themeKey];

  // Calculate card dimensions relative to screen size
  const glassCardWidth = baseScreenSize * 0.65; // ~65% of the smaller dimension
  const glassCardHeight = glassCardWidth * 0.6; // Keep aspect ratio
  const glassCardX = (screenWidth - glassCardWidth) / 2;
  const glassCardY = (screenHeight * 0.4) - (glassCardHeight / 2);

  // Dynamic styles
  const dynamicStyles = StyleSheet.create({
    rootView: { // New style for the outermost view
      flex: 1,
      backgroundColor: currentTheme.background,
    },
    safeArea: { // Will now be transparent, just for content padding
      flex: 1,
      backgroundColor: 'transparent',
      zIndex: 2, // Ensure content is above canvas and card text overlay
    },
    themeButton: { // Styles for the Pressable button
      backgroundColor: currentTheme.buttonBackgroundColor,
      paddingVertical: 10,
      paddingHorizontal: 20,
      borderRadius: 10,
      marginVertical: 15,
      alignSelf: 'center',
      minWidth: 200, // Ensure button is a decent width
      // Border for glassmorphism stroke
      borderColor: currentTheme.buttonBorderColor,
      borderWidth: 1,
      // Shadow properties temporarily removed to address text substrate issue
      // shadowColor: currentTheme.buttonShadowColor,
      // shadowOffset: { width: 0, height: 2 },
      // shadowOpacity: 0.7,
      // shadowRadius: 3.5,
      // elevation: 4, // for Android shadow
    },
    themeButtonText: { // Styles for the Text inside Pressable
      color: currentTheme.buttonTextColor,
      fontSize: 16,
      fontWeight: '600',
      textAlign: 'center',
    },
    title: {
      fontSize: 28,
      fontWeight: 'bold',
      color: currentTheme.titleText,
      textAlign: 'center',
      marginVertical: 20,
    },
    overlayText: {
      color: currentTheme.glassText,
      fontSize: 22,
      fontWeight: 'bold',
      textShadowColor: themeKey === 'dark' ? 'rgba(0, 0, 0, 0.5)' : 'rgba(255,255,255,0.3)',
      textShadowOffset: { width: 1, height: 1 },
      textShadowRadius: 2,
      textAlign: 'center',
    },
  });

  const renderSpheres = () => {
    // Используем максимальный размер экрана для больших сфер
    const maxDimension = Math.max(screenWidth, screenHeight, 600);
    
    return currentTheme.spheres.map((sphere) => {
      // Позиционирование оставляем как есть
      const cx = sphere.cxRatio * screenWidth;
      const cy = sphere.cyRatio * screenHeight;
      
      // Очень большой фиксированный размер для всех сфер
      const maxRadius = Math.min(screenWidth, screenHeight) * 1.2; // Больше 100% для выхода за края
      // Используем фиксированный размер для всех сфер (0.5 от максимального размера экрана)
      let r = maxDimension * 0.5;
      
      // Ограничиваем максимальный размер
      r = Math.min(r, maxRadius);

      return (
        <Circle key={sphere.id} cx={cx} cy={cy} r={r}>
          <RadialGradient
            c={vec(cx, cy)}
            r={r}
            colors={sphere.colors}
          />
        </Circle>
      );
    });
  };

  return (
    <View style={dynamicStyles.rootView}>
      {/* Full Screen Canvas Background */}
      <Canvas style={{ position: 'absolute', width: screenWidth, height: screenHeight, zIndex: 0 }}>
        {/* Dynamically render spheres based on current theme */}
        {renderSpheres()}

        {/* Glassmorphism Card Elements (within the Canvas) */}
        <BackdropBlur
          blur={20}
          clip={rrect(rect(glassCardX, glassCardY, glassCardWidth, glassCardHeight), 20, 20)}
        />
        <Group>
          <RoundedRect x={glassCardX} y={glassCardY} width={glassCardWidth} height={glassCardHeight} r={20}>
            <LinearGradient
              start={vec(glassCardX, glassCardY)}
              end={vec(glassCardX + glassCardWidth, glassCardY + glassCardHeight)}
              colors={currentTheme.glassFill}
            />
          </RoundedRect>
          <RoundedRect x={glassCardX} y={glassCardY} width={glassCardWidth} height={glassCardHeight} r={20} style="stroke" strokeWidth={1.5} color={currentTheme.glassStroke} />
        </Group>
      </Canvas>

      {/* Text Overlay for Glass Card - Positioned absolutely over the Canvas */}
      <View style={[{
        position: 'absolute',
        top: glassCardY + (glassCardHeight / 2) - 15, // Centered vertically in the card
        left: glassCardX,
        width: glassCardWidth,
        alignItems: 'center',
        zIndex: 1, // Above canvas, below safe area content
      }]}>
        <Text style={[dynamicStyles.overlayText, { fontSize: 18 }]}>Blurred Mesh!</Text>
      </View>

      {/* Content Area (Title, Button) - Respects Safe Areas */}
      <SafeAreaView style={dynamicStyles.safeArea}>
        <ScrollView 
          style={styles.scrollViewContainer} 
          contentContainerStyle={styles.scrollViewContent}
          scrollEnabled={false} // Disable scrolling as content should fit
        >
          <Text style={dynamicStyles.title}>Skia Demo</Text>
          <Pressable onPress={toggleTheme} style={dynamicStyles.themeButton}>
            <Text style={dynamicStyles.themeButtonText}>
              {`Switch to ${themeKey === 'light' ? 'Dark' : 'Light'} Mode`}
            </Text>
          </Pressable>
        </ScrollView>
      </SafeAreaView>
    </View>
  );
}

// Static styles that don't depend on the theme
const styles = StyleSheet.create({
  scrollViewContainer: {
    flex: 1,
  },
  scrollViewContent: {
    alignItems: 'center',
    // paddingTop: 40, // Removed: SafeAreaView will handle insets for its children
  },
  // canvasContainer is removed as Canvas is now absolutely positioned
  // Note: title, overlayText, safeArea background are part of dynamicStyles
});