import React from 'react';
import { StyleSheet, View, Text, ScrollView } from 'react-native';
import {
  Canvas,
  Circle,
  RadialGradient,
  vec,
  RoundedRect,
  LinearGradient,
  Group,
  Rect,
} from '@shopify/react-native-skia';

export default function SkiaDemoScreen() {
  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Skia Magic ✨</Text>
      
      {/* Радиальный градиент */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Radial Gradient</Text>
        <Canvas style={styles.canvas}>
          <Circle cx={150} cy={100} r={80}>
            <RadialGradient
              c={vec(150, 100)}
              r={80}
              colors={['#ff6b6b', '#4ecdc4', '#45b7d1']}
            />
          </Circle>
        </Canvas>
      </View>

      {/* Glassmorphism */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Glassmorphism</Text>
        <Canvas style={styles.canvas}>
          <Rect x={0} y={0} width={300} height={200}>
            <LinearGradient
              start={vec(0, 0)}
              end={vec(300, 200)}
              colors={['#667eea', '#764ba2']}
            />
          </Rect>
          
          <Group>
            <RoundedRect
              x={50}
              y={50}
              width={200}
              height={100}
              r={16}
            >
              <LinearGradient
                start={vec(0, 0)}
                end={vec(200, 100)}
                colors={[
                  'rgba(255,255,255,0.25)',
                  'rgba(255,255,255,0.1)'
                ]}
              />
            </RoundedRect>
            
            <RoundedRect
              x={50}
              y={50}
              width={200}
              height={100}
              r={16}
              style="stroke"
              strokeWidth={1}
              color="rgba(255,255,255,0.3)"
            />
          </Group>
        </Canvas>
      </View>

      <Text style={styles.footer}>
        Работает на Android и iOS! 🚀
      </Text>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
    marginVertical: 20,
  },
  section: {
    marginBottom: 30,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 18,
    color: '#fff',
    marginBottom: 15,
    textAlign: 'center',
  },
  canvas: {
    width: 300,
    height: 200,
    alignSelf: 'center',
    borderRadius: 12,
  },
  footer: {
    fontSize: 16,
    color: '#888',
    textAlign: 'center',
    padding: 20,
  },
});