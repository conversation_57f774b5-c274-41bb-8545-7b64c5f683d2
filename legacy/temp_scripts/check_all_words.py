from pymongo import MongoClient
from dotenv import load_dotenv
import os

def check_all_words():
    # Загружаем переменные окружения
    load_dotenv()
    
    # Получаем строку подключения к MongoDB из переменных окружения
    mongo_uri = os.getenv("MONGODB_URL")
    db_name = os.getenv("DATABASE_NAME", "word_master")
    
    if not mongo_uri:
        print("Ошибка: не найдена переменная окружения MONGODB_URL")
        return
    
    try:
        # Подключаемся к MongoDB
        client = MongoClient(mongo_uri)
        db = client[db_name]
        
        # Получаем все слова
        cursor = db.words.find().sort("word", 1)
        
        print("Список всех слов в базе:")
        print("-" * 80)
        
        for doc in cursor:
            word = doc.get('word', '')
            translations = ', '.join(doc.get('translations', []))
            
            # Проверяем, является ли слово фразой (содержит пробелы или знаки препинания)
            is_phrase = ' ' in word or any(c in word for c in ',.!?;:')
            
            print(f"Слово: {word}")
            print(f"Перевод: {translations}")
            print(f"Тип: {'ФРАЗА' if is_phrase else 'Слово'}")
            
            if 'examples' in doc and doc['examples']:
                print("Примеры:")
                for i, ex in enumerate(doc['examples'], 1):
                    print(f"  {i}. EN: {ex.get('en', '')}")
                    print(f"     RU: {ex.get('ru', '')}")
            
            print("-" * 80)
        
    except Exception as e:
        print(f"Ошибка: {e}")
    finally:
        if 'client' in locals():
            client.close()

if __name__ == "__main__":
    check_all_words()
