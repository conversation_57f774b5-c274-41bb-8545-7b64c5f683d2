import os
import random
from pymongo import MongoClient
from dotenv import load_dotenv

# Загружаем переменные окружения из файла .env
load_dotenv()

# Получаем строку подключения из переменных окружения
MONGO_URI = os.getenv('MONGODB_URL')
DB_NAME = os.getenv('DATABASE_NAME', 'word_master')
COLLECTION_NAME = 'words'

if not MONGO_URI:
    print("Ошибка: Не указана строка подключения к MongoDB в переменной окружения MONGODB_URL")
    exit(1)

try:
    print(f"Подключаемся к MongoDB Atlas...")
    # Подключаемся к MongoDB Atlas
    client = MongoClient(MONGO_URI)
    db = client[DB_NAME]
    collection = db[COLLECTION_NAME]
    
    # Проверяем соединение
    client.admin.command('ping')
    print("Успешное подключение к MongoDB Atlas")
    
    # Получаем количество документов в коллекции
    count = collection.count_documents({})
    print(f"Найдено документов в коллекции: {count}")
    
    if count == 0:
        print("Коллекция пуста. Нет слов для отображения.")
    else:
        # Получаем случайный документ
        print("\nПолучаем случайное слово...")
        random_doc = collection.aggregate([{ '$sample': { 'size': 1 } }]).next()
        
        # Извлекаем concept_id из случайного документа
        concept_id = random_doc.get('concept_id')
        
        if not concept_id:
            print("У документа отсутствует concept_id.")
            print("Случайный документ:", random_doc)
        else:
            print(f"\n=== СЛУЧАЙНОЕ СЛОВО ===\n")
            print(f"Concept ID: {concept_id}")
            
            # Находим все документы с таким же concept_id
            concept_words = list(collection.find({"concept_id": concept_id}))
            print(f"Найдено вариантов перевода: {len(concept_words)}")
            
            # Группируем слова по языкам
            words_by_lang = {}
            for word_doc in concept_words:
                lang = word_doc.get('language', 'unknown')
                if lang not in words_by_lang:
                    words_by_lang[lang] = []
                words_by_lang[lang].append(word_doc)
            
            # Выводим информацию о словах по языкам
            for lang, words in words_by_lang.items():
                print(f"\n--- {lang.upper()} ---")
                for word in words:
                    print(f"\nСлово: {word.get('word')}")
                    
                    # Выводим транскрипцию, если есть
                    if 'ipa_pronunciation' in word:
                        print(f"Транскрипция: {word['ipa_pronunciation']}")
                    
                    # Выводим часть речи, если есть
                    if 'part_of_speech' in word:
                        print(f"Часть речи: {word['part_of_speech']}")
                    
                    # Выводим теги, если есть
                    if 'tags' in word and word['tags']:
                        print(f"Теги: {', '.join(word['tags'])}")
                    
                    # Выводим примеры предложений
                    if 'examples' in word and word['examples']:
                        print("\nПримеры предложений:")
                        for i, example in enumerate(word['examples'], 1):
                            print(f"{i}. {example['sentence']}")
                            if 'translation' in example:
                                print(f"   ({example['translation']})")
                            print()
                    
                    print("-" * 50)
            
            print("\n" + "=" * 50)
            
except Exception as e:
    print(f"\nОШИБКА: {e}")
    print("\nУбедитесь, что:")
    print("1. У вас есть доступ к интернету")
    print("2. Ваш IP-адрес добавлен в белый список в настройках MongoDB Atlas")
    print("3. Строка подключения в файле .env корректна")
    print(f"Используемая строка подключения: {MONGO_URI[:40]}...")
    
    if "ServerSelectionTimeoutError" in str(e):
        print("\nПохоже, что возникла проблема с подключением к серверу MongoDB.")
        print("Проверьте настройки сети и убедитесь, что ваш IP-адрес добавлен в белый список в MongoDB Atlas.")
    
finally:
    # Закрываем соединение
    if 'client' in locals():
        client.close()
        print("\nСоединение с MongoDB закрыто.")
