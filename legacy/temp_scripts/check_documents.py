import asyncio
from pymongo import MongoClient
from pprint import pprint
from dotenv import load_dotenv
import os

async def check_documents():
    # Load environment variables
    load_dotenv()
    
    # Get MongoDB connection string from environment
    mongo_uri = os.getenv("MONGODB_URL")
    db_name = os.getenv("DATABASE_NAME", "word_master")
    
    if not mongo_uri:
        print("Error: MONGODB_URL not found in environment variables")
        return
    
    try:
        # Connect to MongoDB
        client = MongoClient(mongo_uri)
        db = client[db_name]
        
        # Get first 5 documents
        print("Checking first 5 documents in 'words' collection...")
        cursor = db.words.find().limit(5)
        
        for i, doc in enumerate(cursor, 1):
            print(f"\nDocument {i}:")
            pprint(doc)
            print("\nMissing fields:")
            if "level" not in doc:
                print("- level")
            if "word" not in doc:
                print("- word")
            if "translations" not in doc:
                print("- translations")
            
    except Exception as e:
        print(f"Error: {e}")
    finally:
        if 'client' in locals():
            client.close()

if __name__ == "__main__":
    asyncio.run(check_documents())
