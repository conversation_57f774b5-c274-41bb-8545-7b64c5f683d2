from pymongo import MongoClient
from dotenv import load_dotenv
import os

def fix_documents():
    # Load environment variables
    load_dotenv()
    
    # Get MongoDB connection string from environment
    mongo_uri = os.getenv("MONGODB_URL")
    db_name = os.getenv("DATABASE_NAME", "word_master")
    
    if not mongo_uri:
        print("Error: MONGODB_URL not found in environment variables")
        return
    
    try:
        # Connect to MongoDB
        client = MongoClient(mongo_uri)
        db = client[db_name]
        
        # Find documents missing the level field
        query = {"level": {"$exists": False}}
        cursor = db.words.find(query)
        
        updated_count = 0
        
        for doc in cursor:
            # Try to extract level from tags
            level = None
            if 'tags' in doc and isinstance(doc['tags'], list):
                for tag in doc['tags']:
                    if isinstance(tag, str) and tag.upper() in ['A1', 'A2', 'B1', 'B2', 'C1', 'C2']:
                        level = tag.upper()
                        break
            
            # If no level found in tags, default to A1
            if not level:
                level = 'A1'
            
            # Update the document
            result = db.words.update_one(
                {"_id": doc["_id"]},
                {"$set": {"level": level}}
            )
            
            if result.modified_count > 0:
                updated_count += 1
                print(f"Updated document {doc['_id']} with level: {level}")
        
        print(f"\nSuccessfully updated {updated_count} documents with missing level field.")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        if 'client' in locals():
            client.close()

if __name__ == "__main__":
    fix_documents()
