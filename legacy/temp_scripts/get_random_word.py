import os
import random
from pymongo import MongoClient
from dotenv import load_dotenv

# Загружаем переменные окружения из файла .env, если он существует
load_dotenv()

# Подключение к MongoDB
MONGO_URI = os.getenv('MONGODB_URI', 'mongodb://localhost:27017/')
DB_NAME = os.getenv('MONGODB_DB', 'enapp')
COLLECTION_NAME = 'words'

try:
    # Подключаемся к MongoDB
    client = MongoClient(MONGO_URI)
    db = client[DB_NAME]
    collection = db[COLLECTION_NAME]
    
    # Получаем количество документов в коллекции
    count = collection.count_documents({})
    
    if count == 0:
        print("Коллекция пуста. Нет слов для отображения.")
    else:
        # Получаем случайный документ
        random_doc = collection.aggregate([{ '$sample': { 'size': 1 } }]).next()
        
        # Извлекаем concept_id из случайного документа
        concept_id = random_doc.get('concept_id')
        
        if not concept_id:
            print("У документа отсутствует concept_id.")
        else:
            # Находим все документы с таким же concept_id
            concept_words = list(collection.find({"concept_id": concept_id}))
            
            print(f"\n=== Случайное слово ===\n")
            
            # Группируем слова по языкам
            words_by_lang = {}
            for word_doc in concept_words:
                lang = word_doc.get('language', 'unknown')
                if lang not in words_by_lang:
                    words_by_lang[lang] = []
                words_by_lang[lang].append(word_doc)
            
            # Выводим информацию о словах по языкам
            for lang, words in words_by_lang.items():
                print(f"\n--- {lang.upper()} ---")
                for word in words:
                    print(f"\nСлово: {word.get('word')}")
                    
                    # Выводим транскрипцию, если есть
                    if 'ipa_pronunciation' in word:
                        print(f"Транскрипция: {word['ipa_pronunciation']}")
                    
                    # Выводим часть речи, если есть
                    if 'part_of_speech' in word:
                        print(f"Часть речи: {word['part_of_speech']}")
                    
                    # Выводим примеры предложений
                    if 'examples' in word and word['examples']:
                        print("\nПримеры предложений:")
                        for i, example in enumerate(word['examples'], 1):
                            print(f"{i}. {example['sentence']}")
                            if 'translation' in example:
                                print(f"   ({example['translation']})")
                            print()
                    
                    print("-" * 50)
            
            print("\n" + "=" * 50)
            
except Exception as e:
    print(f"Произошла ошибка при подключении к MongoDB: {e}")
finally:
    # Закрываем соединение
    if 'client' in locals():
        client.close()
