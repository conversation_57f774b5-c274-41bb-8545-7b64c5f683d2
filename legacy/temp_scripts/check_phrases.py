from pymongo import MongoClient
from dotenv import load_dotenv
import os

def check_phrases():
    # Загружаем переменные окружения
    load_dotenv()
    
    # Получаем строку подключения к MongoDB из переменных окружения
    mongo_uri = os.getenv("MONGODB_URL")
    db_name = os.getenv("DATABASE_NAME", "word_master")
    
    if not mongo_uri:
        print("Ошибка: не найдена переменная окружения MONGODB_URL")
        return
    
    try:
        # Подключаемся к MongoDB
        client = MongoClient(mongo_uri)
        db = client[db_name]
        
        # Находим слова, которые выглядят как фразы (содержат пробелы или знаки препинания)
        query = {
            "$or": [
                {"word": {"$regex": r"[.,!?]"}},  # содержит знаки препинания
                {"word": {"$regex": r"\s"}}       # содержит пробелы
            ]
        }
        
        cursor = db.words.find(query)
        count = 0
        
        print("Найдены следующие фразы (а не отдельные слова):")
        print("-" * 80)
        
        for doc in cursor:
            count += 1
            print(f"ID: {doc['_id']}")
            print(f"Слово/Фраза: {doc['word']}")
            print(f"Перевод: {', '.join(doc['translations'])}")
            if 'examples' in doc and doc['examples']:
                print("Примеры:")
                for ex in doc['examples']:
                    print(f"  EN: {ex.get('en', '')}")
                    print(f"  RU: {ex.get('ru', '')}")
            print("-" * 80)
        
        print(f"\nВсего найдено фраз: {count}")
        
    except Exception as e:
        print(f"Ошибка: {e}")
    finally:
        if 'client' in locals():
            client.close()

if __name__ == "__main__":
    check_phrases()
