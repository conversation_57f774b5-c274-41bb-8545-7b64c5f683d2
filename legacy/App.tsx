import React, { useState, useRef, useCallback, useEffect } from 'react';
import { 
  StyleSheet, 
  View, 
  Text, 
  TextInput, 
  TouchableOpacity, 
  SafeAreaView, 
  ScrollView, 
  Animated, 
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  StatusBar,
  Vibration
} from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { BlurView } from 'expo-blur';
// Animation utilities
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { Canvas, RadialGradient, Rect, vec, Circle, Image, useImage, BackdropBlur, Blur, Group, rect, rrect, Skia } from '@shopify/react-native-skia';
import * as Haptics from 'expo-haptics';

// Функция для воспроизведения тактильного отклика
const triggerHaptic = () => {
  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium).catch(() => {});
};

// Добавляем кастомную анимацию тряски
const shakeAnimation = {
  0: {
    translateX: 0,
  },
  0.1: {
    translateX: -10,
  },
  0.2: {
    translateX: 10,
  },
  0.3: {
    translateX: -8,
  },
  0.4: {
    translateX: 8,
  },
  0.5: {
    translateX: -5,
  },
  0.6: {
    translateX: 5,
  },
  0.7: {
    translateX: -3,
  },
  0.8: {
    translateX: 3,
  },
  0.9: {
    translateX: -1,
  },
  1: {
    translateX: 0,
  },
};

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
const baseScreenSize = Math.min(screenWidth, screenHeight);

// Настройки для градиентов
const spheres = [
  {
    id: 'sphere-1',
    cx: screenWidth * 0.8,
    cy: screenHeight * 0.1,
    r: baseScreenSize * 0.8,
    colors: ['rgba(50, 40, 120, 0.08)', 'rgba(50, 40, 120, 0)'] // Еще темнее и прозрачнее
  },
  {
    id: 'sphere-2',
    cx: screenWidth * 0.3,
    cy: screenHeight * 0.3,
    r: baseScreenSize * 0.7,
    colors: ['rgba(120, 100, 20, 0.06)', 'rgba(120, 100, 20, 0)'] // Еще темнее и прозрачнее
  },
  {
    id: 'sphere-3',
    cx: screenWidth * 0.15,
    cy: screenHeight * 0.6,
    r: baseScreenSize * 1.8,
    colors: ['rgba(0, 80, 80, 0.06)', 'rgba(0, 80, 80, 0)'] // Еще темнее и прозрачнее
  },
  {
    id: 'sphere-4',
    cx: screenWidth * 0.95,
    cy: screenHeight * 0.75,
    r: baseScreenSize * 1.0,
    colors: ['rgba(90, 40, 70, 0.06)', 'rgba(90, 40, 70, 0)'] // Еще темнее и прозрачнее
  }
];

// Demo data for cards
const demoCards = [
  {
    id: 1,
    english: "The cat quickly ran across the street to catch the mouse.",
    translation: "Кот быстро побежал через дорогу, чтобы поймать мышь.",
    word: "ran",
    translationWord: "побежал",
    wordInfo: {
      type: 'глагол',
      form: 'прошедшее время',
      transcription: 'pəbʲɪˈʐal'
    },
    progress: 3
  },
  {
    id: 2,
    english: "She has been working on this project since last month.",
    translation: "Она работает над этим проектом с прошлого месяца.",
    word: "working",
    translationWord: "работает",
    wordInfo: {
      type: 'глагол',
      form: 'настоящее длительное время',
      transcription: 'rɐˈbotəjɪt'
    },
    progress: 1
  }
];

export default function App() {
  const [currentCardIndex, setCurrentCardIndex] = useState(0);
  const [currentCardNumber, setCurrentCardNumber] = useState(5); // Начинаем с 5-й карточки
  const totalCardsInDeck = 20; // Общее количество карточек в колоде
  const currentCard = demoCards[currentCardIndex];
  const [userInput, setUserInput] = useState('');
  const [isCorrect, setIsCorrect] = useState<boolean | null>(null);
  const [showHint, setShowHint] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [wordProgress, setWordProgress] = useState(currentCard.progress);
  const inputRef = useRef<TextInput>(null);
  const feedbackAnim = useRef(new Animated.Value(0)).current;

  // Animation values and transform state
  const slideAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const shakeAnim = useRef(new Animated.Value(0)).current;

  // Split the sentence into parts for display
  const [beforeBlank, afterBlank] = React.useMemo(() => {
    const index = currentCard.english.indexOf(currentCard.word);
    return [
      currentCard.english.substring(0, index),
      currentCard.english.substring(index + currentCard.word.length)
    ];
  }, [currentCard.english, currentCard.word]);

  // Split the translation to highlight the word
  const translationParts = React.useMemo(() => {
    const index = currentCard.translation.indexOf(currentCard.translationWord);
    return [
      currentCard.translation.substring(0, index),
      currentCard.translationWord,
      currentCard.translation.substring(index + currentCard.translationWord.length)
    ];
  }, [currentCard.translation, currentCard.translationWord]);

  // Calculate width based on the target word length
  const inputWidth = currentCard.word.length * 12 + 16;

  // Function to animate card out
  const animateCardOut = useCallback(() => {
    // Reset animations
    slideAnim.setValue(0);
    
    // Animate out to the left with slight rotation
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: -screenWidth,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 0.9,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(rotateAnim, {
        toValue: -5,
        duration: 300,
        useNativeDriver: true,
      })
    ]).start(() => {
      // After animation completes, update to next card and reset
      setCurrentCardIndex(prevIndex => (prevIndex + 1) % demoCards.length);
      setCurrentCardNumber(prev => (prev % totalCardsInDeck) + 1);
      setWordProgress(demoCards[(currentCardIndex + 1) % demoCards.length].progress);
      
      // Reset animations for next card
      slideAnim.setValue(screenWidth);
      scaleAnim.setValue(0.9);
      rotateAnim.setValue(5);
      
      // Animate new card in
      Animated.parallel([
        Animated.spring(slideAnim, {
          toValue: 0,
          useNativeDriver: true,
          friction: 8,
          tension: 60,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          useNativeDriver: true,
          friction: 8,
          tension: 60,
        }),
        Animated.spring(rotateAnim, {
          toValue: 0,
          useNativeDriver: true,
          friction: 8,
          tension: 60,
        })
      ]).start();
      
      // Focus input after animation
      setTimeout(() => {
        inputRef.current?.focus();
      }, 50);
    });
  }, [currentCardIndex, demoCards.length]);
  
  // Function to move to the next card
  const goToNextCard = useCallback(() => {
    setUserInput('');
    setIsCorrect(null);
    setShowHint(false);
    
    // Start the card out animation
    animateCardOut();
  }, [animateCardOut]);

  const checkAnswer = useCallback(async () => {
    const correct = userInput.toLowerCase().trim() === currentCard.word.toLowerCase();
    setIsCorrect(correct);
    
    if (correct) {
      // Один четкий тактильный отклик для правильного ответа
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    } else {
      // Сбрасываем анимацию тряски
      shakeAnim.setValue(0);
      
      // Вибрация для неправильного ответа (500мс)
      const hapticSequence = async () => {
        Vibration.vibrate(500);
      };
      
      // Start both animations and haptic sequence
      Animated.sequence([
        // Shake left
        Animated.timing(shakeAnim, {
          toValue: -10,
          duration: 50,
          useNativeDriver: true,
        }),
        // Shake right
        Animated.timing(shakeAnim, {
          toValue: 10,
          duration: 50,
          useNativeDriver: true,
        }),
        // Shake left
        Animated.timing(shakeAnim, {
          toValue: -10,
          duration: 50,
          useNativeDriver: true,
        }),
        // Shake right
        Animated.timing(shakeAnim, {
          toValue: 10,
          duration: 50,
          useNativeDriver: true,
        }),
        // Return to center
        Animated.timing(shakeAnim, {
          toValue: 0,
          duration: 50,
          useNativeDriver: true,
        })
      ]).start();
      
      // Start haptic sequence
      hapticSequence();
    }
    
    // Move to the next card after delay (longer for incorrect answers to see the shake)
    const delay = correct ? 1000 : 1500;
    setTimeout(() => {
      goToNextCard();
    }, delay);
  }, [userInput, currentCard.word, goToNextCard]);

  // Auto-focus input when component mounts or card changes
  useEffect(() => {
    inputRef.current?.focus();
  }, [currentCardIndex]);

  // Animate feedback when isCorrect changes
  useEffect(() => {
    if (isCorrect !== null) {
      // Reset and animate in
      feedbackAnim.setValue(0);
      Animated.spring(feedbackAnim, {
        toValue: 1,
        useNativeDriver: true,
        friction: 8,
        tension: 40,
      }).start();
    }
  }, [isCorrect]);

  return (
    <SafeAreaProvider>
      <View style={styles.container}>
        <StatusBar barStyle="light-content" />
        
        {/* Gradient Background */}
        <View style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, backgroundColor: '#000000' }}>
          <Canvas style={{ flex: 1 }}>
            {[
              {
                id: 'sphere-1',
                cx: screenWidth * 0.15,
                cy: screenHeight * 0.2,
                r: baseScreenSize * 1.2,
                colors: ['rgba(170, 70, 255, 0.3)', 'rgba(170, 70, 255, 0)']
              },
              {
                id: 'sphere-2',
                cx: screenWidth * 0.85,
                cy: screenHeight * 0.3,
                r: baseScreenSize * 0.7,
                colors: ['rgba(255, 220, 50, 0.3)', 'rgba(255, 220, 50, 0)']
              },
              {
                id: 'sphere-3',
                cx: screenWidth * 0.15,
                cy: screenHeight * 0.6,
                r: baseScreenSize * 1.8,
                colors: ['rgba(0, 200, 200, 0.3)', 'rgba(0, 200, 200, 0)']
              },
              {
                id: 'sphere-4',
                cx: screenWidth * 0.95,
                cy: screenHeight * 0.75,
                r: baseScreenSize * 1.0,
                colors: ['rgba(220, 100, 170, 0.3)', 'rgba(220, 100, 170, 0)']
              }
            ].map((sphere) => (
              <Circle
                key={sphere.id}
                cx={sphere.cx}
                cy={sphere.cy}
                r={sphere.r}
              >
                <RadialGradient
                  c={vec(sphere.cx, sphere.cy)}
                  r={sphere.r}
                  colors={sphere.colors}
                />
              </Circle>
            ))}
            {/* Полупрозрачный черный оверлей для затемнения сфер */}
            <Rect 
              x={0} 
              y={0} 
              width={screenWidth} 
              height={screenHeight} 
              color="rgba(0, 0, 0, 0.5)" 
              blendMode="srcOver"
            />
          </Canvas>
        </View>
        
        <SafeAreaView style={styles.safeArea}>
          {/* Progress Bar Header */}
          <View style={styles.progressHeader}>
            <View style={styles.progressContainer}>
              <Text style={styles.progressText}>
                {currentCardNumber} of {totalCardsInDeck} cards
              </Text>
              <View style={styles.progressBarContainer}>
                <Animated.View style={[styles.progressBar, { width: `${(currentCardNumber / totalCardsInDeck) * 100}%` }]}>
                  <LinearGradient
                    colors={['#7fb4ff', '#4a7dff']}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    style={styles.progressGradient}
                  />
                </Animated.View>
              </View>
            </View>
          </View>
          
          <ScrollView 
            contentContainerStyle={styles.scrollView} 
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
          >
            {/* Main content */}
            <View style={styles.content}>
              {/* Main Card */}
              <Animated.View 
                style={[
                  styles.card,
                  {
                    transform: [
                      { 
                        translateX: Animated.add(
                          slideAnim,
                          shakeAnim
                        )
                      },
                      { scale: scaleAnim },
                      { 
                        rotate: rotateAnim.interpolate({
                          inputRange: [-360, 360],
                          outputRange: ['-360deg', '360deg']
                        })
                      }
                    ]
                  }
                ]}
              >
                <View style={StyleSheet.absoluteFill}>
                  <Canvas style={{ flex: 1 }}>
                    <BackdropBlur blur={20} clip={rrect(rect(0, 0, screenWidth - 16, 400), 20, 20)}>
                      <Rect x={0} y={0} width={screenWidth} height={screenHeight} color="rgba(20, 20, 35, 0.25)" />
                      {/* Большая сфера с эффектом тумана */}
                      <Group>
                        <Circle cx={-50} cy={450} r={350}>
                          <RadialGradient
                            c={vec(-50, 450)}  // Центр сферы
                            r={350}            // Увеличиваем радиус сферы
                            colors={[
                              'rgba(120, 200, 255, 0.4)',  // Более прозрачный центр
                              'rgba(120, 200, 255, 0.25)',
                              'rgba(120, 200, 255, 0.1)',
                              'rgba(120, 200, 255, 0.02)',
                              'rgba(120, 200, 255, 0)'
                            ]}
                            positions={[0, 0.2, 0.5, 0.8, 1]}
                          />
                        </Circle>
                      </Group>
                      <Blur blur={15} />
                    </BackdropBlur>
                  </Canvas>
                </View>
                <View style={styles.cardContent}>
                                  {/* Russian translation with grammar hint */}
                  <View style={styles.translationContainer}>
                    <View style={styles.grammarHintContainer}>
                      <Text style={styles.grammarHintText}>
                        <Text style={styles.wordWithHint}>{translationParts[1]}</Text>
                        <Text style={styles.wordHintSeparator}> — </Text>
                        {currentCard.wordInfo.type}{currentCard.wordInfo.form ? `, ${currentCard.wordInfo.form}` : ''}
                      </Text>
                    </View>
                    <Text style={styles.translation}>
                      {translationParts[0]}
                      <Text style={styles.highlightedWord}>{translationParts[1]}</Text>
                      {translationParts[2]}
                    </Text>
                    <View style={styles.dividerContainer}>
                      <View style={[styles.dividerLine, styles.dividerTop]} />
                      <View style={[styles.dividerLine, styles.dividerBottom]} />
                    </View>
                  </View>

                  {/* English sentence with inline input */}
                  <View style={styles.sentenceContainer}>
                    <Text style={styles.sentence}>
                      {beforeBlank}
                      {' '}{/* Add space before input */}
                      <View style={[styles.inputWrapper, { width: inputWidth }]}>
                        <TextInput
                          ref={inputRef}
                          style={[
                            styles.inlineInput,
                            isFocused && styles.inlineInputFocused,
                            isCorrect === true && styles.inlineInputCorrect,
                            isCorrect === false && styles.inlineInputIncorrect,
                            { width: inputWidth }
                          ]}
                          value={userInput}
                          onChangeText={setUserInput}
                          placeholder=""
                          placeholderTextColor="transparent"
                          autoCapitalize="none"
                          autoCorrect={false}
                          autoFocus
                          onFocus={() => setIsFocused(true)}
                          onBlur={() => setIsFocused(false)}
                          onSubmitEditing={checkAnswer}
                          returnKeyType="done"
                          selectionColor="rgba(127, 180, 255, 0.3)"
                          cursorColor="#7fb4ff"
                          underlineColorAndroid="transparent"
                          caretHidden={false}
                        />
                      </View>
                      {' '}{/* Add space after input */}
                      {afterBlank}
                    </Text>
                  </View>
                  
                  {/* Word progress indicator */}
                  <View style={styles.wordProgressContainer}>
                    {[1, 2, 3, 4, 5].map((step) => {
                      let color = '#3a3a4a'; // Default gray
                      if (step <= wordProgress) {
                        // Set color based on progress
                        if (wordProgress <= 1) color = '#FFD700'; // Yellow
                        else if (wordProgress === 2) color = '#FFA500'; // Orange
                        else if (wordProgress === 3) color = '#7fb4ff'; // Blue
                        else if (wordProgress === 4) color = '#4a7dff'; // Darker blue
                        else color = '#4CAF50'; // Green
                      }
                      return (
                        <View 
                          key={step}
                          style={[
                            styles.wordProgressStep,
                            { backgroundColor: color }
                          ]}
                        />
                      );
                    })}
                  </View>

                  {/* Hint icon */}
                  <View style={styles.hintIconContainer}>
                    <TouchableOpacity 
                      onPress={() => setShowHint(!showHint)}
                      style={styles.hintIconButton}
                    >
                      <Ionicons 
                        name={showHint ? "bulb" : "bulb-outline"} 
                        size={24} 
                        color="rgba(255, 255, 255, 0.6)" 
                      />
                    </TouchableOpacity>
                  </View>
                </View>
              </Animated.View>

              {/* Moved feedback container outside the card */}
              {isCorrect !== null && (
                <Animated.View 
                  style={[
                    styles.feedbackContainer,
                    isCorrect ? styles.feedbackCorrect : styles.feedbackIncorrect,
                    { 
                      marginTop: 16, 
                      marginBottom: 24,
                      opacity: feedbackAnim,
                      transform: [
                        { 
                          translateY: feedbackAnim.interpolate({
                            inputRange: [0, 1],
                            outputRange: [20, 0]
                          })
                        }
                      ]
                    }
                  ]}
                >
                  <Ionicons 
                    name={isCorrect ? "checkmark-circle" : "close-circle"} 
                    size={24} 
                    color={isCorrect ? "#4caf50" : "#f44336"} 
                  />
                  <Text style={styles.feedbackText}>
                    {isCorrect ? 'Correct! Well done!' : 'Try again!'}
                  </Text>
                </Animated.View>
              )}
              
            </View>
          </ScrollView>
        </SafeAreaView>
      </View>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  // Layout
  container: {
    flex: 1,
    backgroundColor: '#000000', // Полностью черный фон
  },
  safeArea: {
    flex: 1,
    paddingHorizontal: 8, // Уменьшаем горизонтальные отступы
    paddingTop: 80, // Уменьшаем отступ сверху еще больше
    paddingBottom: 24, // Оставляем небольшой отступ снизу
  },
  scrollView: {
    flex: 1,
    padding: 0,
  },
  content: {
    flex: 1,
    paddingHorizontal: 8, // Уменьшаем горизонтальные отступы
    paddingTop: 16,
  },
  
  // Cards
  card: {
    borderRadius: 14, // Уменьшаем скругление углов
    overflow: 'hidden',
    marginBottom: 16,
    marginTop: 10,
    backgroundColor: 'rgba(20, 20, 35, 0.25)', // Более темный и менее прозрачный
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.08)', // Более прозрачная обводка
    width: '100%',
  },

  cardOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(30, 30, 46, 0.2)', // Полупрозрачный оверлей
  },
  cardContent: {
    position: 'relative',
    zIndex: 1,
    paddingTop: 44, // Отступ сверху
    paddingHorizontal: 32, // Боковые отступы
    paddingBottom: 24, // Уменьшаем отступ снизу
  },
  cardBlur: {
    padding: 20,
  },
  
  // Progress Header
  progressHeader: {
    backgroundColor: 'rgba(20, 20, 35, 0.7)', // Полупрозрачный темный фон
    paddingTop: 0, // Убираем отступ сверху
    paddingBottom: 30, // Добавляем отступ снизу
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  progressContainer: {
    width: '100%',
    maxWidth: 500, // Ограничиваем максимальную ширину
    alignSelf: 'center',
    paddingHorizontal: 16,
    paddingTop: 0, // Убираем отступ сверху
  },
  progressText: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
    textAlign: 'center',
  },
  progressBarContainer: {
    width: '100%',
    height: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    borderRadius: 4,
  },
  progressGradient: {
    flex: 1,
    borderRadius: 6,
    height: '100%',
  },
  // Hint icon styles
  hintIconContainer: {
    position: 'absolute',
    bottom: 24, // Увеличиваем отступ снизу
    right: 24, // Смещаем от правого края
    top: undefined, // Сбрасываем top
  },
  hintIconButton: {
    padding: 8,
  },
  hintIcon: {
    marginRight: 0, // Reset margin since we're not showing text
  },
  
  // Header
  header: {
    backgroundColor: 'rgba(10, 10, 20, 0.3)',
    padding: 16,
    borderRadius: 16,
    marginBottom: 24,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  headerBlur: {
    borderRadius: 12,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
  },
  
  // Sentence and input
  sentenceContainer: {
    marginTop: 4, // Reduced top margin
    marginBottom: 0,
    paddingBottom: 8,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'center',
    height: 32,
    position: 'relative',
    verticalAlign: 'bottom',
    marginHorizontal: 12, // Увеличили отступы по бокам
    display: 'flex' as 'flex',
  },
  sentence: {
    fontSize: 22,
    color: '#e6e4ff', // Светлый голубовато-пурпурный оттенок
    lineHeight: 34,
    textAlign: 'left', // Выравнивание по левому краю
    marginBottom: 12,
    marginTop: 4,
    textAlignVertical: 'center',
    fontWeight: '400',
    paddingHorizontal: 2, // Минимальные отступы по бокам
  },
  inlineInput: {
    color: '#f0f5ff', // Тот же цвет, что и у английского текста
    fontSize: 22,
    lineHeight: 28,
    height: 28,
    padding: 0,
    paddingHorizontal: 5,
    margin: 0,
    marginBottom: -7,
    marginHorizontal: 5,
    textAlign: 'center',
    minWidth: 60,
    backgroundColor: 'rgba(200, 210, 230, 0.08)', // Очень светлый голубоватый фон
    borderBottomWidth: 2, // Увеличили толщину нижней границы
    borderBottomColor: 'rgba(127, 180, 255, 0.6)', // Голубой цвет границы
    borderWidth: 0,
    borderRadius: 4,
    textAlignVertical: 'center',
    includeFontPadding: false,
  },
  inlineInputFocused: {
    backgroundColor: 'rgba(180, 200, 240, 0.15)', // Легкий голубой оттенок при фокусе
    borderBottomColor: '#7fb4ff', // Голубой акцент при фокусе
    borderBottomWidth: 2.5, // Чуть толще при фокусе
  },
  inlineInputCorrect: {
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    borderBottomColor: '#4caf50', // Зеленое подчеркивание для правильного ответа
  },
  inlineInputIncorrect: {
    backgroundColor: 'rgba(244, 67, 54, 0.1)',
    borderBottomColor: '#f44336', // Красное подчеркивание для неправильного ответа
  },
  highlightedWord: {
    color: '#7fb4ff', // Тот же голубой, что и у инпута
    fontWeight: '500',
    backgroundColor: 'rgba(127, 180, 255, 0.12)', // Светло-голубой фон
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    fontStyle: 'normal',
    textDecorationLine: 'underline',
    textDecorationStyle: 'dotted',
    textDecorationColor: 'rgba(127, 180, 255, 0.7)', // Полупрозрачный голубой
  },
  correctAnswer: {
    position: 'absolute',
    bottom: -24,
    left: 0,
    right: 0,
    color: '#f44336',
    fontSize: 12,
    textAlign: 'center',
  },
  
  // Grammar hint styles
  grammarHintContainer: {
    marginBottom: 8,
    paddingHorizontal: 6, // Такой же отступ, как у текста
    borderLeftWidth: 2,
    borderLeftColor: 'rgba(127, 180, 255, 0.3)', // Левая граница для выделения
    paddingLeft: 8,
    alignSelf: 'flex-start', // Выравнивание по левому краю
    width: '100%', // Занимает всю ширину
  },
  grammarHintText: {
    color: 'rgba(160, 180, 220, 0.6)',
    fontSize: 11, // Уменьшаем размер шрифта
    lineHeight: 15, // Уменьшаем межстрочный интервал
  },
  transcription: {
    opacity: 0.6,
    fontSize: 11,
  },
  wordWithHint: {
    fontWeight: '500',
    color: 'rgba(200, 210, 240, 0.9)',
  },
  wordHintSeparator: {
    color: 'rgba(160, 180, 220, 0.4)',
  },
  
  // Word progress indicator
  wordProgressContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-start', // Выравниваем по левому краю
    marginTop: 35, // Увеличиваем отступ сверху
    marginBottom: 18, // Увеличиваем отступ снизу
    marginLeft: 2, // Смещаем левее
    paddingHorizontal: 0, // Убираем горизонтальные отступы
  },
  wordProgressStep: {
    width: 24,
    height: 4,
    borderRadius: 2,
    marginHorizontal: 2,
    backgroundColor: '#3a3a4a',
  },
  
  // Translation
  translationContainer: {
    marginTop: 0,
    padding: 0,
    paddingHorizontal: 0,  // Минимальный отступ по бокам
    backgroundColor: 'transparent',
    alignSelf: 'flex-start', // Выравниваем по левому краю
    width: '100%',
  },
  translation: {
    position: 'relative',
    fontSize: 16,
    color: 'rgba(220, 220, 240, 0.8)',
    fontStyle: 'normal',
    textAlign: 'left',
    lineHeight: 24,
    paddingTop: 4,
    paddingBottom: 8,
    paddingHorizontal: 2, // Минимальный отступ для текста
    width: '100%',
    fontWeight: '300',
  },
  // Стили для разделительной линии с 3D эффектом
  dividerContainer: {
    width: '40%',
    marginTop: 8,    // Reduced space above divider
    marginBottom: 4,  // Reduced space below divider
    alignSelf: 'center',
    height: 2,
  },
  dividerLine: {
    width: '100%',
    height: 1,
    position: 'absolute',
    left: 0,
    right: 0,
  },
  dividerTop: {
    top: 0,
    backgroundColor: 'rgba(200, 200, 200, 0.08)',
    shadowColor: 'rgba(0, 0, 0, 0.2)',
    shadowOffset: { width: 0, height: 1 },
    shadowRadius: 1,
    elevation: 1,
  },
  dividerBottom: {
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  // Buttons
  hintButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 12,
    marginBottom: 16,
    backgroundColor: 'rgba(157, 78, 221, 0.1)',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(157, 78, 221, 0.3)',
  },
  hintButtonText: {
    color: '#9d4edd',
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '500',
  },
  
  // Feedback
  feedbackContainer: {
    marginTop: 8,
    padding: 14,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
    alignSelf: 'stretch',
    marginHorizontal: 16,
  },
  feedbackCorrect: {
    backgroundColor: 'rgba(76, 175, 80, 0.15)',
    borderColor: 'rgba(76, 175, 80, 0.3)',
  },
  feedbackIncorrect: {
    backgroundColor: 'rgba(244, 67, 54, 0.15)',
    borderColor: 'rgba(244, 67, 54, 0.3)',
  },
  feedbackText: {
    color: '#fff',
    marginLeft: 10,
    fontSize: 15,
    fontWeight: '500',
  },
  
  // Bottom info
  infoContainer: {
    backgroundColor: 'rgba(30, 30, 46, 0.5)',
    borderRadius: 20,
    overflow: 'hidden',
  },
  infoBlur: {
    padding: 16,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoText: {
    color: 'rgba(255, 255, 255, 0.8)',
    marginLeft: 12,
    fontSize: 14,
  },
  
  // Background container
  backgroundContainer: {
    ...StyleSheet.absoluteFillObject,
    zIndex: -1,
    backgroundColor: '#1e1e2e', // Темно-фиолетовый фон на случай, если градиент не загрузится
  },
});