{"timestamp": "2025-06-19T07:10:47.940429", "backend": {"status": "completed", "details": {}, "return_code": 4, "stdout": "", "stderr": "ImportError while loading conftest '/Users/<USER>/MEMO/backend/tests/conftest.py'.\ntests/conftest.py:3: in <module>\n    from fastapi.testclient import TestClient\nvenv_zak/lib/python3.9/site-packages/fastapi/testclient.py:1: in <module>\n    from starlette.testclient import TestClient as TestClient  # noqa\nvenv_zak/lib/python3.9/site-packages/starlette/testclient.py:32: in <module>\n    raise RuntimeError(\nE   RuntimeError: The starlette.testclient module requires the httpx package to be installed.\nE   You can install this with:\nE       $ pip install httpx\n"}, "frontend": {"status": "completed", "details": {}, "return_code": 1, "stdout": "", "stderr": "npm error Missing script: \"test\"\nnpm error\nnpm error To see a list of scripts, run:\nnpm error   npm run\nnpm error A complete log of this run can be found in: /Users/<USER>/.npm/_logs/2025-06-18T23_10_48_900Z-debug-0.log\n"}, "summary": {"total": 0, "passed": 0, "failed": 0}}