- Фокусируйся на одной задаче. В процессе должна быть только одна задача. Иначе будет барьер. 
  - На день должна быть одна задача (или набор нескольких, заранее беру их в работу). Не большая и не скучная. Вот на ней и фокусируйся. 
  - Очень важно вообще убрать все лишнее. Особенно notion, визуализации. Утром начинай день с компа Ерика. Если усадить себя, поставить перед собой материалы для работы, ты увлечься намного легче. А чтобы по ВЧ не проседать делай тренировку ВЧ во время перерывов. Ремайндерсы может тоже. Не успел - значит в следующий перерыв. 
  - Сворачивай все задачи (визуально), кроме текущей. Чтобы не отвлекаться. 
- Если что-то не так -  улучшай систему продуктивности. Радуйся, если нет продуктивнсоти, это прекрасный повод улучшить. 
  - Важно действительно ставить микро-задачи. Вот казалось бы, прочитать инструкцию, даже писать не надо. А займет минут 20. И даже это не простая задача. Может и ее надо дробить. 
- Идеальный процесс - пищещь список мелких задач в тодо листе (разбив на крупные блоки примерно на день) и просто делаешь друг за другом. Мозг обожает так работать. За день можно 10-20 задач выполнять. И то, как выполнять. Просто надиктовываешь что думаешь, если не знаешь, то спрашиваешь ИИ как лучше это сделать. 
  - Лайфхак. Пока он думает над одной задачей - ты надиктовываешь промт для другой. И так ьыстро двигаешься. 



# v1. 🎯 MVP
## 🟡 TASKS BEFORE MVP (backlog unsorted)

### [ ] Hint
**🧠 Sequential thinking - средний приоритет**
у нас есть подсказка с лампочкой. Но при ее нажатии просто вставляется первая буква в инпут. Это непонятно для юзеров. Давай сделаем более очевидно. Типа слово будет так (например) - "W___". Причем чтобы было кол-во "_" зависело от оставшегося кол-ва букв в слове. И цветом акцент сделать. Как у нас инпут выделяется при правильном (зеленый) или непраивльном ответе (красный). В таком же стиле, только фиолетовым например. У нас есть легкий нежный пастельный фиолетовый. Как зефирный.

- Свечение на инпуте в момент нажатия на кнопку "подсказка". Фиолетовое свечение вокруг инпута

### [ ] If nomore cards - message
Now - nothing. Then error

### [ ] Unput size

### [ ] Кол-во неправильных ответов в слове в user_progress
Чтобы потом сортировать по этому 
Но вроде уже есть

### [x] Прибраться в проекте
📖 **См. также:** [Git и Filesystem MCP серверы](md/mcp.md#git-mcp-server---для-управления-кодом)

- Почистить мусор, удалить временные файлы
- Актуализировать документацию

### [ ] Автоподсказка через 10 секунд
**🧠 Sequential thinking - низкий приоритет**
Вставляется первая буква
- Возможность отключить в настройках

### 6 [ ] Нашли ошибку?
- Где ошибка? В родной фразу или в изучаемой? 
- Баллы разработчика (будут вознаграждены)

### 7 [ ] Auth (готовое решение)

### 8 [ ] Локализация для других языков


### [ ] Микро-задачи
- Возможность изменить пароль
- Возможность изменить картинку профиля
- целевое предложение обрезается на нижних символах таких как "g"




----
Согласно вашей документации, вам больше подойдут:

MongoDB MCP Server - для оптимизации запросов БД
Memory MCP Server - для системы интервального повторения
Git MCP Server - для управления кодом
Filesystem MCP Server - для организации проекта



## SPRINT 1 (РАЗРАБОКТА ФУНКЦИОНАЛА) ✅

### 4.1 [x] Стрелки в картчоках на экране тренировки

1. [x] Кнопка "назад" не должна просто переходить к следующей карточке. Она должна показывать уже заполненную предыдущую карточку, чтобы посмотреть, как там все было, то есть как бы, как в истории карточка была. С заполненым инпутом. Это нужно для того, чтобы человек посмотрел, как слово выглядит правильно и запомнил, если ему не хватило задержки. В дальнейшем мы здесь также выведем ошибку, которую он совершил, но это позже. Где-нибудь в комментарии об этом скажи, что в дальнейшем нужно сюда будет добавить на предыдущую такую карточку историческую, то есть ошибку пользователя, если она была.

✅ **РЕАЛИЗОВАНО**: Простая система "назад" - запоминаем только предыдущую карточку с правильным ответом. Кнопка неактивна пока нет предыдущей карточки. При нажатии показывает предыдущую карточку с заполненным правильным ответом и индикацией "Предыдущая карточка". Кнопка "вперед" возвращает к текущей карточке с восстановлением состояния.

🔮 **TODO в будущем**: Добавить отображение ошибки пользователя на предыдущей карточке, если она была.

2. [x] Кнопка "вперед" активна только после ввода пользовтеля (и визуально и технически). Нужна только чтобы не ждать задержку ( например 2.4 секунды при неправильном овтете) между карточками при правильном или непраивльном ответе и на предыдущей карточке. То есть пока карточка показывается, кнопка вперед не активна. Она активна только когда человек нажал Enter и активна она на момент задержки. Либо правильная, либо неправильная. То есть чтобы в следующий карточек без задержки. У неё должно быть два дизайна, но что в принципе уже есть, там одна такая более серая, неактивная и вторая активная. Просто нужно представить, нужно иметь вот эти неактивные и активные дизайны по описанному выше прописанию.

✅ **РЕАЛИЗОВАНО**: Кнопка "вперед" теперь:
- Неактивна (серая) при показе новой карточки
- Активируется (синяя) только после ввода ответа (правильного или неправильного)
- Позволяет пропустить задержку и сразу перейти к действию:
  - При правильном ответе → переход к следующей карточке с анимацией
  - При неправильном ответе → сброс состояния без анимации (та же карточка)
- Возвращает к текущей карточке из просмотра предыдущей с автофокусом инпута
- Использует единую логику сброса состояния (без дублирования кода)

### 4.2 [x] Краткая статистика на главном 

Нужно сделать блок с краткой статистикой пользователя:
- Сколько карточек уже изучено (is_learned : true) из скольки (сколкьов сего карточек)
- Карточек в активной очереди (готово к тренировки)
- Карточек в пассивной очереди "на изучении" (ожидают интервала)
- Шкалу прогресса (сколько слов выучено из общего кол-ва слов по этому языку) градиентом справа налево. С процентами и кол-вом карточек. 

Можно для разных очередей разные цвета сделать.

### [x] 4.2.5 Баг с наследованием прогресс по слову на следующую картчоку


### [] 4.2.6 Оптимизация запрсоов

Подробнее - DATABASE_OPTIMIZATION_ANALYSIS.md

pipeline = [
    {"$match": match_conditions},
    {"$sample": {"size": 1}}  # ← ЭТО МЕДЛЕННО!
]

🔍 Почему $sample медленный?
MongoDB $sample может быть медленным когда:

Большая коллекция - нужно сканировать много документов
Сложные условия фильтрации - $nin с большим списком
Нет подходящих индексов


### 4.4 [x] Мелкие доработки 

1. [x] После того, как дневная цель по карточкам выполнена... то есть там 10, например, карточек, то надо как-то выходить, завершать, так далее.

1.2 [x] Может вообще попробуем поменять кнопку? Вот эту начать тренировку, например, сделать ее более такой нежно-пурпурной градиентом. Примеры я сейчас отправлю /desigh_examples/Screenshot 2025-06-19 at 5.00.57 PM.png

2. [x] 🐞иконка на кнопке "начать тренировку". Сейчас при загрузке экрана она не отображается сразу, а появляется через 1-2 секунды. Это неправильно. Она должна появляться сразу с кнопкой. 

3. [x] После сохранения изменений в профиле, всплывающие окна на русском все равно всплывает. Несмотря на то, что у пользователя английский язык интерфейса

4. [x] Так, пользователь пользователь закончил тренировку, потом в хедере кликнул на страницу профиля, потом нажал "назад" и опять  его перекинуло на экран тренировки, хотя его по идее должно перекинуть на главный экран, потому что кнопка "назад" она должна быть как бы кнопкой "наверх". 

5. [x] хедер что у нас есть на всех экранах должен быть также и на главном экране. Но толкьо без кнопки "назад". Потому что назад некуда. 

И после этого убери кнопки "Профиль" и "выход" с главного экрана. 

6. [x] У нас сейчас при неправильном ответе текущая карточка повторяется и в верхнем прогресс-баре это не обновляется. Нужно чтобы каждый вот, даже если он по текущей карточке, то есть чтобы он обновлял прогресс по карточкам вверху. То есть например человек ответил сначала правильно это первая карточка, потом на вторую он дал ошибку, у него эта карточка повторилась, это уже третья карточка. Он опять ошибку ввел, это уже четвертая карточка, хотя по факту он только две карточки изучил, но надо показать как будто бы четыре.

7. [x] На некоторых карточках дебаг информация об интервале не отображдается сразу после ответа на карточку. На некоторых обновляется сразу. 

8. [x] Более стимпатичный стиль экрана завершения тренировки
- Симпатичнее кнопка
- Кнопка ведет на главную
- Кнопка "Тренирвока еще раз" 

8. [x] И еще, давай из статистики уберем заголовок "Your Progress".

9. [x] На экране завершения тренировки "!" находится на следующей строке после "Сongrats"

10. [x] Сделаем статистику на главном экране чтобы на момент загрузки экрана она была уже загружена.

11. [x] В хедере на главной должен быть отступ типа safearea. Сейчас хедер назжает на камеру и панель телефона где исгнал, вемя, уровень заряда телефона. 

12. [x]По поводу пункта 6. Сейчас лучше. Но счетчик картоки в прогресс-баре наверху обновляется слишком рано. Он обновляется сразу после ответа, хотя фактически мы еще не перешли на новую карточку. Да, фактически это бдет таже картчока, но пока что мы показываем информацию об ошиьке и у нас задердка 2.4 секунды. А счетчик уже обновился. Это непраивльно. 



### 4.5 [x] Анимация таймера на экране тренирвоки. 
Который между кнопками. 

Вот пример. Возьми его за основу и сделай симпатичный таймер между кнопками который будет отсчитывать задержку после ввода карточки и до перехода к слеюущей. Но надо переделать его под цвета и стиль приложения. 

### 4.6 [x] I don't know
СНова проблема. 

И еще надо сделать чтобы при нажатии на enter в пустом поле инпута был похожая функция. Только без дублирования функции. 

Проблема №9 и №10
## SPRINT 2 [ДОБАВЛЕНИЕ СЛОВ] (10 July - 25 July) - ⏸️

### 0. [x] Подготовительные мелкие задачи ✅
#### [x] 0.1 Первым делом попросить sequential thinking проанализировать план. 
И project.md и дать рекомендации по задачам, проекту, технологиям. Да и вообще можно попросить его код проаналаизироватьи дать реокмендации что можно улучшить. Но пока без правок
`md/sequential_thinking about todo.md`
#### [x] 0.2 Инструкцию по быстрому развертыванию на новом macOS
Может тупо докер? Нет, с ним возни больше
#### [x] 0.3  Актуализировать план
#### [x] 0.4 Отдельный список мелких задач (вне стадии, просто куча)
- [ ] Подумать где его разместить. 
  - И как указывать стадию. Хотя если стадия известна, то может сразу в стадию?
  - Может просто разделить на 2 секции "до запуска МВП" и "после запуска МВП"




### 1. [ ] Закончить документацию по добавлению слов 🎯
Не сильно на этом фокусируйся. Там в принципе все готово. Просто просмотри, все ли ок. НЕ надо делать идеально. 
- [x] Разобрать пункт  "хз разобрать отдельно"
- [x] Непонятно у нас тесты делаются из JSON или JSON создается из тестов. Какой вариант лучше?
- [x] Уточнить по поводу context7
- [x] Уточнить что нужно изучить саму инструкцию
- [x] Уточнить использование MCP-сервера Sequential Thinking
- [x] 2.1-2.2 - Почистить этап определения уровня
- [x] Edge cases
- [х] 5.2 - там вообще не такая структура. 
- [x] Разобрать `🚨 РАБОТА С ПРОБЛЕМАМИ`. Возможно отправить в справочную информацию
- [x] Актуализировать краткий воркфлоу в шапке
- [x] Проверить, есть ли в иснтрукции по добавлению слов инфа об "alterbative_answers". И запланировать задачу внедрения в экран тренировок. Но после MVP
- [x] Разобрать справочную информацию
- [x] Разобраться с базовой формой (что это). Не именительный. Именительный только для существительных. 
- [x] Инструкция для A0 или для любого уровня. В 4 шаге я видел там текст про A0
- [x] Описать когда нужно дополнять `vocabulary/language_guides/[язык]_guide.md`. И как. Ведь у нас есть `_language_summary.md`.
  Непонятно у нас есть общий файл документации `_language_summary.md` по всем языкам, а есть гайд для каждого языка. И вот непонятно, когда нужно будет обновить какое-то правило, то где оно будет обновлено в гайде языка или и в сам море тоже. Вот непонятно, что будет обновлено в сам море, что не будет обновлено в сам море, будет ли добавляться в конкретный гайд. В любом случае нужно в конкретный гайд по какому-то языку добавлять всегда. Но вот что добавлять в summary, вот это вопрос.
- [x] Банчи. Просто дай ИИ промт. Пусть сам думает как 
  Супер, а что насчет оптимизации скорости обработки слов? Потому что у меня в целом 5 тысяч слов, как бы на одно слово слишком много тратится и токенов, и времени, и как-то может быть тоже добавен в идеи после каждой итерации. Как можно улучшить скорость, то есть как-то сократить. Это, наверное, не сюда, а в общей список шагов. То есть это не в шаг 4. То есть у нас там сводка будет, только у нас будет банчами, мы будем по 10 слов, по 5 слов. И вот туда мы, наверное, это и добавим, что... То есть в сводку какие-то идеи нужно добавить, как можно улучшить этот процесс. То есть у нас будет краткая сводка по словам. То есть там 10 слов было обработано в таких-то языках проблемы. То есть краткая самая по сводкам. То есть у нас будет каждая сводка по каждому слову, что там улучшить, что это, в каких языках проблемы и так далее. А это будет итоговая сводка по всем словам в банче. И там будет инфа, как можно скорость улучшить, как ты думаешь?
- [ ] Дать пример саммари банчей (в том же треде) `batch_summary_example.md`
- [x] Учесть что для MVP будет 25% от каждого уровня. Не знаю как. Пусть думает. 
  Смотри, я решил в MVP сделать не все 6 тысяч слов, а примерно по 25-30 процентов от каждого уровня, ну кроме А0, в А0 у нас уже утверждено, сколько там примерно 150 слов, мы их трогать не будем. Вот начиная с уровня А1, то есть сколько там, ну примерно у нас всего 6 уровней, примерно по 1000 слов на каждой, и соответственно 25-30 процентов, то есть мы будем добавлять 250 слов с каждого уровня, и вот надо это как-то учесть, то есть я просто хочу сейчас, то есть смотри, мы можем слова пусть все определить, например, создать, но предложения не все определять, то есть можно даже какой-то статус поставить, что типа вот эти будут 2 MVP, а вот остальные там 70 процентов или сколько там, 75 будут в следующих версиях созданы, я думаю это подход правильный, потому что сейчас мне нужно вообще понять грубо есть какие-то ошибки, может быть, потестировать это все, все равно люди даже если они будут изучать что-то, они вряд ли будут там, ну вряд ли они там быстро первые 300 слов пройдут, а к тому времени как они пройдут, я уже как раз остальные слова и добавлю, ну я думаю как-то так, как ты считаешь, какие здесь могут быть проблемы, ну в принципе я думаю все будет нормально, ну просто подумай как вообще решить эту задачу
- [>] Добавить слова 
  - [x] Найти и скачать списки слов (CEFR, Oxford и пр)
  - [x] Разбить на уровни 
  - [x] Создать временные списки слов по уровням
    - [x] Проверить дублирование слов по предыдущим уровням  
  - [x] Проверить A0-B2 через COCA и GOOGLE 6000. Все ли слова учли
  - [ ] Создать полноценные списки слов 
  - [ ] Решить какие слова буду использовать для MVP (тупо 25% от каждого уровня)
- [ ] Решить как я буд использовать в приложении (скорее всего без С-уровня). Сделать корректировки. Слова C1 и С2 не входят в 5-6тыс слов и будут предложены после изучения A0-B2 потом. То есть о них вначале вообще не будем даже говорить, а потом просто когда человек все изучит 100% Мы ему скажем, слушай, давай так еще вот C1, C2 ты изучи Но не будем говорить, что это C1, C2, просто типа давай улучшение уровня, типа следующий уровень И там уже как раз в твое C1, C2, формы, хотя формы будут другие В общем, в любом случае, это явно не сейчас, просто сейчас надо как-то об этом обозначить Решать мы это все будем потом после MVP. Дай задачу ЛЛМ, пусть он подумает, как это всё реализовать, куда всё это добавить, как это всё сделать. Попиши ему вводное и пусть сам думает.
- [ ] Решить когда работать с формами слов (модальная архитектура). Явно не в МВП, а когда? И как? Описать решение в md файле 
  - [x] найти в истории gpt решение и вставить в md файл. `language_forms_templates_architecture.md`
  - [ ] Попросить LLM анализировать решение (с sequential thinking) 
  - [ ] сохранить md файл c решением и дать ссылку в todo (в версии 2, после MVP)
- [ ] Прочитать инструкцию полностью - 600 строк (можно в визуальном режиме) Финально. 
  - [x] 1 шаг
  - [ ] 2 шаг
  - [ ] 3 шаг
  - [ ] 4 шаг
  - [ ] 5 шаг
  - [ ] 6 шаг
  - [ ] 7 шаг
  - [ ] 8 шаг
  - [ ] 9 шаг
  - [ ] 10 шаг
  - [ ] СПРАВОЧНАЯ ИНФОРМАЦИЯ. 
- [ ] Сделать бэкап и папку бэкапов. Может с датами. И нужно ли вообще если есть гит? 
- [x] Попросить критику документации. Что может пойти не так? Есть какие-то критические замечания? Или просто рекомендации и улучшения? 
- [ ] Изменить формат UUID везде. Зачем такой длинный? 
  Но надо учесть что некоторые файлы со словами уже созданы. JSON файлы. Нужно там тоже обновить. 
- [ ] Загрузить элементы в память AUG 


### 2. [ ] Добавить слова 
Кажется задача мелкая, но нет. Огромная. 
> Просто добавляем слова по-порядку и улучшаем документацию. 
Сначала по 1, потом по 2-3, в идеале по 10 шт. (когда докумнетация будет ок)
Будет добавлять по 20-30% от кол-ва. Чтобы успеть за 2 недели. Иначе увязну в долине смерти. Да и вдруг переделывать придется. Лучше на малом кол-ве сначала. 

Обязательно разбивая на уровни. И дедлайн по уровням. Посильный. 
- [ ] A0 - 150 слов (первая цель)
- [ ] A1
  - [ ] Определить список слов
  - [ ] Подготовить все
- [ ] A2
- [ ] B1
- [ ] B2
- [ ] C1
- [ ] C2 (возможно его не будет)



### [ ] ЗАПУСК
**🧠 Sequential thinking - ВЫСОКИЙ ПРИОРИТЕТ**
📖 **См. также:** [MCP серверы для деплоя](md/mcp.md#для-деплоя-и-devops)
- найти решение чтобы можно было работать локально, а потом загружать
- бэкенд и фронтенд. как?
  - render?


## SPRINT 3 (AFFILIATE PROGRAMM)
Это основа заработка.

### УТВЕРЖДЕННЫЕ ЗАДАЧИ:
- 

====================================
### 💡 ⏭️ Unsort (ideas) 
- Система баллов
## SPRINT 4 (ПЕРВЫЕ ПРОДАЖИ)
Цель - начать зарабатывать. Найти 1 клиента, потом 5, 10. Войти в этот режим того что приложение приносит деньги. 

> ⚠️ Возможно партнерскую программу нужно начать уже здесь. Потому что как иначе продавать? С партнеркой продажи становятся обычной задачей. Просто пиши каждый день 10-20 блоггерам и все. Поставь это на поток и все будет. 
Надо как можно быстрее начать зарабатывать. Потому что неизвестно сколько еще будут бесплатными LLM. Каждый месяц тратить 100$ без отдачи будет очень накладно. 

### УТВЕРЖДЕННЫЕ ЗАДАЧИ

1. !Подписка
  - Триал режим
    - 3 месяца, не жадничать, подсадить их хорошенько
    - Но Возможно делать 1 месяц, а потом за какие-то заслуши продлевать (привлечь друзей например)
2. Название приложения
  - обычная задача, просто исследуй, а потом выбирай лучшее
  - может попроси разные ЛЛМ придумать название, на основе project.md 
3. Сайт (сгенерированный на оснвое project.md)
   - `md/LP.md`
4. Публикация в сторы
  - ASO
5. Простая партнерка (одноуровневая) - для первых продаж
6. Веб-ЛК для партнеров (блоггеров)
7. С системой лендингов 
   1. Лендинг приложения видимо тоже будет нужен, на основе него будет создаваться LP для партнеров




============================================================================================================================================================


## MVP TESTS


### 0 [ ] Изучить регрессионные тесты E2E
**🧠 Sequential thinking - средний приоритет**
📖 **См. также:** [Detox и Playwright MCP для E2E тестов](md/mcp.md#стратегия-тестирования-с-mcp)

### 1 [ ] Тесты на смену полосок
**🧠 Sequential thinking - средний приоритет**
- Потестить происходит ли смена полосок с 1 на 2-ю.
- Просто в БД поменять данные и все

### 2 [ ] Отвалился интернет во время трненировки
**🧠 Sequential thinking - низкий приоритет**



====================================

# v2. 🛠️ AFTER MVP (if suxcess)

## 🟡 TASKS AFTER MVP (Backlog unsorted)

- Гостевой режим
- Внедряйте BMAD для AGILE 
  https://github.com/bmadcode/BMAD-METHOD
- Онбординг
- Светлая тема
- Неправильно? Обратная связь
- стрики (сейчас?) - это непростая тема, с онбордингом, но базовый функционал можно внедрить
- Улучшить систему стриков. Полноценно
- Мотивацияонные фразы при загрузке (вместо лоадера). Причем для каждого языка свои
- Адаптивное количество карточек: Корректировка числа карточек (от 20/день) по прогрессу.
- Голосовое произношение: Озвучка (Google Text-to-Speech, 3 языков).
- Виджет
- Напоминания
- **🧠 Memory MCP Server интеграция**: Персонализация системы интервального повторения
  📖 **См. подробно:** [Memory MCP Integration](docs/future/MEMORY_MCP_INTEGRATION.md)
  - Адаптивные интервалы вместо фиксированных
  - Граф знаний для связи слов
  - Персональная аналитика обучения
  - Умная предзагрузка карточек
  - Предсказание сложности слов
- Расширенная форма слова
  **🧠 Sequential thinking - ВЫСОКИЙ ПРИОРИТЕТ**
  📖 **См. также:** [MCP серверы для модульной архитектуры](md/mcp.md#конкретные-рекомендации-по-задачам-из-todomd)
  md/WORDS.md

  - Визуальный прогресс слова из двух частей (лемма)
  - Перевод через LLM
  - Не забыть про уровень словоформ
  - И про уникальный id для каждой словоформ (внутри слова) чтобы отслеживать прогресс
  - Найти решение для фраз более 2-х слов (фразовые глаголы, no way)






# v3 (МЕНЕЕ ВАЖНЫЕ ДОРАБОТКИ)
После того как все необходимые элементы внедрены и можно уже зарабатывать. Теперь можно подумать о дополнительных фичах

## УТВЕРЖДЕННЫЕ ЗАДАЧИ: 
- Alternative answers
  - Что делать с допустимыми словами (alternative_answers)
  - Создаются ли они в главной инструкции? Правильно ли описаны? Есть отдельная `vocabulary/alternative_answers_rules.md`
  - Что насчет экрана тренироки? 
  - Я думаю в МВП их не нужно делать. Но в слова добавлять нужно. Тем более в инструкции у нас это уже внедрено. 

====================================
## 💡 ⏭️ Unsort (ideas) [v2]

- Адаптация экрана тренирвоки для проблемные языки 
  - RTL - (арабский, фарси, урду и т.д.) Если это RTL язык, просто поменять порядок частей предложения при отображении. И Для RTL языков, таких как арабский, предложение должно быть выровнено по правому краю. 
    - Подумать по какому краю выравнивать предложения. ЧТобы было красиво. А то если Английский изучает Арабский, то у него целевая фраза по правому краю выровнена, а родная по левому. Некрасиво.
  - Корейский вежливость в целевом слове подставляется (저는 ___를 좋아해요. (사과))
  - Можно для каждого языка сделать разные стили для экрана тренировки, как исключение. И он будет чуть меняться. Адаптируясь под язык. 
  - 📖 **См. также:** [Адаптация экрана тренировки для разных языков](docs/development/language_specific_training_screen.md) - подробные рекомендации по реализации для корейского, арабского и других проблемных языков
- Расширенная статистика
- Свайпы карточек (функционал стрелок)
- Оффлайн режим 
- отключить т9
- проверка уровня языка при старте
- Слово выучено после года, но интервалы продолжаются (для закрепления). Подумать как.
- Не загружать все данные. Загружать ключ (conecpt_id например), а все остальное хранить в кэше


============================================================================================================================================================
# v4 (ГЕЙМИФИКАЦИЯ) 
## УТВЕРЖДЕННЫЕ ЗАДАЧИ:
- 

====================================
## 💡 ⏭️ Unsort (ideas)
- [ ] **Расширение языковой системы** - добавление новых языков, региональных вариантов, систем письма, лингвистических семей. См. [language_system_expansion.md](future/language_system_expansion.md)
  - [ ] Система диалектов и макроязыков
  - [ ] Поддержка RTL языков (арабский, персидский, урду)
  - [ ] Группировка языков по регионам и семьям
  - [ ] Автоматизация создания переводов с MCP серверами
- [ ] Анимации 
- [ ] Звуки



============================================================================================================================================================
# v5 (ГРАММАТИКА)
Грамматика описана в файле `md/grammar.md`
- 

УТВЕРЖДЕННЫЕ ЗАДАЧИ:
- 

====================================
## 💡 ⏭️ Unsort (ideas)
- Интнервальное повторение тем. Если есть ошикба в артиклях, то повторяем тему артикляов.
- Список проблемных тем актуализируется постоянно и с оставляется рейтинг. 



============================================================================================================================================================






