# 📚 Руководство по добавлению слов в базу данных

> **📋 ИСТОЧНИК ИСТИНЫ**: Это основная документация по добавлению слов. Единый workflow для всех уровней (A0, A1, B1, B2, C1, C2).

## 🚀 **КРАТКИЙ ОБЗОР WORKFLOW**

1. **Определение уровня и выбор слов** - работа со списками слов, создание новых уровней
2. **Анализ концептов** - понимание семантики, изучение языковых особенностей
3. **Создание временных предложений** - подготовка переводов для тестирования
4. **Тестирование по всем языкам** - детальная проверка по критериям A0
5. **Исправление проблем** - анализ ошибок, обновление правил
6. **Создание JSON файлов** - финальное создание в правильной структуре
7. **Валидация** - техническая проверка формата
8. **Импорт в базу данных** - подготовка окружения + загрузка в MongoDB
9. **Обновление статусов** - отметка завершенных слов

---

## 💪 **ПРИНЦИП НЕПРЕРЫВНОГО УЛУЧШЕНИЯ**

> **🌟 КАЖДАЯ СЛОЖНОСТЬ - ЭТО ВОЗМОЖНОСТЬ!**

**НЕ БОЙТЕСЬ ПРОБЛЕМ** - они делают систему сильнее:

### **🔧 Как проблемы становятся решениями:**
- **Проблема**: "быть/be" не работает в азиатских языках
- **Решение**: Правило семантического соответствия
- **Результат**: Теперь можем работать со сложными концептами!

- **Проблема**: CB язык дает ошибки с "kong" vs "ko"
- **Решение**: Краткие памятки в справочниках языков
- **Результат**: Системная проверка для всех редких языков!

### **📈 Эволюция системы:**
1. **Первые слова**: Простые, много ручной работы
2. **Средние слова**: Появляются правила и шаблоны
3. **Сложные слова**: Автоматизация, семантические правила
4. **Будущее**: Система сама подсказывает решения!

### **🎯 Ваша роль:**
- **Каждая проблема** → новое правило в документации
- **Каждое исключение** → улучшение процесса
- **Каждый сложный случай** → опыт для следующих слов

**ПОМНИТЕ**: Благодаря проблемам со словом "быть" мы создали систему, которая теперь работает для ЛЮБЫХ сложных концептов на всех 37 языках!

---

## � **ШАГ 1: ОПРЕДЕЛЕНИЕ УРОВНЯ И ВЫБОР СЛОВ**

### **1.0 Обязательное изучение документации**
**КРИТИЧЕСКИ ВАЖНО** - перед началом работы изучить:
1. **`md/A0_word_list.md`** - список всех A0 слов с приоритетами и статусами
2. **Этот файл** - процедура создания и тестирования (все правила A0 в шаге 4)
3. **`backend/data/words/EXAMPLE.md`** - техническое описание формата JSON файлов
4. **`backend/data/concepts/A0_concepts.json`** - описания концептов для понимания смысла слов
5. **`backend/data/languages/cb_cebuano_guide.md`** - правила для CB языка (обязательно для проверки)

### **1.1 Работа с существующими уровнями**
**Для A0 (текущий уровень):**
- Открыть `md/A0_word_list.md`
- Найти слова со статусом `⏳ TO_BE_CREATED`
- Взять `concept_id` и информацию о слове

### **1.2 Переход на новые уровни (A1, B1, B2, C1, C2)**
> **⚠️ КРИТИЧЕСКИ ВАЖНО**: Перед созданием слов нового уровня ОБЯЗАТЕЛЬНО создать и утвердить список слов!

**Процедура для новых уровней:**

1. **Проверить наличие списка слов**:
   - Для A0: используем `md/A0_word_list.md` (готов)
   - Для A1: проверить `md/A1_word_list.md` - если нет, создать
   - Для B1: проверить `md/B1_word_list.md` - если нет, создать

2. **Если файла списка нет - создать и утвердить**:
   ```markdown
   # [УРОВЕНЬ] Word List

   > **📊 СТАТУС УРОВНЯ [УРОВЕНЬ]**: ⏳ **СОЗДАНИЕ СПИСКА** | Список не готов

   | № | Русское | Английское | Статус | Приоритет | UUID | Файл |
   |---|---------|------------|--------|-----------|------|------|
   | 1 | [слово] | [перевод] | ⏳ TO_BE_CREATED | [приоритет] | [uuid] | [файл].json |
   ```

3. **Утвердить список** (проанализировать полноту, проверить дубликаты)
4. **Обновить статус** на "✅ **СПИСОК УТВЕРЖДЕН** | ⏳ **В ПРОЦЕССЕ СОЗДАНИЯ JSON**"
5. **Продолжить стандартный процесс** создания слов

### **1.3 Выбор конкретных слов для обработки**
- **Единичная обработка**: взять 1 слово со статусом `⏳ TO_BE_CREATED`
- **Пакетная обработка**: взять 10 слов для массовой обработки
- Записать выбранные слова и их `concept_id`

---

## 🧠 **ШАГ 2: АНАЛИЗ И ВЕРИФИКАЦИЯ КОНЦЕПТОВ**

### **2.1 Понимание концепта**
**Концепт** - это семантическое описание идеи, которую выражает слово. Каждый концепт имеет уникальный `concept_id` и связывает переводы одного слова на всех языках.

### **2.1.5 ОБЯЗАТЕЛЬНАЯ ВЕРИФИКАЦИЯ КОНЦЕПТОВ**
**КРИТИЧЕСКИ ВАЖНО**: Перед созданием слов ОБЯЗАТЕЛЬНО проверить и создать качественные концепты!

**ПРОЦЕДУРА ВЕРИФИКАЦИИ:**
1. **Проверить наличие концепта** для каждого выбранного слова в `backend/data/concepts/A0_concepts.json`
2. **Если концепт отсутствует** - создать новый по расширенному формату
3. **Если концепт неполный** - дополнить недостающими элементами
4. **Проверить качество** - соответствие критериям детального концепта

**РАСШИРЕННЫЙ ФОРМАТ КОНЦЕПТА:**
```json
{
  "concept_id": "550e8400-e29b-41d4-a716-446655440012",
  "level": "A0",
  "priority": "ultra_core",
  "category": "emotions",
  "concept_name": "apology_regret",
  "description": {
    "en": "Expression of regret or sorrow for one's mistake or wrongdoing",
    "ru": "Выражение сожаления или раскаяния за свою ошибку или проступок"
  },
  "semantic_field": "emotional_expression",
  "usage_context": "when person made mistake and wants to apologize",
  "not_confused_with": {
    "excuse_me": "polite attention-getting, not apology for wrongdoing",
    "pardon": "request for repetition, not expression of regret"
  },
  "situation_examples": [
    "accidentally bumped into someone",
    "arrived late to meeting",
    "made an error in work"
  ],
  "emotional_tone": "regret, remorse, sorrow",
  "examples": {
    "en": "I'm very sorry for being late",
    "ru": "Мне очень жаль, что опоздал"
  },
  "translation_notes": {
    "general": "Use words expressing regret/sorrow, not attention-getting",
    "specific": {
      "en": "Use 'sorry' not 'excuse me'",
      "de": "Use 'es tut mir leid' not 'entschuldigung'",
      "fr": "Use 'désolé' not 'excusez-moi'"
    }
  }
}
```

**КРИТЕРИИ КАЧЕСТВЕННОГО КОНЦЕПТА:**
- ✅ **Однозначность**: четкое описание без двусмысленности
- ✅ **Контекстность**: ясно указано когда используется
- ✅ **Различимость**: отличается от похожих концептов
- ✅ **Примеры ситуаций**: конкретные случаи использования
- ✅ **Translation notes**: указания для правильного перевода
- ✅ **Эмоциональная окраска**: тон и настроение слова

### **2.2 Команды для работы с концептами**
```bash
# Просмотр концепта и его слов
python -m scripts.words concept 550e8400-e29b-41d4-a716-************

# Список всех концептов A0
python -m scripts.concepts list A0

# Создать новый концепт (если нужно)
python -m scripts.concepts create --word "мама" --en "mother" --category "family"
```

### **2.3 Изучение языковых справочников**
```bash
# Создать справочники для всех языков (если еще не созданы)
python scripts/generate_language_guides.py

# Справочники находятся в:
ls data/words/language_guides/
```

### **2.4 Анализ семантики**
Для каждого выбранного слова:
1. **Понять основное значение** - что означает концепт
2. **Изучить контексты использования** - в каких ситуациях применяется
3. **Определить сложность** - есть ли особенности в разных языках
4. **Найти универсальную конструкцию** - какое предложение будет работать везде

---

## 📝 **ШАГ 3: СОЗДАНИЕ ВРЕМЕННЫХ ПРЕДЛОЖЕНИЙ**

### **3.1 Выбор слов для обработки**
**ПАКЕТНАЯ ОБРАБОТКА (рекомендуется):**
- Взять **10 слов** со статусом `⏳ TO_BE_CREATED` из списка слов
- Записать их concept_id, русские и английские варианты

**ЕДИНИЧНАЯ ОБРАБОТКА:**
- Взять 1 слово для детального анализа

### **3.2 Создание отдельных временных файлов для тестирования**
Создать **отдельный MD файл для каждого слова** в папке `backend/data/words/temp_md/` для удобного тестирования и архивирования:

**РЕКОМЕНДАЦИЯ**: Использовать MCP Server Sequential Thinking для анализа грамматических конструкций и выбора оптимальных предложений.

**ФОРМАТ MARKDOWN ФАЙЛА ТЕСТИРОВАНИЯ (например, `0008_A0_ultra_core_08.md`):**

```markdown
# СЛОВО 8: есть (кушать)/eat - ТЕСТИРОВАНИЕ

**Метаданные:**
- concept_id: 550e8400-e29b-41d4-a716-446655440008
- файлы: 0008_A0_ultra_core_08.md и 0008_A0_ultra_core_08.json
- приоритет: ultra_core

**КОНЦЕПТ**: eating_action - базовое действие приема пищи, выражение желания есть

**Предложение-идея:** "Я хочу есть" / "I want to eat"

## ТАБЛИЦА ТЕСТИРОВАНИЯ

| Язык | Слово | Предложение | Смысл | Падеж | Грамматика | Естественность | A0-уровень | Статус |
|------|-------|-------------|-------|-------|------------|----------------|------------|--------|
| RU   | есть  | Я хочу есть | ✅    | ✅    | ✅         | ✅             | ✅         | ✅     |
| EN   | eat   | I want to eat | ✅  | ✅    | ✅         | ✅             | ✅         | ✅     |
| DE   | essen | Ich will essen | ✅ | ✅    | ✅         | ✅             | ✅         | ✅     |
| ... | ...   | ...         | ...   | ...   | ...        | ...            | ...        | ...    |
// ... все 37 языков

## РЕЗУЛЬТАТ ТЕСТИРОВАНИЯ
- **Всего языков**: 37
- **Успешно**: 37 ✅
- **Проблемы**: 0 ❌
- **Предупреждения**: 0 ⚠️

**ВАЛИДАЦИЯ**: ✅ ДАННОЕ СЛОВО БЫЛО ПРОТЕСТИРОВАНО ПО ВСЕМ 37 ЯЗЫКАМ ПО ВСЕМ ПАРАМЕТРАМ ТАБЛИЦЫ
```

**ПРИНЦИПЫ ИМЕНОВАНИЯ MARKDOWN ФАЙЛОВ:**
- **Новая система**: `[номер_слова]_[приоритет]_[номер_в_приоритете].md`
- Примеры: `0008_A0_ultra_core_08.md`, `0009_A0_ultra_core_09.md`, `0016_A0_core_01.md`
- Это обеспечивает полный контроль качества через таблицы

### **3.3 ПРАВИЛА СОЗДАНИЯ ПРЕДЛОЖЕНИЙ A0 УРОВНЯ**

**БАЗОВЫЕ ТРЕБОВАНИЯ:**
- **Одинаковый смысл**: предложение должно переводиться на все 37 языков с одинаковым смыслом
- **Именительный падеж**: целевое слово ОБЯЗАТЕЛЬНО в именительном падеже во всех языках
- **Простая грамматика**: подлежащее + сказуемое/прилагательное (максимум 4-5 слов)
- **Естественность**: звучит как реальный пример из жизни, понятно ребенку

**ПРАВИЛА ПО АРТИКЛЯМ:**
- **❌ ИЗБЕГАТЬ**: немецкие артикли (der/die/das) - сложная система + падежи
- **✅ ДОПУСТИМЫ**: романские артикли (fr: le/la, it: il/la, es: el/la, pt: o/a) - простые системы
- **✅ ДОПУСТИМЫ**: арабский артикль ال - единый простой артикль

**ФОРМАТ ПРЕДЛОЖЕНИЙ С ПРОПУСКАМИ:**
- **Пропуск `___`**: обязательно в каждом предложении вместо изучаемого слова
- **ЗАГЛАВНЫЕ БУКВЫ**: если слово стоит в начале предложения, оно должно быть с заглавной буквы
  * ❌ НЕПРАВИЛЬНО: `"___ работаю"` → `"я работаю"` (строчная буква в начале)
  * ✅ ПРАВИЛЬНО: `"___ работаю"` → `"Я работаю"` (заглавная буква в начале)

**ЛОГИКА ПРОВЕРКИ ПРЕДЛОЖЕНИЙ:**
- **Структура данных**: `sentence` содержит пропуск `___`, `correct_answers` содержит варианты ответов
- **Для проверки естественности**: подставьте `correct_answers[0]` вместо `___` и проверьте итоговое предложение
- **Пример проверки**:
  * JSON: `{"sentence": "___ работаю", "correct_answers": ["Я"]}`
  * Итоговое предложение для проверки: **"Я работаю"**
  * Проверяйте естественность именно этого итогового предложения на всех языках

**ДОПОЛНИТЕЛЬНЫЕ ПРАВИЛА:**
- **Полные предложения**: ИЗБЕГАТЬ неполных конструкций типа "Я хочу" без объекта. ПРЕДПОЧИТАТЬ полные естественные предложения "Я хочу воду", "Я вижу дом", "Мама работает"
- **Падежи НЕ-целевых слов**: Только ЦЕЛЕВОЕ слово (изучаемое) должно быть в именительном падеже. ВСЕ ОСТАЛЬНЫЕ слова в предложении МОГУТ использовать любые падежи, времена, формы
- **Составные выражения (2-3 слова)**: ДОПУСТИМЫ, если невозможно выразить одним словом без потери смысла. ВАЖНО: предложение НЕ может состоять только из целевого выражения.

**ПРАВИЛА ДЛЯ СОСТАВНЫХ ВЫРАЖЕНИЙ:**
- **Один пропуск `___`** может содержать несколько слов
- **Предложение должно содержать дополнительный контекст** помимо целевого выражения

**ПРИМЕРЫ:**
- ❌ НЕПРАВИЛЬНО: "___" → "Thank you" (предложение = только целевое выражение)
- ❌ НЕПРАВИЛЬНО: "I want to ___ ___" → "I want to give up" (несколько пропусков для одного выражения)
- ✅ ПРАВИЛЬНО: "___ very much" → "Thank you very much" (один пропуск + контекст)
- ✅ ПРАВИЛЬНО: "I want to ___" → "I want to give up" (один пропуск + контекст)
- **Иерархия подлежащих**: ЖЕЛАТЕЛЬНО использовать подлежащее для естественности, НО только если это НЕ ПРОТИВОРЕЧИТ другим требованиям по составлению слов. Иерархия подлежащих:

  **ПРИОРИТЕТ 1 (максимальный)**: Содержательные подлежащие
  - "Врач", "Мама", "Ребенок", "Дерево", "Собака", "Дом" и т.д.
  - Используем ТОЛЬКО если целевое слово остается в именительном падеже. Если не поулчается, то используем 2-й приоритет. 

  **ПРИОРИТЕТ 2 (средний)**: Простые местоимения
  - "Я", "Ты", "Он", "Она", "Это", "Они"
  - Используем если содержательные подлежащие нарушают правила
  - Если тоже нарушают правила, то используем 3-й приоритет

  **ПРИОРИТЕТ 3 (минимальный)**: Без подлежащего
  - Только если даже простые местоимения нарушают правила
  - Стараемся избегать, но допустимо для соблюдения основных требований. Если иначе не добиться простого предложения. Но по возможности попробовать разные варианты предложений, прежде чем использовать предложения без подлежащего.

  **ПРАВИЛО**: Целевое слово ВСЕГДА в именительном падеже > естественность подлежащего

  **ПРИМЕРЫ ПРИМЕНЕНИЯ:**
  - ✅ "Врач работает" (приоритет 1) - если "врач" остается в именительном падеже
  - ✅ "Я работаю" (приоритет 2) - если "врач работает" нарушает правила в некоторых языках
  - ✅ "Работаю" (приоритет 3) - только если даже "я" создает проблемы

### **3.4 Принципы создания предложений**
Для каждого слова создать **ОДНО предложение-концепт**:
- **Одинаковый смысл** на всех 37 языках
- **Одна грамматическая конструкция** для всех языков
- **Полные предложения** (не "Я хочу", а "Я хочу есть")
- **Простота** - максимум 4-5 слов
- **ПРАКТИЧНОСТЬ** - предложения должны быть максимально полезными для реальной жизни

### **3.4.1 ПРИНЦИП ПРАКТИЧНОСТИ ДЛЯ A0**
**КРИТИЧЕСКИ ВАЖНО**: Поскольку A0 - это 150 самых нужных слов, предложения должны моделировать реальные жизненные ситуации:

**ПРИМЕРЫ ПРАКТИЧНЫХ ПРЕДЛОЖЕНИЙ**:
- **"врач"** → "Где врач?" (человек ищет медицинскую помощь)
- **"вода"** → "Мне нужна вода" (базовая потребность)
- **"помощь"** → "Мне нужна помощь" (просьба о содействии)
- **"спасибо"** → "Спасибо большое" (выражение благодарности)

**ИЗБЕГАТЬ АБСТРАКТНЫХ ПРЕДЛОЖЕНИЙ**:
- ❌ "Врач хороший" (оценочное суждение)
- ❌ "Врач существует" (философское утверждение)
- ✅ "Где врач?" (практическая потребность)

**ВОПРОСЫ ДЛЯ ПРОВЕРКИ ПРАКТИЧНОСТИ**:
1. Может ли изучающий A0 использовать это предложение в реальной жизни?
2. Поможет ли это предложение решить базовую потребность?
3. Является ли это типичной ситуацией для начинающего изучать язык?

### **3.5 ВЕРИФИКАЦИЯ ПЕРЕВОДОВ**
**ОБЯЗАТЕЛЬНЫЙ ЭТАП**: Проверка качества переводов перед тестированием

**КАТЕГОРИЗАЦИЯ ЯЗЫКОВ ПО УРОВНЮ УВЕРЕННОСТИ:**
- **🟢 Высокая уверенность (6)**: EN, RU, DE, FR, ES, IT
- **🟡 Средняя уверенность (15)**: PT, NL, SV, DA, NO, PL, UK, TR, ZH, JA, KO, ID, MS, RO, FI
- **🔴 Требуют проверки (16)**: AR, HI, TH, VI, MY, KK, UZ, PA, MR, BN, UR, NE, FA, TL, CB, KA

**ПРОЦЕДУРА ВЕРИФИКАЦИИ:**
1. **Проверить соответствие концепту** - переводы отражают правильное значение
2. **Для языков "требуют проверки"** - особое внимание, поиск в словарях
3. **При сомнениях** - пометить ⚠️ ТРЕБУЕТ ПРОВЕРКИ и предложить варианты
4. **Обязательная проверка** translation_notes из концепта

**ПРОЦЕДУРА ДЛЯ СОМНЕНИЙ:**
```
ЕСЛИ не уверен в переводе:
1. Пометить: ⚠️ ТРЕБУЕТ ПРОВЕРКИ
2. Предложить 2-3 варианта перевода
3. Указать источник сомнений
4. Запросить подтверждение у пользователя
```

---

## 🧪 **ШАГ 4: ТЕСТИРОВАНИЕ ПО ВСЕМ ЯЗЫКАМ**

**⚠️ КРИТИЧЕСКИ ВАЖНО - ОБЯЗАТЕЛЬНОСТЬ ПОЛНЫХ ТАБЛИЦ:**
- **ВСЕГДА** показывать полную таблицу для ВСЕХ 37 языков
- **ВСЕГДА** проверять ВСЕ 9 критериев для каждого языка
- **НИКОГДА** не делать "быстрые проверки" или сокращенные тесты
- **ПОСЛЕ ИСПРАВЛЕНИЙ** - обязательно полное повторное тестирование
- **БЕЗ ИСКЛЮЧЕНИЙ** - каждое слово = полная таблица

### **4.1 ПРОЦЕДУРА ТЕСТИРОВАНИЯ**

**Правила создания предложений находятся в шаге 3.3** - используйте их при проверке.

### **4.2 ПАКЕТНОЕ ТЕСТИРОВАНИЕ 10 СЛОВ**

**ДЛЯ КАЖДОГО СЛОВА ИЗ ПАКЕТА ВЫПОЛНИТЬ:**

**РЕКОМЕНДАЦИЯ**: Использовать MCP Server Sequential Thinking для комплексного анализа проблем и системного подхода к тестированию.

**ОБЯЗАТЕЛЬНО ИСПОЛЬЗОВАТЬ ЯЗЫКОВЫЕ СПРАВОЧНИКИ:**
```bash
# Проверить краткие памятки для проблемных языков
cat data/words/language_guides/ZH.md | head -5  # Китайский
cat data/words/language_guides/JA.md | head -5  # Японский
cat data/words/language_guides/KO.md | head -5  # Корейский
cat data/words/language_guides/CB.md | head -5  # Себуано
```
**Минимум**: использовать краткие памятки (🚨 шапки) для всех языков
**Лучше**: изучить полные справочники для проблемных языков

1. **Взять предложение** из соответствующего MD файла
2. **Подставить correct_answers[0]** вместо `___`
3. **Проверить итоговое предложение** по всем критериям A0 (из шага 3.3)
4. **ОБЯЗАТЕЛЬНО заполнить полную таблицу тестирования для ВСЕХ 37 языков** (не сокращать!)
  - (!!!) Это не просто заполнить таблицу, а проверить действительно предложение по каждому языку и каждому параметру в таблице.
5. **При обнаружении "проблемы" - ОБЯЗАТЕЛЬНЫЙ АЛГОРИТМ АНАЛИЗА**:

   **ШАГ 1: АНАЛИЗ** - действительно ли это проблема?
   - Сравнить с правилами A0 из шага 3.3
   - Подумать: создает ли это реальную сложность для A0?
   - Естественно ли звучит на этом языке?

   **ЧАСТЫЕ "ПРОБЛЕМЫ", КОТОРЫЕ НЕ ЯВЛЯЮТСЯ ПРОБЛЕМАМИ:**

   **1. ИМЕНИТЕЛЬНЫЙ ПАДЕЖ:**
   - ⚠️ "Слово не в именительном падеже"
   - ✅ НЕ ПРОБЛЕМА если: слово в естественной базовой форме для данной конструкции
   - Примеры: FI "apua" (партитив с "tarvita"), PL "pomocy" (родительный с "potrzebować")
   - Алгоритм: попробовать именительный → если неестественно → принять исключение

   **2. АРТИКЛИ:**
   - ⚠️ "Разные артикли в похожих языках"
   - ✅ НЕ ПРОБЛЕМА если: каждый язык использует свои правила артиклей естественно
   - Примеры: DE "die Hilfe" vs EN "help" (без артикля)

   **3. ДЛИННЫЕ СЛОВА:**
   - ⚠️ "Составное слово слишком длинное"
   - ✅ НЕ ПРОБЛЕМА если: это стандартный перевод в данном языке
   - Примеры: TH "ความช่วยเหลือ", MY "အကူအညီ"

   **ШАГ 2А: ЕСЛИ НЕ ПРОБЛЕМА**
   - **Самостоятельно решить**: продолжать с текущим вариантом или слегка адаптировать
   - **Комплексный анализ**: где еще может проявиться похожая "проблема"?
     * Смежные языки (французские артикли → испанские/итальянские)
     * Похожие грамматические конструкции (падежи с определенными глаголами)
     * Другие части речи с похожими особенностями
   - **Документировать исключение**: ⚠️ → анализ → обоснование → ✅ с комментарием
   - **Примеры документирования**:
     * "FI: исключение - партитив естественнее именительного с глаголом 'tarvita'"
     * "DE: артикль 'die' стандартен для женского рода, не проблема"
   - **Предложить обновление правил в документации** (добавить в сводку для подтверждения)
   - **Продолжить тестирование** с выбранным вариантом

   **ШАГ 2Б: ЕСЛИ ДЕЙСТВИТЕЛЬНО ПРОБЛЕМА**
   - **Попытаться самостоятельно пересоздать** предложение для ВСЕГО концепта
   - **Если получилось**: внести изменения и продолжить тестирование
   - **Если не получается**: применить **АЛГОРИТМ РАЗУМНОГО КОМПРОМИССА**:

   **АЛГОРИТМ РЕШЕНИЯ ПРОБЛЕМ (СТРОГАЯ ПОСЛЕДОВАТЕЛЬНОСТЬ):**
   1. **СНАЧАЛА**: Попробовать 3-5 РАЗНЫХ предложений для ВСЕГО концепта
   2. **ЗАТЕМ**: Проверить каждое альтернативное предложение на естественность
   3. **ЗАТЕМ**: Выбрать лучшее решение, которое работает для всех языков
   4. **ТОЛЬКО ЕСЛИ ВСЕ ПОПЫТКИ НЕУДАЧНЫ** → применить алгоритм компромисса
   5. **ПРЕДЛОЖИТЬ УЛУЧШЕНИЕ ДОКУМЕНТАЦИИ** Для предотвращения подобной проблемы в будущем

   **АЛГОРИТМ КОМПРОМИССА (ТОЛЬКО В КРАЙНЕМ СЛУЧАЕ):**
   1. **Убедиться, что испробованы ВСЕ разумные альтернативы**
   2. **Проверить естественность** каждой альтернативы
   3. **Если ДЕЙСТВИТЕЛЬНО ВСЕ альтернативы неестественны** → задать пользователю СИСТЕМНЫЙ ВОПРОС:

      ```
      ВОПРОС: Может ли целевое слово "[слово]" быть в [проблемная форма]
      в [язык], если это единственная естественная форма?
      Или нужно найти другую конструкцию?

      КОНТЕКСТ:
      - Естественная форма: "[естественное предложение]"
      - Альтернативы звучат неестественно: "[список попыток]"
      - Влияет только на [количество] языков из 37
      ```

   4. **Предложить РАЗУМНЫЙ КОМПРОМИСС**: "Рекомендую принять исключение для [язык], так как альтернативы разрушают естественность"

6. **Отметить статус**: ✅ ГОТОВ / ❌ ТРЕБУЕТ ИСПРАВЛЕНИЯ / ⚠️ НУЖЕН ВЫБОР ПОЛЬЗОВАТЕЛЯ

### **4.3 ОБЯЗАТЕЛЬНАЯ ТАБЛИЦА ТЕСТИРОВАНИЯ В MARKDOWN**

**КРИТИЧЕСКИ ВАЖНО**: Каждое слово тестируется через заполнение таблицы в Markdown файле.

**ЕДИНАЯ ТАБЛИЦА ТЕСТИРОВАНИЯ (используется и в процессе, и в Markdown логах):**

| Язык | Слово | Предложение | Смысл | Падеж | Целевое слово | Допустимые ответы | Грамматика | Естественность | A0-уровень | Статус |

**КОЛОНКИ ТАБЛИЦЫ:**
1. **Язык** - код языка (RU, EN, DE...)
2. **Слово** - целевое слово на этом языке
3. **Предложение** - итоговое предложение после подстановки слова
4. **Смысл** - итоговое предложение имеет тот же смысл на всех языках (✅/❌/⚠️)
5. **Падеж** - целевое слово в именительном падеже (✅/❌/⚠️)
6. **Целевое слово** - слово соответствует концепту в базовой узнаваемой форме (✅/❌/⚠️)
7. **Допустимые ответы** - синонимы и альтернативные формы (если есть)
8. **Грамматика** - простая конструкция (4-5 слов), правильные артикли/заглавные (✅/❌/⚠️)
9. **Естественность** - предложение звучит как живая речь носителя языка (✅/❌/⚠️)
10. **A0-уровень** - понятно новичку, нет культурных/сложных моментов (✅/❌/⚠️)
11. **Статус** - итоговый статус строки (✅/❌/⚠️)

**ПРОЦЕСС ЗАПОЛНЕНИЯ ТАБЛИЦЫ:**
- ✅ = критерий выполнен
- ❌ = критерий не выполнен (проблема)
- ⚠️ = требует внимания/обсуждения
- Строка считается готовой только при всех ✅ в статусе

**КРИТЕРИЙ "ЦЕЛЕВОЕ СЛОВО" - КРИТИЧЕСКИ ВАЖНО:**
**ПРИМЕРЫ ПРОБЛЕМ:**
- EN: "I am a doctor" - ✅ am (быть) vs RU: "Я врач" - ❌ Нет слова "быть"! 

**СУТЬ ПРОБЛЕМЫ**:
Пользователь изучает слово "be/быть", но в предложении "Я врач" нет пропуска "___" для этого слова.
Что он должен вводить как перевод "be/быть"? "Я" или "Врач"? Это совершенно другие слова. 
приложение построено так, что не все используют предлоэения. Они скорее вспомогательные. Некоторые юхеры используют первеод по словам. Видит "to be" (eng) > вводить "быть" (ru). 

**ПРАВИЛО ПРОВЕРКИ:**
- ✅ **ПРЯМОЕ СООТВЕТСТВИЕ**: Слово в узнаваемой форме концепта (идти→иду, go→go, essen→esse)
- ✅ **СЕМАНТИЧЕСКОЕ СООТВЕТСТВИЕ**: Тот же концепт, другое слово (be→стать в контексте профессии)
- ❌ **ДРУГОЙ КОНЦЕПТ**: Совершенно другое значение (врач вместо быть, делать вместо есть)
- ⚠️ **СПОРНЫЕ СЛУЧАИ**: Требует анализа и обоснования

**ПРИМЕРЫ СООТВЕТСТВИЙ:**
- ✅ **ПРЯМОЕ**: "go/идти": "I go home" (go) ↔ "Я иду домой" (иду)
- ✅ **СЕМАНТИЧЕСКОЕ**: "be/быть": "I want to be doctor" (be) ↔ "我想当医生" (当=стать)
- ❌ **НЕПРАВИЛЬНОЕ**: "be/быть": "I am doctor" (am) ↔ "Я врач" (врач)

**КОГДА ИСПОЛЬЗОВАТЬ СЕМАНТИЧЕСКОЕ СООТВЕТСТВИЕ:**
- Только если прямое соответствие невозможно найти
- Смысл концепта полностью сохраняется
- Естественность языка важнее формального совпадения
- Обязательно документировать обоснование

**КРИТЕРИЙ "ДОПУСТИМЫЕ ОТВЕТЫ" - ОБЯЗАТЕЛЬНЫЙ ЭТАП:**

**КОГДА ЗАПОЛНЯТЬ:**
- ⚠️ **ТОЛЬКО ПОСЛЕ** успешного тестирования всех 37 языков
- ⚠️ **ТОЛЬКО ПОСЛЕ** получения кодовой фразы валидации
- ⚠️ **НЕ ДО** завершения тестирования

**ЧТО ВКЛЮЧАТЬ:**
- **ТОЛЬКО ТОЧНЫЕ ПЕРЕВОДЫ**: понятно = clear, understood, got it (все = "понятно")
- **ТОЛЬКО РАВНОЗНАЧНЫЕ ВАРИАНТЫ**: да = yes, yeah, yep (все = "да")
- **ТОЛЬКО ОДИНАКОВЫЕ ЗНАЧЕНИЯ**: ошибка = mistake, error (оба = "ошибка")

**СТРОГИЕ ПРАВИЛА:**
- ❌ **НЕ ВКЛЮЧАТЬ близкие по смыслу**: помощь ≠ поддержка, содействие
- ❌ **НЕ ВКЛЮЧАТЬ похожие значения**: быстро ≠ скоро, срочно
- ✅ **ТОЛЬКО если можно перевести ОДИНАКОВО**: mistake = error = ошибка
- ✅ **ТОЛЬКО естественные варианты перевода одного слова**
- Максимум 3-5 вариантов на язык
- Если точных вариантов нет - оставить пустым

**ПРОВЕРКА КОРРЕКТНОСТИ:**
✅ Допустимые ответы созданы ПОСЛЕ успешного тестирования всех языков
✅ Все варианты естественны для носителей языка
✅ Варианты соответствуют контексту предложения

**ПРИ ПРОБЛЕМАХ:**
1. Попробовать другие предложения
2. Если во многих языках не соответствует → сменить концепцию
3. Документировать исключения

**ПРАВИЛА ИСКЛЮЧЕНИЙ ДЛЯ ИМЕНИТЕЛЬНОГО ПАДЕЖА:**
1. **СТРОГИЙ КРИТЕРИЙ**: В таблице проверяем именительный падеж (⚠️ если не именительный)
2. **АНАЛИЗ ИСКЛЮЧЕНИЙ**: При ⚠️ обязательно анализировать:
   - Попробовать 2-3 разных предложения с именительным падежом
   - Если ВСЕ варианты неестественны → можно принять исключение
   - Обязательно документировать причину исключения
3. **ОГРАНИЧЕНИЯ**:
   - Основные языки (RU, EN, DE, FR, ES, IT, PT, NL, SV, DA, NO, PL, UK) - максимум 1-2 исключения
   - Всего исключений: максимум 3-5 языков из 37
4. **ПРИНЯТИЕ ИСКЛЮЧЕНИЯ**: ⚠️ → анализ → обоснование → ✅ с комментарием

**КРИТИЧЕСКИ ВАЖНО - MARKDOWN ФАЙЛЫ КАК ЛОГИ ТЕСТИРОВАНИЯ:**
- **Тестирование проводится в чате** по полному процессу из шага 4.2
- **Markdown файл = лог результатов** проведенного тестирования
- **НЕ тестируем В файле** - только сохраняем результаты тестирования
- **Последовательность**: Тест в чате → Анализ проблем → Исправления → Сохранение финального результата в MD

**ДЕТАЛЬНОЕ ОБЪЯСНЕНИЕ КРИТЕРИЕВ:**
**ВАЖНО**: Все критерии применяются к **ИТОГОВОМУ ПРЕДЛОЖЕНИЮ** (после подстановки слова), кроме "Падеж" - он применяется только к целевому слову.

- **Смысл**: итоговое предложение переводится с тем же значением на всех языках
- **Падеж**: ТОЛЬКО целевое слово (изучаемое) в именительном падеже, не склоняется
- **Грамматика**: простая конструкция (4-5 слов), правильные артикли/заглавные буквы
- **Естественность**: итоговое предложение звучит как живая речь носителя языка
- **A0-уровень**: понятно новичку, нет культурных/сложных моментов

**ПРИМЕР ЗАПОЛНЕНИЯ ТАБЛИЦЫ:**

| Язык | Слово | Предложение | Смысл | Падеж | Грамматика | Естественность | A0-уровень | Статус |
|------|-------|-------------|-------|-------|------------|----------------|------------|--------|
| RU   | есть  | Я хочу есть | ✅    | ✅    | ✅         | ✅             | ✅         | ✅     |
| EN   | eat   | I want to eat | ✅  | ✅    | ✅         | ✅             | ✅         | ✅     |
| DE   | essen | Ich will essen | ✅ | ✅    | ✅         | ✅             | ✅         | ✅     |
| FR   | manger| Je veux manger | ✅ | ✅    | ✅         | ✅             | ✅         | ✅     |
| ... | ...   | ...         | ...   | ...   | ...        | ...            | ...        | ...    |
| **ИТОГО: 37 языков** | | | | | | | | |

**ВАЖНО**: Таблица должна содержать ВСЕ 37 языков, особенно проблемные из списка 5.3!

Результаты тестирвоания выдать в виде таблицы, сохраняя следующую структуру:
- итоги таблицы по 1-му слову по всем языкам
- краткая сводку по 1-му слову по формату ниже
- ... затем также для всех последующих слов
- Затем итоговая сводка по всем 10 словам п.5.4

После каждого слова дать краткую сводку по языку. Например:
```
=== СЛОВО X: [русское]/[английское] ===
Концепт: [описание]
Паттерн: [тип паттерна]
Предложение: "[русское предложение]" / "[английское предложение]"

📊 ПОЛНАЯ ТАБЛИЦА ТЕСТИРОВАНИЯ (37 ЯЗЫКОВ):
[полная таблица со всеми языками и критериями]

АНАЛИЗ ПРОБЛЕМ:
✅ Проблем не найдено
ИЛИ
❌ НАЙДЕНЫ ПРОБЛЕМЫ:
- [описание проблем и предлагаемые решения]

СТАТУС: ✅ ГОТОВ / ❌ ТРЕБУЕТ ИСПРАВЛЕНИЯ
```

### **4.3 Проблемные языки для особого внимания**
**🔴 КРИТИЧЕСКИ ПРОБЛЕМНЫЕ (меняют форму целевого слова):**

> **🎯 КРИТЕРИЙ ПРОБЛЕМНОСТИ**: язык **МЕНЯЕТ ФОРМУ ЦЕЛЕВОГО СЛОВА** в предложениях
> - ❌ "Я вижу собак**у**" (не "собака") - **ПРОБЛЕМА**
> - ✅ "Собака лает" - **НЕ ПРОБЛЕМА**

**Падежные языки (6):**
- **Финский (fi)** - 15 падежей + агглютинация: talo→talossa, äiti→äitini
- **Русский (ru)** - 6 падежей: собака→собаку, дом→дома
- **Польский (pl)** - 7 падежей: dom→domu, mama→mamę
- **Немецкий (de)** - 4 падежа + артикли: der Hund→den Hund
- **Турецкий (tr)** - агглютинация: ev→evimiz, anne→annem
- **Украинский (uk)** - 7 падежей: мама→маму, дім→дому

**Сложные конструкции (4):**
- **Японский (ja)** - частицы は/を/に меняют смысл
- **Корейский (ko)** - агглютинация + вежливость
- **Арабский (ar)** - корневая система + падежи
- **Хинди (hi)** - падежи + постпозиции: लड़का→लड़के को

**Романские языки (5) - артикли + род:**
- **Итальянский (it)**, **Испанский (es)**, **Французский (fr)**, **Португальский (pt)**, **Нидерландский (nl)**

**✅ НЕ ПРОБЛЕМНЫЕ ЯЗЫКИ (22 языка) - слово НЕ меняет форму:**
- **Простые (3)**: en, id, ms - минимальная морфология
- **Средние (19)**: zh, th, vi, my, sv, da, no, kk, uz, pa, mr, bn, ur, ne, fa, tl, cb, ka, ro

### **4.4 ИТОГОВАЯ СВОДКА ПАКЕТА**

После тестирования всех 10 слов создать **ПОЛНУЮ СВОДКУ**:
**ВАЖНО**: Предложить обновление документации согласно п. 5.5 "Принципы непрерывного улучшения". Обновления добавляются в шаг 3.3 "Правила создания предложений A0 уровня".

Пример сводки: 
```
📈 ИТОГИ ПАКЕТА (слова 1-10):
✅ Готовы к импорту: [количество] слов
❌ Требуют исправления: [количество] слов

🔧 ПРОБЛЕМНЫЕ СЛОВА - ЧТО НУЖНО РЕШИТЬ:
  - Слово 3 "хочу/want": падежи в винительном падеже в ru/de/pl
    → ВАРИАНТЫ: 1) "Хочу есть" 2) "Хочу спать" 3) "Хочу играть"
    → РЕКОМЕНДАЦИЯ: вариант 1

  - Слово 7 "большой/big": артикли в немецком
    → ВАРИАНТЫ: 1) "Дом большой" 2) "Большой дом" 3) "Это большой"
    → РЕКОМЕНДАЦИЯ: вариант 1

📋 КРИТЕРИИ ТЕСТИРОВАНИЯ (нужны ли обновления правил в шаге 4.3):
  ❌ КРИТЕРИИ ТЕСТИРОВАНИЯ: обновления НЕ требуются
  ИЛИ
  ⚠️ КРИТЕРИИ ТЕСТИРОВАНИЯ: предлагаю добавить правило "[конкретное правило]" в шаг 4.3
  → См. пункт 5.5 "Принципы непрерывного улучшения" для процедуры обновления

**ВАЖНО**: Предлагать конкретные формулировки правил для добавления в документацию.

### **4.6 КОДОВАЯ ФРАЗА ВАЛИДАЦИИ**
**КРИТИЧЕСКИ ВАЖНО - ОБЯЗАТЕЛЬНАЯ ПРОВЕРКА ГОТОВНОСТИ:**

После успешного тестирования слова по всем 37 языкам и всем критериям, в разделе "РЕЗУЛЬТАТ ТЕСТИРОВАНИЯ" MD файла ОБЯЗАТЕЛЬНО добавить:

```
ВАЛИДАЦИЯ: ✅ ДАННОЕ СЛОВО БЫЛО ПРОТЕСТИРОВАНО ПО ВСЕМ 37 ЯЗЫКАМ ПО ВСЕМ ПАРАМЕТРАМ ТАБЛИЦЫ
```

**УСЛОВИЯ ДОБАВЛЕНИЯ КОДОВОЙ ФРАЗЫ:**
- ✅ Показана таблица тестирования по всем 37 языкам
- ✅ Проверены все критерии из таблицы тестирования (шаг 4.3)
- ✅ Не обнаружено критических проблем
- ✅ Предложение работает естественно на всех языках
- ✅ Пользователь подтвердил готовность слова

**БЕЗ ЭТОЙ ФРАЗЫ СЛОВО СЧИТАЕТСЯ НЕГОТОВЫМ!**

**ДОПОЛНИТЕЛЬНО**: Эту фразу также выводить в краткой сводке по слову в чате для подтверждения.

🎯 ЧТО ПОЛЬЗОВАТЕЛЮ НУЖНО РЕШИТЬ:
1. По слову 3: выбрать вариант (1, 2 или 3) или предложить свой
2. По слову 7: выбрать вариант (1, 2 или 3) или предложить свой
3. Подтвердить обновления правил: да/нет/изменить (если предложены)
4. Подтвердить изменения критериев: да/нет (если нужны)

🎯 СЛЕДУЮЩИЙ ШАГ: Ожидание указаний пользователя по проблемным словам
```

### **4.7 Workflow после пакетной обработки 10 слов**
**КРИТИЧЕСКИ ВАЖНО - ЧЕТКАЯ ПОСЛЕДОВАТЕЛЬНОСТЬ:**

**ПОСЛЕ ТЕСТИРОВАНИЯ 10 MARKDOWN ФАЙЛОВ С ТАБЛИЦАМИ:**
1. **Исправить проблемные слова** (если есть) согласно указаниям пользователя
2. **Обновить статусы в списке слов**: `⏳ TO_BE_CREATED` → `📝 MD_CREATED` для всех готовых слов
3. **Создать JSON файлы** для всех 10 слов (шаг 6)
4. **Обновить статусы в списке слов**: `📝 MD_CREATED` → `📄 JSON_CREATED` для всех созданных JSON
5. **Валидация и импорт** всех 10 JSON файлов (шаги 7-8)
6. **Обновить статусы в списке слов**: `📄 JSON_CREATED` → `✅ COMPLETED` для всех импортированных
7. **Перейти к следующим 10 словам** или завершить работу

**АЛЬТЕРНАТИВНЫЙ WORKFLOW (по выбору пользователя):**
- Можно обрабатывать слова по одному: MD → JSON → импорт → следующее слово
- Но пакетная обработка более эффективна

**КРИТИЧЕСКИ ВАЖНО**: В каждом Markdown файле должна быть заполнена полная таблица тестирования для всех 37 языков!

### **4.5 Принципы непрерывного улучшения**
**Каждая "ошибка" → улучшение системы:**
1. **Анализировать корень проблемы**: почему возникла "ошибка"?
2. **Предлагать улучшение правил**: как предотвратить в будущем?
3. **Думать системно**: какие смежные случаи могут быть?
4. **Спрашивать подтверждение**: "Давайте обновим инструкцию?"
5. **Документировать**: добавлять новые правила в шаг 4.3

**Шаблоны для сводки пакета:**
- **Если НЕ проблема**: "Особенность [описание] НЕ является проблемой, потому что [объяснение]. Предлагаю добавить в правила шага 4.3: '[новое правило]' - подтвердить?"
- **Если РЕАЛЬНАЯ проблема**: "Найдена проблема: [описание] создает сложность для A0. Варианты решения: 1) [вариант] 2) [вариант] 3) [вариант]. Рекомендация: [номер]"

### **4.6 Главное правило тестирования**
**⚠️ КРИТИЧЕСКИ ВАЖНО**: Если слово меняет форму в ЛЮБОМ из проблемных языков - нужно придумать другое предложение для ВСЕГО концепта (всех 37 языков).

**ИСКЛЮЧЕНИЕ - ПРИНЦИП РАЗУМНОГО КОМПРОМИССА**:
Если ВСЕ альтернативные конструкции звучат неестественно, можно принять исключение для 1-2 языков, сохранив естественность для остальных 35-36 языков.

**ПРИОРИТЕТЫ**:
1. **Естественность** (звучит как живая речь) - ВЫСШИЙ ПРИОРИТЕТ
2. **Именительный падеж** целевого слова - важно, но не критично
3. **Единообразие** всех языков - желательно, но не обязательно

**ФОРМУЛА**: Лучше 1 исключение + 36 естественных предложений, чем 37 неестественных предложений.

---

## 🔧 **ШАГ 5: ИСПРАВЛЕНИЕ ПРОБЛЕМ**

### **5.1 Получение указаний пользователя**
После сводки пакета пользователь дает указания по проблемным словам:
```
По слову 8: используй конструкцию "Я хочу есть"
По слову 11: замени на паттерн "Спасибо большое" / "Thanks much"
```

**РЕКОМЕНДАЦИЯ**: Использовать MCP Server Sequential Thinking для системного анализа исправлений и их влияния на смежные случаи.

### **5.2 Принцип исправления**
**КРИТИЧЕСКИ ВАЖНО**:
1. **НЕ исправляйте только один язык** - это нарушит единообразие
2. **Измените предложение для ВСЕГО concept_id** - для всех 37 языков сразу
3. **Найдите новую безопасную конструкцию** для всех языков

**Пример исправления:**
```
❌ Проблемное предложение:
- concept_id: "uuid-123" (слово "какой")
- Русский: "___ это цвет?" → проблема со склонением по родам
- Немецкий: "welche Farbe ist das?" → проблема со склонением

✅ Исправленное предложение:
- concept_id: "uuid-123" (слово "какой")
- Русский: "___ вопрос?" → "какой" ✅
- Немецкий: "welche Frage?" → "welche" ✅
- ВСЕ остальные языки тоже обновляются!
```

### **5.3 Повторное тестирование исправленных слов**
1. **Исправить проблемные концепты** согласно указаниям пользователя
2. **Повторно протестировать ТОЛЬКО исправленные слова** по полной таблице
3. **Показать результаты исправлений**
4. **Убедиться что все проблемы решены**

---

## 📄 **ШАГ 6: СОЗДАНИЕ JSON ФАЙЛОВ (ОПТИМИЗИРОВАННЫЙ ПОДХОД)**

### **6.1 ИСПОЛЬЗОВАНИЕ ШАБЛОНА (ЭКОНОМИЯ 95% ВРЕМЕНИ И ТОКЕНОВ)**

**ИСПРАВЛЕНО**: Шаблон теперь содержит все 37 языков с правильной структурой включая поле `word_info`.

**ВМЕСТО СОЗДАНИЯ С НУЛЯ** используйте готовый шаблон для всех 37 языков:

```bash
# 1. Копируем универсальный шаблон A0
cp backend/data/words/templates/A0_template.json backend/data/words/A0/XXXX_название_XX.json

# 2. Делаем массовые замены из MD файла
sed -i 's/\[CONCEPT_ID\]/реальный_uuid/g' файл.json
sed -i 's/\[PRIORITY\]/ultra_core|core|extended/g' файл.json
sed -i 's/\[PART_OF_SPEECH\]/verb|noun|pronoun|interjection/g' файл.json
sed -i 's/\[RU_WORD\]/реальное_слово/g' файл.json
sed -i 's/\[RU_SENTENCE_WITH___\]/реальное_предложение/g' файл.json
# ... для всех языков

# 3. Готово! Все 37 языков за 2-3 минуты
```

### **6.2 АЛГОРИТМ СОЗДАНИЯ JSON**

**ШАГ 1: ИЗВЛЕЧЕНИЕ ДАННЫХ ИЗ MD ФАЙЛА**
- concept_id (из метаданных)
- priority (ultra_core/core/extended)
- part_of_speech (verb/noun/pronoun/interjection)
- Все переводы слов из таблицы тестирования
- Все предложения из таблицы тестирования

**ШАГ 2: КОПИРОВАНИЕ ШАБЛОНА**
```bash
cp backend/data/words/templates/A0_template.json backend/data/words/A0/XXXX_название_XX.json
```

**ШАГ 3: МАССОВЫЕ ЗАМЕНЫ**
- Заменить [CONCEPT_ID] на реальный UUID
- Заменить [PRIORITY] на реальный приоритет
- Заменить [PART_OF_SPEECH] на реальную часть речи
- Заменить [LANG_WORD] на переводы из таблицы
- Заменить [LANG_SENTENCE_WITH___] на предложения из таблицы

### **6.3 ЭКОНОМИЯ РЕСУРСОВ**
- **Время**: с 10-15 минут → 2-3 минуты (85% экономия)
- **Токены**: с 2000+ → 100-200 (95% экономия)
- **Языки**: автоматически все 37 языков
- **Ошибки**: минимум (шаблон проверен)



### **6.4 ПРАКТИЧЕСКИЙ ПРИМЕР СОЗДАНИЯ JSON**

**Пример для слова "есть/eat" (0008_A0_ultra_core_08.json):**

```bash
# 1. Копируем шаблон
cp backend/data/words/templates/A0_template.json backend/data/words/A0/0008_A0_ultra_core_08.json

# 2. Заменяем метаданные
sed -i 's/\[CONCEPT_ID\]/550e8400-e29b-41d4-a716-446655440008/g' 0008_A0_ultra_core_08.json
sed -i 's/\[PRIORITY\]/ultra_core/g' 0008_ultra_core_08.json
sed -i 's/\[PART_OF_SPEECH\]/verb/g' 0008_ultra_core_08.json

# 3. Заменяем переводы (из таблицы тестирования MD файла)
sed -i 's/\[RU_WORD\]/есть/g' 0008_A0_ultra_core_08.json
sed -i 's/\[EN_WORD\]/eat/g' 0008_A0_ultra_core_08.json
sed -i 's/\[DE_WORD\]/essen/g' 0008_A0_ultra_core_08.json
# ... для всех 37 языков

# 4. Заменяем предложения
sed -i 's/\[RU_SENTENCE_WITH___\]/Я хочу есть/g' 0008_A0_ultra_core_08.json
sed -i 's/\[EN_SENTENCE_WITH___\]/I want to eat/g' 0008_ultra_core_08.json
# ... для всех 37 языков

# 5. Готово! Файл с 37 языками создан за 2-3 минуты
```

### **6.5 ИМЕНОВАНИЕ ФАЙЛОВ**
**НОВЫЙ ФОРМАТ**: `{номер_слова}_{уровень}_{приоритет}_{номер_в_уровне}.{расширение}`

**ПРИМЕРЫ:**
- `0009_A0_ultra_core_09.json` - 9-е слово, A0 уровень, ultra_core приоритет, 9-й в ultra_core
- `0016_A0_core_01.json` - 16-е слово, A0 уровень, core приоритет, 1-й в core
- `0146_A1_02.json` - 146-е слово, A1 уровень, без приоритета, 2-й в A1
- `0200_B1_15.json` - 200-е слово, B1 уровень, без приоритета, 15-й в B1

**КОМПОНЕНТЫ:**
- `номер_слова` - глобальный номер слова (0001-9999)
- `уровень` - A0, A1, B1, B2, C1, C2
- `приоритет` - ultra_core, core, extended (ТОЛЬКО для A0, для других уровней может отсутствовать)
- `номер_в_уровне` - порядковый номер внутри уровня или приоритета (01-99)

**ПРАВИЛА:**
- Приоритет указывается только если есть (в основном A0)
- Для уровней без приоритета: `{номер}_{уровень}_{номер_в_уровне}`
- Применяется к MD, JSON файлам и концептам

### **6.6 ОРГАНИЗАЦИЯ КОНЦЕПТОВ**

**НОВАЯ СИСТЕМА КОНЦЕПТОВ** - отдельный файл для каждого слова:

**СТРУКТУРА ПАПОК:**
```
backend/data/concepts/
├── A0/
│   ├── 0008_A0_ultra_core_08_concept.json
│   ├── 0009_A0_ultra_core_09_concept.json
│   ├── 0010_A0_ultra_core_10_concept.json
│   └── ... (все 146 концептов A0)
├── A1/
│   └── ... (будущие концепты A1)
└── legacy/
    ├── A0_concepts.json (backup старых файлов)
    └── A0_complete_concepts.json (backup)
```

**СВЯЗЬ ФАЙЛОВ** - для каждого слова создается 3 файла:
- `0010_A0_ultra_core_10.md` - тестирование слова
- `0010_A0_ultra_core_10.json` - переводы на 37 языков
- `0010_A0_ultra_core_10_concept.json` - описание концепта

**ФОРМАТ ФАЙЛА КОНЦЕПТА:**
```json
{
  "concept_id": "550e8400-e29b-41d4-a716-446655440010",
  "level": "A0",
  "priority": "ultra_core",
  "category": "social_interaction",
  "concept_name": "help_request",
  "description": {
    "en": "Request for assistance or support",
    "ru": "Просьба о помощи или поддержке"
  },
  "semantic_field": "social_needs",
  "usage_context": "emergency_situations",
  "examples": {
    "en": "I want help, I need help",
    "ru": "Я хочу помощь, Мне нужно помощь"
  },
  "translation_notes": {
    "general": "Use the most common and basic form of 'help' in each language",
    "specific": {
      "asian_languages": "Focus on natural expression",
      "european_languages": "Use nominative case when possible"
    }
  },
  "difficulty_level": "beginner",
  "frequency": "high",
  "cultural_notes": "Universal concept across all cultures",
  "related_concepts": [],
  "antonyms": [],
  "created_date": "2024-06-26",
  "last_updated": "2024-06-26"
}
```

**ПРЕИМУЩЕСТВА НОВОЙ СИСТЕМЫ:**
- ✅ Соответствует схеме именования файлов
- ✅ Модульность - легко работать с отдельными концептами
- ✅ Git-friendly - четкие изменения по файлам
- ✅ Масштабируемость - не создает огромные файлы
- ✅ Консистентность - принцип "1 слово = 1 концепт = 1 файл"
- `0052_extended_01.json` - 52-е слово, extended приоритет

**ПРИНЦИП**: 1 концепт = 1 файл = 37 языков = 37 записей в JSON

### **6.6 ПРЕИМУЩЕСТВА ШАБЛОННОГО ПОДХОДА**

**СТАРЫЙ ПОДХОД (создание с нуля):**
- ⏱️ Время: 10-15 минут на файл
- 🔢 Токены: 2000+ токенов
- 🌍 Языки: часто неполные (3-15 из 37)
- ❌ Ошибки: высокий риск при копировании

**НОВЫЙ ПОДХОД (шаблонный):**
- ⏱️ Время: 2-3 минуты на файл (85% экономия)
- 🔢 Токены: 100-200 токенов (95% экономия)
- 🌍 Языки: автоматически все 37 языков
- ✅ Ошибки: минимум (шаблон проверен)

**ОБЯЗАТЕЛЬНО**: Всегда используйте шаблонный подход для создания JSON файлов!

### **6.7 СОСТАВНЫЕ СЛОВА**
**✅ ПОДДЕРЖИВАЮТСЯ**: Фразовые глаголы (`give up`), составные слова (`сдаваться`), разделяемые глаголы (`aufstehen`).
**ПРИНЦИП**: Один пропуск `___` заменяется любым количеством слов из `correct_answers`.
**ПРИМЕР**: `"Never ___"` + `["give up"]` = `"Never give up"`.

### **6.8 КОНТРОЛЬ КАЧЕСТВА ПРИ ПЕРЕНОСЕ**

**ПРОБЛЕМА**: При переносе из MD в JSON может теряться информация (заглавные буквы, пунктуация, смысл).

**РЕШЕНИЕ - ДВОЙНАЯ ПРОВЕРКА:**
```bash
# 1. Создаем JSON из MD
sed -i '' 's/\[RU_WORD\]/есть/g' файл.json

# 2. БЫСТРАЯ ПРОВЕРКА КАЧЕСТВА
./scripts/check_json_quality.sh файл.json

# 3. Если ошибка - исправляем точечно
sed -i '' 's/"Я хочу есть"/"Я хочу ___"/g' файл.json

# 4. Валидация
python -m scripts.words.validate_word файл.json
```

**ПРИНЦИП**: Лучше потратить 2 минуты на проверку, чем переделывать весь файл.

### **6.9 СПЕЦИАЛЬНАЯ ПРОВЕРКА CB (СЕБУАНО)**
**ОБЯЗАТЕЛЬНО** для каждого слова проверить CB фразы по справочнику:
```bash
# Проверка CB фраз
grep -A 5 '"language": "cb"' файл.json

# Сверка с правилами
cat backend/data/languages/cb_cebuano_guide.md
```
**Частые ошибки**: "kong" → "ko", "katulog" → "tulog", буквальный перевод.

---

## ✅ **ШАГ 7: ВАЛИДАЦИЯ**

### **7.1 Копирование для валидации**
```bash
# Скопировать из папки разработки в папку импорта
cp data/words/A0/ultra_core_07.json data/words/new/ultra_core_07.json
```

### **7.2 Техническая проверка формата**
```bash
# Валидация JSON структуры и обязательных полей
python -m scripts.words validate data/words/new/ultra_core_07.json
```

### **7.3 Проверка готовности к импорту**
```bash
# Расширенная проверка готовности
python scripts/check_import_ready.py
```

### **7.4 Возможные ошибки валидации**
- Проверьте сообщения об ошибках в логах `backend/logs/import_words.log`
- Убедитесь, что все обязательные поля заполнены правильно
- Проверьте формат UUID в поле `concept_id`
- Проверьте коды языков из `frontend/constants/languages.ts`

---

## 📥 **ШАГ 8: ИМПОРТ В БАЗУ ДАННЫХ**

### **8.1 Подготовка окружения (ОБЯЗАТЕЛЬНО)**
```bash
# Активация виртуального окружения
cd /Users/<USER>/MEMO
source venv/bin/activate

# Проверка подключения к MongoDB
python -m scripts.words check_db
```

### **8.2 Импорт слов**
```bash
# Импорт всех файлов из папки new/
python -m scripts.words import
```

### **8.2 Проверка результатов**
- Успешно обработанные файлы будут перемещены в `backend/data/words/imported/`
- Файлы с ошибками - в `backend/data/words/invalid/`
- Подробные логи доступны в `backend/logs/import_words.log`

### **8.3 Проверка импорта в БД**
```bash
# Проверить недавно добавленные слова
python -m scripts.words check_recent --minutes 5

# Проверить подключение к БД
python -m scripts.words check_db
```

### **8.4 Дублирование слов**
- Скрипт автоматически проверяет наличие слов по комбинации `word` и `language`
- Существующие слова не будут добавлены повторно

---

## 📊 **ШАГ 9: ОБНОВЛЕНИЕ СТАТУСОВ**

### **9.1 Система статусов в списке слов**
**КРИТИЧЕСКИ ВАЖНО**: После каждого этапа обновлять статус в соответствующем файле списка слов (`md/A0_word_list.md`, `md/A1_word_list.md`, etc.)

**СТАТУСЫ СЛОВ:**
- `⏳ TO_BE_CREATED` - слово не начато
- `📝 MD_CREATED` - создан и протестирован Markdown файл с таблицей
- `📄 JSON_CREATED` - создан JSON файл, прошел валидацию
- `✅ COMPLETED` - импортировано в базу данных

**ПРОЦЕДУРА ОБНОВЛЕНИЯ СТАТУСОВ:**
1. **После создания Markdown файла с таблицей**: `⏳ TO_BE_CREATED` → `📝 MD_CREATED`
   - **ОБЯЗАТЕЛЬНО**: Добавить название концепта в колонку "Концепт" в `md/A0_word_list.md`
2. **После создания JSON файла**: `📝 MD_CREATED` → `📄 JSON_CREATED`
3. **После успешного импорта**: `📄 JSON_CREATED` → `✅ COMPLETED`

### **9.2 Обновление списка слов после каждого этапа**

### **9.2 Обновление README в папках уровней**
Обновить файл `backend/data/words/A0/README.md`:
- Изменить статус файла с "⏳ TO BE CREATED" на "✅ COMPLETED"
- Добавить таблицу с концептами (UUID, русские слова, английские слова, описания)
- Обновить прогресс (X/15 ULTRA-CORE, X/37 CORE, X/93 EXTENDED)

### **9.3 Пример обновления**
```markdown
### `ultra_core_07.json` - Basic Needs & Actions ✅ COMPLETED
| Concept ID | Russian | English | Description |
|------------|---------|---------|-------------|
| 550e8400-e29b-41d4-a716-************ | хочу | want | Basic desire |

**Прогресс**: 7/15 ULTRA-CORE завершено
```

### **9.2 Обновление README в папках уровней**
Обновить файл `backend/data/words/A0/README.md`:
- Изменить статус файла с "⏳ TO BE CREATED" на "✅ COMPLETED"
- Добавить таблицу с концептами (UUID, русские слова, английские слова, описания)
- Обновить прогресс (X/15 ULTRA-CORE, X/37 CORE, X/93 EXTENDED)

### **9.3 Пример обновления**
```markdown
### `ultra_core_07.json` - Basic Needs & Actions ✅ COMPLETED
| Concept ID | Russian | English | Description |
|------------|---------|---------|-------------|
| 550e8400-e29b-41d4-a716-************ | хочу | want | Basic desire |

**Прогресс**: 7/15 ULTRA-CORE завершено
```

---

## 📋 **ПРИЛОЖЕНИЯ**

### **Структура папок и workflow**

#### **Организация файлов**
```
backend/data/words/
├── A0/                          # Папка для A0 слов (разработка)
│   ├── ultra_core_01.json      # Концепт 1 (я)
│   ├── ultra_core_02.json      # Концепт 2 (ты)
│   ├── ultra_core_03.json      # Концепт 3 (да)
│   ├── ultra_core_04.json      # Концепт 4 (нет)
│   ├── ultra_core_05.json      # Концепт 5 (что)
│   ├── ultra_core_06.json      # Концепт 6 (где)
│   ├── ultra_core_07.json      # Концепт 7 (хочу)
│   ├── ultra_core_08.json      # Концепт 8 (есть/кушать)
│   └── ... (до ultra_core_15)  # Остальные ULTRA-CORE
├── A1/                         # Папка для A1 слов (в будущем)
├── temp_txt/                   # Временные TXT файлы для тестирования
│   ├── ultra_core_08.txt       # Отдельный файл для каждого слова (как JSON)
│   ├── ultra_core_09.txt       # Отдельный файл для каждого слова (как JSON)
│   ├── core_01.txt             # Отдельный файл для каждого слова (как JSON)
│   └── archived/               # Архивированные TXT файлы
├── new/                        # Готовые к импорту файлы
├── imported/                   # Успешно импортированные файлы
├── invalid/                    # Файлы с ошибками
└── logs/import_words.log       # Логи импорта
```

#### **Принципы организации**
1. **По уровням**: отдельная папка для каждого уровня (A0, A1, B1, etc.)
2. **По концептам**: 1 концепт на файл (1 слово × 37 языков = 37 записей)
3. **Документация**: README.md в каждой папке уровня
4. **Workflow**: разработка в папке уровня → копирование в `new/` → импорт → архив в `imported/`

#### **Рабочий процесс с папками**
1. **Создание**: файлы создаются в папке уровня (`A0/`, `A1/`, etc.)
2. **Тестирование**: файлы остаются в папке уровня для тестирования
3. **Валидация**: копирование в `new/` только готовых файлов
4. **Импорт**: автоматическое перемещение в `imported/` после успешного импорта
5. **Ошибки**: файлы с ошибками попадают в `invalid/`

---

### **Команды и инструменты**

#### **Основные команды**
```bash
# Проверка подключения к БД
python -m scripts.words check_db

# Очистка коллекции (с подтверждением)
python -m scripts.words clear_words

# Принудительная очистка (без подтверждения)
python -m scripts.words clear_words --force
```

#### **Работа с концептами**
```bash
# Список всех концептов A0
python -m scripts.concepts list A0

# Просмотр концепта и его слов
python -m scripts.words concept 550e8400-e29b-41d4-a716-************

# Создать новый концепт
python -m scripts.concepts create --word "мама" --en "mother" --category "family"

# Импорт новых концептов
python -m scripts.concepts import data/concepts/new_concepts.json
```

#### **Дополнительные инструменты**
```bash
# Создать языковые справочники
python scripts/generate_language_guides.py

# Создать шаблоны JSON файлов
python scripts/generate_word_templates.py

# Расширенное тестирование
python test_a0_word_creation.py

# Проверка готовности к импорту
python scripts/check_import_ready.py
```

---

### **Примеры и шаблоны**

#### **Примеры ПРАВИЛЬНЫХ A0 конструкций**
**✅ Безопасные примеры (проверены по всем 37 языкам):**

```json
{
  "concept_id": "uuid-1",
  "word": "мама",
  "language": "ru",
  "level": "A0",
  "priority": "ultra_core",
  "examples": [{"sentence": "___ работает", "correct_answers": ["мама"]}]
}
```

**Проверка по сложным языкам:**
- 🇷🇺 Русский: "мама" ✅ (им.п.)
- 🇩🇪 Немецкий: "Mutter arbeitet" → "Mutter" ✅ (им.п.)
- 🇫🇮 Финский: "äiti työskentelee" → "äiti" ✅ (без суффиксов)
- 🇹🇷 Турецкий: "anne çalışıyor" → "anne" ✅ (без суффиксов)
- 🇵🇱 Польский: "mama pracuje" → "mama" ✅ (им.п.)

#### **Разнообразие безопасных конструкций**
**✅ Хорошее разнообразие:**
- "___ лает" (действие)
- "___ большой" (описание)
- "___ здесь" (местоположение)
- "Это ___" (указание)
- "___ работает" (профессиональная деятельность)
- "___ светит" (природные явления)
- "___ холодная" (физические свойства)

**❌ Плохое разнообразие (нарушает правила):**
- "Моя ___" (притяжательность)
- "Я вижу ___" (падежи)
- "Иду к ___" (падежи)
- "Думаю о ___" (падежи)

#### **Структура приоритетов A0**
- **🔥 ULTRA-CORE (15 слов)** - "первые слова ребенка", абсолютный минимум
- **🟡 CORE (35 слов)** - базовый набор для выживания
- **📚 EXTENDED (96 слов)** - расширенный набор для полноценного общения

**Поле priority в JSON:**
```json
{
  "priority": "ultra_core"    // "ultra_core", "core" или "extended"
}
```

---

### **Возможные проблемы и решения**

#### **Проблемы с файлами**
1. **Файл не найден**
   - Убедитесь, что файл находится в правильной папке
   - Проверьте правильность пути к файлу

#### **Проблемы с базой данных**
1. **Ошибка подключения к MongoDB**
   - Проверьте строку подключения в `.env`
   - Убедитесь, что IP-адрес в белом списке MongoDB Atlas

#### **Проблемы с тестированием**
1. **Слово меняет форму в некоторых языках**
   - Изменить предложение для ВСЕГО концепта (всех 37 языков)
   - Найти новую безопасную конструкцию
   - Обновить правила в `EXAMPLE.md` если нужно

2. **Неестественное звучание**
   - Проанализировать: реальная проблема или особенность языка?
   - Предложить альтернативные варианты
   - Обновить правила для будущих случаев

---

### **Принципы качества**

#### **Основные принципы**
1. **Единообразие** - одно предложение на концепт для всех языков
2. **Простота** - только базовые грамматические конструкции для A0
3. **Естественность** - предложения должны звучать как живая речь
4. **Универсальность** - работает на всех 37 языках без исключений

#### **Критерии качества**
- **Все правила A0** описаны в шаге 5 этого файла
- **Процедура тестирования** описана в шаге 5
- **Техническое описание JSON формата** находится в `backend/data/words/EXAMPLE.md`

#### **Непрерывное улучшение**
**Процедура описана в шаге 5.5** - каждая проблема становится улучшением системы.

---

## 🏆 **ИСТОРИЯ УСПЕХА: КАК ПРОБЛЕМЫ СТАЛИ РЕШЕНИЯМИ**

### **📚 Эволюция правил:**

**ЭТАП 1: Простые слова (1-7)**
- Проблема: Нет четких критериев
- Решение: Создали таблицу тестирования
- Результат: Структурированный процесс

**ЭТАП 2: Грамматические проблемы (8-17)**
- Проблема: CB язык дает ошибки ("kong" vs "ko")
- Решение: Краткие памятки в справочниках
- Результат: Системная проверка редких языков

**ЭТАП 3: Сложные концепты (быть/be)**
- Проблема: Азиатские языки используют "стать" вместо "быть"
- Решение: Правило семантического соответствия
- Результат: Можем работать с ЛЮБЫМИ сложными концептами!

### **🎯 Что это означает для будущих слов:**

**ДЛЯ ПРОСТЫХ СЛОВ**: Процесс автоматизирован, правила отработаны
**ДЛЯ СРЕДНИХ СЛОВ**: Есть шаблоны и проверенные подходы
**ДЛЯ СЛОЖНЫХ СЛОВ**: Семантические правила решают "невозможные" случаи

### **💡 Принцип работы:**
1. **Встретили проблему** → не паникуем, это нормально
2. **Анализируем причину** → понимаем лингвистическую суть
3. **Создаем правило** → документируем решение
4. **Применяем системно** → помогает всем будущим словам

**РЕЗУЛЬТАТ**: Каждое новое слово создается быстрее предыдущего, потому что система становится умнее!

---

> **📋 ВАЖНО**: Этот файл содержит процедуру и workflow. Техническое описание JSON формата находится в `backend/data/words/EXAMPLE.md`.
