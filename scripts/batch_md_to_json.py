#!/usr/bin/env python3
"""
Batch конвертер MD файлов в JSON
Автор: Augment Agent
Дата: 2025-01-26
"""

import os
import sys
from pathlib import Path
from md_to_json_converter import MDToJSONConverter

def batch_convert_directory(md_dir: str, json_dir: str = None):
    """Конвертация всех MD файлов в директории"""
    
    converter = MDToJSONConverter()
    md_path = Path(md_dir)
    
    if not md_path.exists():
        print(f"❌ Директория не найдена: {md_dir}")
        return False
    
    # Поиск всех MD файлов
    md_files = list(md_path.glob("**/*.md"))
    
    if not md_files:
        print(f"❌ MD файлы не найдены в: {md_dir}")
        return False
    
    print(f"🔍 Найдено {len(md_files)} MD файлов")
    
    success_count = 0
    error_count = 0
    
    for md_file in md_files:
        print(f"\n{'='*60}")
        print(f"📄 Обработка: {md_file.name}")
        
        # Определение выходного пути
        if json_dir:
            output_path = Path(json_dir) / md_file.name.replace('.md', '.json')
        else:
            output_path = str(md_file).replace('/md/', '/json/').replace('.md', '.json')
        
        # Конвертация
        if converter.convert_file(str(md_file), output_path):
            success_count += 1
            print(f"✅ Успешно: {md_file.name}")
        else:
            error_count += 1
            print(f"❌ Ошибка: {md_file.name}")
    
    print(f"\n{'='*60}")
    print(f"📊 ИТОГОВАЯ СТАТИСТИКА:")
    print(f"✅ Успешно: {success_count}")
    print(f"❌ Ошибки: {error_count}")
    print(f"📈 Успешность: {success_count/(success_count+error_count)*100:.1f}%")
    
    return error_count == 0

def main():
    """Основная функция"""
    if len(sys.argv) < 2:
        print("Использование:")
        print("  python batch_md_to_json.py <md_directory> [json_directory]")
        print("\nПримеры:")
        print("  python batch_md_to_json.py vocabulary/md/A0")
        print("  python batch_md_to_json.py vocabulary/md vocabulary/json")
        sys.exit(1)
    
    md_dir = sys.argv[1]
    json_dir = sys.argv[2] if len(sys.argv) > 2 else None
    
    print("🚀 BATCH КОНВЕРТЕР MD → JSON")
    print(f"📂 Входная директория: {md_dir}")
    if json_dir:
        print(f"📁 Выходная директория: {json_dir}")
    else:
        print("📁 Выходная директория: автоматическая (md → json)")
    
    success = batch_convert_directory(md_dir, json_dir)
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
