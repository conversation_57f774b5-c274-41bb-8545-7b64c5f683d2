#!/usr/bin/env python3
"""
Надежный конвертер MD файлов в JSON для проекта Word Master
Автор: Augment Agent
Дата: 2025-01-26
"""

import re
import json
import sys
import os
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import uuid

class MDToJSONConverter:
    """Конвертер MD файлов в JSON с множественными проверками"""
    
    def __init__(self):
        self.language_codes = {
            'Русский': 'ru', 'English': 'en', 'Deutsch': 'de', 'Français': 'fr',
            'Español': 'es', 'Italiano': 'it', 'Português': 'pt', 'Nederlands': 'nl',
            'Polski': 'pl', 'Українська': 'uk', 'Suomi': 'fi', 'Svenska': 'sv',
            'Dansk': 'da', 'Norsk': 'no', 'Türkçe': 'tr', 'العربية': 'ar',
            'فارسی': 'fa', 'हिन्दी': 'hi', 'বাংলা': 'bn', 'اردو': 'ur',
            'ਪੰਜਾਬੀ': 'pa', 'मराठी': 'mr', 'नेपाली': 'ne', '中文': 'zh',
            '日本語': 'ja', '한국어': 'ko', 'ไทย': 'th', 'Tiếng Việt': 'vi',
            'မြန်မာ': 'my', 'Bahasa Indonesia': 'id', 'Bahasa Melayu': 'ms',
            'Tagalog': 'tl', 'Cebuano': 'cb', 'Қазақ': 'kk', 'O\'zbek': 'uz',
            'Magyar': 'hu', 'Română': 'ro', 'ქართული': 'ka'
        }
        self.required_languages = set(self.language_codes.values())
        self.errors = []
        self.warnings = []
    
    def validate_md_file(self, file_path: str) -> bool:
        """Предварительная валидация MD файла"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Проверка наличия обязательных маркеров
            required_markers = [
                'PARSER_START_METADATA',
                'PARSER_END_METADATA', 
                'PARSER_START_TABLE',
                'PARSER_END_TABLE',
                'PARSER_VALIDATION_STATUS'
            ]
            
            for marker in required_markers:
                if marker not in content:
                    self.errors.append(f"Отсутствует маркер: {marker}")
                    return False
            
            return True
            
        except Exception as e:
            self.errors.append(f"Ошибка чтения файла: {e}")
            return False
    
    def extract_metadata(self, content: str) -> Dict:
        """Извлечение метаданных из MD файла"""
        metadata = {}
        
        # Поиск блока метаданных
        metadata_pattern = r'<!-- PARSER_START_METADATA -->(.*?)<!-- PARSER_END_METADATA -->'
        metadata_match = re.search(metadata_pattern, content, re.DOTALL)
        
        if not metadata_match:
            self.errors.append("Не найден блок метаданных")
            return metadata
        
        metadata_text = metadata_match.group(1).strip()
        
        # Парсинг метаданных
        for line in metadata_text.split('\n'):
            line = line.strip()
            if ':' in line:
                key, value = line.split(':', 1)
                metadata[key.strip()] = value.strip()
        
        # Валидация обязательных полей
        required_fields = ['concept_id', 'word_ru', 'word_en', 'priority', 'level']
        for field in required_fields:
            if field not in metadata:
                self.errors.append(f"Отсутствует обязательное поле: {field}")
        
        return metadata
    
    def parse_translation_table(self, content: str) -> List[Dict]:
        """Парсинг таблицы переводов"""
        translations = []
        
        # Поиск блока таблицы
        table_pattern = r'<!-- PARSER_START_TABLE -->(.*?)<!-- PARSER_END_TABLE -->'
        table_match = re.search(table_pattern, content, re.DOTALL)
        
        if not table_match:
            self.errors.append("Не найдена таблица переводов")
            return translations
        
        table_text = table_match.group(1).strip()
        lines = table_text.split('\n')
        
        # Поиск строк таблицы (пропускаем заголовок и разделитель)
        table_rows = []
        for line in lines:
            line = line.strip()
            if line.startswith('|') and not line.startswith('|---'):
                # Проверяем что это не заголовок
                if 'Язык' not in line and 'Код' not in line:
                    table_rows.append(line)
        
        # Парсинг каждой строки
        for row in table_rows:
            cells = [cell.strip() for cell in row.split('|')[1:-1]]  # Убираем пустые ячейки по краям
            
            if len(cells) >= 4:  # Минимум: язык, код, слово, предложение
                translation = {
                    'language_name': cells[0],
                    'language_code': cells[1] if len(cells) > 1 else '',
                    'word': cells[2] if len(cells) > 2 else '',
                    'sentence': cells[3] if len(cells) > 3 else '',
                    'status': cells[-1] if len(cells) > 4 else '❌'
                }
                translations.append(translation)
        
        return translations
    
    def validate_translations(self, translations: List[Dict]) -> bool:
        """Валидация переводов"""
        found_languages = set()
        
        for translation in translations:
            lang_code = translation.get('language_code', '')
            if lang_code:
                found_languages.add(lang_code)
            
            # Проверка обязательных полей
            if not translation.get('word'):
                self.warnings.append(f"Пустое слово для языка {translation.get('language_name', 'неизвестный')}")
            
            if not translation.get('sentence'):
                self.warnings.append(f"Пустое предложение для языка {translation.get('language_name', 'неизвестный')}")
        
        # Проверка наличия всех языков
        missing_languages = self.required_languages - found_languages
        if missing_languages:
            self.errors.append(f"Отсутствуют языки: {', '.join(missing_languages)}")
            return False
        
        return True
    
    def build_json_structure(self, metadata: Dict, translations: List[Dict]) -> List[Dict]:
        """Создание JSON структуры"""
        json_data = []
        
        for translation in translations:
            # Извлечение word_info из предложения или метаданных
            word_info = self.extract_word_info(translation, metadata)
            
            # Создание записи для каждого языка
            record = {
                "concept_id": metadata.get('concept_id', ''),
                "word": translation.get('word', ''),
                "language": translation.get('language_code', ''),
                "level": metadata.get('level', 'A0'),
                "priority": metadata.get('priority', 'core'),
                "part_of_speech": metadata.get('part_of_speech', 'unknown'),
                "examples": [
                    {
                        "sentence": translation.get('sentence', '').replace(translation.get('word', ''), '___'),
                        "correct_answers": [translation.get('word', '')],
                        "word_info": word_info
                    }
                ]
            }
            json_data.append(record)
        
        return json_data
    
    def extract_word_info(self, translation: Dict, metadata: Dict) -> str:
        """Извлечение информации о слове"""
        lang_code = translation.get('language_code', '')
        
        # Базовая информация на основе части речи
        part_of_speech = metadata.get('part_of_speech', 'unknown')
        
        if lang_code == 'ru':
            if part_of_speech == 'verb':
                return "Глагол, инфинитив"
            elif part_of_speech == 'noun':
                return "Существительное"
            elif part_of_speech == 'adjective':
                return "Прилагательное"
            else:
                return "Слово"
        else:
            if part_of_speech == 'verb':
                return "Verb, infinitive"
            elif part_of_speech == 'noun':
                return "Noun"
            elif part_of_speech == 'adjective':
                return "Adjective"
            else:
                return "Word"
    
    def validate_json_output(self, json_data: List[Dict]) -> bool:
        """Финальная валидация JSON"""
        if not json_data:
            self.errors.append("Пустой JSON результат")
            return False
        
        # Проверка структуры каждой записи
        required_fields = ['concept_id', 'word', 'language', 'level', 'priority', 'examples']
        
        for i, record in enumerate(json_data):
            for field in required_fields:
                if field not in record:
                    self.errors.append(f"Запись {i}: отсутствует поле {field}")
                    return False
        
        return True
    
    def convert_file(self, md_path: str, output_path: Optional[str] = None) -> bool:
        """Основная функция конвертации"""
        self.errors = []
        self.warnings = []
        
        print(f"🔄 Конвертация файла: {md_path}")
        
        # 1. Предварительная валидация
        if not self.validate_md_file(md_path):
            print("❌ Ошибка валидации MD файла")
            self.print_errors()
            return False
        
        # 2. Чтение файла
        try:
            with open(md_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            self.errors.append(f"Ошибка чтения файла: {e}")
            self.print_errors()
            return False
        
        # 3. Извлечение метаданных
        metadata = self.extract_metadata(content)
        if self.errors:
            print("❌ Ошибка извлечения метаданных")
            self.print_errors()
            return False
        
        # 4. Парсинг таблицы
        translations = self.parse_translation_table(content)
        if self.errors:
            print("❌ Ошибка парсинга таблицы")
            self.print_errors()
            return False
        
        # 5. Валидация переводов
        if not self.validate_translations(translations):
            print("❌ Ошибка валидации переводов")
            self.print_errors()
            return False
        
        # 6. Создание JSON
        json_data = self.build_json_structure(metadata, translations)
        
        # 7. Финальная валидация
        if not self.validate_json_output(json_data):
            print("❌ Ошибка валидации JSON")
            self.print_errors()
            return False
        
        # 8. Сохранение результата
        if not output_path:
            output_path = md_path.replace('.md', '.json').replace('/md/', '/json/')
        
        try:
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ Успешно сконвертировано: {output_path}")
            if self.warnings:
                print("⚠️ Предупреждения:")
                for warning in self.warnings:
                    print(f"  - {warning}")
            
            return True
            
        except Exception as e:
            self.errors.append(f"Ошибка сохранения JSON: {e}")
            self.print_errors()
            return False
    
    def print_errors(self):
        """Вывод ошибок"""
        if self.errors:
            print("🚨 ОШИБКИ:")
            for error in self.errors:
                print(f"  ❌ {error}")
        
        if self.warnings:
            print("⚠️ ПРЕДУПРЕЖДЕНИЯ:")
            for warning in self.warnings:
                print(f"  ⚠️ {warning}")

def main():
    """Основная функция"""
    if len(sys.argv) < 2:
        print("Использование: python md_to_json_converter.py <path_to_md_file> [output_path]")
        sys.exit(1)
    
    md_path = sys.argv[1]
    output_path = sys.argv[2] if len(sys.argv) > 2 else None
    
    converter = MDToJSONConverter()
    success = converter.convert_file(md_path, output_path)
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
