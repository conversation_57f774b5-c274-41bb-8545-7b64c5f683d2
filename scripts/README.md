# 🔧 SCRIPTS - Скрипты проекта uMemo

Утилиты и скрипты для установки, тестирования и развертывания проекта.

## 📁 СТРУКТУРА

```
scripts/
├── setup/              # 🛠️ Установка и настройка
│   ├── install_mongodb.sh
│   └── quick-setup-linux.sh
├── testing/            # 🧪 Тестирование
│   └── run_tests.py
└── deployment/         # 🚀 Развертывание
    └── (будущие скрипты)
```

## 🚀 ИСПОЛЬЗОВАНИЕ

### **Установка:**
```bash
# Установка MongoDB
./scripts/setup/install_mongodb.sh

# Быстрая настройка на Linux
./scripts/setup/quick-setup-linux.sh
```

### **Тестирование:**
```bash
# Запуск тестов
python scripts/testing/run_tests.py
```

## 📝 ОПИСАНИЕ СКРИПТОВ

- **install_mongodb.sh** - установка MongoDB
- **quick-setup-linux.sh** - быстрая настройка проекта на Linux
- **run_tests.py** - запуск тестов проекта
