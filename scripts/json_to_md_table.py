#!/usr/bin/env python3
"""
Генератор MD таблиц из JSON файлов для тестирования
Автор: Augment Agent
Дата: 2025-01-26
"""

import json
import sys
from datetime import datetime
from pathlib import Path

class JSONToMDGenerator:
    """Генератор MD таблиц из JSON файлов"""
    
    def __init__(self):
        self.language_order = [
            'ru', 'en', 'de', 'fr', 'es', 'it', 'pt', 'nl', 'pl', 'uk',
            'fi', 'sv', 'da', 'no', 'tr', 'ar', 'fa', 'hi', 'bn', 'ur',
            'pa', 'mr', 'ne', 'zh', 'ja', 'ko', 'th', 'vi', 'my', 'id',
            'ms', 'tl', 'cb', 'kk', 'uz', 'hu', 'ro', 'ka'
        ]
        
        self.language_names = {
            'ru': 'Русский', 'en': 'English', 'de': 'De<PERSON><PERSON>', 'fr': 'Français',
            'es': 'Español', 'it': 'Italiano', 'pt': 'Português', 'nl': 'Nederlands',
            'pl': 'Polski', 'uk': 'Українська', 'fi': 'Suomi', 'sv': 'Svenska',
            'da': 'Dansk', 'no': 'Norsk', 'tr': 'Türkçe', 'ar': 'العربية',
            'fa': 'فارسی', 'hi': 'हिन्दी', 'bn': 'বাংলা', 'ur': 'اردو',
            'pa': 'ਪੰਜਾਬੀ', 'mr': 'मराठी', 'ne': 'नेपाली', 'zh': '中文',
            'ja': '日本語', 'ko': '한국어', 'th': 'ไทย', 'vi': 'Tiếng Việt',
            'my': 'မြန်မာ', 'id': 'Bahasa Indonesia', 'ms': 'Bahasa Melayu',
            'tl': 'Tagalog', 'cb': 'Cebuano', 'kk': 'Қазақ', 'uz': 'O\'zbek',
            'hu': 'Magyar', 'ro': 'Română', 'ka': 'ქართული'
        }
    
    def load_json(self, json_path: str) -> dict:
        """Загрузка JSON файла"""
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ Ошибка загрузки JSON: {e}")
            return None
    
    def generate_header(self, data: dict) -> str:
        """Генерация заголовка MD файла"""
        word_ru = data.get('word_ru', 'неизвестно')
        word_en = data.get('word_en', 'unknown')
        concept_id = data.get('concept_id', 'no-id')
        priority = data.get('priority', 'unknown')
        
        progress = data.get('testing_progress', {})
        completed = progress.get('completed_languages', 0)
        total = progress.get('total_languages', 37)
        status = progress.get('status', 'not_started')
        phrase = progress.get('testing_phrase', 'не указано')
        
        header = f"""# СЛОВО: {word_ru}/{word_en} - ТЕСТИРОВАНИЕ

**Метаданные:**
- concept_id: {concept_id}
- приоритет: {priority}
- статус: {status}

**Концепт:** {data.get('concept_description', 'описание не указано')}
**Предложение-идея:** {phrase}

**Прогресс тестирования:** {completed}/{total} языков ({completed/total*100:.1f}%)

## ТАБЛИЦА ТЕСТИРОВАНИЯ

"""
        return header
    
    def generate_table(self, data: dict) -> str:
        """Генерация MD таблицы"""
        examples = data.get('examples', {})
        
        # Заголовок таблицы
        table = """| Язык | Код | Слово | Предложение | Смысл | Падеж | Целевое слово | Грамматика | Естественность | A0-уровень | Статус |
|------|-----|-------|-------------|-------|-------|---------------|------------|----------------|------------|--------|
"""
        
        # Строки для каждого языка
        for lang_code in self.language_order:
            if lang_code not in examples:
                continue
                
            lang_data = examples[lang_code]
            testing = lang_data.get('testing', {})
            
            lang_name = self.language_names.get(lang_code, lang_code.upper())
            word = lang_data.get('word', '???')
            sentence = lang_data.get('sentence', '???').replace('___', word)
            
            # Статусы тестирования
            meaning = testing.get('meaning', 'pending')
            case = testing.get('case', 'pending')
            target_word = testing.get('target_word', 'pending')
            grammar = testing.get('grammar', 'pending')
            naturalness = testing.get('naturalness', 'pending')
            a0_level = testing.get('a0_level', 'pending')
            status = testing.get('status', 'pending')
            
            # Конвертация статусов в эмодзи
            def status_to_emoji(s):
                if s == '✅' or s == 'passed':
                    return '✅'
                elif s == '❌' or s == 'failed':
                    return '❌'
                elif s == '⚠️' or s == 'warning':
                    return '⚠️'
                else:
                    return '⏳'
            
            meaning_emoji = status_to_emoji(meaning)
            case_emoji = status_to_emoji(case)
            target_emoji = status_to_emoji(target_word)
            grammar_emoji = status_to_emoji(grammar)
            natural_emoji = status_to_emoji(naturalness)
            a0_emoji = status_to_emoji(a0_level)
            status_emoji = status_to_emoji(status)
            
            table += f"| {lang_name} | {lang_code} | {word} | {sentence} | {meaning_emoji} | {case_emoji} | {target_emoji} | {grammar_emoji} | {natural_emoji} | {a0_emoji} | {status_emoji} |\n"
        
        return table
    
    def generate_summary(self, data: dict) -> str:
        """Генерация итоговой сводки"""
        progress = data.get('testing_progress', {})
        completed = progress.get('completed_languages', 0)
        total = progress.get('total_languages', 37)
        passed = progress.get('passed_languages', 0)
        failed_langs = progress.get('failed_languages', [])
        
        summary = f"""
## РЕЗУЛЬТАТ ТЕСТИРОВАНИЯ

- **Всего языков**: {total}
- **Протестировано**: {completed} ✅
- **Успешно**: {passed} ✅
- **Проблемы**: {len(failed_langs)} ❌

"""
        
        if failed_langs:
            summary += f"**Проблемные языки**: {', '.join(failed_langs)}\n\n"
        
        if completed == total and len(failed_langs) == 0:
            summary += "**ВАЛИДАЦИЯ**: ✅ ДАННОЕ СЛОВО БЫЛО ПРОТЕСТИРОВАНО ПО ВСЕМ 37 ЯЗЫКАМ ПО ВСЕМ ПАРАМЕТРАМ ТАБЛИЦЫ\n"
        
        summary += f"\n**Последнее обновление**: {progress.get('last_updated', 'неизвестно')}\n"
        
        return summary
    
    def generate_md_file(self, json_path: str, output_path: str = None) -> bool:
        """Генерация полного MD файла"""
        print(f"🔄 Генерация MD таблицы из: {json_path}")
        
        # Загрузка JSON
        data = self.load_json(json_path)
        if not data:
            return False
        
        # Генерация MD контента
        header = self.generate_header(data)
        table = self.generate_table(data)
        summary = self.generate_summary(data)
        
        md_content = header + table + summary
        
        # Определение выходного файла
        if not output_path:
            json_file = Path(json_path)
            output_path = json_file.with_suffix('.md')
        
        # Сохранение MD файла
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(md_content)
            
            print(f"✅ MD таблица создана: {output_path}")
            return True
            
        except Exception as e:
            print(f"❌ Ошибка сохранения MD: {e}")
            return False
    
    def show_progress(self, json_path: str):
        """Показать прогресс тестирования"""
        data = self.load_json(json_path)
        if not data:
            return
        
        progress = data.get('testing_progress', {})
        examples = data.get('examples', {})
        
        print(f"\n📊 ПРОГРЕСС ТЕСТИРОВАНИЯ:")
        print(f"Слово: {data.get('word_ru', '?')}/{data.get('word_en', '?')}")
        print(f"Статус: {progress.get('status', 'unknown')}")
        print(f"Прогресс: {progress.get('completed_languages', 0)}/{progress.get('total_languages', 37)} языков")
        
        # Показать статусы по языкам
        pending_langs = []
        passed_langs = []
        failed_langs = []
        
        for lang_code in self.language_order:
            if lang_code not in examples:
                continue
            
            testing = examples[lang_code].get('testing', {})
            status = testing.get('status', 'pending')
            
            if status in ['✅', 'passed']:
                passed_langs.append(lang_code)
            elif status in ['❌', 'failed']:
                failed_langs.append(lang_code)
            else:
                pending_langs.append(lang_code)
        
        print(f"✅ Готовы: {len(passed_langs)} ({', '.join(passed_langs[:5])}{'...' if len(passed_langs) > 5 else ''})")
        print(f"❌ Проблемы: {len(failed_langs)} ({', '.join(failed_langs)})")
        print(f"⏳ Ожидают: {len(pending_langs)} ({', '.join(pending_langs[:5])}{'...' if len(pending_langs) > 5 else ''})")

def main():
    """Основная функция"""
    if len(sys.argv) < 2:
        print("Использование:")
        print("  python json_to_md_table.py <json_file> [output_md_file]")
        print("  python json_to_md_table.py <json_file> --progress")
        sys.exit(1)
    
    json_path = sys.argv[1]
    
    generator = JSONToMDGenerator()
    
    if len(sys.argv) > 2 and sys.argv[2] == '--progress':
        generator.show_progress(json_path)
    else:
        output_path = sys.argv[2] if len(sys.argv) > 2 else None
        success = generator.generate_md_file(json_path, output_path)
        sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
