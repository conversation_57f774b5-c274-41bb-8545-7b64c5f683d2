#!/usr/bin/env python3
"""
Показ прогресса тестирования JSON файлов
Автор: Augment Agent
Дата: 2025-01-26
"""

import json
import sys
from datetime import datetime
from pathlib import Path

class TestingProgressViewer:
    """Просмотр прогресса тестирования JSON файлов"""
    
    def __init__(self):
        self.language_names = {
            'ru': 'Русский', 'en': 'English', 'de': 'Deutsch', 'fr': 'Français',
            'es': 'Español', 'it': 'Italiano', 'pt': 'Português', 'nl': 'Nederlands',
            'pl': 'Polski', 'uk': 'Українська', 'fi': 'Suomi', 'sv': 'Svenska',
            'da': 'Dansk', 'no': 'Norsk', 'tr': 'Türk<PERSON>e', 'ar': 'العربية',
            'fa': 'فارسی', 'hi': 'हिन्दी', 'bn': 'বাংলা', 'ur': 'اردو',
            'pa': 'ਪੰਜਾਬੀ', 'mr': 'मराठी', 'ne': 'नेपाली', 'zh': '中文',
            'ja': '日本語', 'ko': '한국어', 'th': 'ไทย', 'vi': 'Tiếng Việt',
            'my': 'မြန်မာ', 'id': 'Bahasa Indonesia', 'ms': 'Bahasa Melayu',
            'tl': 'Tagalog', 'cb': 'Cebuano', 'kk': 'Қазақ', 'uz': 'O\'zbek',
            'hu': 'Magyar', 'ro': 'Română', 'ka': 'ქართული'
        }
        
        self.super_critical = ['fi', 'hu', 'ja', 'ar']
        self.problematic = [
            'fi', 'hu', 'ru', 'pl', 'de', 'tr', 'uk', 'ja', 'ko', 'ar', 
            'hi', 'zh', 'th', 'it', 'es', 'fr', 'pt', 'nl'
        ]
    
    def load_json(self, json_path: str) -> dict:
        """Загрузка JSON файла"""
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ Ошибка загрузки JSON: {e}")
            return None
    
    def show_basic_info(self, data: dict):
        """Показать базовую информацию"""
        metadata = data.get('testing_metadata', {})
        
        print("🔤 ИНФОРМАЦИЯ О СЛОВЕ:")
        print(f"   Слово: {metadata.get('word_ru', '?')}/{metadata.get('word_en', '?')}")
        print(f"   Концепт: {metadata.get('concept_description', 'не указан')}")
        print(f"   Приоритет: {metadata.get('priority', 'unknown')}")
        print(f"   Предложение: {metadata.get('testing_phrase', 'не указано')}")
        print(f"   Создано: {metadata.get('created_date', 'неизвестно')}")
        print()
    
    def show_progress_summary(self, data: dict):
        """Показать сводку прогресса"""
        progress = data.get('testing_metadata', {}).get('testing_progress', {})
        
        status = progress.get('status', 'unknown')
        completed = progress.get('completed_languages', 0)
        total = progress.get('total_languages', 37)
        passed = progress.get('passed_languages', 0)
        failed_langs = progress.get('failed_languages', [])
        warning_langs = progress.get('warning_languages', [])
        stage = progress.get('current_testing_stage', 'unknown')
        
        # Эмодзи для статуса
        status_emoji = {
            'not_started': '⏳',
            'in_progress': '🔄',
            'completed': '✅',
            'failed': '❌'
        }.get(status, '❓')
        
        print("📊 ПРОГРЕСС ТЕСТИРОВАНИЯ:")
        print(f"   Статус: {status_emoji} {status}")
        print(f"   Этап: {stage}")
        print(f"   Прогресс: {completed}/{total} языков ({completed/total*100:.1f}%)")
        print(f"   Успешно: ✅ {passed}")
        print(f"   Проблемы: ❌ {len(failed_langs)}")
        print(f"   Предупреждения: ⚠️ {len(warning_langs)}")
        print(f"   Обновлено: {progress.get('last_updated', 'неизвестно')}")
        print()
    
    def show_language_details(self, data: dict, show_all: bool = False):
        """Показать детали по языкам"""
        testing_results = data.get('testing_metadata', {}).get('testing_results', {})
        
        # Группировка языков по статусу
        passed_langs = []
        failed_langs = []
        warning_langs = []
        pending_langs = []
        
        for lang_code, result in testing_results.items():
            status = result.get('status', 'pending')
            if status == '✅' or status == 'passed':
                passed_langs.append(lang_code)
            elif status == '❌' or status == 'failed':
                failed_langs.append(lang_code)
            elif status == '⚠️' or status == 'warning':
                warning_langs.append(lang_code)
            else:
                pending_langs.append(lang_code)
        
        print("🌍 СТАТУС ПО ЯЗЫКАМ:")
        
        # Готовые языки
        if passed_langs:
            print(f"✅ ГОТОВЫЕ ({len(passed_langs)}):")
            if show_all or len(passed_langs) <= 10:
                for lang in passed_langs:
                    lang_name = self.language_names.get(lang, lang.upper())
                    print(f"   {lang}: {lang_name}")
            else:
                shown = passed_langs[:5]
                for lang in shown:
                    lang_name = self.language_names.get(lang, lang.upper())
                    print(f"   {lang}: {lang_name}")
                print(f"   ... и еще {len(passed_langs) - 5}")
            print()
        
        # Проблемные языки
        if failed_langs:
            print(f"❌ ПРОБЛЕМНЫЕ ({len(failed_langs)}):")
            for lang in failed_langs:
                lang_name = self.language_names.get(lang, lang.upper())
                result = testing_results.get(lang, {})
                notes = result.get('notes', '')
                print(f"   {lang}: {lang_name}")
                if notes:
                    print(f"      Проблема: {notes}")
            print()
        
        # Предупреждения
        if warning_langs:
            print(f"⚠️ ПРЕДУПРЕЖДЕНИЯ ({len(warning_langs)}):")
            for lang in warning_langs:
                lang_name = self.language_names.get(lang, lang.upper())
                result = testing_results.get(lang, {})
                notes = result.get('notes', '')
                print(f"   {lang}: {lang_name}")
                if notes:
                    print(f"      Заметка: {notes}")
            print()
        
        # Ожидающие
        if pending_langs:
            print(f"⏳ ОЖИДАЮТ ТЕСТИРОВАНИЯ ({len(pending_langs)}):")
            if show_all or len(pending_langs) <= 10:
                for lang in pending_langs:
                    lang_name = self.language_names.get(lang, lang.upper())
                    is_super_critical = "🔥" if lang in self.super_critical else ""
                    is_problematic = "⚠️" if lang in self.problematic else ""
                    print(f"   {lang}: {lang_name} {is_super_critical}{is_problematic}")
            else:
                shown = pending_langs[:5]
                for lang in shown:
                    lang_name = self.language_names.get(lang, lang.upper())
                    is_super_critical = "🔥" if lang in self.super_critical else ""
                    is_problematic = "⚠️" if lang in self.problematic else ""
                    print(f"   {lang}: {lang_name} {is_super_critical}{is_problematic}")
                print(f"   ... и еще {len(pending_langs) - 5}")
            print()
    
    def show_next_steps(self, data: dict):
        """Показать рекомендуемые следующие шаги"""
        progress = data.get('testing_metadata', {}).get('testing_progress', {})
        testing_results = data.get('testing_metadata', {}).get('testing_results', {})
        
        status = progress.get('status', 'unknown')
        stage = progress.get('current_testing_stage', 'unknown')
        failed_langs = progress.get('failed_languages', [])
        
        print("🎯 СЛЕДУЮЩИЕ ШАГИ:")
        
        if status == 'not_started':
            print("   1. Начать тестирование с супер-критичных языков (fi, hu, ja, ar)")
            print("   2. Загрузить JSON в чат и начать пошаговое тестирование")
        
        elif status == 'in_progress':
            if stage == 'stage_0_super_diagnostic':
                if failed_langs:
                    print("   1. Исправить проблемы в супер-критичных языках")
                    print("   2. Создать новое предложение для всего концепта")
                    print("   3. Повторить супер-диагностику")
                else:
                    print("   1. Продолжить тестирование остальных проблемных языков")
                    print("   2. Перейти к этапу 1 (полная проверка проблемных)")
            
            elif stage == 'stage_1_problematic':
                if failed_langs:
                    print("   1. Исправить проблемы в проблемных языках")
                    print("   2. Вернуться к супер-диагностике с новым предложением")
                else:
                    print("   1. Перейти к этапу 2 (тестирование всех остальных языков)")
            
            elif stage == 'stage_2_full':
                if failed_langs:
                    print("   1. Исправить проблемы в оставшихся языках")
                    print("   2. Вернуться к супер-диагностике")
                else:
                    print("   1. Завершить тестирование")
                    print("   2. Обновить статус на 'completed'")
        
        elif status == 'completed':
            print("   ✅ Тестирование завершено!")
            print("   📁 Файл готов для импорта в базу данных")
        
        elif status == 'failed':
            print("   1. Проанализировать критические проблемы")
            print("   2. Создать новое предложение для концепта")
            print("   3. Начать тестирование заново")
        
        print()
    
    def show_detailed_table(self, data: dict, language: str = None):
        """Показать детальную таблицу тестирования"""
        testing_results = data.get('testing_metadata', {}).get('testing_results', {})
        
        if language:
            if language not in testing_results:
                print(f"❌ Язык {language} не найден")
                return
            
            result = testing_results[language]
            lang_name = self.language_names.get(language, language.upper())
            
            print(f"🔍 ДЕТАЛИ ТЕСТИРОВАНИЯ: {language} ({lang_name})")
            print(f"   Слово: {result.get('word', '?')}")
            print(f"   Предложение: {result.get('sentence', '?')}")
            print(f"   Смысл: {result.get('meaning', 'pending')}")
            print(f"   Падеж: {result.get('case', 'pending')}")
            print(f"   Целевое слово: {result.get('target_word', 'pending')}")
            print(f"   Допустимые ответы: {result.get('acceptable_answers', 'pending')}")
            print(f"   Грамматика: {result.get('grammar', 'pending')}")
            print(f"   Естественность: {result.get('naturalness', 'pending')}")
            print(f"   A0-уровень: {result.get('a0_level', 'pending')}")
            print(f"   Статус: {result.get('status', 'pending')}")
            if result.get('notes'):
                print(f"   Заметки: {result.get('notes')}")
        else:
            # Показать сводную таблицу
            print("📋 СВОДНАЯ ТАБЛИЦА ТЕСТИРОВАНИЯ:")
            print("Язык | Статус | Проблемы")
            print("-" * 40)
            
            for lang_code in sorted(testing_results.keys()):
                result = testing_results[lang_code]
                lang_name = self.language_names.get(lang_code, lang_code.upper())
                status = result.get('status', 'pending')
                
                # Подсчет проблем
                problems = []
                for criterion in ['meaning', 'case', 'target_word', 'grammar', 'naturalness', 'a0_level']:
                    value = result.get(criterion, 'pending')
                    if value == '❌' or value == 'failed':
                        problems.append(criterion)
                
                problem_text = ', '.join(problems) if problems else '-'
                print(f"{lang_code:4} | {status:6} | {problem_text}")
    
    def show_full_report(self, json_path: str, show_all: bool = False, language: str = None):
        """Показать полный отчет"""
        data = self.load_json(json_path)
        if not data:
            return False
        
        print("=" * 60)
        print("📊 ОТЧЕТ О ТЕСТИРОВАНИИ СЛОВА")
        print("=" * 60)
        print()
        
        self.show_basic_info(data)
        self.show_progress_summary(data)
        
        if language:
            self.show_detailed_table(data, language)
        else:
            self.show_language_details(data, show_all)
            self.show_next_steps(data)
        
        return True

def main():
    """Основная функция"""
    if len(sys.argv) < 2:
        print("Использование:")
        print("  python show_testing_progress.py <json_file> [--all] [--lang <code>]")
        print("\nПримеры:")
        print("  python show_testing_progress.py testing/word_0008.json")
        print("  python show_testing_progress.py testing/word_0008.json --all")
        print("  python show_testing_progress.py testing/word_0008.json --lang ru")
        sys.exit(1)
    
    json_path = sys.argv[1]
    show_all = '--all' in sys.argv
    language = None
    
    if '--lang' in sys.argv:
        lang_index = sys.argv.index('--lang')
        if lang_index + 1 < len(sys.argv):
            language = sys.argv[lang_index + 1]
    
    viewer = TestingProgressViewer()
    success = viewer.show_full_report(json_path, show_all, language)
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
