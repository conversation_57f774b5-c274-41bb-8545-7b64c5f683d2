# БЕКАП СПРАВОЧНОЙ ИНФОРМАЦИИ
# Дата создания: 2025-01-26
# Исходный файл: _WORDS_CREATION_GUIDE_WORKFLOW.md (строки 457-638)

## 📚 **СПРАВОЧНАЯ ИНФОРМАЦИЯ**

### **Файловая структура:**
```
vocabulary/
├── concepts/A0/           # Концепты слов
├── json/A0/              # JSON файлы для импорта
├── md/A0/                # MD логи тестирования
├── word_lists/           # Списки слов по уровням
├── language_guides/      # Справочники языков
├── create_words_rules.md # Правила создания предложений
└── _WORDS_CREATION_GUIDE_WORKFLOW.md # ЭТА ИНСТРУКЦИЯ
```

### **Полезные команды:**
```bash
# Проверка статуса слов
python -m scripts.words status A0

# Поиск слова в базе
python -m scripts.words find "врач"

# Экспорт слов уровня
python -m scripts.words export A0
```

### **Подготовительный Workflow:**

**Перед созданием каждого слова:**
- [ ] Выполнить **ШАГ 1: ПОДГОТОВКА И ИЗУЧЕНИЕ** полностью

**При обнаружении проблем:**
- [ ] Изучить подробные справочники проблемных языков в `vocabulary/language_guides/`
- [ ] Sequential Thinking: Найдено решение
- [ ] Справочники обновлены с новыми правилами
- [ ] Краткие саммари дополнены при необходимости


### 📚 **ПРАВИЛА ОБНОВЛЕНИЯ ДОКУМЕНТАЦИИ ПРИ ПРОБЛЕМАХ**

#### **Алгоритм решения проблем:**
1. **ОПРЕДЕЛИТЬ ТИП ПРОБЛЕМЫ** - языковая, универсальная, или специфичная
2. **НАЙТИ ИСТОЧНИК** - в каких языках возникает проблема
3. **ИЗУЧИТЬ СПРАВОЧНИКИ** - проверить существующие решения
4. **СОЗДАТЬ РЕШЕНИЕ** - новое предложение или правило

**ВАЖНО:** При обнаружении проблем четко определяйте, куда добавлять решение:

#### **🎯 ЕСЛИ проблема УНИВЕРСАЛЬНАЯ** (влияет на создание предложений в целом):
→ **Обновляем:** `vocabulary/create_words_rules.md`
- Примеры: новые принципы создания предложений, общие грамматические правила, рекомендации по уровням

#### **🌍 ЕСЛИ проблема ЯЗЫКОВАЯ** (особенность группы языков):
→ **Обновляем:** `vocabulary/language_guides/_language_summary.md`
- Примеры: проблемы с артиклями в романских языках, падежи в германских языках, культурные особенности азиатских языков

#### **🔍 ЕСЛИ проблема СПЕЦИФИЧНАЯ** (один конкретный язык):
→ **Обновляем:** `vocabulary/language_guides/[язык]_guide.md`
- Примеры: особенности финской падежной системы, нюансы китайских иероглифов, региональные варианты себуано

#### **🔄 ЕСЛИ проблема с АЛЬТЕРНАТИВНЫМИ ОТВЕТАМИ**:
→ **Обновляем:** `vocabulary/alternative_answers_rules.md`
- Примеры: новые правила отбора синонимов, решения грамматических конфликтов

**ФОРМАТ MD ЛОГА (например, `0008_A0_ultra_core_08_log.md`):**

```markdown
# ТЕСТИРОВАНИЕ: есть/eat

**Концепт:** eating_action - базовое действие приема пищи
**Предложение:** "Я хочу есть" / "I want to eat"
**Статус:** ✅ ГОТОВ
**Дата тестирования:** 2025-01-26


## ТАБЛИЦА РЕЗУЛЬТАТОВ ТЕСТИРОВАНИЯ

| Язык | Код | Слово | Предложение | Смысл | Падеж | Целевое слово | Грамматика | Естественность | A0-уровень | Статус |
|------|-----|-------|-------------|-------|-------|---------------|------------|----------------|------------|--------|
| Русский | ru | есть | Я хочу есть | ✅ | ✅ | ✅ есть=eat | ✅ | ✅ | ✅ | ✅ |
| English | en | eat | I want to eat | ✅ | ✅ | ✅ eat=есть | ✅ | ✅ | ✅ | ✅ |
| Deutsch | de | essen | Ich will essen | ✅ | ✅ | ✅ essen=eat | ✅ | ✅ | ✅ | ✅ |
| ... все 37 языков ... |

## ИТОГИ ТЕСТИРОВАНИЯ
- **Успешно**: 37/37 языков (100%)
- **Проблемы**: 0 ❌
- **Предупреждения**: 0 ⚠️

**ВАЛИДАЦИЯ**: ✅ ДАННОЕ СЛОВО БЫЛО ПРОТЕСТИРОВАНО ПО ВСЕМ 37 ЯЗЫКАМ ПО ВСЕМ ПАРАМЕТРАМ ТАБЛИЦЫ
```

**Критерии оценки колонок:**

1. **Язык:** код языка (RU, EN, DE, etc.)
2. **Слово:** целевое слово на данном языке
3. **Предложение:** итоговое предложение с подставленным целевым словом
4. **Смысл:** итоговое предложение имеет тот же смысл на всех языках
5. **Естественность:** предложение звучит как живая речь носителя языка
6. **Базовая форма:** целевое слово представлено в правильной базовой форме:
   - Существительные: именительный падеж единственного числа
   - Глаголы: инфинитив (неопределенная форма)
   - Прилагательные: мужской род единственного числа именительного падежа (где применимо)
   - Наречия: базовая форма без степеней сравнения
   - Другие части речи: словарная форма
7. **Уровень:** предложение по грамматике и лексике соответствует уровню слова. Например, для A0 (понятно новичку, простая конструкция 4-5 слов)
8. **Статус:** итоговая оценка результата тестирования для данного языка

**Процесс заполнения статусов:**

**Для колонок 4-7 (критерии оценки):**
- ✅ = критерий выполнен
- ❌ = критерий не выполнен (проблема)
- ⚠️ = требует внимания/обсуждения

**Для колонки 8 (итоговый статус):**
- ✅ = язык успешно прошел тестирование (все критерии ✅ или ⚠️ с обоснованием)
- ❌ = язык не прошел тестирование (есть критические проблемы ❌)
- ⚠️ = язык прошел с оговорками (требует внимания, но не критично)

**Алгоритм анализа проблем:**

**ШАГ 1: АНАЛИЗ** - действительно ли это проблема?
- Сравнить с правилами A0 из CreateWordsRules.md
- Подумать: создает ли это реальную сложность для A0?
- Естественно ли звучит на этом языке?

**Частые "проблемы", которые НЕ являются проблемами:**

1. **ИМЕНИТЕЛЬНЫЙ ПАДЕЖ:**
   - ⚠️ "Слово не в именительном падеже"
   - ✅ НЕ ПРОБЛЕМА если: слово в естественной базовой форме
   - Алгоритм: попробовать именительный → если неестественно → принять исключение

2. **АРТИКЛИ:**
   - ⚠️ "Разные артикли в похожих языках"
   - ✅ НЕ ПРОБЛЕМА если: каждый язык использует свои правила естественно

3. **ДЛИННЫЕ СЛОВА:**
   - ⚠️ "Составное слово слишком длинное"
   - ✅ НЕ ПРОБЛЕМА если: это стандартный перевод в данном языке

**ШАГ 2А: ЕСЛИ НЕ ПРОБЛЕМА**
- Документировать исключение: ⚠️ → анализ → обоснование → ✅ с комментарием
- Предложить обновление правил в документации
- Продолжить тестирование с выбранным вариантом

**ШАГ 2Б: ЕСЛИ ДЕЙСТВИТЕЛЬНО ПРОБЛЕМА**
- Попытаться пересоздать предложение для ВСЕГО концепта
- Если не получается → применить алгоритм решения проблем

**Алгоритм решения проблем:**

**СТРОГАЯ ПОСЛЕДОВАТЕЛЬНОСТЬ:**
1. **СНАЧАЛА:** Попробовать 3-5 РАЗНЫХ предложений для ВСЕГО концепта
2. **ЗАТЕМ:** Проверить каждое новое предложение на естественность
3. **ЗАТЕМ:** Выбрать лучшее решение, которое работает для всех языков
4. **ТОЛЬКО ЕСЛИ ВСЕ ПОПЫТКИ НЕУДАЧНЫ** → применить алгоритм компромисса

**Алгоритм компромисса (только в крайнем случае):**
- Убедиться, что испробованы ВСЕ разумные варианты предложений
- Если ДЕЙСТВИТЕЛЬНО все варианты неестественны → задать пользователю системный вопрос
- Предложить разумный компромисс

**Принципы непрерывного улучшения:**
1. Анализировать корень проблемы
2. Предлагать улучшение правил
3. Думать системно
4. **Активно использовать Sequential Thinking** для глубокого анализа проблем и пошагового создания решений
5. Предлагать документировать новые правила, чтобы избежать подобных проблем в будущем
6. Иметь свободу принятия решений, если есть уверенность. Если сомнения - спросить юзера (оператора)
7. **ВСЕГДА добавлять рекомендации по улучшению системы** в блок MD файла

**Главное правило тестирования:**
Если слово меняет форму в ЛЮБОМ из проблемных языков - нужно придумать другое предложение для ВСЕГО концепта (всех 37 языков).

**Приоритеты критериев:**
1. **Естественность** (колонка 5) - ВЫСШИЙ ПРИОРИТЕТ. Предложение должно звучать как живая речь носителя языка
2. **Смысл** (колонка 4) - очень важно. Предложение должно передавать одинаковый смысл на всех языках
3. **Базовая форма** (колонка 6) - важно, но не критично. Целевое слово должно быть в базовой форме. Если в 1-2 языках не получается идеально, но это все равно узнаваемая словарная форма и предложение звучит естественно - это приемлемо
4. **Уровень A0** (колонка 7) - важно для обучения. Предложение должно быть простым и понятным новичку
