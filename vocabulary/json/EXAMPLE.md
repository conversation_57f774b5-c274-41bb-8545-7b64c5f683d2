# Документация по формату файлов со словами

Этот документ содержит полное описание формата файлов со словами для импорта в базу данных Word Master.

## 🧠 **ВАЖНО: Система концептов (обновлено 2025-06-23)**

**Теперь мы используем workflow "Концепт-первый"!**

### **Что такое концепт:**
- **Концепт** = семантическое описание идеи, которую выражает слово
- **Один концепт** = одно слово на всех языках (связаны через `concept_id`)
- **Коллекция `concepts`** = описания концептов в базе данных
- **Коллекция `words`** = слова, связанные с концептами через `concept_id`

### **Новый workflow:**
1. **Найти concept_id** в `md/A0_word_list.md` (все UUID уже созданы)
2. **Изучить концепт**: `python -m scripts.words concept [concept_id]`
3. **Понять семантику** и контекст использования
4. **Создать JSON файл** с 1 концептом на 37 языках
5. **Использовать готовый concept_id** (НЕ генерировать новый!)

### **Пример команд:**
```bash
# Посмотреть концепт "хочу"
python -m scripts.words concept 550e8400-e29b-41d4-a716-************

# Список всех концептов A0
python -m scripts.concepts list A0
```

## Связанные файлы

- `EXAMPLE.md` (этот файл) - документация по формату
- `EXAMPLE.json` - пример файла, соответствующий данному формату
- `md/A0_word_list.md` - список всех A0 слов с готовыми concept_id

## Формат файла

Файл должен содержать массив объектов в формате JSON, где каждый объект представляет собой слово или фразу. Для примера валидного файла смотрите `EXAMPLE.json` в этой же директории.

## Обязательные поля

- `concept_id` (string): Уникальный идентификатор концепта (одно слово на разных языках имеет одинаковый concept_id)
  - Должен быть валидным UUID v4 (например, `"550e8400-e29b-41d4-a716-************"`)
  - Можно сгенерировать с помощью онлайн-генератора UUID
  
- `word` (string): Само слово или фраза

- `language` (string): Код языка (ISO 639-1, 2 буквы)
  - Например: `"en"`, `"ru"`, `"es"`

- `level` (string): Уровень сложности
  - Допустимые значения: `"A0"`, `"A1"`, `"A2"`, `"B1"`, `"B2"`, `"C1"`, `"C2"`

- `part_of_speech` (string): Часть речи
  - Например: `"noun"`, `"verb"`, `"adjective"`, `"adverb"`

- `examples` (array): Массив примеров использования
  - Должен содержать РОВНО 1 пример (не больше и не меньше)
  - Это требование валидатора
  - Каждый пример содержит:
    - `sentence` (string): Предложение с пропуском (обозначается `___`)
      - Должно быть составлено с использованием слов и грамматики текущего уровня сложности слова (`level`)
      - Допустимо использовать слова и грамматику на один уровень проще, если это необходимо
      - Важно, чтобы предложение не содержало сложных конструкций, которые еще не изучались на текущем уровне
      - Например, для слова уровня A1 используйте только базовую лексику и простые конструкции

      **СПЕЦИАЛЬНЫЕ ПРАВИЛА ДЛЯ A0 УРОВНЯ:**

      > **📋 ВСЕ ПРАВИЛА A0 ПЕРЕНЕСЕНЫ**: Полные правила создания и тестирования A0 слов находятся в файле `vocabulary/_WORDS_CREATION_GUIDE_WORKFLOW.md` в шаге 5. Этот файл содержит только техническое описание JSON формата.
    - `correct_answers` (array): Массив возможных правильных ответов
    - `word_info` (string, опционально): Грамматическая информация о слове в пропуске
      - **Формат:** `ключевая_информация, дополнительная_информация` (через запятую)
      - **Для ИИ-агента:** Выбирайте 2-3 самых важных грамматических признака слова в данном контексте
      
      ### Общие рекомендации для всех языков:
      1. Всегда указывайте часть речи (существительное, глагол и т.д.)
      2. Указывайте только те характеристики, которые важны в данном контексте
      3. Используйте стандартные сокращения для языка
      4. Разделяйте характеристики запятыми
      
      ### Языковые справочники
      Для каждого языка существуют свои особенности. Смотрите соответствующие справочники:
      - [Русский язык](language_guides/RU.md)
      - [Английский язык](language_guides/EN.md)
      - [Испанский язык](language_guides/ES.md)
      
      **Примеры:**
      - Русский: `сущ., род.п., ед.ч.`
      - Английский: `verb, past simple`
      - Испанский: `verbo, presente, 1a persona`
      
      **Важно:** Если для языка еще нет справочника, используйте общие рекомендации и укажите язык в начале: `[ru] глагол, прошедшее время`

## Специальные поля для A0 уровня

- `priority` (string): Приоритет для A0 слов (ОБЯЗАТЕЛЬНО для level: "A0")
  - Допустимые значения: `"ultra_core"`, `"core"`, `"extended"`
  - **ultra_core** (15 слов): "первые слова ребенка" - абсолютный минимум
  - **core** (35 слов): базовый набор для выживания
  - **extended** (101 слово): расширенный набор для полноценного общения

## Рекомендуемые поля

- `ipa_pronunciation` (string): Транскрипция в международном фонетическом алфавите (IPA)

- `frequency_rank` (number): Ранг частотности (чем меньше, тем чаще используется слово)

- `tags` (array): Массив тегов для фильтрации
  - Например: `["basic", "common", "food"]`

## Технические поля (заполняются автоматически)

- `created_at` (ISODate): Дата создания записи
- `updated_at` (ISODate): Дата обновления записи

## Пример файла

См. `EXAMPLE.json` для примера валидного JSON-файла.

## Процесс работы с файлами слов

1. **Подготовка файла**
   - Создайте JSON-файл по примеру `EXAMPLE.json`
   - Сохраните его в папку `backend/data/words/new/`

2. **Валидация**
   Перед импортом проверьте файл с помощью команды:
   ```bash
   # Из корня проекта
   python -m scripts.words validate backend/data/words/new/your_file.json
   ```
   
   Это проверит:
   - Валидность JSON
   - Наличие обязательных полей
   - Корректность форматов (UUID, даты и т.д.)
   - Согласованность переводов (одинаковые concept_id для перевода одного слова)

3. **Импорт**
   После успешной валидации выполните импорт:
   ```bash
   python -m scripts.words import
   ```
   
   Файл будет автоматически:
   - Обработан и импортирован в базу данных
   - Перемещен в папку `backend/data/words/imported/` при успешном импорте
   - Или перемещен в `backend/data/words/invalid/` при ошибках
