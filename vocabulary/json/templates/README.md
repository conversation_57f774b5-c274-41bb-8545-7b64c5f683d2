# Шаблоны JSON файлов для A0 слов

Этот каталог содержит шаблоны для создания JSON файлов с A0 словами.

## Доступные шаблоны:

### 🔥 ultra_core_template.json
- **Приоритет**: ultra_core (концепты 1-15)
- **Пример**: "хочу" / "want"
- **Использование**: Самые критичные слова для выживания

### 🎯 core_template.json  
- **Приоритет**: core (концепты 16-52)
- **Пример**: "мама" / "mother"
- **Использование**: Базовые слова для ежедневного общения

### 📚 extended_template.json
- **Приоритет**: extended (концепты 53-145)
- **Пример**: "мы" / "we"
- **Использование**: Расширенный словарь

## Как использовать:

1. **Скопируйте нужный шаблон**:
   ```bash
   cp data/words/templates/ultra_core_template.json data/words/A0/concept_007.json
   ```

2. **Замените заглушки**:
   - `[LANG_WORD]` → правильный перевод
   - `[LANG_SENTENCE_WITH___]` → предложение с пропуском
   - Обновите `concept_id` на нужный

3. **Валидируйте**:
   ```bash
   python -m scripts.words validate data/words/A0/concept_007.json
   ```

4. **Скопируйте в new/ для импорта**:
   ```bash
   cp data/words/A0/concept_007.json data/words/new/
   ```

5. **Импортируйте**:
   ```bash
   python -m scripts.words import
   ```

## Проверка качества:

Обязательно протестируйте по всем 37 проблемным языкам:
```bash
python test_a0_word_creation.py
```
