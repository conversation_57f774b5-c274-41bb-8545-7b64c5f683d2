# 📄 JSON FILES - Переводы слов

JSON файлы с переводами слов на 37 языков для импорта в базу данных.

## 📁 СТРУКТУРА

```
json/
├── A0/                # JSON файлы A0 уровня
│   ├── 0008_A0_ultra_core_08.json
│   ├── 0009_A0_ultra_core_09.json
│   └── ... (готовые к импорту)
├── A1/                # Будущие JSON файлы A1
├── templates/         # Шаблоны для создания новых файлов
│   ├── A0_template.json
│   └── README.md
├── imported/          # Файлы уже импортированные в БД
├── new/               # Файлы готовые к импорту
├── EXAMPLE.md         # 📋 ТЕХНИЧЕСКОЕ ОПИСАНИЕ JSON ФОРМАТА
└── README.md          # Этот файл
```

## 📋 WORKFLOW

### **Создание нового JSON файла:**
1. Скопировать шаблон: `cp templates/A0_template.json A0/новый_файл.json`
2. Заполнить переводы на 37 языков
3. Проверить формат согласно `EXAMPLE.md`
4. Переместить в `new/` для импорта

### **Импорт в базу данных:**
1. Файлы из `new/` импортируются в MongoDB
2. После успешного импорта перемещаются в `imported/`

## 📊 СТАТИСТИКА

- **A0 файлы**: 8 готовых JSON файлов
- **Шаблоны**: 7 шаблонов для разных приоритетов
- **Импортированные**: 7 старых файлов

## 🔗 СВЯЗАННЫЕ ФАЙЛЫ

Каждый JSON файл связан с:
- **MD файл**: `../md/A0/0008_A0_ultra_core_08.md` (тестирование)
- **Концепт**: `../concepts/A0/0008_A0_ultra_core_08_concept.json` (описание)

## 📖 ДОКУМЕНТАЦИЯ

- **`EXAMPLE.md`** - полное техническое описание JSON формата
- **`vocabulary/_WORDS_CREATION_GUIDE_WORKFLOW.md`** - главная инструкция по созданию слов
- **`templates/README.md`** - описание шаблонов
