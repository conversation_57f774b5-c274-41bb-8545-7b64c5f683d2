# JSON ТЕСТИРОВАНИЕ - СООТВЕТСТВИЕ КРИТЕРИЕВ

## 📋 **СТРУКТУРА ТЕСТОВОГО БЛОКА**

### **🎯 ОСНОВНЫЕ ПОЛЯ:**

```json
"testing_metadata": {
  "concept_id": "UUID концепта",
  "word_ru": "русское слово", 
  "word_en": "английское слово",
  "priority": "ultra_core/core/extended",
  "level": "A0",
  "part_of_speech": "verb/noun/adjective/etc",
  "concept_description": "описание концепта",
  "testing_phrase": "Я хочу есть / I want to eat",
  "created_date": "дата создания",
  
  "testing_progress": {
    "status": "not_started/in_progress/completed/failed",
    "completed_languages": 0,
    "total_languages": 37,
    "passed_languages": 0,
    "failed_languages": [],
    "warning_languages": [],
    "last_updated": "дата последнего обновления",
    "current_testing_stage": "stage_0_super_diagnostic/stage_1_problematic/stage_2_full",
    "notes": "заметки о процессе"
  },
  
  "testing_summary": {
    "total_languages": 37,
    "successful": 0,
    "problems": 0,
    "warnings": 0,
    "final_status": "pending/ready/needs_fixes",
    "analysis_problems": "анализ найденных проблем",
    "sentence_pattern": "паттерн предложения",
    "validation_phrase": "ДАННОЕ СЛОВО БЫЛО ПРОТЕСТИРОВАНО ПО ВСЕМ 37 ЯЗЫКАМ ПО ВСЕМ ПАРАМЕТРАМ ТАБЛИЦЫ"
  }
}
```

## 🧪 **КРИТЕРИИ ТЕСТИРОВАНИЯ ДЛЯ КАЖДОГО ЯЗЫКА**

### **📊 СООТВЕТСТВИЕ MD ↔ JSON:**

| MD Колонка | JSON Поле | Описание | Значения |
|------------|-----------|----------|----------|
| **Язык** | `language_code` | Код языка | ru, en, de, fr, etc. |
| **Слово** | `word` | Целевое слово на языке | есть, eat, essen, etc. |
| **Предложение** | `sentence` | Итоговое предложение | Я хочу есть, I want to eat |
| **Смысл** | `meaning` | Одинаковый смысл на всех языках | ✅/❌/⚠️/pending |
| **Базовая форма** | `base_form` | Целевое слово в базовой (словарной) форме | ✅/❌/⚠️/pending |
| **Целевое слово** | `target_word` | Слово соответствует концепту | ✅/❌/⚠️/pending |
| **Допустимые ответы** | `acceptable_answers` | Синонимы и альтернативы | ✅/❌/⚠️/pending |
| **Грамматика** | `grammar` | Простая конструкция, правильные артикли | ✅/❌/⚠️/pending |
| **Естественность** | `naturalness` | Звучит как живая речь носителя | ✅/❌/⚠️/pending |
| **A0-уровень** | `a0_level` | Понятно новичку, нет сложностей | ✅/❌/⚠️/pending |
| **Статус** | `status` | Итоговый статус строки | ✅/❌/⚠️/pending |
| **Заметки** | `notes` | Дополнительные комментарии | текст |

### **🔍 ДЕТАЛЬНОЕ ОПИСАНИЕ КРИТЕРИЕВ:**

#### **1. MEANING (Смысл):**
- **✅ Passed**: Итоговое предложение имеет тот же смысл на всех языках
- **❌ Failed**: Смысл отличается от других языков
- **⚠️ Warning**: Небольшие смысловые нюансы, требует обсуждения
- **pending**: Еще не проверено

#### **2. BASE_FORM (Базовая форма):**
- **✅ Passed**: Целевое слово в правильной базовой форме (существительные: именительный падеж в падежных языках / словарная форма в языках без падежей; глаголы: инфинитив; прилагательные: базовая форма; наречия: базовая форма)
- **❌ Failed**: Целевое слово склоняется/изменяется неправильно
- **⚠️ Warning**: Исключение может быть оправдано (естественность важнее)
- **pending**: Еще не проверено

#### **3. TARGET_WORD (Целевое слово):**
- **✅ Passed**: Слово точно соответствует изучаемому концепту
- **❌ Failed**: Слово не появляется в предложении или неправильное
- **⚠️ Warning**: Семантическое соответствие (be→стать вместо быть)
- **pending**: Еще не проверено

#### **4. ACCEPTABLE_ANSWERS (Допустимые ответы):**
- **✅ Passed**: Есть точные синонимы/альтернативы или их нет
- **❌ Failed**: Указаны неточные варианты
- **⚠️ Warning**: Спорные варианты, требует уточнения
- **pending**: Еще не проверено

#### **5. GRAMMAR (Грамматика):**
- **✅ Passed**: Простая конструкция (4-5 слов), правильные артикли/заглавные
- **❌ Failed**: Сложная грамматика, ошибки в артиклях/заглавных
- **⚠️ Warning**: Пограничные случаи
- **pending**: Еще не проверено

#### **6. NATURALNESS (Естественность):**
- **✅ Passed**: Звучит как живая речь носителя языка
- **❌ Failed**: Неестественно, искусственно
- **⚠️ Warning**: Приемлемо, но не идеально
- **pending**: Еще не проверено

#### **7. A0_LEVEL (A0-уровень):**
- **✅ Passed**: Понятно новичку, нет культурных/сложных моментов
- **❌ Failed**: Слишком сложно для A0
- **⚠️ Warning**: Пограничный случай
- **pending**: Еще не проверено

#### **8. STATUS (Итоговый статус):**
- **✅ Passed**: Все критерии выполнены, язык готов
- **❌ Failed**: Есть критические проблемы
- **⚠️ Warning**: Есть предупреждения, но в целом приемлемо
- **pending**: Тестирование не завершено

## 🔄 **WORKFLOW ТЕСТИРОВАНИЯ В JSON:**

### **1. 📝 СОЗДАНИЕ:**
```bash
# Создать JSON из шаблона
cp vocabulary/templates/json_testing_complete_template.json vocabulary/testing/word_XXXX.json
# Заполнить базовые поля: concept_id, слова, предложения
```

### **2. 🧪 ТЕСТИРОВАНИЕ В ЧАТЕ:**
```
LLM: Загружаю JSON файл для тестирования...
LLM: Слово: есть/eat, Концепт: прием пищи
LLM: Предложение: "Я хочу есть" / "I want to eat"
LLM: Начинаю супер-диагностику (fi, hu, ja, ar)...

LLM: Финский (fi): "Haluan syödä"
- meaning: ✅ (тот же смысл)
- case: ✅ (syödä в инфинитиве)
- target_word: ✅ (syödä = есть)
- grammar: ✅ (простая конструкция)
- naturalness: ✅ (естественно)
- a0_level: ✅ (понятно новичку)
- status: ✅

LLM: Обновляю testing_results.fi...
LLM: Переходим к венгерскому?
```

### **3. 📊 ОБНОВЛЕНИЕ ПРОГРЕССА:**
```json
"testing_progress": {
  "status": "in_progress",
  "completed_languages": 4,
  "passed_languages": 3,
  "failed_languages": ["de"],
  "current_testing_stage": "stage_0_super_diagnostic"
}
```

### **4. ✅ ФИНАЛИЗАЦИЯ:**
```json
"testing_summary": {
  "final_status": "ready",
  "validation_phrase": "ДАННОЕ СЛОВО БЫЛО ПРОТЕСТИРОВАНО ПО ВСЕМ 37 ЯЗЫКАМ ПО ВСЕМ ПАРАМЕТРАМ ТАБЛИЦЫ"
}
```

## 🎯 **ПРЕИМУЩЕСТВА JSON ПОДХОДА:**

### **✅ МАКСИМАЛЬНАЯ НАДЕЖНОСТЬ:**
- Один файл = один источник истины
- Нет конвертации данных
- Структурированное хранение результатов

### **📈 ОТСЛЕЖИВАНИЕ ПРОГРЕССА:**
- Встроенная статистика тестирования
- История изменений в одном файле
- Возможность возобновить с любого места

### **🔄 ГИБКОСТЬ:**
- Легко добавлять новые критерии
- Программная обработка результатов
- Автоматическая валидация

### **📚 СОХРАНЕНИЕ ИСТОРИИ:**
- Полный лог процесса тестирования
- Заметки и комментарии
- Анализ проблем и решений

**Тестовый блок остается в финальном JSON как ценная история процесса создания слова!** 🚀✨
