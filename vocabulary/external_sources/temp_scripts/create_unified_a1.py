#!/usr/bin/env python3
"""
Создание единого списка A1 слов в формате: английское_слово = русский_перевод
"""

import re

def extract_existing_words_with_translations(filepath):
    """Извлекает существующие слова с переводами"""
    words_dict = {}
    
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
        
        # Ищем слова в формате "номер. word = перевод"
        matches = re.findall(r'\d+\.\s*([^=]+?)\s*=\s*([^=\n]+)', content)
        
        for word, translation in matches:
            word_clean = word.strip().lower()
            translation_clean = translation.strip()
            words_dict[word_clean] = translation_clean
    
    return words_dict

def load_new_words_list(filepath):
    """Загружает список новых слов без переводов"""
    new_words = []
    
    with open(filepath, 'r', encoding='utf-8') as f:
        for line in f:
            word = line.strip().lower()
            if word:
                new_words.append(word)
    
    return new_words

def load_a0_words():
    """Загружает A0 слова для исключения"""
    a0_words = set()
    
    # A0 слова из временного файла
    a0_list = [
        "я", "ты", "да", "нет", "что", "где", "хочу", "есть", "пить", "помощь", 
        "спасибо", "извините", "врач", "вода", "это", "идти", "спать", "видеть", 
        "говорить", "понимать", "знать", "жить", "делать", "мама", "папа", "друг", 
        "человек", "ребенок", "сейчас", "сегодня", "завтра", "здесь", "там", "когда", 
        "как", "скоро", "хлеб", "еда", "голодный", "больной", "деньги", "работа", 
        "больница", "телефон", "опасно", "хорошо", "плохо", "большой", "маленький", 
        "дом", "мы", "вы", "они", "то", "иметь", "давать", "брать", "любить", 
        "приходить", "покупать", "сын", "дочь", "брат", "сестра", "потом", "вчера", 
        "утром", "днем", "вечером", "далеко", "близко", "молоко", "мясо", "горячий", 
        "холодный", "рис", "чай", "кофе", "стол", "стул", "кровать", "машина", 
        "дорога", "магазин", "школа", "дверь", "окно", "книга", "одежда", "новый", 
        "старый", "туалет", "быстрый", "медленный", "легкий", "тяжелый", "дорогой", 
        "дешевый", "правильный", "важный", "один", "два", "три", "десять", "сто", 
        "тысяча", "много", "мало", "все", "ничего", "сколько", "пожалуйста", "привет", 
        "пока", "почему", "белый", "черный", "красный", "синий", "налево", "направо", 
        "прямо", "вверх", "вниз", "больно", "устал", "автобус", "поезд", "самолет", 
        "голова", "сердце", "живот", "рука", "нога", "полиция", "потерялся", 
        "не понимаю", "срочно", "зачем", "какой", "первый", "медленно", "можно", 
        "нельзя", "открыто", "закрыто", "вкусно"
    ]
    
    # Добавляем английские эквиваленты основных A0 слов
    a0_english = {
        "i", "you", "yes", "no", "what", "where", "want", "eat", "drink", "help",
        "thank", "sorry", "doctor", "water", "this", "go", "sleep", "see", "speak",
        "understand", "know", "live", "do", "mother", "father", "friend", "person",
        "child", "now", "today", "tomorrow", "here", "there", "when", "how", "soon",
        "bread", "food", "hungry", "sick", "money", "work", "hospital", "phone",
        "dangerous", "good", "bad", "big", "small", "house", "we", "they", "have",
        "give", "take", "love", "come", "buy", "son", "daughter", "brother", "sister",
        "then", "yesterday", "morning", "day", "evening", "far", "near", "milk",
        "meat", "hot", "cold", "rice", "tea", "coffee", "table", "chair", "bed",
        "car", "road", "shop", "school", "door", "window", "book", "clothes", "new",
        "old", "toilet", "fast", "slow", "easy", "heavy", "expensive", "cheap",
        "right", "important", "one", "two", "three", "ten", "hundred", "thousand",
        "many", "little", "all", "nothing", "how much", "please", "hello", "bye",
        "why", "white", "black", "red", "blue", "left", "right", "straight", "up",
        "down", "hurt", "tired", "bus", "train", "plane", "head", "heart", "stomach",
        "hand", "leg", "police", "lost", "urgent", "what", "first", "slowly", "can",
        "cannot", "open", "closed", "tasty"
    }
    
    return a0_english

def is_outdated_word(word):
    """Проверяет, является ли слово устаревшим"""
    outdated_words = {
        "fax", "cassette", "floppy", "cd", "dvd", "pager", "typewriter",
        "telegram", "newspaper", "magazine", "cart", "carriage", "typist",
        "operator", "kerosene", "lamp", "факс", "кассета", "дискета",
        "диск", "журнал", "газета", "телеграмма", "машинистка", "телефонистка"
    }

    return word.lower() in outdated_words

def create_translation_for_new_word(word):
    """Создает базовый перевод для нового слова (требует ручной проверки)"""
    # Простые переводы для некоторых слов
    basic_translations = {
        "bookstore": "книжный магазин",
        "borrow": "занимать",
        "bottom": "дно",
        "bowl": "миска",
        "brain": "мозг",
        "breathe": "дышать",
        "bridge": "мост",
        "bright": "яркий",
        "broken": "сломанный",
        "brush": "щетка",
        "bucket": "ведро",
        "burger": "бургер",
        "butterfly": "бабочка",
        "button": "кнопка",
        "cafe": "кафе",
        "camp": "лагерь",
        "candy": "конфета",
        "cap": "кепка",
        "care": "забота",
        "careful": "осторожный",
        "carefully": "осторожно",
        "cartoon": "мультфильм",
        "case": "случай",
        "catch": "ловить",
        "celebrate": "праздновать",
        "celebration": "празднование",
        "character": "персонаж",
        "church": "церковь",
        "circle": "круг",
        "classmate": "одноклассник",
        "clever": "умный",
        "closed": "закрытый",
        "cloth": "ткань",
        "cloud": "облако",
        "cloudy": "облачный",
        "coach": "тренер",
        "code": "код",
        "coke": "кола",
        "collect": "собирать",
        "collection": "коллекция",
        "color": "цвет",
        "contest": "конкурс",
        "cookie": "печенье",
        "cop": "полицейский",
        "copy": "копия",
        "corn": "кукуруза",
        "corner": "угол",
        "couch": "диван",
        "cover": "покрывать",
        "cry": "плакать",
        "cute": "милый",
        "daddy": "папочка",
        "did": "делал",
        "dig": "копать",
        "does": "делает",
        "doing": "делание",
        "doll": "кукла",
        "drama": "драма",
        "dream": "мечта",
        "drop": "капля",
        "drum": "барабан",
        "dry": "сухой",
        "due": "должный",
        "either": "либо",
        "elementary": "начальный",
        "email": "электронная почта",
        "engineer": "инженер",
        "everyday": "повседневный",
        "everywhere": "везде",
        "excellent": "отличный",
        "excuse": "извинение",
        "factory": "фабрика",
        "fair": "справедливый",
        "fairy": "фея",
        "fan": "вентилятор",
        "favorite": "любимый",
        "feed": "кормить",
        "fever": "лихорадка",
        "field": "поле",
        "fight": "драться",
        "file": "файл",
        "fishing": "рыбалка",
        "flag": "флаг",
        "focus": "фокус",
        "foggy": "туманный",
        "following": "следующий",
        "foreign": "иностранный",
        "foreigner": "иностранец",
        "frog": "лягушка",
        "garbage": "мусор",
        "ghost": "призрак",
        "gift": "подарок",
        "glad": "рад",
        "glasses": "очки",
        "goal": "цель",
        "gold": "золото",
        "grade": "класс",
        "grammar": "грамматика",
        "grandma": "бабушка",
        "grandpa": "дедушка",
        "grape": "виноград",
        "grass": "трава",
        "gray": "серый",
        "greet": "приветствовать",
        "ground": "земля",
        "guest": "гость",
        "guy": "парень",
        "habit": "привычка",
        "had": "имел",
        "haircut": "стрижка",
        "hall": "зал",
        "hamburger": "гамбургер",
        "handsome": "красивый",
        "has": "имеет",
        "headache": "головная боль",
        "heart": "сердце",
        "heavy": "тяжелый",
        "hers": "её",
        "hide": "прятать",
        "hill": "холм",
        "hold": "держать",
        "hole": "дыра",
        "hometown": "родной город",
        "hurry": "спешить",
        "hurt": "болеть",
        "ideal": "идеальный",
        "inside": "внутри",
        "internet": "интернет",
        "interviewer": "интервьюер",
        "is": "является",
        "item": "предмет",
        "jet": "самолет",
        "jewelry": "украшения",
        "judge": "судья",
        "jump": "прыгать",
        "kick": "пинать",
        "kid": "ребенок",
        "kill": "убивать",
        "king": "король",
        "kiss": "целовать",
        "kite": "воздушный змей",
        "knee": "колено",
        "knife": "нож",
        "lady": "леди",
        "lazy": "ленивый",
        "leader": "лидер",
        "leaf": "лист",
        "lily": "лилия",
        "living": "живущий",
        "lonely": "одинокий",
        "lovely": "прекрасный",
        "luck": "удача",
        "lucky": "удачливый",
        "mail": "почта",
        "math": "математика",
        "matter": "дело",
        "medicine": "лекарство",
        "memory": "память",
        "merry": "веселый",
        "middle": "средний",
        "mind": "разум",
        "mine": "мой",
        "mobile": "мобильный",
        "mom": "мама",
        "mommy": "мамочка",
        "monkey": "обезьяна",
        "moon": "луна",
        "musician": "музыкант",
        "nationality": "национальность",
        "neck": "шея",
        "neighbor": "сосед",
        "noise": "шум",
        "notebook": "тетрадь",
        "officer": "офицер",
        "ok": "хорошо",
        "olympics": "олимпиада",
        "opera": "опера",
        "ours": "наш",
        "owner": "владелец",
        "palace": "дворец",
        "pants": "штаны",
        "pardon": "прощение",
        "peace": "мир",
        "pet": "домашнее животное",
        "pick": "выбирать",
        "picnic": "пикник",
        "pizza": "пицца",
        "pleasure": "удовольствие",
        "pocket": "карман",
        "poem": "стихотворение",
        "pollution": "загрязнение",
        "pop": "поп",
        "poster": "плакат",
        "practice": "практика",
        "pray": "молиться",
        "prince": "принц",
        "princess": "принцесса",
        "program": "программа",
        "push": "толкать",
        "rabbit": "кролик",
        "rainy": "дождливый",
        "rat": "крыса",
        "reporter": "репортер",
        "review": "обзор",
        "ribbon": "лента",
        "ring": "кольцо",
        "role": "роль",
        "rose": "роза",
        "row": "ряд",
        "rude": "грубый",
        "ruler": "линейка",
        "sailor": "моряк",
        "sale": "продажа",
        "save": "сохранять",
        "saw": "пила",
        "seat": "место",
        "self": "сам",
        "set": "набор",
        "shake": "трясти",
        "shelf": "полка",
        "ship": "корабль",
        "shoulder": "плечо",
        "shy": "застенчивый",
        "side": "сторона",
        "sight": "зрение",
        "sign": "знак",
        "sir": "сэр",
        "site": "сайт",
        "size": "размер",
        "sky": "небо",
        "smart": "умный",
        "smell": "запах",
        "smile": "улыбка",
        "smith": "кузнец",
        "smoke": "дым",
        "smoking": "курение",
        "snowy": "снежный",
        "soccer": "футбол",
        "social": "социальный",
        "sofa": "диван",
        "solve": "решать",
        "speech": "речь",
        "spot": "место",
        "spy": "шпион",
        "stage": "сцена",
        "step": "шаг",
        "stone": "камень",
        "store": "магазин",
        "straight": "прямой",
        "strange": "странный",
        "strict": "строгий",
        "subway": "метро",
        "successful": "успешный",
        "suggestion": "предложение",
        "sum": "сумма",
        "sunny": "солнечный",
        "sunshine": "солнечный свет",
        "super": "супер",
        "surf": "серфинг",
        "surprise": "сюрприз",
        "survey": "опрос",
        "sweet": "сладкий",
        "technology": "технология",
        "temple": "храм",
        "theater": "театр",
        "thick": "толстый",
        "thin": "тонкий",
        "throw": "бросать",
        "tiger": "тигр",
        "tight": "тесный",
        "tool": "инструмент",
        "top": "верх",
        "touch": "трогать",
        "towel": "полотенце",
        "tower": "башня",
        "toy": "игрушка",
        "traffic": "движение",
        "train": "поезд",
        "travel": "путешествовать",
        "tree": "дерево",
        "trip": "поездка",
        "trousers": "брюки",
        "truck": "грузовик",
        "true": "правда",
        "try": "пытаться",
        "tube": "труба",
        "tuesday": "вторник",
        "turkey": "индейка",
        "turn": "поворачивать",
        "tv": "телевизор",
        "twelve": "двенадцать",
        "twenty": "двадцать",
        "twice": "дважды",
        "two": "два",
        "type": "тип",
        "ugly": "уродливый",
        "umbrella": "зонт",
        "uncle": "дядя",
        "under": "под",
        "underline": "подчеркивать",
        "understand": "понимать",
        "university": "университет",
        "until": "до",
        "up": "вверх",
        "upstairs": "наверху",
        "us": "нас",
        "use": "использовать",
        "useful": "полезный",
        "usually": "обычно",
        "vacation": "отпуск",
        "vase": "ваза",
        "vegetable": "овощ",
        "very": "очень",
        "video": "видео",
        "village": "деревня",
        "visit": "посещать",
        "visitor": "посетитель",
        "volleyball": "волейбол",
        "vote": "голосовать",
        "wait": "ждать",
        "waiter": "официант",
        "waitress": "официантка",
        "wake": "просыпаться",
        "walk": "идти",
        "wall": "стена",
        "want": "хотеть",
        "war": "война",
        "warm": "теплый",
        "was": "был",
        "wash": "мыть",
        "watch": "смотреть",
        "water": "вода",
        "way": "путь",
        "we": "мы",
        "wear": "носить",
        "weather": "погода",
        "website": "веб-сайт",
        "wednesday": "среда",
        "week": "неделя",
        "weekend": "выходные",
        "welcome": "добро пожаловать",
        "well": "хорошо",
        "were": "были",
        "west": "запад",
        "what": "что",
        "wheel": "колесо",
        "when": "когда",
        "where": "где",
        "which": "который",
        "white": "белый",
        "who": "кто",
        "whose": "чей",
        "why": "почему",
        "wife": "жена",
        "will": "будет",
        "win": "выигрывать",
        "wind": "ветер",
        "window": "окно",
        "wine": "вино",
        "winter": "зима",
        "wish": "желать",
        "with": "с",
        "without": "без",
        "woman": "женщина",
        "wonderful": "замечательный",
        "word": "слово",
        "work": "работа",
        "worker": "рабочий",
        "world": "мир",
        "worry": "беспокоиться",
        "would": "бы",
        "write": "писать",
        "writer": "писатель",
        "writing": "письмо",
        "wrong": "неправильный",
        "yard": "двор",
        "yeah": "да",
        "year": "год",
        "yellow": "желтый",
        "yes": "да",
        "yesterday": "вчера",
        "yet": "еще",
        "yogurt": "йогурт",
        "you": "ты",
        "young": "молодой",
        "yours": "твой",
        "yourself": "себя",
        "zoo": "зоопарк"
    }
    
    return basic_translations.get(word.lower(), f"[ПЕРЕВОД НУЖЕН: {word}]")

def main():
    print("Создание единого списка A1...")
    
    # Загружаем данные
    existing_words = extract_existing_words_with_translations('../word_lists/A1_TEMP_word_list.md')
    new_words = load_new_words_list('unique_new_a1_words.txt')
    a0_words = load_a0_words()
    
    print(f"Существующих слов с переводами: {len(existing_words)}")
    print(f"Новых слов: {len(new_words)}")
    print(f"A0 слов для исключения: {len(a0_words)}")
    
    # Объединяем все слова
    all_words = {}
    
    # Добавляем существующие слова (исключая A0 и устаревшие)
    for word, translation in existing_words.items():
        if word not in a0_words and not is_outdated_word(word):
            all_words[word] = translation
    
    # Добавляем новые слова (исключая A0 и устаревшие)
    for word in new_words:
        if word not in a0_words and not is_outdated_word(word) and word not in all_words:
            translation = create_translation_for_new_word(word)
            all_words[word] = translation
    
    # Сортируем по алфавиту
    sorted_words = sorted(all_words.items())
    
    print(f"\nИтоговое количество слов: {len(sorted_words)}")
    
    # Создаем единый список
    output_lines = [
        "# A1 UNIFIED WORD LIST",
        "",
        f"**TOTAL WORDS**: {len(sorted_words)}",
        f"**FORMAT**: english_word = russian_translation",
        "",
        "## WORD LIST:",
        ""
    ]
    
    for i, (word, translation) in enumerate(sorted_words, 1):
        output_lines.append(f"{i:4d}. {word} = {translation}")
    
    # Сохраняем файл
    output_file = '../word_lists/A1_UNIFIED_word_list.md'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(output_lines))
    
    print(f"Единый список сохранен в: {output_file}")
    
    # Статистика
    needs_translation = sum(1 for _, translation in sorted_words if "[ПЕРЕВОД НУЖЕН:" in translation)
    print(f"\nСтатистика:")
    print(f"  Слов с переводами: {len(sorted_words) - needs_translation}")
    print(f"  Слов требующих перевода: {needs_translation}")
    print(f"  Исключено A0 слов: {len([w for w in existing_words.keys() if w in a0_words])}")
    print(f"  Исключено устаревших слов: {len([w for w in existing_words.keys() if is_outdated_word(w)])}")

if __name__ == "__main__":
    main()
