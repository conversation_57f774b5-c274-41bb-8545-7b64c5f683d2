#!/usr/bin/env python3
"""
Проверка покрытия наших слов против COCA 5000 и Google 6000
"""

import re

def load_our_words():
    """Загружает все наши слова A0-B2"""
    all_words = set()
    
    # A0 слова
    try:
        with open('../word_lists/A0_word_list.md', 'r', encoding='utf-8') as f:
            content = f.read()
            matches = re.findall(r'\|\s*\d+\s*\|\s*[^|]+\s*\|\s*([^|]+?)\s*\|', content)
            for word in matches:
                word_clean = word.strip().lower()
                if word_clean and word_clean != 'английское':
                    all_words.add(word_clean)
    except FileNotFoundError:
        print("⚠️ A0 файл не найден")
    
    # A1-B2 слова
    for level in ['A1', 'A2', 'B1', 'B2']:
        try:
            with open(f'../word_lists/temp_word_lists/{level}_TEMP_word_list.md', 'r', encoding='utf-8') as f:
                content = f.read()
                matches = re.findall(r'\d+\.\s*([^=]+?)\s*=', content)
                for word in matches:
                    word_clean = word.strip().lower()
                    # Убираем альтернативные написания
                    if '/' in word_clean:
                        word_clean = word_clean.split('/')[0]
                    all_words.add(word_clean)
        except FileNotFoundError:
            print(f"⚠️ {level} файл не найден")
    
    return all_words

def load_google_words(limit=6000):
    """Загружает первые N слов из Google 10k"""
    words = set()
    try:
        with open('google_10000_english.txt', 'r', encoding='utf-8') as f:
            for i, line in enumerate(f):
                if i >= limit:
                    break
                word = line.strip().lower()
                if word and not is_english_specific(word):
                    words.add(word)
    except FileNotFoundError:
        print("⚠️ Google файл не найден")
    
    return words

def load_coca_words(limit=5000):
    """Загружает первые N слов из COCA"""
    words = set()
    try:
        with open('coca_5000_words.txt', 'r', encoding='utf-8') as f:
            for i, line in enumerate(f):
                if i >= limit:
                    break
                word = line.strip().lower()
                if word and not is_english_specific(word):
                    words.add(word)
    except FileNotFoundError:
        print("⚠️ COCA файл не найден")
    
    return words

def is_english_specific(word):
    """Проверяет, является ли слово специфичным для английского"""
    
    # Артикли
    if word in ['a', 'an', 'the']:
        return True
    
    # Английские сокращения и конструкции
    english_specific = {
        'am', 'pm', 'a.m.', 'p.m.',
        'mr', 'mrs', 'ms', 'dr',
        "'s", "'re", "'ll", "'ve", "'d", "'m",
        'gonna', 'wanna', 'gotta',
        'yeah', 'yep', 'nope',
        'ok', 'okay'
    }
    
    if word in english_specific:
        return True
    
    # Слова с апострофами
    if "'" in word:
        return True
    
    # Очень короткие служебные слова (1-2 буквы)
    if len(word) <= 2 and word in ['i', 'me', 'my', 'he', 'we', 'us', 'it', 'is', 'be', 'do', 'to', 'of', 'in', 'on', 'at', 'by', 'or', 'if', 'so', 'no']:
        return True
    
    return False

def analyze_missing_words(our_words, source_words, source_name, limit):
    """Анализирует отсутствующие слова"""
    missing = source_words - our_words
    covered = source_words & our_words
    
    print(f"\n📊 АНАЛИЗ ПОКРЫТИЯ {source_name} (первые {limit} слов):")
    print(f"Слов в источнике (после фильтрации): {len(source_words)}")
    print(f"Покрыто нашими списками: {len(covered)} ({len(covered)/len(source_words)*100:.1f}%)")
    print(f"Отсутствует в наших списках: {len(missing)} ({len(missing)/len(source_words)*100:.1f}%)")
    
    if missing:
        print(f"\n🔍 ОТСУТСТВУЮЩИЕ СЛОВА {source_name} (первые 30):")
        sorted_missing = sorted(missing)
        for i, word in enumerate(sorted_missing[:30], 1):
            print(f"{i:2d}. {word}")
        
        if len(missing) > 30:
            print(f"    ... и еще {len(missing) - 30} слов")
    
    return missing, covered

def categorize_missing_words(missing_words):
    """Категоризирует отсутствующие слова"""
    categories = {
        'numbers': set(),
        'pronouns': set(),
        'prepositions': set(),
        'conjunctions': set(),
        'modal_verbs': set(),
        'common_verbs': set(),
        'common_nouns': set(),
        'adjectives': set(),
        'adverbs': set(),
        'other': set()
    }
    
    # Простая категоризация по ключевым словам
    for word in missing_words:
        if word.isdigit() or word in ['one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine', 'ten', 'hundred', 'thousand']:
            categories['numbers'].add(word)
        elif word in ['i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his', 'her', 'its', 'our', 'their']:
            categories['pronouns'].add(word)
        elif word in ['in', 'on', 'at', 'by', 'for', 'with', 'from', 'to', 'of', 'about', 'under', 'over', 'through', 'between']:
            categories['prepositions'].add(word)
        elif word in ['and', 'or', 'but', 'if', 'when', 'while', 'because', 'although', 'since', 'unless']:
            categories['conjunctions'].add(word)
        elif word in ['can', 'could', 'will', 'would', 'should', 'must', 'may', 'might']:
            categories['modal_verbs'].add(word)
        elif word in ['be', 'have', 'do', 'get', 'make', 'go', 'come', 'take', 'give', 'see', 'know', 'think', 'say', 'tell']:
            categories['common_verbs'].add(word)
        elif word.endswith('ly'):
            categories['adverbs'].add(word)
        else:
            categories['other'].add(word)
    
    return categories

def main():
    print("=== АНАЛИЗ ПОКРЫТИЯ ПРОТИВ АВТОРИТЕТНЫХ ИСТОЧНИКОВ ===")
    
    # Загружаем наши слова
    print("Загружаем наши слова A0-B2...")
    our_words = load_our_words()
    print(f"Наших слов: {len(our_words)}")
    
    # Загружаем внешние источники
    print("\nЗагружаем внешние источники...")
    google_words = load_google_words(6000)
    coca_words = load_coca_words(5000)
    
    # Анализируем покрытие
    google_missing, google_covered = analyze_missing_words(our_words, google_words, "GOOGLE", 6000)
    coca_missing, coca_covered = analyze_missing_words(our_words, coca_words, "COCA", 5000)
    
    # Находим слова, отсутствующие в обоих источниках
    common_missing = google_missing & coca_missing
    
    print(f"\n🎯 КРИТИЧЕСКИ ВАЖНЫЕ ОТСУТСТВУЮЩИЕ СЛОВА:")
    print(f"Отсутствуют И в Google, И в COCA: {len(common_missing)} слов")
    
    if common_missing:
        print(f"\nСписок критически важных слов (первые 50):")
        sorted_common = sorted(common_missing)
        for i, word in enumerate(sorted_common[:50], 1):
            print(f"{i:2d}. {word}")
    
    # Категоризируем отсутствующие слова
    if common_missing:
        print(f"\n📋 КАТЕГОРИЗАЦИЯ ОТСУТСТВУЮЩИХ СЛОВ:")
        categories = categorize_missing_words(common_missing)
        
        for category, words in categories.items():
            if words:
                print(f"{category.replace('_', ' ').title()}: {len(words)} слов")
                if len(words) <= 10:
                    print(f"  {', '.join(sorted(words))}")
    
    # Общая статистика
    print(f"\n📈 ОБЩАЯ СТАТИСТИКА ПОКРЫТИЯ:")
    total_external = len(google_words | coca_words)
    total_covered = len((google_words | coca_words) & our_words)
    print(f"Уникальных слов в Google+COCA: {total_external}")
    print(f"Покрыто нашими списками: {total_covered} ({total_covered/total_external*100:.1f}%)")
    print(f"Общий процент покрытия: {total_covered/total_external*100:.1f}%")

if __name__ == "__main__":
    main()
