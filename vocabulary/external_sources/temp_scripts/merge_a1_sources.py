#!/usr/bin/env python3
"""
Объединение A1 слов из Oxford и CEFR-J с очисткой от английских специфичных элементов
"""

import csv
import re

def load_existing_words(filepath):
    """Загружает уже существующие слова из временного файла"""
    existing_words = set()
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
            # Ищем английские слова в формате "word = перевод"
            matches = re.findall(r'(\w+)\s*=\s*[^=\n]+', content)
            for word in matches:
                existing_words.add(word.lower().strip())
    except FileNotFoundError:
        pass
    return existing_words

def load_oxford_a1():
    """Загружает слова Oxford A1"""
    words = set()
    with open('oxford_5000_by_levels/oxford_a1.csv', 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        next(reader)  # Пропускаем заголовок
        for row in reader:
            if row:
                word = row[0].strip().lower()
                word_class = row[1].strip().lower()
                # Исключаем английские специфичные элементы
                if not is_english_specific(word, word_class):
                    words.add(word)
    return words

def load_cefrj_a1():
    """Загружает слова CEFR-J A1"""
    words = set()
    with open('cefrj_by_levels/cefrj_a1.csv', 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        next(reader)  # Пропускаем заголовок
        for row in reader:
            if row:
                word = row[0].strip().lower()
                pos = row[1].strip().lower() if len(row) > 1 else ""
                # Исключаем английские специфичные элементы
                if not is_english_specific(word, pos):
                    words.add(word)
    return words

def is_english_specific(word, word_class):
    """Проверяет, является ли слово специфичным для английского языка"""
    
    # Артикли
    if word in ['a', 'an', 'the']:
        return True
    
    # Части речи, специфичные для английского
    english_specific_pos = [
        'indefinite article', 'definite article', 'article',
        'determiner'  # часто включает артикли
    ]
    
    if word_class in english_specific_pos:
        return True
    
    # Специфичные английские слова/конструкции
    english_specific_words = [
        'am', 'pm', 'a.m.', 'p.m.', 'a.m./a.m./am/am',
        'mr', 'mrs', 'ms', 'dr',  # титулы
        "'s", "'re", "'ll", "'ve", "'d",  # сокращения
    ]
    
    if word in english_specific_words:
        return True
    
    # Слова с апострофами (английские сокращения)
    if "'" in word:
        return True
    
    return False

def clean_word(word):
    """Очищает слово от лишних символов"""
    # Убираем точки, запятые, скобки
    word = re.sub(r'[.,()\/]', '', word)
    # Убираем множественные пробелы
    word = re.sub(r'\s+', ' ', word)
    return word.strip().lower()

def main():
    print("Загружаем существующие слова...")
    existing_words = load_existing_words('../word_lists/A1_TEMP_word_list.md')
    print(f"Найдено существующих слов: {len(existing_words)}")
    
    print("Загружаем Oxford A1...")
    oxford_words = load_oxford_a1()
    print(f"Oxford A1 слов (после фильтрации): {len(oxford_words)}")
    
    print("Загружаем CEFR-J A1...")
    cefrj_words = load_cefrj_a1()
    print(f"CEFR-J A1 слов (после фильтрации): {len(cefrj_words)}")
    
    # Объединяем все источники
    all_new_words = oxford_words | cefrj_words
    print(f"Всего уникальных новых слов: {len(all_new_words)}")
    
    # Исключаем уже существующие
    new_words = all_new_words - existing_words
    print(f"Новых слов для добавления: {len(new_words)}")
    
    # Сортируем для удобства
    sorted_new_words = sorted(new_words)
    
    # Выводим результат
    print("\n=== НОВЫЕ СЛОВА ДЛЯ ДОБАВЛЕНИЯ ===")
    for i, word in enumerate(sorted_new_words, 1):
        print(f"{i:3d}. {word}")
    
    # Сохраняем в файл
    with open('new_a1_words.txt', 'w', encoding='utf-8') as f:
        for word in sorted_new_words:
            f.write(f"{word}\n")
    
    print(f"\nНовые слова сохранены в файл: new_a1_words.txt")
    
    # Статистика исключенных слов
    print("\n=== СТАТИСТИКА ИСКЛЮЧЕНИЙ ===")
    oxford_excluded = 0
    cefrj_excluded = 0
    
    # Подсчет исключенных из Oxford
    with open('oxford_5000_by_levels/oxford_a1.csv', 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        next(reader)
        for row in reader:
            if row:
                word = row[0].strip().lower()
                word_class = row[1].strip().lower()
                if is_english_specific(word, word_class):
                    oxford_excluded += 1
    
    # Подсчет исключенных из CEFR-J
    with open('cefrj_by_levels/cefrj_a1.csv', 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        next(reader)
        for row in reader:
            if row:
                word = row[0].strip().lower()
                pos = row[1].strip().lower() if len(row) > 1 else ""
                if is_english_specific(word, pos):
                    cefrj_excluded += 1
    
    print(f"Исключено из Oxford: {oxford_excluded} слов")
    print(f"Исключено из CEFR-J: {cefrj_excluded} слов")

if __name__ == "__main__":
    main()
