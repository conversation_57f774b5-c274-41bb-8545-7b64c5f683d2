#!/usr/bin/env python3
"""
Удаление дублей из списков слов и обновление счетчиков
"""

import re

def load_level_words(level):
    """Загружает слова из временного файла уровня"""
    words = []
    filename = f'../word_lists/temp_word_lists/{level}_TEMP_word_list.md'
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        # Разделяем на заголовок и слова
        header_lines = []
        word_lines = []
        in_word_section = False
        
        for line in lines:
            if line.strip().startswith('## WORD LIST:'):
                header_lines.append(line)
                header_lines.append('\n')
                in_word_section = True
            elif in_word_section:
                # Проверяем, является ли строка словом
                match = re.match(r'\s*\d+\.\s*([^=]+?)\s*=\s*(.+)', line)
                if match:
                    word = match.group(1).strip()
                    translation = match.group(2).strip()
                    word_lines.append((word, translation))
            else:
                header_lines.append(line)
        
        return header_lines, word_lines
    except FileNotFoundError:
        print(f"⚠️ {level} файл не найден")
        return [], []

def save_level_words(level, header_lines, word_lines):
    """Сохраняет обновленный список слов"""
    filename = f'../word_lists/temp_word_lists/{level}_TEMP_word_list.md'
    
    # Обновляем счетчик в заголовке
    for i, line in enumerate(header_lines):
        if line.startswith('**TOTAL WORDS**:'):
            header_lines[i] = f'**TOTAL WORDS**: {len(word_lines)}\n'
            break
    
    # Создаем новый файл
    output_lines = header_lines.copy()
    
    # Добавляем перенумерованные слова
    for i, (word, translation) in enumerate(word_lines, 1):
        output_lines.append(f'{i:4d}. {word} = {translation}\n')
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.writelines(output_lines)

def main():
    print("=== УДАЛЕНИЕ ДУБЛЕЙ ===\n")
    
    # Дубли, которые нужно удалить (из результатов проверки)
    duplicates_to_remove = {
        'A1': {
            'afternoon', 'airplane', 'close', 'correct', 'dad', 'delicious', 
            'doctor', 'few', 'home', 'later', 'mom', 'that', 'which',
            'short', 'program'  # дубли с A2 и B1
        },
        'A2': {
            'pain',  # дубль с A0
            'honor'  # дубль с B2
        },
        'B1': {
            'urgent',  # дубль с A0
            'onto', 'organization', 'recognize',  # дубли с A2
            'organizer'  # дубль с B2
        },
        'B2': {
            # Все дубли уже удалены из предыдущих уровней
        }
    }
    
    # Обрабатываем каждый уровень
    for level in ['A1', 'A2', 'B1', 'B2']:
        print(f"Обрабатываем {level}...")
        
        header_lines, word_lines = load_level_words(level)
        if not word_lines:
            continue
            
        original_count = len(word_lines)
        
        # Удаляем дубли
        words_to_remove = duplicates_to_remove.get(level, set())
        filtered_words = []
        
        removed_count = 0
        for word, translation in word_lines:
            word_clean = word.strip().lower()
            # Убираем слова с слэшами (берем первый вариант)
            if '/' in word_clean:
                word_clean = word_clean.split('/')[0]
                
            if word_clean not in words_to_remove:
                filtered_words.append((word, translation))
            else:
                removed_count += 1
                print(f"  Удален дубль: {word}")
        
        # Сохраняем обновленный файл
        save_level_words(level, header_lines, filtered_words)
        
        print(f"  {level}: {original_count} → {len(filtered_words)} слов (удалено {removed_count})")
    
    print("\n✅ Дубли удалены, счетчики обновлены!")
    
    # Показываем итоговую статистику
    print("\n📊 ИТОГОВАЯ СТАТИСТИКА:")
    
    # A0 считаем отдельно
    a0_count = 148  # из предыдущей проверки
    print(f"A0: {a0_count} слов")
    
    total = a0_count
    for level in ['A1', 'A2', 'B1', 'B2']:
        header_lines, word_lines = load_level_words(level)
        count = len(word_lines)
        print(f"{level}: {count} слов")
        total += count
    
    print(f"ИТОГО: {total} слов")

if __name__ == "__main__":
    main()
