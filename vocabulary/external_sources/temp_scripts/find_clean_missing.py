#!/usr/bin/env python3
"""
Чистый поиск отсутствующих ценных слов из A1_TEMP
"""

import re

def extract_clean_words_from_temp():
    """Извлекает только чистые английские слова с переводами из TEMP"""
    clean_words = {}
    
    with open('../word_lists/A1_TEMP_word_list.md', 'r', encoding='utf-8') as f:
        content = f.read()
        
        # Ищем строки формата "номер. english_word = русский_перевод"
        matches = re.findall(r'\d+\.\s*([a-zA-Z][a-zA-Z\s\-/]*[a-zA-Z])\s*=\s*([а-яё][а-яё\s\-,()]*[а-яё])', content, re.IGNORECASE)
        
        for word, translation in matches:
            word_clean = word.strip().lower()
            translation_clean = translation.strip()
            
            # Дополнительная фильтрация
            if is_good_word_pair(word_clean, translation_clean):
                clean_words[word_clean] = translation_clean
    
    return clean_words

def extract_words_from_final():
    """Извлекает слова из FINAL"""
    final_words = set()
    
    with open('../word_lists/A1_FINAL_word_list.md', 'r', encoding='utf-8') as f:
        content = f.read()
        
        matches = re.findall(r'\d+\.\s*([^=]+?)\s*=\s*\[НУЖЕН ПЕРЕВОД\]', content)
        
        for word in matches:
            word_clean = word.strip().lower()
            final_words.add(word_clean)
    
    return final_words

def is_good_word_pair(word, translation):
    """Проверяет качество пары слово-перевод"""
    
    # Исключаем слишком короткие
    if len(word) < 2 or len(translation) < 2:
        return False
    
    # Исключаем слова с цифрами
    if any(char.isdigit() for char in word):
        return False
    
    # Исключаем английские специфичные
    english_specific = ['a', 'an', 'the', 'am', 'pm', 'mr', 'mrs', 'ms', 'dr']
    if word in english_specific:
        return False
    
    # Исключаем слова с апострофами
    if "'" in word:
        return False
    
    # Исключаем устаревшие
    outdated = ['cd', 'dvd', 'fax', 'cassette', 'magazine', 'newspaper']
    if word in outdated:
        return False
    
    # Исключаем переводы с пометками
    bad_translation_indicators = ['❌', 'дубль', 'устарело', 'нужен', 'перевод']
    for indicator in bad_translation_indicators:
        if indicator.lower() in translation.lower():
            return False
    
    return True

def load_a0_words():
    """A0 слова для проверки дублей"""
    return {
        "i", "you", "yes", "no", "what", "where", "want", "eat", "drink", "help",
        "thank", "sorry", "doctor", "water", "this", "go", "sleep", "see", "speak",
        "understand", "know", "live", "do", "mother", "father", "friend", "person",
        "child", "now", "today", "tomorrow", "here", "there", "when", "how", "soon",
        "bread", "food", "hungry", "sick", "money", "work", "hospital", "phone",
        "dangerous", "good", "bad", "big", "small", "house", "we", "they", "have",
        "give", "take", "love", "come", "buy", "son", "daughter", "brother", "sister",
        "then", "yesterday", "morning", "day", "evening", "far", "near", "milk",
        "meat", "hot", "cold", "rice", "tea", "coffee", "table", "chair", "bed",
        "car", "road", "shop", "school", "door", "window", "book", "clothes", "new",
        "old", "toilet", "fast", "slow", "easy", "heavy", "expensive", "cheap",
        "right", "important", "one", "two", "three", "ten", "hundred", "thousand",
        "many", "little", "all", "nothing", "please", "hello", "bye",
        "why", "white", "black", "red", "blue", "left", "straight", "up",
        "down", "hurt", "tired", "bus", "train", "plane", "head", "heart", "stomach",
        "hand", "leg", "police", "lost", "first", "slowly", "can",
        "cannot", "open", "closed", "tasty"
    }

def main():
    print("=== ПОИСК ЧИСТЫХ ОТСУТСТВУЮЩИХ СЛОВ ===")
    
    # Загружаем данные
    temp_words = extract_clean_words_from_temp()
    final_words = extract_words_from_final()
    a0_words = load_a0_words()
    
    print(f"Чистых слов в TEMP: {len(temp_words)}")
    print(f"Слов в FINAL: {len(final_words)}")
    
    # Находим отсутствующие НЕ-A0 слова
    missing_words = {}
    for word, translation in temp_words.items():
        if word not in final_words and word not in a0_words:
            missing_words[word] = translation
    
    print(f"Отсутствующих ценных слов: {len(missing_words)}")
    
    if missing_words:
        print("\n=== ОТСУТСТВУЮЩИЕ ЦЕННЫЕ СЛОВА ===")
        for i, (word, translation) in enumerate(sorted(missing_words.items()), 1):
            print(f"{i:3d}. {word} = {translation}")
        
        # Сохраняем
        with open('clean_missing_words.txt', 'w', encoding='utf-8') as f:
            for word, translation in sorted(missing_words.items()):
                f.write(f"{word} = {translation}\n")
        
        print(f"\nОтсутствующие слова сохранены в: clean_missing_words.txt")
    else:
        print("\n✅ Все ценные слова учтены!")
    
    # Проверяем A0 дубли в TEMP
    a0_duplicates = {word: translation for word, translation in temp_words.items() 
                     if word in a0_words}
    
    print(f"\nA0 дублей в TEMP: {len(a0_duplicates)}")
    if a0_duplicates:
        print("Примеры A0 дублей:")
        for i, (word, translation) in enumerate(list(a0_duplicates.items())[:10], 1):
            print(f"  {i}. {word} = {translation}")

if __name__ == "__main__":
    main()
