#!/usr/bin/env python3
"""
Добавление рекомендованных слов в соответствующие уровни
"""

import re

def load_level_words(level):
    """Загружает слова из файла уровня"""
    filename = f'../word_lists/temp_word_lists/{level}_TEMP_word_list.md'
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        # Разделяем на заголовок и слова
        header_lines = []
        word_lines = []
        in_word_section = False
        
        for line in lines:
            if line.strip().startswith('## WORD LIST:'):
                header_lines.append(line)
                header_lines.append('\n')
                in_word_section = True
            elif in_word_section:
                # Проверяем, является ли строка словом
                match = re.match(r'\s*\d+\.\s*([^=]+?)\s*=\s*(.+)', line)
                if match:
                    word = match.group(1).strip()
                    translation = match.group(2).strip()
                    word_lines.append((word, translation))
            else:
                header_lines.append(line)
        
        return header_lines, word_lines
    except FileNotFoundError:
        print(f"⚠️ {level} файл не найден")
        return [], []

def save_level_words(level, header_lines, word_lines):
    """Сохраняет обновленный список слов"""
    filename = f'../word_lists/temp_word_lists/{level}_TEMP_word_list.md'
    
    # Обновляем счетчик в заголовке
    for i, line in enumerate(header_lines):
        if line.startswith('**TOTAL WORDS**:'):
            header_lines[i] = f'**TOTAL WORDS**: {len(word_lines)}\n'
            break
    
    # Создаем новый файл
    output_lines = header_lines.copy()
    
    # Добавляем перенумерованные слова
    for i, (word, translation) in enumerate(word_lines, 1):
        output_lines.append(f'{i:4d}. {word} = {translation}\n')
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.writelines(output_lines)

def add_words_to_level(level, new_words):
    """Добавляет новые слова в уровень"""
    print(f"Добавляем слова в {level}...")
    
    header_lines, word_lines = load_level_words(level)
    if not word_lines:
        return
        
    original_count = len(word_lines)
    
    # Добавляем новые слова
    for word, translation in new_words:
        word_lines.append((word, translation))
        print(f"  + {word} = {translation}")
    
    # Сортируем по алфавиту
    word_lines.sort(key=lambda x: x[0].lower())
    
    # Сохраняем обновленный файл
    save_level_words(level, header_lines, word_lines)
    
    print(f"  {level}: {original_count} → {len(word_lines)} слов (+{len(new_words)})")

def main():
    print("=== ДОБАВЛЕНИЕ РЕКОМЕНДОВАННЫХ СЛОВ ===\n")
    
    # Слова для добавления
    words_to_add = {
        'A1': [
            ('your', 'твой, ваш')
        ],
        'B1': [
            ('according', 'согласно, в соответствии с'),
            ('boundary', 'граница, предел'),
            ('collaboration', 'сотрудничество'),
            ('combat', 'бой, борьба; бороться')
        ],
        'B2': [
            ('chronic', 'хронический'),
            ('clinical', 'клинический, медицинский'),
            ('compliance', 'соблюдение, соответствие'),
            ('comply', 'соблюдать, подчиняться')
        ]
    }
    
    # Добавляем слова в каждый уровень
    total_added = 0
    for level, words in words_to_add.items():
        add_words_to_level(level, words)
        total_added += len(words)
        print()
    
    print(f"✅ Всего добавлено: {total_added} слов")
    
    # Показываем итоговую статистику
    print("\n📊 ИТОГОВАЯ СТАТИСТИКА:")
    
    # A0 считаем отдельно
    a0_count = 148  # известно из предыдущих проверок
    print(f"A0: {a0_count} слов")
    
    total = a0_count
    for level in ['A1', 'A2', 'B1', 'B2']:
        header_lines, word_lines = load_level_words(level)
        count = len(word_lines)
        print(f"{level}: {count} слов")
        total += count
    
    print(f"ИТОГО: {total} слов")
    
    print(f"\nПрирост: +{total_added} слов (было ~7030, стало {total})")

if __name__ == "__main__":
    main()
