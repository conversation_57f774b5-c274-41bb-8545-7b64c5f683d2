#!/usr/bin/env python3
"""
Очистка и перенумерация A1 списка
"""

import re

def clean_and_renumber():
    """Очищает и перенумеровывает список"""
    
    # Читаем файл
    with open('../word_lists/A1_TEMP_word_list_2.md', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Извлекаем только строки со словами
    word_lines = []
    for line in lines:
        # Ищем строки формата "   номер. слово = [НУЖЕН ПЕРЕВОД]"
        match = re.match(r'\s*\d+\.\s*(.+?)\s*=\s*\[НУЖЕН ПЕРЕВОД\]', line)
        if match:
            word = match.group(1).strip()
            # Исключаем проблемные слова
            if not should_exclude_word(word):
                word_lines.append(word)
    
    print(f"Найдено чистых слов: {len(word_lines)}")
    
    # Создаем новый файл
    output_lines = [
        "# A1 WORD LIST (CLEAN FROM SCRATCH)",
        "",
        f"**TOTAL WORDS**: {len(word_lines)}",
        f"**SOURCES**: Oxford A1 + CEFR-J A1",
        f"**FILTERED**: English-specific elements, A0 duplicates, outdated words",
        f"**FORMAT**: english_word = [НУЖЕН ПЕРЕВОД]",
        "",
        "## WORD LIST:",
        ""
    ]
    
    # Добавляем слова с правильной нумерацией
    for i, word in enumerate(word_lines, 1):
        output_lines.append(f"{i:4d}. {word} = [НУЖЕН ПЕРЕВОД]")
    
    # Сохраняем
    with open('../word_lists/A1_FINAL_word_list.md', 'w', encoding='utf-8') as f:
        f.write('\n'.join(output_lines))
    
    print(f"Финальный список сохранен: A1_FINAL_word_list.md")
    print(f"Готов для добавления переводов!")

def should_exclude_word(word):
    """Проверяет, нужно ли исключить слово"""
    
    # Исключаем слова с множественными точками или слэшами
    if word.count('.') > 1 or '/am/' in word or 'a.m.' in word:
        return True
    
    # Исключаем слова с апострофами
    if "'" in word:
        return True
    
    # Исключаем очевидно английские специфичные
    english_specific = ['mr', 'mrs', 'ms', 'dr', 'am', 'pm']
    if word.lower() in english_specific:
        return True
    
    return False

if __name__ == "__main__":
    clean_and_renumber()
