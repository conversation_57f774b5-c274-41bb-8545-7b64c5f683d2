#!/usr/bin/env python3
"""
Поиск дублей между существующими и новыми A1 словами
"""

import re

def extract_existing_words(filepath):
    """Извлекает существующие слова из временного файла"""
    existing_words = {}
    
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
        
        # Ищем слова в формате "номер. word = перевод"
        matches = re.findall(r'\d+\.\s*(\w+(?:\s+\w+)*)\s*=\s*([^=\n]+)', content)
        
        for word, translation in matches:
            word_clean = word.strip().lower()
            translation_clean = translation.strip()
            existing_words[word_clean] = translation_clean
    
    return existing_words

def load_new_words(filepath):
    """Загружает новые слова из файла"""
    new_words = set()
    
    with open(filepath, 'r', encoding='utf-8') as f:
        for line in f:
            word = line.strip().lower()
            if word:
                new_words.add(word)
    
    return new_words

def find_duplicates(existing_words, new_words):
    """Находит дубли между существующими и новыми словами"""
    existing_set = set(existing_words.keys())
    duplicates = existing_set & new_words
    
    return duplicates

def main():
    print("Анализ дублей в A1 словах...")
    
    # Загружаем данные
    existing_words = extract_existing_words('../word_lists/A1_TEMP_word_list.md')
    new_words = load_new_words('new_a1_words.txt')
    
    print(f"Существующих слов: {len(existing_words)}")
    print(f"Новых слов: {len(new_words)}")
    
    # Находим дубли
    duplicates = find_duplicates(existing_words, new_words)
    
    print(f"\nНайдено дублей: {len(duplicates)}")
    
    if duplicates:
        print("\n=== ДУБЛИ ===")
        for i, word in enumerate(sorted(duplicates), 1):
            translation = existing_words.get(word, "???")
            print(f"{i:3d}. {word} = {translation}")
    
    # Уникальные новые слова
    unique_new = new_words - set(existing_words.keys())
    print(f"\nУникальных новых слов: {len(unique_new)}")
    
    # Сохраняем уникальные новые слова
    with open('unique_new_a1_words.txt', 'w', encoding='utf-8') as f:
        for word in sorted(unique_new):
            f.write(f"{word}\n")
    
    print("Уникальные новые слова сохранены в: unique_new_a1_words.txt")
    
    # Итоговая статистика
    total_unique = len(existing_words) + len(unique_new)
    print(f"\n=== ИТОГОВАЯ СТАТИСТИКА ===")
    print(f"Существующие слова: {len(existing_words)}")
    print(f"Уникальные новые: {len(unique_new)}")
    print(f"Дубли (исключены): {len(duplicates)}")
    print(f"ИТОГО уникальных слов: {total_unique}")

if __name__ == "__main__":
    main()
