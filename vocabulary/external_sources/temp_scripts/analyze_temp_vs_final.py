#!/usr/bin/env python3
"""
Анализ A1_TEMP_word_list.md vs A1_FINAL_word_list.md
Проверяем, не потеряли ли мы важные слова
"""

import re

def extract_words_from_temp():
    """Извлекает слова из A1_TEMP_word_list.md"""
    temp_words = {}
    
    with open('../word_lists/A1_TEMP_word_list.md', 'r', encoding='utf-8') as f:
        content = f.read()
        
        # Ищем слова в формате "номер. word = перевод"
        matches = re.findall(r'\d+\.\s*([^=]+?)\s*=\s*([^=\n]+)', content)
        
        for word, translation in matches:
            word_clean = word.strip().lower()
            translation_clean = translation.strip()
            
            # Исключаем строки с пометками об устаревании
            if "❌ УСТАРЕЛО" not in translation_clean and "УСТАРЕЛО" not in translation_clean:
                temp_words[word_clean] = translation_clean
    
    return temp_words

def extract_words_from_final():
    """Извлекает слова из A1_FINAL_word_list.md"""
    final_words = set()
    
    with open('../word_lists/A1_FINAL_word_list.md', 'r', encoding='utf-8') as f:
        content = f.read()
        
        # Ищем слова в формате "номер. word = [НУЖЕН ПЕРЕВОД]"
        matches = re.findall(r'\d+\.\s*([^=]+?)\s*=\s*\[НУЖЕН ПЕРЕВОД\]', content)
        
        for word in matches:
            word_clean = word.strip().lower()
            final_words.add(word_clean)
    
    return final_words

def is_valuable_word(word, translation):
    """Проверяет, является ли слово ценным (с хорошим переводом)"""
    
    # Исключаем слова без перевода или с плохими переводами
    bad_indicators = [
        "[ПЕРЕВОД НУЖЕН:",
        "[НУЖЕН ПЕРЕВОД]",
        "???",
        "перевод",
        "нужен",
        "требует"
    ]
    
    for indicator in bad_indicators:
        if indicator.lower() in translation.lower():
            return False
    
    # Исключаем очень короткие переводы (вероятно неполные)
    if len(translation.strip()) < 2:
        return False
    
    # Исключаем английские специфичные слова
    english_specific = ['a', 'an', 'the', 'am', 'pm', 'mr', 'mrs', 'ms', 'dr']
    if word.lower() in english_specific:
        return False
    
    # Исключаем слова с апострофами
    if "'" in word:
        return False
    
    return True

def main():
    print("=== АНАЛИЗ A1_TEMP vs A1_FINAL ===")
    
    # Загружаем данные
    temp_words = extract_words_from_temp()
    final_words = extract_words_from_final()
    
    print(f"Слов в TEMP файле: {len(temp_words)}")
    print(f"Слов в FINAL файле: {len(final_words)}")
    
    # Находим слова, которые есть в TEMP, но нет в FINAL
    missing_words = {}
    for word, translation in temp_words.items():
        if word not in final_words and is_valuable_word(word, translation):
            missing_words[word] = translation
    
    print(f"\nЦенных слов отсутствует в FINAL: {len(missing_words)}")
    
    if missing_words:
        print("\n=== ОТСУТСТВУЮЩИЕ ЦЕННЫЕ СЛОВА ===")
        for i, (word, translation) in enumerate(sorted(missing_words.items()), 1):
            print(f"{i:3d}. {word} = {translation}")
        
        # Сохраняем отсутствующие слова
        with open('missing_valuable_words.txt', 'w', encoding='utf-8') as f:
            for word, translation in sorted(missing_words.items()):
                f.write(f"{word} = {translation}\n")
        
        print(f"\nОтсутствующие слова сохранены в: missing_valuable_words.txt")
    else:
        print("\n✅ Все ценные слова из TEMP файла присутствуют в FINAL!")
    
    # Статистика по качеству переводов в TEMP
    good_translations = sum(1 for word, translation in temp_words.items() 
                           if is_valuable_word(word, translation))
    
    print(f"\n=== СТАТИСТИКА TEMP ФАЙЛА ===")
    print(f"Всего слов: {len(temp_words)}")
    print(f"С хорошими переводами: {good_translations}")
    print(f"Требуют доработки: {len(temp_words) - good_translations}")
    
    # Проверяем пересечения
    intersection = set(temp_words.keys()) & final_words
    print(f"\nПересечение TEMP и FINAL: {len(intersection)} слов")

if __name__ == "__main__":
    main()
