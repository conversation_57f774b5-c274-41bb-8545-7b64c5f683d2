#!/usr/bin/env python3
"""
Анализ соотношения частотности и CEFR уровней
Сравнивает COCA/Google частотные списки с Oxford CEFR уровнями
"""

import csv
import os

def load_oxford_words_by_level():
    """Загружает слова Oxford по уровням CEFR"""
    oxford_words = {}
    levels = ['a1', 'a2', 'b1', 'b2', 'c1']
    
    for level in levels:
        oxford_words[level] = set()
        filepath = f'oxford_5000_by_levels/oxford_{level}.csv'
        
        if os.path.exists(filepath):
            with open(filepath, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                next(reader)  # Пропускаем заголовок
                for row in reader:
                    if row:  # Проверяем, что строка не пустая
                        word = row[0].strip().lower()
                        oxford_words[level].add(word)
    
    return oxford_words

def load_frequency_words(filename, limit=None):
    """Загружает частотный список слов"""
    words = []
    with open(filename, 'r', encoding='utf-8') as f:
        for i, line in enumerate(f):
            if limit and i >= limit:
                break
            word = line.strip().lower()
            if word:
                words.append(word)
    return words

def analyze_frequency_vs_cefr(freq_words, oxford_words, name):
    """Анализирует соотношение частотности и CEFR уровней"""
    print(f"\n=== АНАЛИЗ {name} ===")
    
    # Анализ по диапазонам частотности
    ranges = [
        (0, 500, "1-500 (супер частотные)"),
        (500, 1000, "501-1000 (очень частотные)"),
        (1000, 2000, "1001-2000 (частотные)"),
        (2000, 3000, "2001-3000 (средние)"),
        (3000, 5000, "3001-5000 (менее частотные)")
    ]
    
    for start, end, description in ranges:
        if start >= len(freq_words):
            continue
            
        end = min(end, len(freq_words))
        range_words = set(freq_words[start:end])
        
        print(f"\n{description} ({end-start} слов):")
        total_found = 0
        
        for level in ['a1', 'a2', 'b1', 'b2', 'c1']:
            intersection = range_words & oxford_words[level]
            count = len(intersection)
            total_found += count
            percentage = (count / len(range_words)) * 100 if range_words else 0
            print(f"  {level.upper()}: {count:3d} слов ({percentage:5.1f}%)")
        
        coverage = (total_found / len(range_words)) * 100 if range_words else 0
        print(f"  Всего найдено в Oxford: {total_found}/{len(range_words)} ({coverage:.1f}%)")

def main():
    print("Загружаем данные Oxford...")
    oxford_words = load_oxford_words_by_level()
    
    # Статистика Oxford
    print("\n=== СТАТИСТИКА OXFORD ===")
    for level in ['a1', 'a2', 'b1', 'b2', 'c1']:
        count = len(oxford_words[level])
        print(f"Oxford {level.upper()}: {count} слов")
    
    # Анализ COCA
    if os.path.exists('coca_5000_words.txt'):
        print("\nЗагружаем COCA 5000...")
        coca_words = load_frequency_words('coca_5000_words.txt', 5000)
        analyze_frequency_vs_cefr(coca_words, oxford_words, "COCA 5000")
    
    # Анализ Google
    if os.path.exists('google_10000_english.txt'):
        print("\nЗагружаем Google 10000...")
        google_words = load_frequency_words('google_10000_english.txt', 5000)  # Берем первые 5000 для сравнения
        analyze_frequency_vs_cefr(google_words, oxford_words, "GOOGLE 10000 (первые 5000)")

if __name__ == "__main__":
    main()
