#!/usr/bin/env python3
"""
Перенумерация A1_FINAL_word_list.md после добавления слов
"""

import re

def renumber_final_list():
    """Перенумеровывает весь список"""
    
    # Читаем файл
    with open('../word_lists/A1_FINAL_word_list.md', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Извлекаем слова
    words = []
    header_lines = []
    in_header = True
    
    for line in lines:
        if in_header:
            if line.strip().startswith('## WORD LIST:'):
                header_lines.append(line)
                header_lines.append('\n')
                in_header = False
            else:
                header_lines.append(line)
        else:
            # Ищем строки со словами
            match = re.match(r'\s*\d+\.\s*(.+?)\s*=\s*(.+)', line)
            if match:
                word = match.group(1).strip()
                translation = match.group(2).strip()
                words.append((word, translation))
    
    # Обновляем заголовок с количеством слов
    for i, line in enumerate(header_lines):
        if line.startswith('**TOTAL WORDS**:'):
            header_lines[i] = f'**TOTAL WORDS**: {len(words)}\n'
            break
    
    # Создаем новый файл
    output_lines = header_lines.copy()
    
    # Добавляем перенумерованные слова
    for i, (word, translation) in enumerate(words, 1):
        output_lines.append(f'{i:4d}. {word} = {translation}\n')
    
    # Сохраняем
    with open('../word_lists/A1_FINAL_word_list.md', 'w', encoding='utf-8') as f:
        f.writelines(output_lines)
    
    print(f"Список перенумерован: {len(words)} слов")
    
    # Проверяем добавленные слова
    added_words = ['every', 'last', 'my', 'our', 'their']
    found_words = []
    
    for word, translation in words:
        if word.lower() in added_words:
            found_words.append(f"{word} = {translation}")
    
    print("\nДобавленные слова:")
    for word_pair in found_words:
        print(f"  ✅ {word_pair}")

if __name__ == "__main__":
    renumber_final_list()
