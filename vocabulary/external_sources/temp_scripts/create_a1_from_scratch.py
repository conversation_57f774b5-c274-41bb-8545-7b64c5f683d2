#!/usr/bin/env python3
"""
Создание A1 списка с нуля из Oxford + CEFR-J с применением алгоритма фильтрации
"""

import csv
import re

def load_oxford_a1():
    """Загружает слова Oxford A1"""
    words = set()
    print("Загружаем Oxford A1...")

    with open('oxford_5000_by_levels/oxford_a1.csv', 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        next(reader)  # Пропускаем заголовок
        for row in reader:
            if row:
                word = row[0].strip().lower()
                word_class = row[1].strip().lower() if len(row) > 1 else ""

                # Применяем фильтр английских специфичных элементов
                if not is_english_specific(word, word_class):
                    words.add(word)

    print(f"Oxford A1: {len(words)} слов после фильтрации")
    return words

def load_cefrj_a1():
    """Загружает слова CEFR-J A1"""
    words = set()
    print("Загружаем CEFR-J A1...")

    with open('cefrj_by_levels/cefrj_a1.csv', 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        next(reader)  # Пропускаем заголовок
        for row in reader:
            if row:
                word = row[0].strip().lower()
                pos = row[1].strip().lower() if len(row) > 1 else ""

                # Применяем фильтр английских специфичных элементов
                if not is_english_specific(word, pos):
                    words.add(word)

    print(f"CEFR-J A1: {len(words)} слов после фильтрации")
    return words

def is_english_specific(word, word_class):
    """Проверяет, является ли слово специфичным для английского языка"""

    # Артикли
    if word in ['a', 'an', 'the']:
        return True

    # Части речи, специфичные для английского
    english_specific_pos = [
        'indefinite article', 'definite article', 'article',
        'determiner'
    ]

    if word_class in english_specific_pos:
        return True

    # Специфичные английские слова/конструкции
    english_specific_words = [
        'am', 'pm', 'a.m.', 'p.m.', 'a.m./a.m./am/am',
        'mr', 'mrs', 'ms', 'dr',  # титулы
        "'s", "'re", "'ll", "'ve", "'d",  # сокращения
    ]

    if word in english_specific_words:
        return True

    # Слова с апострофами (английские сокращения)
    if "'" in word:
        return True

    # Слова с точками (временные обозначения)
    if word.count('.') > 1 or '/am/' in word:
        return True

    return False

def load_a0_words():
    """Загружает A0 слова для исключения"""
    a0_english = {
        "i", "you", "yes", "no", "what", "where", "want", "eat", "drink", "help",
        "thank", "sorry", "doctor", "water", "this", "go", "sleep", "see", "speak",
        "understand", "know", "live", "do", "mother", "father", "friend", "person",
        "child", "now", "today", "tomorrow", "here", "there", "when", "how", "soon",
        "bread", "food", "hungry", "sick", "money", "work", "hospital", "phone",
        "dangerous", "good", "bad", "big", "small", "house", "we", "they", "have",
        "give", "take", "love", "come", "buy", "son", "daughter", "brother", "sister",
        "then", "yesterday", "morning", "day", "evening", "far", "near", "milk",
        "meat", "hot", "cold", "rice", "tea", "coffee", "table", "chair", "bed",
        "car", "road", "shop", "school", "door", "window", "book", "clothes", "new",
        "old", "toilet", "fast", "slow", "easy", "heavy", "expensive", "cheap",
        "right", "important", "one", "two", "three", "ten", "hundred", "thousand",
        "many", "little", "all", "nothing", "how much", "please", "hello", "bye",
        "why", "white", "black", "red", "blue", "left", "right", "straight", "up",
        "down", "hurt", "tired", "bus", "train", "plane", "head", "heart", "stomach",
        "hand", "leg", "police", "lost", "urgent", "what", "first", "slowly", "can",
        "cannot", "open", "closed", "tasty"
    }

    return a0_english

def is_outdated_word(word):
    """Проверяет, является ли слово устаревшим"""
    outdated_words = {
        "fax", "cassette", "floppy", "cd", "dvd", "pager", "typewriter",
        "telegram", "newspaper", "magazine", "cart", "carriage", "typist",
        "operator", "kerosene", "lamp"
    }

    return word.lower() in outdated_words

def main():
    print("=== СОЗДАНИЕ A1 СПИСКА С НУЛЯ ===")

    # ШАГ 1: Объединение источников
    oxford_words = load_oxford_a1()
    cefrj_words = load_cefrj_a1()

    # Объединяем
    all_words = oxford_words | cefrj_words
    print(f"Объединено уникальных слов: {len(all_words)}")

    # ШАГ 2: Фильтрация уже применена в load функциях

    # ШАГ 3: Удаление дублей с A0
    a0_words = load_a0_words()
    words_after_a0 = all_words - a0_words
    print(f"После исключения A0 дублей: {len(words_after_a0)} слов")
    print(f"Исключено A0 дублей: {len(all_words) - len(words_after_a0)}")

    # ШАГ 4: Удаление устаревших слов
    final_words = {word for word in words_after_a0 if not is_outdated_word(word)}
    print(f"После исключения устаревших: {len(final_words)} слов")
    print(f"Исключено устаревших: {len(words_after_a0) - len(final_words)}")

    # ШАГ 5: Сортировка по алфавиту
    sorted_words = sorted(final_words)

    # ШАГ 6: Создание файла
    output_lines = [
        "# A1 WORD LIST (FROM SCRATCH)",
        "",
        f"**TOTAL WORDS**: {len(sorted_words)}",
        f"**SOURCES**: Oxford A1 + CEFR-J A1",
        f"**FILTERED**: English-specific elements, A0 duplicates, outdated words",
        "",
        "## WORD LIST:",
        ""
    ]

    for i, word in enumerate(sorted_words, 1):
        output_lines.append(f"{i:4d}. {word} = [НУЖЕН ПЕРЕВОД]")

    # Сохраняем файл
    output_file = '../word_lists/A1_TEMP_word_list_2.md'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(output_lines))

    print(f"\nНовый A1 список сохранен в: {output_file}")
    print(f"Готов для добавления переводов!")

if __name__ == "__main__":
    main()