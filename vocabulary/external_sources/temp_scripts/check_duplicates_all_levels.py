#!/usr/bin/env python3
"""
Проверка дублей между всеми уровнями A0-B2
"""

import re

def load_a0_words():
    """Загружает слова A0 из табличного формата"""
    words = set()
    try:
        with open('../word_lists/A0_word_list.md', 'r', encoding='utf-8') as f:
            content = f.read()
            # Ищем строки таблицы с английскими словами
            matches = re.findall(r'\|\s*\d+\s*\|\s*[^|]+\s*\|\s*([^|]+?)\s*\|', content)
            for word in matches:
                word_clean = word.strip().lower()
                if word_clean and word_clean != 'английское':  # исключаем заголовок
                    words.add(word_clean)
    except FileNotFoundError:
        print("⚠️ A0 файл не найден")
    
    return words

def load_level_words(level):
    """Загружает слова из временного файла уровня"""
    words = set()
    filename = f'../word_lists/temp_word_lists/{level}_TEMP_word_list.md'
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
            # Ищем слова в формате "номер. word = ..."
            matches = re.findall(r'\d+\.\s*([^=]+?)\s*=', content)
            for word in matches:
                word_clean = word.strip().lower()
                # Убираем слова с слэшами (берем первый вариант)
                if '/' in word_clean:
                    word_clean = word_clean.split('/')[0]
                words.add(word_clean)
    except FileNotFoundError:
        print(f"⚠️ {level} файл не найден")
    
    return words

def find_duplicates_between_levels(level1_words, level2_words, level1_name, level2_name):
    """Находит дубли между двумя уровнями"""
    duplicates = level1_words & level2_words
    return duplicates

def main():
    print("=== ПРОВЕРКА ДУБЛЕЙ МЕЖДУ ВСЕМИ УРОВНЯМИ ===\n")
    
    # Загружаем все уровни
    levels = {
        'A0': load_a0_words(),
        'A1': load_level_words('A1'),
        'A2': load_level_words('A2'),
        'B1': load_level_words('B1'),
        'B2': load_level_words('B2')
    }
    
    # Показываем количество слов в каждом уровне
    print("📊 КОЛИЧЕСТВО СЛОВ ПО УРОВНЯМ:")
    total_words = 0
    for level, words in levels.items():
        print(f"{level}: {len(words)} слов")
        total_words += len(words)
    print(f"ИТОГО: {total_words} слов\n")
    
    # Проверяем дубли между всеми парами уровней
    print("🔍 ПРОВЕРКА ДУБЛЕЙ МЕЖДУ УРОВНЯМИ:")
    
    level_names = list(levels.keys())
    total_duplicates = 0
    duplicate_details = []
    
    for i in range(len(level_names)):
        for j in range(i + 1, len(level_names)):
            level1 = level_names[i]
            level2 = level_names[j]
            
            duplicates = find_duplicates_between_levels(
                levels[level1], levels[level2], level1, level2
            )
            
            if duplicates:
                print(f"❌ {level1} ↔ {level2}: {len(duplicates)} дублей")
                duplicate_details.append((level1, level2, duplicates))
                total_duplicates += len(duplicates)
            else:
                print(f"✅ {level1} ↔ {level2}: дублей нет")
    
    print(f"\n📈 ИТОГО НАЙДЕНО ДУБЛЕЙ: {total_duplicates}")
    
    # Показываем детали дублей
    if duplicate_details:
        print("\n🔍 ДЕТАЛИ ДУБЛЕЙ:")
        for level1, level2, duplicates in duplicate_details:
            print(f"\n--- {level1} ↔ {level2} ({len(duplicates)} дублей) ---")
            sorted_duplicates = sorted(duplicates)
            for i, word in enumerate(sorted_duplicates[:20], 1):  # показываем первые 20
                print(f"{i:2d}. {word}")
            if len(duplicates) > 20:
                print(f"    ... и еще {len(duplicates) - 20} слов")
    
    # Проверяем уникальные слова
    print("\n🎯 АНАЛИЗ УНИКАЛЬНОСТИ:")
    all_words = set()
    for words in levels.values():
        all_words.update(words)
    
    unique_count = len(all_words)
    duplicate_count = total_words - unique_count
    
    print(f"Всего уникальных слов: {unique_count}")
    print(f"Всего дублей: {duplicate_count}")
    print(f"Процент дублирования: {duplicate_count/total_words*100:.1f}%")
    
    # Рекомендации
    if duplicate_count > 0:
        print(f"\n💡 РЕКОМЕНДАЦИИ:")
        print(f"1. Удалить {duplicate_count} дублей")
        print(f"2. Итоговое количество слов: {unique_count}")
        print(f"3. Экономия: {duplicate_count} слов")

if __name__ == "__main__":
    main()
