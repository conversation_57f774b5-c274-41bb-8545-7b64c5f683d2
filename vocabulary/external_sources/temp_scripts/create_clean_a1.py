#!/usr/bin/env python3
"""
Создание чистого единого списка A1 без устаревших слов
"""

import re

def extract_existing_words_with_translations(filepath):
    """Извлекает существующие слова с переводами"""
    words_dict = {}
    
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
        
        # Ищем слова в формате "номер. word = перевод"
        matches = re.findall(r'\d+\.\s*([^=]+?)\s*=\s*([^=\n]+)', content)
        
        for word, translation in matches:
            word_clean = word.strip().lower()
            translation_clean = translation.strip()
            
            # Пропускаем строки с "УСТАРЕЛО"
            if "❌ УСТАРЕЛО" not in translation_clean:
                words_dict[word_clean] = translation_clean
    
    return words_dict

def load_new_words_list(filepath):
    """Загружает список новых слов без переводов"""
    new_words = []
    
    with open(filepath, 'r', encoding='utf-8') as f:
        for line in f:
            word = line.strip().lower()
            if word:
                new_words.append(word)
    
    return new_words

def load_a0_words():
    """Загружает A0 слова для исключения"""
    a0_english = {
        "i", "you", "yes", "no", "what", "where", "want", "eat", "drink", "help",
        "thank", "sorry", "doctor", "water", "this", "go", "sleep", "see", "speak",
        "understand", "know", "live", "do", "mother", "father", "friend", "person",
        "child", "now", "today", "tomorrow", "here", "there", "when", "how", "soon",
        "bread", "food", "hungry", "sick", "money", "work", "hospital", "phone",
        "dangerous", "good", "bad", "big", "small", "house", "we", "they", "have",
        "give", "take", "love", "come", "buy", "son", "daughter", "brother", "sister",
        "then", "yesterday", "morning", "day", "evening", "far", "near", "milk",
        "meat", "hot", "cold", "rice", "tea", "coffee", "table", "chair", "bed",
        "car", "road", "shop", "school", "door", "window", "book", "clothes", "new",
        "old", "toilet", "fast", "slow", "easy", "heavy", "expensive", "cheap",
        "right", "important", "one", "two", "three", "ten", "hundred", "thousand",
        "many", "little", "all", "nothing", "how much", "please", "hello", "bye",
        "why", "white", "black", "red", "blue", "left", "right", "straight", "up",
        "down", "hurt", "tired", "bus", "train", "plane", "head", "heart", "stomach",
        "hand", "leg", "police", "lost", "urgent", "what", "first", "slowly", "can",
        "cannot", "open", "closed", "tasty"
    }
    
    return a0_english

def is_outdated_word(word):
    """Проверяет, является ли слово устаревшим"""
    outdated_words = {
        "fax", "cassette", "floppy", "cd", "dvd", "pager", "typewriter",
        "telegram", "newspaper", "magazine", "cart", "carriage", "typist",
        "operator", "kerosene", "lamp", "факс", "кассета", "дискета", 
        "диск", "журнал", "газета", "телеграмма", "машинистка", "телефонистка"
    }
    
    return word.lower() in outdated_words

def create_translation_for_new_word(word):
    """Создает базовый перевод для нового слова"""
    basic_translations = {
        "bookstore": "книжный магазин",
        "borrow": "занимать",
        "bottom": "дно",
        "bowl": "миска",
        "brain": "мозг",
        "breathe": "дышать",
        "bridge": "мост",
        "bright": "яркий",
        "broken": "сломанный",
        "brush": "щетка",
        "bucket": "ведро",
        "burger": "бургер",
        "butterfly": "бабочка",
        "button": "кнопка",
        "cafe": "кафе",
        "camp": "лагерь",
        "candy": "конфета",
        "cap": "кепка",
        "care": "забота",
        "careful": "осторожный",
        "carefully": "осторожно",
        "cartoon": "мультфильм",
        "case": "случай",
        "catch": "ловить",
        "celebrate": "праздновать",
        "celebration": "празднование",
        "character": "персонаж",
        "church": "церковь",
        "circle": "круг",
        "classmate": "одноклассник",
        "clever": "умный",
        "closed": "закрытый",
        "cloth": "ткань",
        "cloud": "облако",
        "cloudy": "облачный",
        "coach": "тренер",
        "code": "код",
        "coke": "кола",
        "collect": "собирать",
        "collection": "коллекция",
        "color": "цвет",
        "contest": "конкурс",
        "cookie": "печенье",
        "cop": "полицейский",
        "copy": "копия",
        "corn": "кукуруза",
        "corner": "угол",
        "couch": "диван",
        "cover": "покрывать",
        "cry": "плакать",
        "cute": "милый",
        "daddy": "папочка",
        "did": "делал",
        "dig": "копать",
        "does": "делает",
        "doing": "делание",
        "doll": "кукла",
        "drama": "драма",
        "dream": "мечта",
        "drop": "капля",
        "drum": "барабан",
        "dry": "сухой",
        "due": "должный",
        "either": "либо",
        "elementary": "начальный",
        "email": "электронная почта",
        "engineer": "инженер",
        "everyday": "повседневный",
        "everywhere": "везде",
        "excellent": "отличный",
        "excuse": "извинение",
        "factory": "фабрика",
        "fair": "справедливый",
        "fairy": "фея",
        "fan": "вентилятор",
        "favorite": "любимый",
        "feed": "кормить",
        "fever": "лихорадка",
        "field": "поле",
        "fight": "драться",
        "file": "файл",
        "fishing": "рыбалка",
        "flag": "флаг",
        "focus": "фокус",
        "foggy": "туманный",
        "following": "следующий",
        "foreign": "иностранный",
        "foreigner": "иностранец",
        "frog": "лягушка",
        "garbage": "мусор",
        "ghost": "призрак",
        "gift": "подарок",
        "glad": "рад",
        "glasses": "очки",
        "goal": "цель",
        "gold": "золото",
        "grade": "класс",
        "grammar": "грамматика",
        "grandma": "бабушка",
        "grandpa": "дедушка",
        "grape": "виноград",
        "grass": "трава",
        "gray": "серый",
        "greet": "приветствовать",
        "ground": "земля",
        "guest": "гость",
        "guy": "парень",
        "habit": "привычка",
        "had": "имел",
        "haircut": "стрижка",
        "hall": "зал",
        "hamburger": "гамбургер",
        "handsome": "красивый",
        "has": "имеет",
        "headache": "головная боль",
        "heart": "сердце",
        "heavy": "тяжелый",
        "hers": "её",
        "hide": "прятать",
        "hill": "холм",
        "hold": "держать",
        "hole": "дыра",
        "hometown": "родной город",
        "hurry": "спешить",
        "hurt": "болеть",
        "ideal": "идеальный",
        "inside": "внутри",
        "internet": "интернет",
        "interviewer": "интервьюер",
        "is": "является",
        "item": "предмет"
    }
    
    return basic_translations.get(word.lower(), f"[НУЖЕН ПЕРЕВОД]")

def main():
    print("Создание чистого единого списка A1...")
    
    # Загружаем данные
    existing_words = extract_existing_words_with_translations('../word_lists/A1_TEMP_word_list.md')
    new_words = load_new_words_list('unique_new_a1_words.txt')
    a0_words = load_a0_words()
    
    print(f"Существующих слов с переводами (без устаревших): {len(existing_words)}")
    print(f"Новых слов: {len(new_words)}")
    print(f"A0 слов для исключения: {len(a0_words)}")
    
    # Объединяем все слова
    all_words = {}
    
    # Добавляем существующие слова (исключая A0 и устаревшие)
    for word, translation in existing_words.items():
        if word not in a0_words and not is_outdated_word(word):
            all_words[word] = translation
    
    # Добавляем новые слова (исключая A0 и устаревшие)
    for word in new_words:
        if word not in a0_words and not is_outdated_word(word) and word not in all_words:
            translation = create_translation_for_new_word(word)
            all_words[word] = translation
    
    # Сортируем по алфавиту
    sorted_words = sorted(all_words.items())
    
    print(f"\nИтоговое количество слов: {len(sorted_words)}")
    
    # Создаем единый список
    output_lines = [
        "# A1 UNIFIED WORD LIST (CLEAN)",
        "",
        f"**TOTAL WORDS**: {len(sorted_words)}",
        f"**FORMAT**: english_word = russian_translation",
        "",
        "## WORD LIST:",
        ""
    ]
    
    for i, (word, translation) in enumerate(sorted_words, 1):
        output_lines.append(f"{i:4d}. {word} = {translation}")
    
    # Сохраняем файл
    output_file = '../word_lists/A1_CLEAN_word_list.md'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(output_lines))
    
    print(f"Чистый единый список сохранен в: {output_file}")
    
    # Статистика
    needs_translation = sum(1 for _, translation in sorted_words if "[НУЖЕН ПЕРЕВОД]" in translation)
    print(f"\nСтатистика:")
    print(f"  Слов с переводами: {len(sorted_words) - needs_translation}")
    print(f"  Слов требующих перевода: {needs_translation}")

if __name__ == "__main__":
    main()
