#!/usr/bin/env python3
"""
Создание A2 списка с нуля из Oxford + CEFR-J с применением алгоритма фильтрации
"""

import csv
import re

def load_oxford_a2():
    """Загружает слова Oxford A2"""
    words = set()
    print("Загружаем Oxford A2...")

    with open('oxford_5000_by_levels/oxford_a2.csv', 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        next(reader)  # Пропускаем заголовок
        for row in reader:
            if row:
                word = row[0].strip().lower()
                word_class = row[1].strip().lower() if len(row) > 1 else ""

                # Применяем фильтр английских специфичных элементов
                if not is_english_specific(word, word_class):
                    words.add(word)

    print(f"Oxford A2: {len(words)} слов после фильтрации")
    return words

def load_cefrj_a2():
    """Загружает слова CEFR-J A2"""
    words = set()
    print("Загружаем CEFR-J A2...")

    with open('cefrj_by_levels/cefrj_a2.csv', 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        next(reader)  # Пропускаем заголовок
        for row in reader:
            if row:
                word = row[0].strip().lower()
                pos = row[1].strip().lower() if len(row) > 1 else ""

                # Применяем фильтр английских специфичных элементов
                if not is_english_specific(word, pos):
                    words.add(word)

    print(f"CEFR-J A2: {len(words)} слов после фильтрации")
    return words

def is_english_specific(word, word_class):
    """Проверяет, является ли слово специфичным для английского языка"""

    # Артикли
    if word in ['a', 'an', 'the']:
        return True

    # Части речи, специфичные для английского
    english_specific_pos = [
        'indefinite article', 'definite article', 'article',
        'determiner'
    ]

    if word_class in english_specific_pos:
        return True

    # Специфичные английские слова/конструкции
    english_specific_words = [
        'am', 'pm', 'a.m.', 'p.m.', 'a.m./a.m./am/am',
        'mr', 'mrs', 'ms', 'dr',  # титулы
        "'s", "'re", "'ll", "'ve", "'d",  # сокращения
    ]

    if word in english_specific_words:
        return True

    # Слова с апострофами (английские сокращения)
    if "'" in word:
        return True

    # Слова с точками (временные обозначения)
    if word.count('.') > 1 or '/am/' in word:
        return True

    return False

def load_previous_levels_words():
    """Загружает слова A0 + A1 для исключения дублей"""
    previous_words = set()

    # A0 слова
    a0_words = {
        "i", "you", "yes", "no", "what", "where", "want", "eat", "drink", "help",
        "thank", "sorry", "doctor", "water", "this", "go", "sleep", "see", "speak",
        "understand", "know", "live", "do", "mother", "father", "friend", "person",
        "child", "now", "today", "tomorrow", "here", "there", "when", "how", "soon",
        "bread", "food", "hungry", "sick", "money", "work", "hospital", "phone",
        "dangerous", "good", "bad", "big", "small", "house", "we", "they", "have",
        "give", "take", "love", "come", "buy", "son", "daughter", "brother", "sister",
        "then", "yesterday", "morning", "day", "evening", "far", "near", "milk",
        "meat", "hot", "cold", "rice", "tea", "coffee", "table", "chair", "bed",
        "car", "road", "shop", "school", "door", "window", "book", "clothes", "new",
        "old", "toilet", "fast", "slow", "easy", "heavy", "expensive", "cheap",
        "right", "important", "one", "two", "three", "ten", "hundred", "thousand",
        "many", "little", "all", "nothing", "please", "hello", "bye",
        "why", "white", "black", "red", "blue", "left", "straight", "up",
        "down", "hurt", "tired", "bus", "train", "plane", "head", "heart", "stomach",
        "hand", "leg", "police", "lost", "first", "slowly", "can",
        "cannot", "open", "closed", "tasty"
    }

    previous_words.update(a0_words)

    # A1 слова - загружаем из финального файла
    try:
        with open('../word_lists/temp_word_lists/A1_TEMP_word_list.md', 'r', encoding='utf-8') as f:
            content = f.read()
            # Ищем слова в формате "номер. word = ..."
            matches = re.findall(r'\d+\.\s*([^=]+?)\s*=', content)
            for word in matches:
                word_clean = word.strip().lower()
                # Убираем слова с слэшами (альтернативные написания)
                if '/' in word_clean:
                    word_clean = word_clean.split('/')[0]
                previous_words.add(word_clean)
    except FileNotFoundError:
        print("⚠️ A1 файл не найден, используем только A0")

    return previous_words

def is_outdated_word(word):
    """Проверяет, является ли слово устаревшим"""
    outdated_words = {
        "fax", "cassette", "floppy", "cd", "dvd", "pager", "typewriter",
        "telegram", "newspaper", "magazine", "cart", "carriage", "typist",
        "operator", "kerosene", "lamp"
    }

    return word.lower() in outdated_words

def main():
    print("=== СОЗДАНИЕ A2 СПИСКА С НУЛЯ ===")

    # ШАГ 1: Объединение источников
    oxford_words = load_oxford_a2()
    cefrj_words = load_cefrj_a2()

    # Объединяем
    all_words = oxford_words | cefrj_words
    print(f"Объединено уникальных слов: {len(all_words)}")

    # ШАГ 2: Фильтрация уже применена в load функциях

    # ШАГ 3: Удаление дублей с A0 + A1
    previous_words = load_previous_levels_words()
    words_after_duplicates = all_words - previous_words
    print(f"После исключения A0+A1 дублей: {len(words_after_duplicates)} слов")
    print(f"Исключено дублей: {len(all_words) - len(words_after_duplicates)}")

    # ШАГ 4: Удаление устаревших слов
    final_words = {word for word in words_after_duplicates if not is_outdated_word(word)}
    print(f"После исключения устаревших: {len(final_words)} слов")
    print(f"Исключено устаревших: {len(words_after_duplicates) - len(final_words)}")

    # ШАГ 5: Сортировка по алфавиту
    sorted_words = sorted(final_words)

    # ШАГ 6: Создание файла
    output_lines = [
        "# A2 TEMP WORD LIST",
        "",
        f"**TOTAL WORDS**: {len(sorted_words)}",
        f"**SOURCES**: Oxford A2 + CEFR-J A2",
        f"**FILTERED**: English-specific elements, A0+A1 duplicates, outdated words",
        "",
        "## WORD LIST:",
        ""
    ]

    for i, word in enumerate(sorted_words, 1):
        output_lines.append(f"{i:4d}. {word} = [НУЖЕН ПЕРЕВОД]")

    # Сохраняем файл
    output_file = '../word_lists/temp_word_lists/A2_TEMP_word_list.md'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(output_lines))

    print(f"\nНовый A2 список сохранен в: {output_file}")
    print(f"Готов для добавления переводов!")

if __name__ == "__main__":
    main()