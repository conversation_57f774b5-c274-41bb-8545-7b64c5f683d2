#!/usr/bin/env python3
"""
Показывает исключенные английские специфичные слова
"""

import csv

def is_english_specific(word, word_class):
    """Проверяет, является ли слово специфичным для английского языка"""
    
    # Артикли
    if word in ['a', 'an', 'the']:
        return True
    
    # Части речи, специфичные для английского
    english_specific_pos = [
        'indefinite article', 'definite article', 'article',
        'determiner'  # часто включает артикли
    ]
    
    if word_class in english_specific_pos:
        return True
    
    # Специфичные английские слова/конструкции
    english_specific_words = [
        'am', 'pm', 'a.m.', 'p.m.', 'a.m./a.m./am/am',
        'mr', 'mrs', 'ms', 'dr',  # титулы
        "'s", "'re", "'ll", "'ve", "'d",  # сокращения
    ]
    
    if word in english_specific_words:
        return True
    
    # Слова с апострофами (английские сокращения)
    if "'" in word:
        return True
    
    return False

def show_excluded_oxford():
    """Показывает исключенные слова из Oxford"""
    print("=== ИСКЛЮЧЕННЫЕ СЛОВА ИЗ OXFORD A1 ===")
    excluded = []
    
    with open('oxford_5000_by_levels/oxford_a1.csv', 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        next(reader)
        for row in reader:
            if row:
                word = row[0].strip().lower()
                word_class = row[1].strip().lower()
                if is_english_specific(word, word_class):
                    excluded.append((word, word_class))
    
    for word, word_class in sorted(excluded):
        print(f"  {word} ({word_class})")
    
    return len(excluded)

def show_excluded_cefrj():
    """Показывает исключенные слова из CEFR-J"""
    print("\n=== ИСКЛЮЧЕННЫЕ СЛОВА ИЗ CEFR-J A1 ===")
    excluded = []
    
    with open('cefrj_by_levels/cefrj_a1.csv', 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        next(reader)
        for row in reader:
            if row:
                word = row[0].strip().lower()
                pos = row[1].strip().lower() if len(row) > 1 else ""
                if is_english_specific(word, pos):
                    excluded.append((word, pos))
    
    for word, pos in sorted(excluded):
        print(f"  {word} ({pos})")
    
    return len(excluded)

def main():
    oxford_count = show_excluded_oxford()
    cefrj_count = show_excluded_cefrj()
    
    print(f"\nВсего исключено:")
    print(f"  Oxford: {oxford_count} слов")
    print(f"  CEFR-J: {cefrj_count} слов")

if __name__ == "__main__":
    main()
