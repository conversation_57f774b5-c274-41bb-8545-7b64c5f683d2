#!/usr/bin/env python3
"""
Детальный анализ отсутствующих важных слов
"""

import re

def load_our_words():
    """Загружает все наши слова A0-B2"""
    all_words = set()
    
    # A0 слова
    try:
        with open('../word_lists/A0_word_list.md', 'r', encoding='utf-8') as f:
            content = f.read()
            matches = re.findall(r'\|\s*\d+\s*\|\s*[^|]+\s*\|\s*([^|]+?)\s*\|', content)
            for word in matches:
                word_clean = word.strip().lower()
                if word_clean and word_clean != 'английское':
                    all_words.add(word_clean)
    except FileNotFoundError:
        pass
    
    # A1-B2 слова
    for level in ['A1', 'A2', 'B1', 'B2']:
        try:
            with open(f'../word_lists/temp_word_lists/{level}_TEMP_word_list.md', 'r', encoding='utf-8') as f:
                content = f.read()
                matches = re.findall(r'\d+\.\s*([^=]+?)\s*=', content)
                for word in matches:
                    word_clean = word.strip().lower()
                    if '/' in word_clean:
                        word_clean = word_clean.split('/')[0]
                    all_words.add(word_clean)
        except FileNotFoundError:
            pass
    
    return all_words

def load_coca_words(limit=5000):
    """Загружает COCA слова"""
    words = set()
    try:
        with open('coca_5000_words.txt', 'r', encoding='utf-8') as f:
            for i, line in enumerate(f):
                if i >= limit:
                    break
                word = line.strip().lower()
                if word and not is_english_specific(word):
                    words.add(word)
    except FileNotFoundError:
        pass
    return words

def is_english_specific(word):
    """Фильтрует английские специфичные слова"""
    if word in ['a', 'an', 'the', 'i', 'me', 'my', 'he', 'she', 'it', 'we', 'they', 'him', 'her', 'us', 'them']:
        return True
    if "'" in word:
        return True
    if word in ['am', 'pm', 'mr', 'mrs', 'ms', 'dr', 'ok', 'okay']:
        return True
    return False

def categorize_missing_words(missing_words):
    """Детальная категоризация отсутствующих слов"""
    categories = {
        'essential_concepts': set(),
        'country_nationality': set(),
        'professional_terms': set(),
        'technology': set(),
        'grammar_forms': set(),
        'proper_nouns': set(),
        'slang_informal': set(),
        'outdated': set(),
        'too_specific': set(),
        'worth_adding': set()
    }
    
    # Важные концепты, которые стоит добавить
    essential = {
        'according', 'added', 'changing', 'coming', 'combined', 'clinical', 
        'chronic', 'collective', 'combat', 'commentary', 'compliance', 'comply',
        'constitutional', 'boundary', 'carrier', 'chamber', 'charter', 'cluster',
        'collaboration', 'commissioner', 'congress', 'canvas', 'clay'
    }
    
    # Страны и национальности
    countries = {
        'american', 'british', 'canadian', 'chinese', 'african', 'asian', 'arab', 'catholic'
    }
    
    # Профессиональные термины
    professional = {
        'administrator', 'attorney', 'ceo', 'accounting', 'census', 'amendment'
    }
    
    # Технологии
    tech = {'cd', 'auto', 'archive', 'array', 'alpha', 'android'}
    
    # Грамматические формы
    grammar = {'your', 'respectively', 'anytime', 'and/or', 'amid'}
    
    # Собственные имена
    proper = {'christmas', 'bible'}
    
    # Сленг/неформальное
    slang = {'ass', 'butt', 'babe'}
    
    # Слишком специфичные
    specific = {'casino', 'cart'}
    
    for word in missing_words:
        if word in essential:
            categories['essential_concepts'].add(word)
        elif word in countries:
            categories['country_nationality'].add(word)
        elif word in professional:
            categories['professional_terms'].add(word)
        elif word in tech:
            categories['technology'].add(word)
        elif word in grammar:
            categories['grammar_forms'].add(word)
        elif word in proper:
            categories['proper_nouns'].add(word)
        elif word in slang:
            categories['slang_informal'].add(word)
        elif word in specific:
            categories['too_specific'].add(word)
        else:
            categories['worth_adding'].add(word)
    
    return categories

def main():
    print("=== ДЕТАЛЬНЫЙ АНАЛИЗ ОТСУТСТВУЮЩИХ СЛОВ ===\n")
    
    our_words = load_our_words()
    coca_words = load_coca_words(5000)
    
    missing = coca_words - our_words
    
    print(f"Отсутствующих слов из COCA 5000: {len(missing)}")
    
    categories = categorize_missing_words(missing)
    
    print("\n📋 КАТЕГОРИЗАЦИЯ ОТСУТСТВУЮЩИХ СЛОВ:\n")
    
    # Показываем каждую категорию
    for category, words in categories.items():
        if words:
            category_name = category.replace('_', ' ').title()
            print(f"🔹 {category_name}: {len(words)} слов")
            sorted_words = sorted(words)
            
            if category in ['essential_concepts', 'worth_adding']:
                print(f"   РЕКОМЕНДУЕТСЯ ДОБАВИТЬ:")
                for word in sorted_words:
                    print(f"   + {word}")
            elif category in ['slang_informal', 'too_specific', 'outdated']:
                print(f"   НЕ РЕКОМЕНДУЕТСЯ:")
                for word in sorted_words:
                    print(f"   - {word}")
            else:
                print(f"   НА РАССМОТРЕНИЕ:")
                for word in sorted_words:
                    print(f"   ? {word}")
            print()
    
    # Рекомендации
    essential_count = len(categories['essential_concepts'])
    worth_adding_count = len(categories['worth_adding'])
    
    print("💡 РЕКОМЕНДАЦИИ:")
    print(f"1. Обязательно добавить: {essential_count} слов (essential_concepts)")
    print(f"2. Рассмотреть для добавления: {worth_adding_count} слов")
    print(f"3. Игнорировать: сленг, слишком специфичные, устаревшие")
    
    total_recommended = essential_count + worth_adding_count
    print(f"\nИтого рекомендуется добавить: ~{total_recommended} слов")
    print(f"Это увеличит покрытие COCA с 89.5% до ~{(len(coca_words & our_words) + total_recommended)/len(coca_words)*100:.1f}%")

if __name__ == "__main__":
    main()
