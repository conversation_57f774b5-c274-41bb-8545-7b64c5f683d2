# 🔍 Статус CEFR Маппинга для Внешних Источников

## ✅ **ИСТОЧНИКИ С CEFR УРОВНЯМИ**

### 1. Oxford 5000 ⭐⭐⭐⭐⭐
- ✅ **Полная разбивка по уровням A1-C1**
- ✅ **Официальная классификация Cambridge/Oxford**
- ✅ **Готов к использованию**

### 2. CEFR-J Vocabulary Profile ⭐⭐⭐⭐⭐
- ✅ **Полная разбивка по уровням A1-C2**
- ✅ **Академическая классификация Tokyo University**
- ✅ **Готов к использованию**

### 3. Maximax Words-CEFR Dataset ⭐⭐⭐⭐
- ✅ **Машинная классификация A1-C2**
- ✅ **248k+ слов с уровнями**
- ✅ **Готов к использованию**

## ❌ **ИСТОЧНИКИ БЕЗ CEFR УРОВНЕЙ**

### 4. Google 10000 English
- ❌ **НЕТ разбивки по уровням CEFR**
- ✅ **Есть частотность (Google Trillion Word Corpus)**
- 🔍 **Поиск результат**: Готовых маппингов НЕ НАЙДЕНО
- 📝 **Статус**: Только справочно для частотного анализа

### 5. COCA 5000 Word Frequency
- ❌ **НЕТ разбивки по уровням CEFR**
- ✅ **Есть частотность + части речи**
- 🔍 **Поиск результат**: Готовых маппингов НЕ НАЙДЕНО
- 📝 **Статус**: Только справочно для частотного анализа

## 🔬 **ИССЛЕДОВАНИЯ И ВОЗМОЖНОСТИ**

### Найденные исследования:
1. **"Word distribution in vocabulary and COCA word frequency and CEFR levels"** (2022)
   - Есть корреляция между частотностью и CEFR
   - НО готового маппинга нет

2. **"Automatic CEFR Level Prediction"** исследования
   - Существуют алгоритмы автоматической классификации
   - НО готовых результатов для Google 10k/COCA 5k нет

3. **English Vocabulary Profile (EVP)**
   - Упоминается как источник CEFR классификации
   - НО сайт недоступен, готовых файлов не найдено

### Потенциальные решения:
1. **Машинная классификация**: Использовать Maximax алгоритм для классификации Google 10k/COCA 5k
2. **Пересечение с Oxford/CEFR-J**: Найти пересечения и присвоить уровни
3. **Частотная эвристика**: Использовать корреляцию частотность→CEFR из исследований

## 📊 **ИТОГОВАЯ ТАБЛИЦА ГОТОВНОСТИ**

| Источник | Слов | CEFR A1 | CEFR A2 | CEFR B1 | CEFR B2 | CEFR C1 | CEFR C2 | Статус |
|----------|------|---------|---------|---------|---------|---------|---------|--------|
| **Oxford 5000** | 5943 | ✅ 1076 | ✅ 990 | ✅ 902 | ✅ 1571 | ✅ 1404 | ❌ - | ГОТОВ |
| **CEFR-J** | 9935 | ✅ 1164 | ✅ 1411 | ✅ 2446 | ✅ 2778 | ✅ 1111 | ✅ 1025 | ГОТОВ |
| **Maximax** | 248k+ | ✅ ~40k | ✅ ~40k | ✅ ~40k | ✅ ~40k | ✅ ~40k | ✅ ~40k | ГОТОВ |
| **Google 10k** | 10000 | ❌ НЕТ | ❌ НЕТ | ❌ НЕТ | ❌ НЕТ | ❌ НЕТ | ❌ НЕТ | СПРАВОЧНО |
| **COCA 5k** | 5000 | ❌ НЕТ | ❌ НЕТ | ❌ НЕТ | ❌ НЕТ | ❌ НЕТ | ❌ НЕТ | СПРАВОЧНО |

## 🎯 **РЕКОМЕНДАЦИИ**

### Для создания словарей MVP:
1. **Используйте Oxford 5000 + CEFR-J** как основные источники
2. **Maximax** - для расширения и проверки
3. **Google 10k/COCA 5k** - только для валидации частотности

### Для будущего развития:
1. **Создать автоматический маппинг** Google 10k → CEFR
2. **Найти пересечения** между источниками
3. **Использовать частотную эвристику** для классификации

## ⚠️ **ВАЖНО**

**Google 10000 English и COCA 5000 НЕ ИМЕЮТ готовых CEFR классификаций.**

Эти источники ценны для:
- ✅ Проверки частотности выбранных слов
- ✅ Валидации популярности слов
- ✅ Справочной информации

НО НЕ подходят для:
- ❌ Прямого создания словарей по уровням
- ❌ Определения сложности слов
- ❌ CEFR классификации

---
*Обновлено: 2025-01-16*
*Статус поиска: Завершен - готовых CEFR маппингов для Google 10k/COCA 5k НЕТ*
