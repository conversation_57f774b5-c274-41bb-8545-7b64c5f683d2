# 📚 External Vocabulary Sources

Этот каталог содержит авторитетные внешние источники словарей для создания наших списков слов по уровням CEFR.

## 🎯 **ОСНОВНЫЕ ИСТОЧНИКИ**

### 1. **Oxford 5000** ⭐⭐⭐⭐⭐
- **Авторитетность**: Oxford University Press
- **Описание**: Официальный список 5000 наиболее важных слов английского языка
- **Формат**: CSV с разбивкой по уровням CEFR
- **Файлы**:
  - `oxford-5000.csv` - полный список (5948 слов)
  - `oxford_5000_by_levels/` - разбивка по уровням:
    - `oxford_a1.csv` - 1076 слов
    - `oxford_a2.csv` - 990 слов  
    - `oxford_b1.csv` - 902 слова
    - `oxford_b2.csv` - 1571 слово
    - `oxford_c1.csv` - 1404 слова

### 2. **CEFR-J Vocabulary Profile** ⭐⭐⭐⭐⭐
- **Авторитетность**: Tokyo University of Foreign Studies
- **Описание**: Японская адаптация CEFR с детальной классификацией слов
- **Формат**: CSV с частями речи и частотностью
- **Файлы**:
  - `cefrj_vocabulary_profile.csv` - полный профиль (7801 слово)
  - `cefrj_by_levels/` - разбивка по уровням:
    - `cefrj_a1.csv` - 1164 слова
    - `cefrj_a2.csv` - 1164 слова
    - `cefrj_b1.csv` - 1164 слова  
    - `cefrj_b2.csv` - 1164 слова
    - `cefrj_c1.csv` - 1164 слова (из Octanove)
    - `cefrj_c2.csv` - 1164 слова (из Octanove)

> Google и Coca используй для проверки все ли слова добавлены. А то мало ли, может что-то пропустио. То есть после добавлений всех слов пройдись по првым 5000 из COCA и первым 6000 из Google и посмотри есть ли слова, которых нет в списках слов.  

### 3. ~~**Maximax Words-CEFR Dataset**~~ ❌ **ИСКЛЮЧЕН**
- **Проблема**: Использует нестандартную систему уровней (1-6 с дробями)
- **Не совместимо**: С CEFR стандартом (A1, A2, B1, B2, C1, C2)
- **Статус**: Убран из основных источников
- **Причина**: 167k слов уровня "6", дробные уровни типа "4.43528970046929"

### 3. **Google 10000 English** ⭐⭐⭐⭐
- **Авторитетность**: Google Trillion Word Corpus
- **Описание**: 10000 наиболее частотных английских слов по данным Google
- **Особенности**: Основан на n-gram анализе триллиона слов из веб-страниц
- **✅ СОРТИРОВКА**: УЖЕ отсортирован по частотности (1="the", 2="of", 3="and"...)
- **⚠️ ОГРАНИЧЕНИЕ**: НЕТ разбивки по уровням CEFR
- **Использование**: Справочно для частотного анализа
- **Файлы**:
  - `google_10000_english.txt` - список 10000 слов по частотности

### 4. **COCA 5000 Word Frequency** ⭐⭐⭐⭐⭐
- **Авторитетность**: Corpus of Contemporary American English
- **Описание**: 5000 наиболее частотных слов современного американского английского
- **Особенности**: Включает части речи, частотность, распределение по жанрам
- **✅ СОРТИРОВКА**: УЖЕ отсортирован по частотности (1="the", 2="be", 3="and"...)
- **⚠️ ОГРАНИЧЕНИЕ**: НЕТ разбивки по уровням CEFR
- **Использование**: Справочно для частотного анализа и валидации
- **Файлы**:
  - `coca_5000_words.txt` - простой список 5000 слов
  - `coca_5000_frequency.csv` - детальная статистика

## 📊 **СРАВНИТЕЛЬНАЯ ТАБЛИЦА**

| Источник | A1 слов | A2 слов | B1 слов | B2 слов | C1 слов | C2 слов | Всего | CEFR | Статус |
|----------|---------|---------|---------|---------|---------|---------|-------|------|--------|
| **Oxford 5000** | 1076 | 990 | 902 | 1571 | 1404 | - | 5943 | ✅ | ОСНОВНОЙ |
| **CEFR-J** | 1164 | 1411 | 2446 | 2778 | 1111 | 1025 | 9935 | ✅ | ОСНОВНОЙ |
| ~~**Maximax**~~ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | 248k+ | ❌ | ИСКЛЮЧЕН |
| **Google 10k** | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | 10000 | ❌ | СПРАВОЧНО |
| **COCA 5k** | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | 5000 | ❌ | СПРАВОЧНО |

## ⚠️ **ВАЖНЫЕ ОГРАНИЧЕНИЯ**

### Источники БЕЗ уровней CEFR:
- **Google 10000 English**: Только частотность, без CEFR классификации
- **COCA 5000**: Только частотность и части речи, без CEFR классификации

### 🔍 Детальный анализ поиска CEFR маппингов:
**См. файл `CEFR_MAPPING_STATUS.md`** - подробный отчет о поиске готовых CEFR классификаций для всех источников.

### Рекомендации по использованию:
- **Для создания словарей по уровням**: Используйте Oxford 5000, CEFR-J, Maximax
- **Для частотного анализа**: Используйте Google 10k, COCA 5k как справочные источники
- **Для валидации**: Проверяйте, что выбранные слова есть в частотных списках

## 🔍 **ДОПОЛНИТЕЛЬНЫЕ ИСТОЧНИКИ**

### Cambridge A1 Movers Analysis
- **Файл**: `cambridge_a1_analysis.md`
- **Содержание**: Анализ 35 полезных слов из Cambridge A1 Movers
- **Статус**: Ручной анализ, небольшой объем

### Oxford 3000 Analysis
- **Файл**: `oxford_3000_analysis.md`
- **Содержание**: Анализ Oxford 3000 (подмножество Oxford 5000)
- **Статус**: Дополнительная информация

### Google 10000 English
- **Файл**: `google_10000_english.txt`
- **Содержание**: 10000 наиболее частотных слов по Google Corpus
- **✅ СОРТИРОВКА**: Файл УЖЕ отсортирован по частотности!
  - **Строка 1**: "the" (самое частотное слово)
  - **Строка 2**: "of" (второе по частотности)
  - **Строка 3**: "and" (третье по частотности)
  - **Строка N**: слово ранга N по частотности
- **Статус**: Готов к использованию, без разбивки по уровням

### COCA 5000 Frequency
- **Файл**: `coca_5000_words.txt` (простой список) + `coca_5000_frequency.csv` (детали)
- **Содержание**: 5000 слов с детальной статистикой частотности
- **✅ СОРТИРОВКА**: Файлы УЖЕ отсортированы по частотности!
  - **Строка 1**: "the" (самое частотное слово)
  - **Строка 2**: "be" (второе по частотности)
  - **Строка 3**: "and" (третье по частотности)
  - **Строка N**: слово ранга N по частотности
- **Логика частотности**:
  - **Ранг 1-1000**: Самые частотные слова (кандидаты для A1-A2)
  - **Ранг 1000-3000**: Средняя частотность (кандидаты для B1-B2)
  - **Ранг 3000-5000**: Менее частотные (кандидаты для C1-C2)
- **Колонки**: rank, lemma, PoS, freq, perMil, range, disp + жанры
- **⚠️ ВАЖНО**: НЕТ уровней CEFR - только частотность!
- **Использование**: Приоритизация слов внутри уровней Oxford/CEFR-J
- **Статус**: Готов к использованию, без разбивки по уровням

## 🎯 **РЕКОМЕНДАЦИИ ДЛЯ ИСПОЛЬЗОВАНИЯ**

### Для создания словарей по уровням CEFR:
1. **A1 уровень**: Oxford A1 (1076) + CEFR-J A1 (1164) = 2240 слов
2. **A2-C2 уровни**: Oxford + CEFR-J (полное покрытие)
3. **Расширение**: Maximax (машинная классификация)

### Для частотного анализа и валидации:
1. **Google 10k**: ❌ НЕТ CEFR - только справочно для частотности
2. **COCA 5k**: ❌ НЕТ CEFR - только справочно для академической частотности
   - **Ранг 1-1000**: Приоритет для A1-A2 слов
   - **Ранг 1000-3000**: Приоритет для B1-B2 слов
   - **Ранг 3000-5000**: Приоритет для C1-C2 слов
3. **Проверка**: Убедиться, что выбранные слова есть в частотных списках

### ⚠️ ВНИМАНИЕ:
- **Google 10k и COCA 5k НЕ ИМЕЮТ уровней CEFR**
- **Используйте их только как справочные источники**
- **Для создания словарей используйте Oxford 5000, CEFR-J, Maximax**

## 📁 **СТРУКТУРА ФАЙЛОВ**

```
external_sources/
├── README.md                           # Этот файл
├── CEFR_MAPPING_STATUS.md              # Детальный анализ CEFR маппингов
├── oxford-5000.csv                     # Полный Oxford 5000
├── oxford_5000_by_levels/              # Oxford по уровням
│   ├── oxford_a1.csv
│   ├── oxford_a2.csv
│   ├── oxford_b1.csv
│   ├── oxford_b2.csv
│   └── oxford_c1.csv
├── cefrj_vocabulary_profile.csv        # Полный CEFR-J
├── cefrj_by_levels/                    # CEFR-J по уровням
│   ├── cefrj_a1.csv
│   ├── cefrj_a2.csv
│   ├── cefrj_b1.csv
│   ├── cefrj_b2.csv
│   ├── cefrj_c1.csv
│   └── cefrj_c2.csv
├── octanove-vocabulary-profile-c1c2-1.0.csv  # C1/C2 слова

├── google_10000_english.txt           # Google 10k частотность
├── coca_5000_words.txt                # COCA 5k простой список
├── archive/                           # Архивные файлы
│   └── coca_5000_frequency.csv        # COCA 5k детальная статистика
├── cambridge_a1_analysis.md           # Cambridge анализ
└── oxford_3000_analysis.md            # Oxford 3000 анализ
```

## ✅ **СТАТУС ГОТОВНОСТИ**

- ✅ **Oxford 5000**: Полностью готов, разбит по уровням CEFR
- ✅ **CEFR-J**: Полностью готов, разбит по уровням CEFR
- ❌ **Maximax**: ИСКЛЮЧЕН (нестандартная система уровней)
- ⚠️ **Google 10k**: Скачан, НО БЕЗ уровней CEFR (только справочно)
- ⚠️ **COCA 5k**: Скачан, НО БЕЗ уровней CEFR (только справочно)
- ⚠️ **Cambridge**: Только анализ, нужен полный список
- ✅ **Структура**: Организована и документирована

## 🚀 **СЛЕДУЮЩИЕ ШАГИ**

1. Создать объединенные списки слов для каждого уровня
2. Провести анализ пересечений между источниками
3. Выбрать оптимальные 250 слов для MVP каждого уровня
4. Интегрировать в основную систему словарей

---
*Обновлено: 2025-01-16*
