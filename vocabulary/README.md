# 📚 VOCABULARY - Словарные данные uMemo

Все данные для изучения языков: концепты, переводы, тестирование слов на 37 языках.

## 🚀 БЫСТРЫЙ СТАРТ

**👉 [ГЛАВНАЯ ИНСТРУКЦИЯ: _WORDS_CREATION_GUIDE_WORKFLOW.md](vocabulary/_WORDS_CREATION_GUIDE_WORKFLOW.md)**

Полный workflow создания и тестирования новых слов от А до Я.

## 📁 СТРУКТУРА ПАПОК

```
vocabulary/
├── concepts/           # Концепты слов
│   ├── A0/            # 148 концептов A0 (0001_A0_ultra_core_01_concept.json)
│   ├── A1/            # Будущие концепты A1
│   └── legacy/        # Backup старых файлов концептов
├── md/                # MD файлы тестирования
│   ├── A0/            # MD файлы A0 (0008_A0_ultra_core_08.md)
│   └── A1/            # Будущие MD файлы A1
├── json/              # JSON файлы переводов
│   ├── A0/            # JSON файлы A0 (0008_A0_ultra_core_08.json)
│   ├── A1/            # Будущие JSON файлы A1
│   ├── templates/     # Шаблоны JSON файлов
│   ├── imported/      # Импортированные в БД файлы
│   ├── new/           # Готовые к импорту файлы
│   └── EXAMPLE.md     # 📋 Техническое описание JSON формата
├── word_lists/        # Списки слов
│   ├── A0_word_list.md
│   └── A1_word_list.md (будущие)
├── language_guides/   # Справочники по языкам
│   ├── cb_cebuano_guide.md
│   └── ... (37 языков)
├── _WORDS_CREATION_GUIDE_WORKFLOW.md # 📋 ГЛАВНАЯ ИНСТРУКЦИЯ по созданию слов
└── README.md          # Описание структуры данных
```

## 📊 СТАТИСТИКА

- **Концепты**: 148 файлов A0 (все слова покрыты)
- **MD файлы**: 13 файлов тестирования
- **JSON файлы**: 8 готовых файлов A0
- **Справочники**: 37 языков

## 🔗 СВЯЗЬ ФАЙЛОВ

Каждое слово = 3 файла:
- `md/A0/0010_A0_ultra_core_10.md` - тестирование
- `json/A0/0010_A0_ultra_core_10.json` - переводы на 37 языков
- `concepts/A0/0010_A0_ultra_core_10_concept.json` - концепт

## �� ДОКУМЕНТАЦИЯ

- **[vocabulary/_WORDS_CREATION_GUIDE_WORKFLOW.md](vocabulary/_WORDS_CREATION_GUIDE_WORKFLOW.md)** - главная инструкция
- **[json/EXAMPLE.md](json/EXAMPLE.md)** - техническое описание JSON
- **[word_lists/A0_word_list.md](word_lists/A0_word_list.md)** - список слов
