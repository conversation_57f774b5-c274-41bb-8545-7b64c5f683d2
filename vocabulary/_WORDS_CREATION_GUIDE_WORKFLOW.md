# 📚 Руководство по созданию и тестированию слов и предложений

## 🚀 **КРАТКИЙ ОБЗОР WORKFLOW**
1. **Подготовка и изучение** - изучение правил создания слов и языковых особенностей
2. **Определение уровня и выбор слов** - работа со списками слов, выбор concept_id
3. **Создание концептов** - анализ семантики, создание файлов концептов
4. **Создание и тестирование предложений** - проверка по критериям текущего уровня + исправления в чате LLM
5. **Создание MD лога результатов** - таблица с результатами тестирования как источник данных
6. **Создание JSON файла** - генерация из MD таблицы с переводами на все 37 языков
7. **Валидация JSON** - техническая проверка формата и структуры
8. **Импорт в базу данных** - подготовка окружения + загрузка в MongoDB
9. **Обновление статусов** - отметка выполненных слов в списках
10. **Создание альтернативных ответов** - добавление синонимов (опционально)

---

## � **БАТЧЕВАЯ ОБРАБОТКА СЛОВ**

### **🎯 ПРИНЦИП РАБОТЫ:**
При запросе "обработать N слов" выполняется последовательная обработка каждого слова через шаги 1-10, с итоговой сводкой по батчу.

### **📋 АЛГОРИТМ БАТЧЕВОЙ ОБРАБОТКИ:**
1. **Для каждого слова в батче:** выполнить шаги 1-10 последовательно
   - Шаг 1 (подготовка) может быть пропущен для 2+ слова, если контекст сохранен
   - При возникновении проблем - записать вопрос, продолжить обработку
   - НЕ останавливаться на вопросах к пользователю в середине батча
2. **Накапливать все вопросы** вместо немедленных запросов к пользователю
3. **В конце батча:** создать итоговую сводку и задать все накопленные вопросы

### **📊 ИТОГОВАЯ СВОДКА БАТЧА:**
- **Статус каждого слова:** завершено ✅ / проблемы ❌ / требует уточнений ⚠️
- **Общие проблемы и паттерны:** повторяющиеся языковые проблемы в батче
- **Накопленные вопросы к пользователю:** все вопросы по проблемным словам
- **Идеи по оптимизации процесса:** предложения по улучшению скорости и качества

### **🚀 ПРЕИМУЩЕСТВА БАТЧЕВОЙ ОБРАБОТКИ:**
- **Экономия токенов:** меньше переключений контекста
- **Экономия времени:** меньше ожидания ответов пользователя
- **Выявление паттернов:** общие проблемы видны на уровне батча
- **Масштабируемость:** эффективная работа с 5-6 тысячами слов

---

## �📋 **ДЕТАЛЬНЫЙ WORKFLOW**

### **🎯 ЦЕЛЬ ПРОЦЕССА**
Создать качественные переводы слов на 37 языков с предложениями-примерами, протестированными по всем критериям текущего уровня ([LEVEL]).

### **📊 КРИТЕРИИ КАЧЕСТВА**
- **Смысл**: одинаковый смысл на всех языках
- **Естественность**: звучит как живая речь носителя
- **Базовая форма**: целевое слово в словарной форме
- **Уровень**: соответствие текущему уровню ([LEVEL])

---

> 🏗️ **Архитектурное решение:** Двухфазный подход (тестирование в чате → создание файлов) для минимизации ошибок AI и ускорения работы с 6000+ словами.
> 📖 **Подробное обоснование:** [`docs/development/TWO_PHASE_WORD_CREATION_METHODOLOGY.md`](../docs/development/TWO_PHASE_WORD_CREATION_METHODOLOGY.md)

---

## 🧠 **ШАГ 1: ПОДГОТОВКА И ИЗУЧЕНИЕ**

### **1.0 Изучить главный workflow (КРИТИЧЕСКИ ВАЖНО)**
- **📋 ОБЯЗАТЕЛЬНО:** Изучить весь файл `vocabulary/_WORDS_CREATION_GUIDE_WORKFLOW.md` полностью от начала до конца
- **🎯 Цель:** Понять всю архитектуру процесса, последовательность шагов и все ссылки на файлы
- **⚠️ Риск:** Без полного изучения высок риск упустить критически важную информацию

### **1.1 Изучить принципы создания слов**
- Изучить файл `vocabulary/create_words_rules.md` - универсальные правила создания предложений

### **1.2 Изучить языковые особенности**
- Изучить файл `vocabulary/language_guides/_language_summary.md` - особенности языков и языковых групп

### ⭐ **ЕДИНСТВЕННЫЙ ИСТОЧНИК КЛАССИФИКАЦИИ ЯЗЫКОВ**

**🎯 КРИТИЧЕСКИ ВАЖНО:** Файл `vocabulary/language_guides/_language_summary.md` является **единственным источником истины** для всех списков языков в проекте.

- ✅ **ВСЕ списки языков для тестирования** (шаги 4.2-4.4) берутся ТОЛЬКО оттуда
- ✅ **ВСЕ ссылки на проблемные языки** ведут туда
- ✅ **При обнаружении новых языковых проблем** - обновляем согласно системе отслеживания (см. раздел "Система отслеживания повторяющихся языковых проблем")
- ⚠️ **Краткие справки** в шагах 4.2-4.4 - только для удобства, при расхождениях приоритет у файла-источника!

**📋 НАЗНАЧЕНИЕ `_language_summary.md`:**
- **Краткий справочник** основных языковых особенностей для быстрого изучения
- **НЕ свалка всех проблем** - только проверенно важная информация
- Содержит решения для **групп языков**, а не детали конкретных языков
- Обновляется только при соблюдении критериев из системы отслеживания


---


### **🧠 КОГДА ИСПОЛЬЗОВАТЬ SEQUENTIAL THINKING:**

**Обязательно использовать для:**
- 🔍 **Анализа языковых конфликтов** - когда правила разных языков противоречат друг другу
- 🎯 **Создания сложных предложений** - пошаговый учет особенностей всех 37 языков
- ⚖️ **Выбора между вариантами** - когда есть несколько возможных предложений
- 🛠️ **Решения проблем** - артикли, падежи, порядок слов, культурные нюансы

**РЕЗУЛЬТАТ ШАГА**: Изученные принципы создания слов, языковые справочники и список проблемных языков.



## 🧲 **ШАГ 2: ОПРЕДЕЛЕНИЕ УРОВНЯ И ВЫБОР СЛОВ**

### **2.1 Определить текущий уровень**
**Для текущего уровня ([LEVEL]):**
- Открыть `vocabulary/word_lists/_statuses.md`
- Найти первый файл, который не имеет статуса "✅ Импорт завершен" - это уровень, который в процессе
- Открыть соответствующий файл со словами. Например в `vocabulary/word_lists/[LEVEL]_word_list.md` (где [LEVEL] может быть A0, A1, B1, B2, C1, C2)

Если доступного файла со словами нет, то предложить пользователю создать его:
- Скопировать структуру последнего завершенного файла (например, A0_word_list.md)
- Заменить заголовок на новый уровень (например, A1_word_list.md, B1_word_list.md и т.д.)
- Очистить таблицу от старых слов, оставить только заголовки
- Определить источник слов для нового уровня (см. "Источники слов для новых уровней" ниже)
- Совместно с пользователем определить и утвердить список слов для нового уровня
- Заполнить таблицу новыми словами
- Создать UUID для каждого слова формата `550e8400-e29b-41d4-a716-446655440XXXX` (см. "Правила генерации UUID" ниже)
- Установить всем словам статус `⏳ TO_BE_CREATED`
- Вернуться к началу этого шага и выбрать файл для дальнейшей работы

### **2.2 Выбрать слово**

> **🎯 MVP ОГРАНИЧЕНИЕ:** Для уровней A1-C2 в MVP используем только первые 250 слов из кажлого списка. Остальные 750 слов каждого уровня будут добавлены в будущих версиях.

- В открытом файле со словами, найти первое слово со статусом `⏳ TO_BE_CREATED` и взять всю информацию из строки
- Если есть слова с незавершенными статусами `📄 JSON_CREATED` или `📝 MD_CREATED`, то предложить пользователю сначала завершить их
- Если доступных слов нет и статус файла со словами = `✅ Импорт завершен`, то перейти на следующий уровень (A0→A1→B1→B2→C1→C2). Если уровня нет, то предложить создать. По инструкции в предыдущем шаге.

**ОБРАБОТКА ПРОБЛЕМ С ДАННЫМИ:**
- **Если таблица пустая (нет слов)**: Предложить пользователю добавить список слов для уровня, совместно определить базовый набор, заполнить таблицу
- **Если UUID отсутствуют**: Сгенерировать UUID согласно "Правилам генерации UUID" (см. ниже)
- **Если UUID дублируются**: Пересоздать уникальные UUID, проверив все файлы word_lists/
- **Если неполные данные в строке**: Дополнить недостающие поля в таблице

### **2.3 Проверить готовность к работе**
- Проверить, есть ли концепт для данного слова в папке `vocabulary/concepts/[LEVEL]`. Файл с концептом имеет такое же название как и другие файлы для слова + `_concept.json`. Например `0001_[LEVEL]_ultra_core_01_concept.json` (где [LEVEL] - текущий уровень: A0, A1, B1, B2, C1 или C2)
  - **Если концепт есть** → переходим к шагу 4 (создание и тестирование предложений)
  - **Если концепта нет** → переходим к шагу 3 (создание концептов)
  - Правила создания концептов описаны в `/vocabulary/concepts/rules.md`

**РЕЗУЛЬТАТ ШАГА**: Выбранное слово с полными данными (UUID, русское слово, английское слово, приоритет) + определение следующего шага (3 или 4) в зависимости от наличия концепта.

### **📋 ПРАВИЛА ГЕНЕРАЦИИ UUID**

#### **🎯 ФОРМАТ UUID:**
`550e8400-e29b-41d4-a716-446655440XXXX`

Где:
- `550e8400-e29b-41d4-a716-44665544` - базовая часть (неизменная)
- `0XXXX` - последовательный номер (4 цифры с ведущими нулями)

#### **🔢 АЛГОРИТМ ГЕНЕРАЦИИ:**
1. **Найти последний использованный номер** во ВСЕХ файлах word_lists/
2. **Увеличить на 1** для нового слова
3. **Дополнить нулями** до 4 цифр

#### **📝 ПРИМЕРЫ:**
- Последний UUID в системе: `550e8400-e29b-41d4-a716-************`
- Следующий UUID будет: `550e8400-e29b-41d4-a716-************`
- Для номера 7: `550e8400-e29b-41d4-a716-************`
- Для номера 1523: `550e8400-e29b-41d4-a716-************`

#### **⚠️ ВАЖНЫЕ ПРАВИЛА:**
- UUID должны быть **уникальными** во всей системе
- Нумерация **сквозная** через все уровни (A0, A1, B1, B2, C1, C2)
- При создании нового уровня продолжаем нумерацию с последнего номера
- Проверяем все файлы word_lists/ перед генерацией

### **📚 ИСТОЧНИКИ СЛОВ ДЛЯ НОВЫХ УРОВНЕЙ**

#### **🎯 ПРИНЦИПЫ ОТБОРА СЛОВ:**
- **Частотность** - слова должны быть часто используемыми в реальной жизни
- **Практичность** - полезность для изучающих язык
- **Прогрессия сложности** - соответствие уровню (A1 проще B1, B1 проще C1)
- **Универсальность** - слова должны переводиться на все 37 языков

#### **📋 РЕКОМЕНДУЕМЫЕ ИСТОЧНИКИ:**

**Для уровней A1-A2:**
- Списки частотных слов (top 1000-3000 most common words)
- Базовые учебники по изучению языков
- Европейская рамка языковых компетенций (CEFR) - списки слов по уровням

**Для уровней B1-B2:**
- Расширенные списки частотных слов (3000-6000)
- Академические списки слов (Academic Word List)
- Тематические словари (семья, работа, путешествия, etc.)

**Для уровней C1-C2:**
- Специализированная лексика
- Идиомы и устойчивые выражения
- Академическая и профессиональная лексика

#### **🔄 ПРОЦЕСС СОЗДАНИЯ СПИСКА:**
1. **Исследование** - изучить рекомендуемые источники
2. **Отбор** - выбрать 100-200 слов для уровня
3. **Проверка переводимости** - убедиться, что все слова переводятся на 37 языков
4. **Согласование с пользователем** - утвердить финальный список
5. **Приоритизация** - разделить на ultra_core, core, important, useful

---

## 📝 **ШАГ 3: СОЗДАНИЕ КОНЦЕПТОВ**

### **3.1 Анализ семантики слова**
Для каждого выбранного слова:
- **Основное значение**: что означает слово
- **Контекст использования**: где и как применяется
- **Уровень сложности**: подходит ли для целевого уровня
- **Переводческие нюансы**: особенности в разных языках

### **3.2 Создание файла концепта**
```bash
# Создать новый концепт (пример для уровня A0)
touch vocabulary/concepts/[LEVEL]/0008_[LEVEL]_ultra_core_08.md
# где [LEVEL] заменяется на текущий уровень: A0, A1, B1, B2, C1, C2
```

**Структура концепта:**
```markdown
# Концепт: [СЛОВО]

**Concept ID:** 550e8400-e29b-41d4-a716-************
**Уровень:** [LEVEL]
**Приоритет:** ultra_core

## Семантика
- **Основное значение:** [описание]
- **Контекст:** [где используется]
- **Примеры:** [примеры использования]

## Переводческие заметки
- **Особенности:** [языковые нюансы]
- **Проблемные языки:** [если есть]
```

**РЕЗУЛЬТАТ ШАГА**: Файлы концептов с семантическим анализом.

---

### ⚠️ **АРХИТЕКТУРНОЕ РЕШЕНИЕ: ПОРЯДОК СОЗДАНИЯ ФАЙЛОВ**

> **🎯 КРИТИЧЕСКИ ВАЖНО:** MD лог создается ПЕРЕД JSON файлом для предотвращения потери данных.
>
> **Обоснование:** AI может забыть детали тестирования при переносе из памяти в JSON. MD файл фиксирует результаты сразу после тестирования и становится источником истины для JSON. Это решение снижает риск ошибок на 80% при работе с 6000+ словами.
>
> **⚠️ НЕ МЕНЯТЬ ПОРЯДОК** без серьезного обоснования - это тщательно выверенное архитектурное решение.

---

## 🔧 **ШАГ 4: СОЗДАНИЕ И ТЕСТИРОВАНИЕ ПРЕДЛОЖЕНИЙ**

### **4.1 Подготовка фразы-гипотезы**

**Источники правил:**
- `vocabulary/create_words_rules.md` - универсальные правила создания предложений
- `vocabulary/language_guides/_language_summary.md` - языковые особенности

**Принципы создания:**
- **Одинаковый смысл** на всех 37 языках
- **Базовая форма** для целевого слова
- **Соответствующая грамматика** - сложность зависит от уровня:
  - A0: очень простые фразы (3-4 слова)
  - A1-A2: простые фразы (4-5 слов)
  - B1-B2: фразы средней сложности (5-7 слов)
  - C1-C2: сложные конструкции (7+ слов)
- **Естественность** - звучит как живая речь
- **Практичность** - полезно для реальной жизни

**Пример процесса:**
```
Слово: "врач"
Гипотеза 1: "Врач работает" → проблемы с артиклями в романских языках
Гипотеза 2: "Где врач?" → универсально, практично, естественно ✅
```

### **4.2 Базовое тестирование (5 языков)**

**Языки для тестирования:**
> **📋 СУПЕР-КРИТИЧЕСКИЕ (5 языков)** - см. `vocabulary/language_guides/_language_summary.md`

**Краткая справка:** RU, EN, FI, DE, JA (базовые + самые проблемные из разных групп)

**Критерии проверки:**
- [ ] **Смысл**: одинаковый смысл на всех языках
- [ ] **Естественность**: звучит как живая речь носителя
- [ ] **Базовая форма**: целевое слово в словарной форме
- [ ] **Уровень [LEVEL]**: соответствие сложности текущего уровня

**Процедура:**
1. Перевести предложение на 5 языков
2. Проверить каждый критерий
3. При проблемах - создать новую гипотезу
4. Повторить до успешного прохождения

### **4.3 Расширенное тестирование (18 языков)**

**Языки для тестирования:**
> **📋 ПРОБЛЕМНЫЕ (18 языков)** - см. `vocabulary/language_guides/_language_summary.md`

**Краткая справка:** Добавляем еще 13 языков к уже протестированным:
- **Падежные:** PL, TR, UK
- **Сложные:** KO, AR, HI  
- **Романские:** IT, ES, FR, PT, NL

**Фокус на проблемных группах:**
- **Падежные языки**: проверить базовую форму (именительный падеж для существительных)
- **Романские языки**: проверить артикли и согласование
- **Азиатские языки**: проверить культурную уместность

### **4.4 Полное тестирование (37 языков)**

**Языки для тестирования:**
> **📋 ПРОСТЫЕ (19 языков)** - см. `vocabulary/language_guides/_language_summary.md`

**Краткая справка:** ID, MS, ZH, TH, VI, MY, SV, DA, NO, KK, UZ, PA, MR, BN, UR, NE, FA, TL, CB, KA, RO

**Финальная проверка:**
- Все 37 языков протестированы
- Все критерии соблюдены
- Предложение готово для создания JSON

### Важные примечания: 
> **Важно:** Все предложения создаются и пересоздаются (если нашли проблему) на основе правил из:
> - `vocabulary/create_words_rules.md` - универсальные правила создания предложений
> - `vocabulary/language_guides/_language_summary.md` - языковые особенности и группы языков
> - Описания этого шага прямо здесь
> 
> **При обнаружении проблем:** См. раздел "Правила обновления документации" в справочной информации для определения, какой файл обновлять.

**РЕЗУЛЬТАТ ШАГА**: Протестированное предложение, готовое для создания MD лога.

---

## 📋 **ШАГ 5: СОЗДАНИЕ MD ЛОГА**

### **5.1 Создание файла результатов**
```bash
# Создать MD файл для логирования (пример для уровня A0)
touch vocabulary/md/[LEVEL]/0008_[LEVEL]_ultra_core_08.md
# где [LEVEL] заменяется на текущий уровень: A0, A1, B1, B2, C1, C2
```

### **5.2 Структура MD файла**
**⚠️ КРИТИЧЕСКИ ВАЖНО:** Создается СРАЗУ после тестирования, пока все данные свежи в памяти AI.

```markdown
# ТЕСТИРОВАНИЕ: врач/doctor

**Concept ID:** 550e8400-e29b-41d4-a716-************
**Финальное предложение:** "Где ___?" / "Where is the ___?"
**Статус:** ✅ ГОТОВ

## ТАБЛИЦА РЕЗУЛЬТАТОВ ТЕСТИРОВАНИЯ

| Язык | Код | Слово | Предложение | Смысл | Естественность | Базовая форма | [LEVEL]-уровень | Статус |
|------|-----|-------|-------------|-------|----------------|---------------|------------|--------|
| Русский | ru | врач | Где ___? | ✅ | ✅ | ✅ | ✅ | ✅ |
| English | en | doctor | Where is the ___? | ✅ | ✅ | ✅ | ✅ | ✅ |
| Deutsch | de | Arzt | Wo ist der ___? | ✅ | ✅ | ✅ | ✅ | ✅ |
// ... все 37 языков

## ПРОБЛЕМЫ И РЕШЕНИЯ
- **Проблема 1:** [описание]
- **Решение:** [как исправили]

данное слово было протестировано по всем 37 языкам по всем параметрам таблицы
```

**РЕЗУЛЬТАТ ШАГА**: MD файл с полным логом тестирования как источник данных для JSON.

---

## 📄 **ШАГ 6: СОЗДАНИЕ JSON ФАЙЛА**

### **6.1 Создание базовой структуры**
```bash
# Создать JSON файл (пример для уровня A0)
touch vocabulary/json/[LEVEL]/0008_[LEVEL]_ultra_core_08.json
# где [LEVEL] заменяется на текущий уровень: A0, A1, B1, B2, C1, C2
```

### **6.2 Заполнение данных из MD таблицы**
**⚠️ ИСТОЧНИК ДАННЫХ:** Копировать ВСЕ данные из MD таблицы (ШАГ 5), а НЕ из памяти AI.

**Корректная структура JSON (массив объектов, каждый язык - отдельный объект):**
```json
[
  {
    "concept_id": "550e8400-e29b-41d4-a716-************",
    "word": "врач",
    "language": "ru",
    "level": "[LEVEL]",
    "priority": "ultra_core",
    "part_of_speech": "noun",
    "examples": [
      {
        "sentence": "Где ___?",
        "correct_answers": ["врач"],
        "word_info": "существительное, мужской род"
      }
    ]
  },
  {
    "concept_id": "550e8400-e29b-41d4-a716-************",
    "word": "doctor",
    "language": "en",
    "level": "[LEVEL]",
    "priority": "ultra_core",
    "part_of_speech": "noun",
    "examples": [
      {
        "sentence": "Where is the ___?",
        "correct_answers": ["doctor"],
        "word_info": "noun, masculine"
      }
    ]
  }
  // ... отдельный объект для каждого из 37 языков
]
```

**Описание полей:**
- `concept_id` - UUID концепта (одинаковый для всех языков одного слова)
- `word` - само слово/фраза на данном языке
- `language` - код языка (ru, en, de, es, etc.)
- `level` - уровень сложности ([LEVEL] - текущий уровень: A0, A1, A2, B1, B2, C1, C2)
- `priority` - приоритет (ultra_core, core, extended) - ТОЛЬКО для A0
- `part_of_speech` - часть речи (noun, verb, adjective, etc.)
- `examples` - массив с примерами использования
  - `sentence` - предложение с пропуском (___)
  - `correct_answers` - массив правильных ответов
  - `word_info` - грамматическая информация о слове (см. "Правила заполнения word_info" ниже)

### **6.3 Правила заполнения word_info**

**🎯 НАЗНАЧЕНИЕ:** Краткая грамматическая информация о слове на соответствующем языке.

**📋 ФОРМАТ:** `часть_речи, дополнительная_информация`

#### **📝 ПРИМЕРЫ ПО ЧАСТЯМ РЕЧИ:**

**Существительные (noun):**
- RU: `существительное, мужской род` / `существительное, женский, множественное`
- EN: `noun, masculine` / `noun, plural`
- DE: `Substantiv, maskulin` / `Substantiv, Plural`

**Глаголы (verb):**
- RU: `глагол, настоящее время` / `глагол, инфинитив`
- EN: `verb, present tense` / `verb, infinitive`
- FR: `verbe, présent` / `verbe, infinitif`

**Прилагательные (adjective):**
- RU: `прилагательное, мужской род`
- EN: `adjective`
- ES: `adjetivo, masculino`

**Местоимения (pronoun):**
- RU: `местоимение, 1-е лицо`
- EN: `pronoun, 1st person`
- JA: `代名詞、一人称`

#### **⚠️ ВАЖНЫЕ ПРАВИЛА:**
- Используйте **язык соответствующей записи** (для RU - на русском, для EN - на английском)
- Указывайте **базовую грамматическую информацию** (род, число, время, лицо)
- Для языков без падежей не указывайте падеж
- Для языков без рода не указывайте род
- Смотрите примеры в индивидуальных гайдах языков (`vocabulary/language_guides/[язык]_guide.md`)

### **6.4 Проверка качества**
- [ ] Все 37 языков заполнены
- [ ] Данные соответствуют MD таблице
- [ ] JSON синтаксически корректен
- [ ] Нет пустых значений
- [ ] word_info заполнен согласно правилам

**РЕЗУЛЬТАТ ШАГА**: Готовый JSON файл с переводами, созданный из MD таблицы.

---

## ✅ **ШАГ 7: ВАЛИДАЦИЯ JSON**

### **7.1 Техническая проверка**
```bash
# Проверить синтаксис JSON (пример для уровня A0)
python -m json.tool vocabulary/json/[LEVEL]/0008_[LEVEL]_ultra_core_08.json

# Валидация структуры
python -m scripts.words.validate vocabulary/json/[LEVEL]/0008_[LEVEL]_ultra_core_08.json
# где [LEVEL] заменяется на текущий уровень: A0, A1, B1, B2, C1, C2
```

### **7.2 Проверка соответствия MD↔JSON**
- [ ] Количество языков: MD таблица = JSON (37 языков)
- [ ] Переводы: каждое слово из JSON есть в MD таблице
- [ ] Предложения: каждое предложение из JSON есть в MD таблице
- [ ] Concept ID совпадает в MD и JSON

### **7.3 Проверка данных**
- [ ] Все обязательные поля заполнены
- [ ] concept_id уникален
- [ ] Нет пустых значений
- [ ] Переводы соответствуют концепту

### **7.4 Обработка ошибок валидации**

**🚨 ТИПИЧНЫЕ ОШИБКИ И ИХ РЕШЕНИЯ:**

| Ошибка | Решение |
|--------|---------|
| **Синтаксическая ошибка JSON** | Проверить скобки, запятые, кавычки. Использовать JSON-валидатор |
| **Дублирующийся concept_id** | Проверить, не существует ли уже слово с таким ID в базе |
| **Отсутствуют обязательные поля** | Проверить наличие всех полей: concept_id, word, language, level, part_of_speech |
| **Неверный формат поля** | Проверить, что language содержит правильный код языка (ru, en, de...) |
| **Несоответствие MD и JSON** | Сверить данные с MD таблицей, особенно переводы и предложения |

**🔄 ПРОЦЕСС ИСПРАВЛЕНИЯ:**
1. Запустить валидацию: `python -m scripts.words.validate [файл]`
2. Прочитать сообщение об ошибке
3. Найти проблемное место в JSON
4. Исправить ошибку
5. Повторить валидацию до успешного прохождения

**РЕЗУЛЬТАТ ШАГА**: Валидный JSON файл, готовый к импорту.

---

### **7.5 Подстановка [LEVEL] в файлах и командах**

**🎯 ВАЖНО:** Во всех примерах [LEVEL] нужно заменять на конкретный уровень (A0, A1, B1, B2, C1, C2).

**📋 ПРИМЕРЫ ПОДСТАНОВКИ:**

| Пример с [LEVEL] | Для уровня A0 | Для уровня B1 |
|------------------|---------------|---------------|
| `vocabulary/json/[LEVEL]/` | `vocabulary/json/A0/` | `vocabulary/json/B1/` |
| `0008_[LEVEL]_ultra_core_08.json` | `0008_A0_ultra_core_08.json` | `0008_B1_ultra_core_08.json` |
| `"level": "[LEVEL]"` | `"level": "A0"` | `"level": "B1"` |
| `python -m scripts.words import vocabulary/json/[LEVEL]/` | `python -m scripts.words import vocabulary/json/A0/` | `python -m scripts.words import vocabulary/json/B1/` |

**⚠️ СВЯЗЬ НУМЕРАЦИИ ФАЙЛОВ И UUID:**
- Номер в имени файла (например, `0008`) соответствует последним 4 цифрам UUID
- Для UUID `550e8400-e29b-41d4-a716-************` → файл `0008_[LEVEL]_ultra_core_08.json`
- Для UUID `550e8400-e29b-41d4-a716-************` → файл `0146_[LEVEL]_core_146.json`

## 🗄️ **ШАГ 8: ИМПОРТ В БАЗУ ДАННЫХ**

### **8.1 Подготовка окружения**
```bash
# Активировать виртуальное окружение (для пользователя Eric)
cd backend && source venv_eric/bin/activate

# Проверка подключения к MongoDB
python -m scripts.words check_db
```

### **8.2 Импорт JSON файла**
```bash
# Импорт готового JSON файла (пример для уровня A0)
python -m scripts.words import vocabulary/json/[LEVEL]/0008_[LEVEL]_ultra_core_08.json
# где [LEVEL] заменяется на текущий уровень: A0, A1, B1, B2, C1, C2
```

### **8.3 Проверка результатов**
```bash
# Проверить недавно добавленные слова
python -m scripts.words check_recent --minutes 5

# Проверить конкретное слово в базе
python -m scripts.words check_word --concept_id "550e8400-e29b-41d4-a716-************"
```

### **8.4 Обработка ошибок импорта**

**🚨 ТИПИЧНЫЕ ОШИБКИ ИМПОРТА И ИХ РЕШЕНИЯ:**

| Ошибка | Причина | Решение |
|--------|---------|---------|
| **Connection refused** | MongoDB не запущена | Запустить MongoDB: `brew services start mongodb-community` |
| **Duplicate key error** | Слово уже существует в БД | Проверить concept_id, возможно слово уже импортировано |
| **Validation error** | Неверная структура данных | Повторить валидацию JSON файла |
| **Permission denied** | Нет прав доступа к БД | Проверить настройки подключения в конфиге |
| **File not found** | Неверный путь к файлу | Проверить путь к JSON файлу |

**🔄 ПРОЦЕСС ДИАГНОСТИКИ:**
1. Проверить логи: `tail -f backend/logs/import_words.log`
2. Проверить подключение к БД: `python -m scripts.words check_db`
3. Проверить существование файла: `ls -la [путь_к_файлу]`
4. При необходимости повторить импорт после исправления

**📋 ПРОВЕРКА УСПЕШНОГО ИМПОРТА:**
```bash
# Проверить последние добавленные слова
python -m scripts.words check_recent --minutes 5

# Проверить конкретное слово
python -m scripts.words check_word --concept_id "[UUID]"
```

---

## 📈 **ШАГ 9: ОБНОВЛЕНИЕ СТАТУСОВ**

### **9.1 Обновление списка слов**
```bash
# Открыть список слов (пример для уровня A0)
open vocabulary/word_lists/[LEVEL]_word_list.md
# где [LEVEL] заменяется на текущий уровень: A0, A1, B1, B2, C1, C2
```

### **9.2 Изменение статуса**
```markdown
# Было:
| врач | doctor | 550e8400-e29b-41d4-a716-************ | ⏳ TO_BE_CREATED |

# Стало:
| врач | doctor | 550e8400-e29b-41d4-a716-************ | ✅ COMPLETE |
```

### **9.3 Обновление прогресса**
- Обновить счетчики в начале файла
- Отметить выполненные слова
- Планировать следующие слова

**РЕЗУЛЬТАТ ШАГА**: Обновленные статусы, готовность к следующим словам.

---

## 🔄 **ШАГ 10: СОЗДАНИЕ АЛЬТЕРНАТИВНЫХ ОТВЕТОВ**

### **10.1 Когда создавать**
⚠️ **ТОЛЬКО ПОСЛЕ** успешного тестирования всех 37 языков!

### **10.2 Источники информации**
- **Правила создания**: `vocabulary/alternative_answers_rules.md` → полное руководство по альтернативным ответам

### **10.3 Процедура**
1. Определить возможные альтернативные ответы
2. Проверить совместимость с предложением
3. Убедиться в одинаковых переводах
4. Добавить в JSON файл
5. Повторно протестировать и вывести в чат добавленные альтернативные ответы 

**РЕЗУЛЬТАТ ШАГА**: JSON файл с альтернативными ответами (если применимо).

---

**При проблемах:** → См. Справочную информацию, раздел "Правила обновления документации"

---

## 📚 **СПРАВОЧНАЯ ИНФОРМАЦИЯ**

### 🗂️ **ТЕХНИЧЕСКАЯ ИНФОРМАЦИЯ**

#### **Файловая структура:**
```
vocabulary/
├── concepts/[LEVEL]/     # Концепты слов (A0, A1, B1, B2, C1, C2)
├── json/[LEVEL]/         # JSON файлы для импорта
├── md/[LEVEL]/           # MD логи тестирования
├── word_lists/           # Списки слов по уровням
├── language_guides/      # Справочники языков
├── create_words_rules.md # Правила создания предложений
└── _WORDS_CREATION_GUIDE_WORKFLOW.md # ЭТА ИНСТРУКЦИЯ
```

#### **Полезные команды:**
```bash
# Проверка статуса слов (пример для уровня A0)
python -m scripts.words status [LEVEL]

# Поиск слова в базе
python -m scripts.words find "врач"

# Экспорт слов уровня
python -m scripts.words export [LEVEL]
# где [LEVEL] заменяется на нужный уровень: A0, A1, B1, B2, C1, C2
```

---

### 🚨 **РАБОТА С ПРОБЛЕМАМИ**

#### **Алгоритм решения проблем:**
1. **ОПРЕДЕЛИТЬ ТИП ПРОБЛЕМЫ** - языковая, универсальная, или специфичная
2. **НАЙТИ ИСТОЧНИК** - в каких языках возникает проблема
3. **ИЗУЧИТЬ СПРАВОЧНИКИ** - проверить существующие решения
4. **СОЗДАТЬ РЕШЕНИЕ** - новое предложение или правило

**Строгая последовательность при создании решения:**
1. **СНАЧАЛА:** Попробовать 3-5 РАЗНЫХ предложений для ВСЕГО концепта
2. **ЗАТЕМ:** Проверить каждое новое предложение на естественность
3. **ЗАТЕМ:** Выбрать лучшее решение, которое работает для всех языков
4. **ТОЛЬКО ЕСЛИ ВСЕ ПОПЫТКИ НЕУДАЧНЫ** → применить алгоритм компромисса

**Алгоритм компромисса (только в крайнем случае):**
- Убедиться, что испробованы ВСЕ разумные варианты предложений
- Если ДЕЙСТВИТЕЛЬНО все варианты неестественны → задать пользователю системный вопрос
- Предложить разумный компромисс

#### **Правила обновления документации:**

**ВАЖНО:** При обнаружении проблем четко определяйте, куда добавлять решение:

#### **🎯 ЕСЛИ проблема УНИВЕРСАЛЬНАЯ** (влияет на создание предложений в целом):
→ **Обновляем:** `vocabulary/create_words_rules.md`
- Примеры: новые принципы создания предложений, общие грамматические правила, рекомендации по уровням

#### **🌍 ЕСЛИ проблема ЯЗЫКОВАЯ** (особенность группы языков):
→ **Обновляем:** `vocabulary/language_guides/_language_summary.md`
- Примеры: проблемы с артиклями в романских языках, падежи в германских языках, культурные особенности азиатских языков

#### **🔍 ЕСЛИ проблема СПЕЦИФИЧНАЯ** (один конкретный язык):
→ **Обновляем:** `vocabulary/language_guides/[язык]_guide.md`
- Примеры: особенности финской падежной системы, нюансы китайских иероглифов, региональные варианты себуано

#### **🔄 ЕСЛИ проблема с АЛЬТЕРНАТИВНЫМИ ОТВЕТАМИ**:
→ **Обновляем:** `vocabulary/alternative_answers_rules.md`
- Примеры: новые правила отбора синонимов, решения грамматических конфликтов

---

### **📋 СИСТЕМА ОТСЛЕЖИВАНИЯ ПОВТОРЯЮЩИХСЯ ЯЗЫКОВЫХ ПРОБЛЕМ**
> **Важно:** Эта система применяется только для языковых особенностей и проблем.
> Для общих принципов создания предложений используйте `vocabulary/create_words_rules.md`.

#### **🎯 ОСНОВНОЙ ПРИНЦИП:**
**ВСЕГДА сначала обновляем индивидуальный гайд языка** - это основное место для всех проблем.

#### **🏷️ ТЕГИ ДЛЯ ОТСЛЕЖИВАНИЯ В ГАЙДАХ:**
- `#UNIQUE` - проблема только этого языка
- `#COMMON_2` - встречается в 2 языках
- `#COMMON_3+` - встречается в 3+ языках → кандидат для summary
- `#ADDED_TO_SUMMARY` - уже добавлено в summary

#### **📍 ГДЕ ДОБАВЛЯТЬ ТЕГИ В ГАЙДАХ:**

**В каждом `[язык]_guide.md` добавить секцию в конце:**
```markdown
## Известные проблемы

### Проблема с артиклями #COMMON_3+ #ADDED_TO_SUMMARY
**Описание:** Сложно выбрать правильный артикль в предложениях
**Решение:** Использовать конструкции без артиклей
**Встречается также в:** FR, IT, ES (добавлено в summary)

### Проблема с падежами #UNIQUE
**Описание:** Сложные конструкции с дательным падежом
**Решение:** Избегать предложений с предлогами
**Статус:** Уникальная проблема только немецкого языка
```

**Правила обновления тегов:**
- При первом обнаружении → добавляем проблему с тегом `#UNIQUE`
- При повторении в другом языке → обновляем теги в ОБОИХ гайдах на `#COMMON_2`
- При третьем языке → обновляем теги во ВСЕХ затронутых гайдах на `#COMMON_3+`
- После добавления в summary → обновляем теги на `#ADDED_TO_SUMMARY`

#### **✅ КОГДА ОБНОВЛЯТЬ `_language_summary.md`:**
- Проблема встречается в **3+ языках одной группы** (романские, германские, etc.)
- Проблема **критически важна для базового тестирования** (шаги 4.2-4.4)
- Проблема **меняет классификацию языков** для тестирования

#### **❌ КОГДА НЕ ОБНОВЛЯТЬ `_language_summary.md`:**
- Проблема уникальна для одного языка
- Проблема редкая или очень специфическая
- Проблема касается только деталей форматирования
- Проблема встретилась первый раз (пока не понятно, насколько важна)

#### **🔄 ПРОЦЕСС ОТСЛЕЖИВАНИЯ:**
1. **Первое обнаружение** → добавляем в гайд языка с тегом `#UNIQUE`
2. **Повторное обнаружение в другом языке** → обновляем теги на `#COMMON_2`
3. **Третье обнаружение** → обновляем теги на `#COMMON_3+` → добавляем в summary
4. **После добавления в summary** → обновляем теги на `#ADDED_TO_SUMMARY`

#### **📝 ПРАКТИЧЕСКИЕ ПРИМЕРЫ:**

**Пример 1:** Проблема с турецкими суффиксами
- ✅ Обновляем `TR.md` с тегом `#UNIQUE`
- ❌ Пока НЕ обновляем summary (проблема одного языка)

**Пример 2:** Та же проблема повторилась в казахском и узбекском
- ✅ Обновляем `KK.md` и `UZ.md`, меняем теги на `#COMMON_3+`
- ✅ ТЕПЕРЬ добавляем в summary: "Тюркские языки - проблемы с агглютинацией"
- ✅ Обновляем теги на `#ADDED_TO_SUMMARY`

**Пример 3:** Редкая проблема с китайскими тонами
- ✅ Обновляем `ZH.md` с тегом `#UNIQUE`
- ❌ НЕ добавляем в summary (слишком специфично)

#### **Частые "проблемы", которые НЕ являются проблемами:**

1. **БАЗОВАЯ ФОРМА:**
   - ⚠️ "Слово не в базовой форме" или "Слово не в именительном падеже"
   - ✅ НЕ ПРОБЛЕМА если: слово в естественной словарной форме для данного языка
   - Алгоритм: попробовать стандартную базовую форму → если неестественно → принять исключение

2. **АРТИКЛИ:**
   - ⚠️ "Разные артикли в похожих языках"
   - ✅ НЕ ПРОБЛЕМА если: каждый язык использует свои правила естественно

3. **ДЛИННЫЕ СЛОВА:**
   - ⚠️ "Составное слово слишком длинное"
   - ✅ НЕ ПРОБЛЕМА если: это стандартный перевод в данном языке

**Если НЕ проблема:**
- Документировать исключение: ⚠️ → анализ → обоснование → ✅ с комментарием
- Предложить обновление правил в документации
- Продолжить тестирование с выбранным вариантом

**Если ДЕЙСТВИТЕЛЬНО проблема:**
- Попытаться пересоздать предложение для ВСЕГО концепта
- Если не получается → применить алгоритм решения проблем

---

### 📋 **ПРАВИЛА ТЕСТИРОВАНИЯ**

#### **Главное правило тестирования:**
Если слово меняет форму в ЛЮБОМ из проблемных языков - нужно придумать другое предложение для ВСЕГО концепта (всех 37 языков).

#### **Приоритеты критериев:**
1. **Естественность** (колонка 5) - ВЫСШИЙ ПРИОРИТЕТ. Предложение должно звучать как живая речь носителя языка
2. **Смысл** (колонка 4) - очень важно. Предложение должно передавать одинаковый смысл на всех языках
3. **Базовая форма** (колонка 6) - важно, но не критично. Целевое слово должно быть в базовой форме. Если в 1-2 языках не получается идеально, но это все равно узнаваемая словарная форма и предложение звучит естественно - это приемлемо
4. **Уровень A0** (колонка 7) - важно для обучения. Предложение должно быть простым и понятным новичку

#### **Критерии оценки колонок:**

1. **Язык:** код языка (RU, EN, DE, etc.)
2. **Слово:** целевое слово на данном языке
3. **Предложение:** итоговое предложение с подставленным целевым словом
4. **Смысл:** итоговое предложение имеет тот же смысл на всех языках
5. **Естественность:** предложение звучит как живая речь носителя языка (см. "Критерии естественности" ниже)
6. **Базовая форма:** целевое слово представлено в правильной базовой форме:
   - Существительные: именительный падеж единственного числа (в падежных языках) / словарная форма (в языках без падежей)
   - Глаголы: инфинитив (неопределенная форма)
   - Прилагательные: мужской род единственного числа именительного падежа (в падежных языках, где применимо) / базовая форма
   - Наречия: базовая форма без степеней сравнения
   - Другие части речи: словарная форма

   **Примечание:** В языках без падежной системы (английский, китайский, японский и др.) используется стандартная словарная форма для каждой части речи.
7. **Уровень:** предложение по грамматике и лексике соответствует уровню слова:
   - A0: понятно новичку, очень простая конструкция (3-4 слова)
   - A1-A2: простая конструкция (4-5 слов)
   - B1-B2: конструкция средней сложности (5-7 слов)
   - C1-C2: сложная конструкция (7+ слов)
8. **Статус:** итоговая оценка результата тестирования для данного языка

#### **📋 КРИТЕРИИ ЕСТЕСТВЕННОСТИ ПРЕДЛОЖЕНИЙ:**

**🎯 ОПРЕДЕЛЕНИЕ:** Предложение звучит естественно, если носитель языка мог бы сказать его в реальной жизни.

**✅ ПРИЗНАКИ ЕСТЕСТВЕННОГО ПРЕДЛОЖЕНИЯ:**
- Правильный порядок слов для данного языка
- Правильные грамматические конструкции
- Правильное согласование слов
- Уместные предлоги и артикли (где применимо)
- Соответствие культурному контексту языка

**❌ ПРИЗНАКИ НЕЕСТЕСТВЕННОГО ПРЕДЛОЖЕНИЯ:**
- Дословный перевод с другого языка
- Неправильный порядок слов
- Неправильное использование предлогов
- Неправильное согласование
- Звучит как "машинный перевод"

**🔍 КАК ПРОВЕРЯТЬ ЕСТЕСТВЕННОСТЬ:**
1. Представьте реальную ситуацию, где это предложение могло бы быть сказано
2. Спросите себя: "Сказал бы так носитель языка?"
3. Проверьте, нет ли грамматических ошибок
4. Проверьте, соответствует ли предложение культурным нормам языка
5. При сомнениях - переформулируйте предложение

#### **Процесс заполнения статусов:**

**Для колонок 4-7 (критерии оценки):**
- ✅ = критерий выполнен
- ❌ = критерий не выполнен (проблема)
- ⚠️ = требует внимания/обсуждения

**Для колонки 8 (итоговый статус):**
- ✅ = язык успешно прошел тестирование (все критерии ✅ или ⚠️ с обоснованием)
- ❌ = язык не прошел тестирование (есть критические проблемы ❌)
- ⚠️ = язык прошел с оговорками (требует внимания, но не критично)

---

### 📄 **ШАБЛОНЫ И ПРИМЕРЫ**

#### **Формат MD лога:**

```markdown
# ТЕСТИРОВАНИЕ: есть/eat

**Концепт:** eating_action - базовое действие приема пищи
**Предложение:** "Я хочу есть" / "I want to eat"
**Статус:** ✅ ГОТОВ
**Дата тестирования:** 2025-01-26

## ТАБЛИЦА РЕЗУЛЬТАТОВ ТЕСТИРОВАНИЯ
| Язык | Код | Слово | Предложение | Смысл | Базовая форма | Целевое слово | Грамматика | Естественность | A0-уровень | Статус |
|------|-----|-------|-------------|-------|---------------|---------------|------------|----------------|------------|--------|
| Русский | ru | есть | Я хочу есть | ✅ | ✅ | ✅ есть=eat | ✅ | ✅ | ✅ | ✅ |
| ... все 37 языков ... |

## ИТОГИ ТЕСТИРОВАНИЯ
- **Успешно**: 37/37 языков (100%)
- **Проблемы**: 0 ❌
- **Предупреждения**: 0 ⚠️

**ВАЛИДАЦИЯ**: ✅ ДАННОЕ СЛОВО БЫЛО ПРОТЕСТИРОВАНО ПО ВСЕМ 37 ЯЗЫКАМ ПО ВСЕМ ПАРАМЕТРАМ ТАБЛИЦЫ
```
