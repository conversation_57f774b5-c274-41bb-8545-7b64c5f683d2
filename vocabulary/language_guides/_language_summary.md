
# 🌍 Языковые особенности и группы языков

> **� Часть workflow:** Используется в шагах 4.2-4.4 при тестировании и решении языковых проблем
>
> **� Назначение:** Справочник по особенностям языков и языковых групп для эффективного тестирования

## ⭐ **ЕДИНСТВЕННЫЙ ИСТОЧНИК ИСТИНЫ**

**🎯 ВАЖНО:** Этот файл является **единственным источником классификации языков** во всем проекте.

- ✅ **ВСЕ списки языков для тестирования** берутся ТОЛЬКО отсюда
- ✅ **ВСЕ ссылки на проблемные языки** ведут сюда
- ✅ **При изменении классификации** - обновляем ТОЛЬКО этот файл
- ❌ **НЕ дублируем** списки языков в других файлах

> **📢 Правило:** Если видите список языков в другом файле - замените на ссылку сюда!

## 🎯 **ЗОНА ОТВЕТСТВЕННОСТИ ЭТОГО ФАЙЛА**

**ЭТОТ ФАЙЛ СОДЕРЖИТ:** Специфические языковые особенности
- ✅ Группировка языков по типу проблем
- ✅ Типичные проблемы языковых групп (романские, германские, славянские, etc)
- ✅ Быстрый справочник для тестирования
- ✅ Решения частых языковых проблем
- ✅ Языки, требующие особого внимания

**ЭТОТ ФАЙЛ НЕ СОДЕРЖИТ:** Универсальные правила создания предложений
- ❌ Общие принципы формирования предложений
- ❌ Универсальные грамматические правила
- ❌ Алгоритмы создания предложений
- ❌ Рекомендации по уровням обучения

## 📚 **КУДА ОБРАЩАТЬСЯ ПРИ ПРОБЛЕМАХ**

**ЕСЛИ проблема универсальная** (влияет на создание предложений в целом):
→ Обращаемся к `create_words_rules.md`

**ЕСЛИ проблема языковая** (особенность группы языков):
→ Обновляем ЭТОТ файл (`_language_summary.md`)

**ЕСЛИ проблема очень специфичная** (один конкретный язык):
→ Обновляем индивидуальный справочник `language_guides/[язык]_guide.md`

> **📖 См. также:**
> - `create_words_rules.md` для универсальных правил создания предложений
> - `alternative_answers_rules.md` для правил создания альтернативных ответов

---

## 📊 **МАСТЕР-КЛАССИФИКАЦИЯ ЯЗЫКОВ ДЛЯ ТЕСТИРОВАНИЯ**

> **🎯 Соответствует шагам 4.2-4.4 основного workflow**

### 🔥 **СУПЕР-КРИТИЧЕСКИЕ (5 языков) - ШАГ 4.2**
**Языки:** RU, EN, FI, DE, JA
**Назначение:** Базовое тестирование - быстро отбраковать плохие предложения
**Особенности:**
- RU, EN - базовые языки проекта
- FI - самый сложный (15+ падежей)
- DE - сложные артикли и падежи
- JA - совершенно другая структура
**Когда использовать:** Первичная проверка естественности предложения

### ⚠️ **ПРОБЛЕМНЫЕ (18 языков) - ШАГ 4.3**
**Языки:** RU, EN, FI, DE, JA + PL, TR, UK, KO, AR, HI, IT, ES, FR, PT, NL
**Назначение:** Расширенное тестирование по основным критериям
**Группировка:**
- **Падежные:** PL, TR, UK (+ уже протестированные FI, DE, RU)
- **Сложные:** KO, AR, HI (+ уже протестированный JA)
- **Романские:** IT, ES, FR, PT, NL
**Когда использовать:** После успешного базового тестирования

### ✅ **ПРОСТЫЕ (19 языков) - ШАГ 4.4**
**Языки:** ID, MS, ZH, TH, VI, MY, SV, DA, NO, KK, UZ, PA, MR, BN, UR, NE, FA, TL, CB, KA, RO
**Назначение:** Полное тестирование - финальная проверка
**Особенности:** Менее проблемные, но требуют внимания к деталям
**Когда использовать:** Финальная проверка всех 37 языков

---

## � **КОНКРЕТНЫЕ ПРАВИЛА ПО ЯЗЫКОВЫМ ГРУППАМ**

### **ПРАВИЛА ПО АРТИКЛЯМ:**
- **❌ ИЗБЕГАТЬ**: немецкие артикли (der/die/das) - сложная система + падежи
- **✅ ДОПУСТИМЫ**: романские артикли (fr: le/la, it: il/la, es: el/la, pt: o/a) - простые системы
- **✅ ДОПУСТИМЫ**: арабский артикль ال - единый простой артикль
- **✅ БЕЗОПАСНО**: азиатские языки (zh, ja, ko, th, vi) - артиклей нет

### **ПРАВИЛА ПО ПАДЕЖАМ:**
- **❌ ОЧЕНЬ СЛОЖНО**: финский (fi) - 15+ падежей, избегать сложных конструкций
- **⚠️ ОСТОРОЖНО**: немецкий (de) - 4 падежа, проверять именительный
- **⚠️ ОСТОРОЖНО**: славянские (ru, pl, uk) - 6-7 падежей, целевое слово в именительном
- **✅ БЕЗОПАСНО**: романские, английский - падежей нет или минимум

### **ПРАВИЛА ПО ПОРЯДКУ СЛОВ:**
- **⚠️ ПРОВЕРЯТЬ**: германские (de, nl) - глагол может уходить в конец
- **⚠️ ПРОВЕРЯТЬ**: японский (ja), корейский (ko) - SOV порядок
- **✅ ПРИВЫЧНО**: романские, славянские - SVO как в русском/английском

### **КУЛЬТУРНЫЕ ОСОБЕННОСТИ:**
- **⚠️ ОСТОРОЖНО**: арабский (ar) - религиозные контексты, формальность
- **⚠️ ОСТОРОЖНО**: азиатские (ja, ko, zh) - уровни вежливости, иерархия
- **✅ НЕЙТРАЛЬНО**: европейские языки - меньше культурных ограничений

---

## 🏗️ **ДОПОЛНИТЕЛЬНЫЕ ГРУППИРОВКИ ПО ЯЗЫКОВЫМ СЕМЬЯМ**

> **🎯 Для понимания типа проблем, не для тестирования**

### **РОМАНСКИЕ (FR, ES, IT, PT, RO)**
- **Проблемы:** артикли, род, согласование
- **Совет:** избегать сложных артиклей, проверять согласование

### **ГЕРМАНСКИЕ (DE, NL, SV, DA, NO)**
- **Проблемы:** падежи (DE), порядок слов, составные слова
- **Совет:** особая осторожность с немецким, проверять порядок слов

### **СЛАВЯНСКИЕ (RU, PL, UK)**
- **Проблемы:** падежи, аспекты глаголов
- **Совет:** целевое слово строго в именительном падеже

### **АЗИАТСКИЕ (ZH, JA, KO, TH, VI, MY, HI, etc)**
- **Проблемы:** иероглифы, тоны, культурные контексты
- **Совет:** проверять культурную уместность, формальность

---

## ⚠️ **СУПЕР-СЛОЖНЫЕ ЯЗЫКИ (особое внимание)**

- **FI (финский)** - 15+ падежей, избегать сложных конструкций
- **AR (арабский)** - культурные контексты, религиозные нюансы
- **ZH (китайский)** - иероглифы, тоны, отсутствие грамматики
- **CB (себуано)** - мало ресурсов, региональные варианты

---

## 🔧 **АЛГОРИТМ РЕШЕНИЯ ЯЗЫКОВЫХ ПРОБЛЕМ**

### **ШАГ 1: ОПРЕДЕЛИТЬ ТИП ПРОБЛЕМЫ**
- Универсальная → `create_words_rules.md`
- Групповая → этот файл
- Специфичная → индивидуальный справочник

### **ШАГ 2: НАЙТИ РЕШЕНИЕ**
1. Проверить соответствующий раздел этого файла
2. Изучить индивидуальный справочник проблемного языка
3. При необходимости использовать внешние ресурсы

### **ШАГ 3: ДОКУМЕНТИРОВАТЬ РЕШЕНИЕ**
- Обновить соответствующий файл
- Добавить пример в раздел "Решения частых проблем"
- Предложить улучшение процесса

---

## 📝 **РЕШЕНИЯ ЧАСТЫХ ПРОБЛЕМ**

*Этот раздел будет пополняться по мере обнаружения и решения проблем*

### **Проблема: Артикли в романских языках**
**Решение:** Использовать предложения без артиклей или с универсальными конструкциями

### **Проблема: Падежи в германских языках**
**Решение:** Избегать конструкций, требующих сложных падежей

### **Проблема: Культурная неуместность в азиатских языках**
**Решение:** Проверять культурные контексты, использовать нейтральные формулировки

---

## 📋 **КАНДИДАТЫ ДЛЯ ДОБАВЛЕНИЯ В SUMMARY**

*Проблемы, которые встречаются в нескольких языках, но пока не добавлены в основные разделы*

> **🔄 Как использовать этот раздел:**
> 1. Добавляйте сюда проблемы с тегом `#COMMON_3+`
> 2. После добавления в основные разделы - удаляйте отсюда
> 3. Используйте для отслеживания повторяющихся проблем

*Пока пусто - будет заполняться по мере обнаружения повторяющихся проблем*