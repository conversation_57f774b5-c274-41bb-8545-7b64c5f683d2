{"concept_id": "[CONCEPT_ID]", "word_ru": "[RU_WORD]", "word_en": "[EN_WORD]", "priority": "[PRIORITY]", "level": "A0", "part_of_speech": "[PART_OF_SPEECH]", "concept_description": "[CONCEPT_DESCRIPTION]", "testing_progress": {"status": "not_started", "completed_languages": 0, "total_languages": 37, "passed_languages": 0, "failed_languages": [], "last_updated": "[DATE]", "testing_phrase": "[RU_SENTENCE] / [EN_SENTENCE]"}, "examples": {"ru": {"word": "[RU_WORD]", "sentence": "[RU_SENTENCE_WITH___]", "correct_answers": ["[RU_WORD]"], "word_info": "[RU_WORD_INFO]", "testing": {"meaning": "pending", "base_form": "pending", "target_word": "pending", "grammar": "pending", "naturalness": "pending", "a0_level": "pending", "status": "pending", "notes": ""}}, "en": {"word": "[EN_WORD]", "sentence": "[EN_SENTENCE_WITH___]", "correct_answers": ["[EN_WORD]"], "word_info": "[EN_WORD_INFO]", "testing": {"meaning": "pending", "base_form": "pending", "target_word": "pending", "grammar": "pending", "naturalness": "pending", "a0_level": "pending", "status": "pending", "notes": ""}}, "de": {"word": "[DE_WORD]", "sentence": "[DE_SENTENCE_WITH___]", "correct_answers": ["[DE_WORD]"], "word_info": "[DE_WORD_INFO]", "testing": {"meaning": "pending", "base_form": "pending", "target_word": "pending", "grammar": "pending", "naturalness": "pending", "a0_level": "pending", "status": "pending", "notes": ""}}, "fr": {"word": "[FR_WORD]", "sentence": "[FR_SENTENCE_WITH___]", "correct_answers": ["[FR_WORD]"], "word_info": "[FR_WORD_INFO]", "testing": {"meaning": "pending", "base_form": "pending", "target_word": "pending", "grammar": "pending", "naturalness": "pending", "a0_level": "pending", "status": "pending", "notes": ""}}, "es": {"word": "[ES_WORD]", "sentence": "[ES_SENTENCE_WITH___]", "correct_answers": ["[ES_WORD]"], "word_info": "[ES_WORD_INFO]", "testing": {"meaning": "pending", "base_form": "pending", "target_word": "pending", "grammar": "pending", "naturalness": "pending", "a0_level": "pending", "status": "pending", "notes": ""}}, "it": {"word": "[IT_WORD]", "sentence": "[IT_SENTENCE_WITH___]", "correct_answers": ["[IT_WORD]"], "word_info": "[IT_WORD_INFO]", "testing": {"meaning": "pending", "base_form": "pending", "target_word": "pending", "grammar": "pending", "naturalness": "pending", "a0_level": "pending", "status": "pending", "notes": ""}}, "pt": {"word": "[PT_WORD]", "sentence": "[PT_SENTENCE_WITH___]", "correct_answers": ["[PT_WORD]"], "word_info": "[PT_WORD_INFO]", "testing": {"meaning": "pending", "base_form": "pending", "target_word": "pending", "grammar": "pending", "naturalness": "pending", "a0_level": "pending", "status": "pending", "notes": ""}}, "nl": {"word": "[NL_WORD]", "sentence": "[NL_SENTENCE_WITH___]", "correct_answers": ["[NL_WORD]"], "word_info": "[NL_WORD_INFO]", "testing": {"meaning": "pending", "base_form": "pending", "target_word": "pending", "grammar": "pending", "naturalness": "pending", "a0_level": "pending", "status": "pending", "notes": ""}}, "pl": {"word": "[PL_WORD]", "sentence": "[PL_SENTENCE_WITH___]", "correct_answers": ["[PL_WORD]"], "word_info": "[PL_WORD_INFO]", "testing": {"meaning": "pending", "base_form": "pending", "target_word": "pending", "grammar": "pending", "naturalness": "pending", "a0_level": "pending", "status": "pending", "notes": ""}}, "uk": {"word": "[UK_WORD]", "sentence": "[UK_SENTENCE_WITH___]", "correct_answers": ["[UK_WORD]"], "word_info": "[UK_WORD_INFO]", "testing": {"meaning": "pending", "base_form": "pending", "target_word": "pending", "grammar": "pending", "naturalness": "pending", "a0_level": "pending", "status": "pending", "notes": ""}}, "fi": {"word": "[FI_WORD]", "sentence": "[FI_SENTENCE_WITH___]", "correct_answers": ["[FI_WORD]"], "word_info": "[FI_WORD_INFO]", "testing": {"meaning": "pending", "base_form": "pending", "target_word": "pending", "grammar": "pending", "naturalness": "pending", "a0_level": "pending", "status": "pending", "notes": ""}}, "sv": {"word": "[SV_WORD]", "sentence": "[SV_SENTENCE_WITH___]", "correct_answers": ["[SV_WORD]"], "word_info": "[SV_WORD_INFO]", "testing": {"meaning": "pending", "base_form": "pending", "target_word": "pending", "grammar": "pending", "naturalness": "pending", "a0_level": "pending", "status": "pending", "notes": ""}}, "da": {"word": "[DA_WORD]", "sentence": "[DA_SENTENCE_WITH___]", "correct_answers": ["[DA_WORD]"], "word_info": "[DA_WORD_INFO]", "testing": {"meaning": "pending", "base_form": "pending", "target_word": "pending", "grammar": "pending", "naturalness": "pending", "a0_level": "pending", "status": "pending", "notes": ""}}, "no": {"word": "[NO_WORD]", "sentence": "[NO_SENTENCE_WITH___]", "correct_answers": ["[NO_WORD]"], "word_info": "[NO_WORD_INFO]", "testing": {"meaning": "pending", "base_form": "pending", "target_word": "pending", "grammar": "pending", "naturalness": "pending", "a0_level": "pending", "status": "pending", "notes": ""}}, "tr": {"word": "[TR_WORD]", "sentence": "[TR_SENTENCE_WITH___]", "correct_answers": ["[TR_WORD]"], "word_info": "[TR_WORD_INFO]", "testing": {"meaning": "pending", "base_form": "pending", "target_word": "pending", "grammar": "pending", "naturalness": "pending", "a0_level": "pending", "status": "pending", "notes": ""}}, "ar": {"word": "[AR_WORD]", "sentence": "[AR_SENTENCE_WITH___]", "correct_answers": ["[AR_WORD]"], "word_info": "[AR_WORD_INFO]", "testing": {"meaning": "pending", "base_form": "pending", "target_word": "pending", "grammar": "pending", "naturalness": "pending", "a0_level": "pending", "status": "pending", "notes": ""}}}}