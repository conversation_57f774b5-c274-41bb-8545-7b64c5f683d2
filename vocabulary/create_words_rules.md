
# 📝 Правила создания предложений для слов

> **🔗 Часть workflow:** Используется в шагах 4.1-4.4 основного файла `_WORDS_CREATION_GUIDE_WORKFLOW.md`
>
> **📋 Назначение:** Создание качественных предложений-гипотез для тестирования на всех 37 языках

## 🎯 **ЗОНА ОТВЕТСТВЕННОСТИ ЭТОГО ФАЙЛА**

**ЭТОТ ФАЙЛ СОДЕРЖИТ:** Универсальные правила создания предложений
- ✅ Принципы формирования предложений (структура, длина, сложность)
- ✅ Общие грамматические правила (артикли, падежи, времена)
- ✅ Алгоритмы и процедуры создания
- ✅ Рекомендации по уровням обучения

**ЭТОТ ФАЙЛ НЕ СОДЕРЖИТ:** Специфические языковые особенности
- ❌ Проблемы конкретных языков или языковых групп
- ❌ Исключения для отдельных языков
- ❌ Культурные и региональные особенности

## 📚 **КУДА ОБРАЩАТЬСЯ ПРИ ПРОБЛЕМАХ**

**ЕСЛИ проблема универсальная** (влияет на создание предложений в целом):
→ Обновляем ЭТОТ файл (`create_words_rules.md`)

**ЕСЛИ проблема языковая** (особенность группы языков):
→ Обращаемся к `language_guides/_language_summary.md`

**ЕСЛИ проблема очень специфичная** (один конкретный язык):
→ Обращаемся к индивидуальному справочнику в `language_guides/[язык]_guide.md`

> **📖 См. также:**
> - `alternative_answers_rules.md` для правил создания альтернативных ответов (шаг 6)
> - `language_guides/_language_summary.md` для особенностей языков и языковых групп

---

## 📚 **ОГЛАВЛЕНИЕ**

1. [Базовые требования](#базовые-требования)
2. [Правила по артиклям](#правила-по-артиклям)
3. [Формат предложений с пропусками](#формат-предложений-с-пропусками)
4. [Дополнительные правила](#дополнительные-правила)
5. [Принципы создания предложений](#принципы-создания-предложений)
6. [Принцип практичности](#принцип-практичности)
7. [Рекомендации по уровням](#рекомендации-по-уровням)
8. [Процедура верификации](#процедура-верификации-переводов)

---

## **БАЗОВЫЕ ТРЕБОВАНИЯ:**
- **Одинаковый смысл**: предложение должно переводиться на все 37 языков с одинаковым смыслом
- **Базовая форма**: целевое слово ОБЯЗАТЕЛЬНО в базовой (словарной) форме во всех языках
- **Простая грамматика**: подлежащее + сказуемое/прилагательное (максимум 4-5 слов)
- **Естественность**: звучит как реальный пример из жизни, понятно ребенку
- **Разнообразие предложений**: стремитесь к разнообразию конструкций, но в рамках безопасных правил

**Уточнение по базовым формам:**
- **Существительные**: именительный падеж единственного числа (в падежных языках) / словарная форма (в языках без падежей)
- **Глаголы**: инфинитив (неопределенная форма)
- **Прилагательные**: базовая форма (в падежных языках - мужской род единственного числа именительного падежа, где применимо)
- **Наречия**: базовая форма без степеней сравнения
- **Другие части речи**: словарная форма

**ПРАВИЛА ПО АРТИКЛЯМ:**
- **❌ ИЗБЕГАТЬ**: немецкие артикли (der/die/das) - сложная система + падежи
- **✅ ДОПУСТИМЫ**: романские артикли (fr: le/la, it: il/la, es: el/la, pt: o/a) - простые системы
- **✅ ДОПУСТИМЫ**: арабский артикль ال - единый простой артикль

**ФОРМАТ ПРЕДЛОЖЕНИЙ С ПРОПУСКАМИ:**
- **Пропуск `___`**: обязательно в каждом предложении вместо изучаемого слова
- **ЗАГЛАВНЫЕ БУКВЫ**: если слово стоит в начале предложения, оно должно быть с заглавной буквы
  * ❌ НЕПРАВИЛЬНО: `"___ работаю"` → `"я работаю"` (строчная буква в начале)
  * ✅ ПРАВИЛЬНО: `"___ работаю"` → `"Я работаю"` (заглавная буква в начале)

**ЛОГИКА ПРОВЕРКИ ПРЕДЛОЖЕНИЙ:**
- **Структура данных**: `sentence` содержит пропуск `___`, `correct_answers` содержит варианты ответов
- **Для проверки естественности**: подставьте `correct_answers[0]` вместо `___` и проверьте итоговое предложение
- **Пример проверки**:
  * JSON: `{"sentence": "___ работаю", "correct_answers": ["Я"]}`
  * Итоговое предложение для проверки: **"Я работаю"**
  * Проверяйте естественность именно этого итогового предложения на всех языках

**ДОПОЛНИТЕЛЬНЫЕ ПРАВИЛА:**
- **Полные предложения**: ИЗБЕГАТЬ неполных конструкций типа "Я хочу" без объекта. ПРЕДПОЧИТАТЬ полные естественные предложения "Я хочу воду", "Я вижу дом", "Мама работает"
- **Формы НЕ-целевых слов**: Только ЦЕЛЕВОЕ слово (изучаемое) должно быть в базовой форме. ВСЕ ОСТАЛЬНЫЕ слова в предложении МОГУТ использовать любые падежи, времена, формы
- **Составные выражения (2-3 слова)**: ДОПУСТИМЫ, если невозможно выразить одним словом без потери смысла. ВАЖНО: предложение НЕ может состоять только из целевого выражения.

**ПРАВИЛА ДЛЯ СОСТАВНЫХ ВЫРАЖЕНИЙ:**
- **Один пропуск `___`** может содержать несколько слов
- **Предложение должно содержать дополнительный контекст** помимо целевого выражения

**ПРИМЕРЫ:**
- ❌ НЕПРАВИЛЬНО: "___" → "Thank you" (предложение = только целевое выражение)
- ❌ НЕПРАВИЛЬНО: "I want to ___ ___" → "I want to give up" (несколько пропусков для одного выражения)
- ✅ ПРАВИЛЬНО: "___ very much" → "Thank you very much" (один пропуск + контекст)
- ✅ ПРАВИЛЬНО: "I want to ___" → "I want to give up" (один пропуск + контекст)
- **Иерархия подлежащих**: ЖЕЛАТЕЛЬНО использовать подлежащее для естественности, НО только если это НЕ ПРОТИВОРЕЧИТ другим требованиям по составлению слов. Иерархия подлежащих:

  **ПРИОРИТЕТ 1 (максимальный)**: Содержательные подлежащие
  - "Врач", "Мама", "Ребенок", "Дерево", "Собака", "Дом" и т.д.
  - Используем ТОЛЬКО если целевое слово остается в базовой форме. Если не получается, то используем 2-й приоритет.

  **ПРИОРИТЕТ 2 (средний)**: Простые местоимения
  - "Я", "Ты", "Он", "Она", "Это", "Они"
  - Используем если содержательные подлежащие нарушают правила
  - Если тоже нарушают правила, то используем 3-й приоритет

  **ПРИОРИТЕТ 3 (минимальный)**: Без подлежащего
  - Только если даже простые местоимения нарушают правила
  - Стараемся избегать, но допустимо для соблюдения основных требований. Если иначе не добиться простого предложения. Но по возможности попробовать разные варианты предложений, прежде чем использовать предложения без подлежащего.

  **ПРАВИЛО**: Целевое слово ВСЕГДА в базовой форме > естественность подлежащего

  **ПРИМЕРЫ ПРИМЕНЕНИЯ:**
  - ✅ "Врач работает" (приоритет 1) - если "врач" остается в базовой форме
  - ✅ "Я работаю" (приоритет 2) - если "врач работает" нарушает правила в некоторых языках
  - ✅ "Работаю" (приоритет 3) - только если даже "я" создает проблемы

## **ПРИНЦИПЫ СОЗДАНИЯ ПРЕДЛОЖЕНИЙ**
Для каждого слова создать **ОДНО предложение-концепт**:
- **Одинаковый смысл** на всех 37 языках
- **Одна грамматическая конструкция** для всех языков
- **Полные предложения** (не "Я хочу", а "Я хочу есть")
- **Простота** - максимум 4-5 слов
- **ПРАКТИЧНОСТЬ** - предложения должны быть максимально полезными для реальной жизни

### **ПРИНЦИП ПРАКТИЧНОСТИ**
**КРИТИЧЕСКИ ВАЖНО**: Предложения должны моделировать реальные жизненные ситуации, соответствующие уровню изучающего.

**ПРИМЕРЫ ПРАКТИЧНЫХ ПРЕДЛОЖЕНИЙ**:
- **"врач"** → "Где врач?" (поиск медицинской помощи)
- **"вода"** → "Мне нужна вода" (базовая потребность)
- **"помощь"** → "Мне нужна помощь" (просьба о содействии)
- **"спасибо"** → "Спасибо большое" (выражение благодарности)

**ИЗБЕГАТЬ АБСТРАКТНЫХ ПРЕДЛОЖЕНИЙ**:
- ❌ "Врач хороший" (оценочное суждение)
- ❌ "Врач существует" (философское утверждение)
- ✅ "Где врач?" (практическая потребность)

**ВОПРОСЫ ДЛЯ ПРОВЕРКИ ПРАКТИЧНОСТИ**:
1. Может ли изучающий данного уровня использовать это предложение в реальной жизни?
2. Поможет ли это предложение решить типичную задачу для этого уровня?
3. Соответствует ли сложность предложения целевому уровню?

---

## **РЕКОМЕНДАЦИИ ПО УРОВНЯМ**
Составлять предложения из слов и грамматики уровня. Если это A0, то только из A0 слов и элементарной грамматики. Если A1, то только из A0+A1 слов, грамматики и так далее.

### **A0 (Survival Level - 150 слов)**
**Цель:** Базовые потребности и выживание
- **Длина предложений:** 3-5 слов максимум
- **Контексты:** Еда, жилье, транспорт, здоровье, семья, числа
- **Грамматика:** Простейшие конструкции, настоящее время
- **Примеры:**
  - "Где врач?" (поиск помощи)
  - "Мне нужна вода" (базовая потребность)
  - "Сколько это стоит?" (покупки)
- **Избегать:** Абстрактные понятия, сложные времена, условные предложения

### **A1 (Basic Level - 500-800 слов)**
**Цель:** Повседневное общение, расширение контекстов
- **Длина предложений:** 5-7 слов
- **Контексты:** Работа, учеба, хобби, планы, описание людей и мест
- **Грамматика:** Простые времена (прошедшее, будущее), базовые союзы
- **Примеры:**
  - "Врач сказал принимать лекарство" (медицинские инструкции)
  - "Я изучаю английский язык" (личная информация)
  - "Вчера мы ходили в театр" (рассказ о событиях)
- **Можно:** Простые вопросы, описания, планы

### **A2 (Elementary Level - 1000-1500 слов)**
**Цель:** Описание опыта, мнений, планов
- **Длина предложений:** 6-10 слов
- **Контексты:** Путешествия, культура, личный опыт, работа, образование
- **Грамматика:** Сложные времена, условные предложения, модальные глаголы
- **Примеры:**
  - "Если врач разрешит, поеду в отпуск" (условные предложения)
  - "Я думаю, что это хорошая идея" (выражение мнения)
  - "Когда закончу работу, пойду домой" (временные конструкции)
- **Можно:** Выражение мнений, сравнения, планы

### **B1+ (Intermediate+ Levels)**
**Цель:** Сложные коммуникативные задачи
- **Длина предложений:** 8-12+ слов
- **Контексты:** Профессиональные, академические, культурные
- **Грамматика:** Сложные конструкции, идиомы, стилистические варианты
- **Примеры:**
  - "Несмотря на рекомендации врача, он продолжал курить" (сложные связи)
  - "В случае необходимости обратитесь к специалисту" (формальный стиль)
- **Можно:** Абстрактные понятия, культурные контексты, профессиональная лексика

---

## **ПРОЦЕДУРА ВЕРИФИКАЦИИ ПЕРЕВОДОВ**

**ОСНОВНЫЕ ШАГИ:**
1. **Проверить соответствие концепту** - переводы отражают правильное значение
2. **Для сложных языков** - особое внимание, поиск в словарях (см. `_language_summary.md`)
3. **При сомнениях** - пометить ⚠️ ТРЕБУЕТ ПРОВЕРКИ и предложить варианты
4. **Обязательная проверка** translation_notes из концепта

**ПРОЦЕДУРА ДЛЯ СОМНЕНИЙ:**
```
ЕСЛИ не уверен в переводе:
1. Пометить: ⚠️ ТРЕБУЕТ ПРОВЕРКИ
2. Предложить 2-3 варианта перевода
3. Указать источник сомнений
4. Запросить подтверждение у пользователя
```

> **📖 Категоризация языков по сложности:** См. `language_guides/_language_summary.md`

---

## 🔗 **СВЯЗАННЫЕ ФАЙЛЫ И ШАГИ**

- **Шаг 4.1:** Подготовка фразы-гипотезы → используйте этот файл
- **Шаги 4.2-4.4:** Тестирование → см. основной файл `_WORDS_CREATION_GUIDE_WORKFLOW.md`
- **Шаг 6:** Альтернативные ответы → см. `alternative_answers_rules.md`
- **Языковые особенности:** → см. `language_guides/_language_summary.md`