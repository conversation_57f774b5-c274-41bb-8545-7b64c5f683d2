
**БАЗОВЫЕ ТРЕБОВАНИЯ:**
- **Одинаковый смысл**: предложение должно переводиться на все 37 языков с одинаковым смыслом
- **Именительный падеж**: целевое слово ОБЯЗАТЕЛЬНО в именительном падеже во всех языках
- **Простая грамматика**: подлежащее + сказуемое/прилагательное (максимум 4-5 слов)
- **Естественность**: звучит как реальный пример из жизни, понятно ребенку
- **Разнообразие предложений**: стремитесь к разнообразию конструкций, но в рамках безопасных правил

**ПРАВИЛА ПО АРТИКЛЯМ:**
- **❌ ИЗБЕГАТЬ**: немецкие артикли (der/die/das) - сложная система + падежи
- **✅ ДОПУСТИМЫ**: романские артикли (fr: le/la, it: il/la, es: el/la, pt: o/a) - простые системы
- **✅ ДОПУСТИМЫ**: арабский артикль ال - единый простой артикль

**ФОРМАТ ПРЕДЛОЖЕНИЙ С ПРОПУСКАМИ:**
- **Пропуск `___`**: обязательно в каждом предложении вместо изучаемого слова
- **ЗАГЛАВНЫЕ БУКВЫ**: если слово стоит в начале предложения, оно должно быть с заглавной буквы
  * ❌ НЕПРАВИЛЬНО: `"___ работаю"` → `"я работаю"` (строчная буква в начале)
  * ✅ ПРАВИЛЬНО: `"___ работаю"` → `"Я работаю"` (заглавная буква в начале)

**ЛОГИКА ПРОВЕРКИ ПРЕДЛОЖЕНИЙ:**
- **Структура данных**: `sentence` содержит пропуск `___`, `correct_answers` содержит варианты ответов
- **Для проверки естественности**: подставьте `correct_answers[0]` вместо `___` и проверьте итоговое предложение
- **Пример проверки**:
  * JSON: `{"sentence": "___ работаю", "correct_answers": ["Я"]}`
  * Итоговое предложение для проверки: **"Я работаю"**
  * Проверяйте естественность именно этого итогового предложения на всех языках

**ДОПОЛНИТЕЛЬНЫЕ ПРАВИЛА:**
- **Полные предложения**: ИЗБЕГАТЬ неполных конструкций типа "Я хочу" без объекта. ПРЕДПОЧИТАТЬ полные естественные предложения "Я хочу воду", "Я вижу дом", "Мама работает"
- **Падежи НЕ-целевых слов**: Только ЦЕЛЕВОЕ слово (изучаемое) должно быть в именительном падеже. ВСЕ ОСТАЛЬНЫЕ слова в предложении МОГУТ использовать любые падежи, времена, формы
- **Составные выражения (2-3 слова)**: ДОПУСТИМЫ, если невозможно выразить одним словом без потери смысла. ВАЖНО: предложение НЕ может состоять только из целевого выражения.

**ПРАВИЛА ДЛЯ СОСТАВНЫХ ВЫРАЖЕНИЙ:**
- **Один пропуск `___`** может содержать несколько слов
- **Предложение должно содержать дополнительный контекст** помимо целевого выражения

**ПРИМЕРЫ:**
- ❌ НЕПРАВИЛЬНО: "___" → "Thank you" (предложение = только целевое выражение)
- ❌ НЕПРАВИЛЬНО: "I want to ___ ___" → "I want to give up" (несколько пропусков для одного выражения)
- ✅ ПРАВИЛЬНО: "___ very much" → "Thank you very much" (один пропуск + контекст)
- ✅ ПРАВИЛЬНО: "I want to ___" → "I want to give up" (один пропуск + контекст)
- **Иерархия подлежащих**: ЖЕЛАТЕЛЬНО использовать подлежащее для естественности, НО только если это НЕ ПРОТИВОРЕЧИТ другим требованиям по составлению слов. Иерархия подлежащих:

  **ПРИОРИТЕТ 1 (максимальный)**: Содержательные подлежащие
  - "Врач", "Мама", "Ребенок", "Дерево", "Собака", "Дом" и т.д.
  - Используем ТОЛЬКО если целевое слово остается в именительном падеже. Если не поулчается, то используем 2-й приоритет. 

  **ПРИОРИТЕТ 2 (средний)**: Простые местоимения
  - "Я", "Ты", "Он", "Она", "Это", "Они"
  - Используем если содержательные подлежащие нарушают правила
  - Если тоже нарушают правила, то используем 3-й приоритет

  **ПРИОРИТЕТ 3 (минимальный)**: Без подлежащего
  - Только если даже простые местоимения нарушают правила
  - Стараемся избегать, но допустимо для соблюдения основных требований. Если иначе не добиться простого предложения. Но по возможности попробовать разные варианты предложений, прежде чем использовать предложения без подлежащего.

  **ПРАВИЛО**: Целевое слово ВСЕГДА в именительном падеже > естественность подлежащего

  **ПРИМЕРЫ ПРИМЕНЕНИЯ:**
  - ✅ "Врач работает" (приоритет 1) - если "врач" остается в именительном падеже
  - ✅ "Я работаю" (приоритет 2) - если "врач работает" нарушает правила в некоторых языках
  - ✅ "Работаю" (приоритет 3) - только если даже "я" создает проблемы

### **3.5 Принципы создания предложений** 
Для каждого слова создать **ОДНО предложение-концепт**:
- **Одинаковый смысл** на всех 37 языках
- **Одна грамматическая конструкция** для всех языков
- **Полные предложения** (не "Я хочу", а "Я хочу есть")
- **Простота** - максимум 4-5 слов
- **ПРАКТИЧНОСТЬ** - предложения должны быть максимально полезными для реальной жизни

### **3.5.1 ПРИНЦИП ПРАКТИЧНОСТИ ДЛЯ A0** 
**КРИТИЧЕСКИ ВАЖНО**: Поскольку A0 - это 150 самых нужных слов, предложения должны моделировать реальные жизненные ситуации:

**ПРИМЕРЫ ПРАКТИЧНЫХ ПРЕДЛОЖЕНИЙ**:
- **"врач"** → "Где врач?" (человек ищет медицинскую помощь)
- **"вода"** → "Мне нужна вода" (базовая потребность)
- **"помощь"** → "Мне нужна помощь" (просьба о содействии)
- **"спасибо"** → "Спасибо большое" (выражение благодарности)

**ИЗБЕГАТЬ АБСТРАКТНЫХ ПРЕДЛОЖЕНИЙ**:
- ❌ "Врач хороший" (оценочное суждение)
- ❌ "Врач существует" (философское утверждение)
- ✅ "Где врач?" (практическая потребность)

**ВОПРОСЫ ДЛЯ ПРОВЕРКИ ПРАКТИЧНОСТИ**:
1. Может ли изучающий A0 использовать это предложение в реальной жизни?
2. Поможет ли это предложение решить базовую потребность?
3. Является ли это типичной ситуацией для начинающего изучать язык?


**КАТЕГОРИЗАЦИЯ ЯЗЫКОВ ПО УРОВНЮ УВЕРЕННОСТИ:**
- **🟢 Высокая уверенность (6)**: EN, RU, DE, FR, ES, IT
- **🟡 Средняя уверенность (15)**: PT, NL, SV, DA, NO, PL, UK, TR, ZH, JA, KO, ID, MS, RO, FI
- **🔴 Требуют проверки (16)**: AR, HI, TH, VI, MY, KK, UZ, PA, MR, BN, UR, NE, FA, TL, CB, KA

**ПРОЦЕДУРА ВЕРИФИКАЦИИ:**
1. **Проверить соответствие концепту** - переводы отражают правильное значение
2. **Для языков "требуют проверки"** - особое внимание, поиск в словарях
3. **При сомнениях** - пометить ⚠️ ТРЕБУЕТ ПРОВЕРКИ и предложить варианты
4. **Обязательная проверка** translation_notes из концепта

**ПРОЦЕДУРА ДЛЯ СОМНЕНИЙ:**
```
ЕСЛИ не уверен в переводе:
1. Пометить: ⚠️ ТРЕБУЕТ ПРОВЕРКИ
2. Предложить 2-3 варианта перевода
3. Указать источник сомнений
4. Запросить подтверждение у пользователя
```

---

## **АЛЬТЕРНАТИВНЫЕ ОТВЕТЫ (ALTERNATIVE_ANSWERS)**

### **ПРОБЛЕМА: Грамматическая совместимость альтернативных ответов**

#### **Описание проблемы**
Разные синонимы могут требовать разной грамматики в предложении, что создает технические сложности.

**Пример:**
- Предложение: "This is ___"
- Основное слово: "mistake" → нужно "This is **a** mistake"
- Альтернативное: "error" → нужно "This is **an** error"
- **Проблема**: Пользователь вводит только "mistake" или "error", но система не знает, какой артикль подставить

#### **Текущее решение (MVP)**
**ПРИНЦИП**: Избегать альтернативных ответов с грамматическими конфликтами.

**Стратегии:**
1. **Альтернативы только для совместимых слов**:
   ```json
   // ✅ ХОРОШО - нет грамматических конфликтов
   {
     "sentence": "Я хочу ___",
     "correct_answers": ["есть"],
     "alternative_answers": ["кушать"]
   }
   ```

2. **Разные предложения для семантически разных слов**:
   ```json
   // mistake: "I made ___" (без артикля)
   // error: "System shows ___" (без артикля)
   ```

3. **Отказ от альтернатив при грамматических проблемах**:
   ```json
   // ❌ Проблема с артиклями - НЕ создаем альтернативы
   {
     "sentence": "This is ___",
     "correct_answers": ["a mistake"],
     "alternative_answers": []  // Оставляем пустым
   }
   ```

#### **Документирование отказов**
**ОБЯЗАТЕЛЬНО**: При создании альтернативных ответов указывать случаи отказа:
```
АЛЬТЕРНАТИВНЫЕ ОТВЕТЫ:
- Рассмотрены: mistake, error
- Отказ от "error": проблема с артиклями (a mistake vs an error)
- Решение: только основное слово "mistake"
- Будущее решение: программные артикли (см. vocabulary/future_ideas/programmatic_articles.md)
```

#### **Будущее решение: Программные артикли**
**Полное описание**: `vocabulary/future_ideas/programmatic_articles.md`

**Идея**: Система автоматически подставляет правильную грамматику в зависимости от введенного слова.
**Статус**: Отложено до роста пользовательской базы и необходимости улучшения UX.


### **Определение и назначение**

**Альтернативные ответы** - это синонимы и альтернативные формы целевого слова, которые **подходят к конкретному предложению** и которые пользователь может ввести вместо основного варианта.

**КРИТИЧЕСКИ ВАЖНО**: Альтернативы привязаны к конкретному предложению, а не к слову в общем!

**Назначение:**
- **Справедливость**: не наказывать за знание синонимов, подходящих к контексту
- **Естественность**: учесть варианты, которые носитель языка использовал бы в данном предложении
- **Формальность**: учесть ты/вы формы, если предложение допускает обе

### **Когда создавать альтернативные ответы**

⚠️ **КРИТИЧЕСКИ ВАЖНО**: Альтернативные ответы создаются **ТОЛЬКО ПОСЛЕ** успешного тестирования всех 37 языков!

**СТРОГИЕ УСЛОВИЯ:**
- ⚠️ **ТОЛЬКО ПОСЛЕ** успешного тестирования всех 37 языков
- ⚠️ **ТОЛЬКО ПОСЛЕ** получения кодовой фразы валидации
- ⚠️ **НЕ ДО** завершения тестирования

**Последовательность:**
1. ✅ Создать основное предложение с `correct_answers[0]`
2. ✅ Протестировать все 37 языков
3. ✅ Убедиться, что все языки прошли тестирование
4. ✅ **ТОЛЬКО ТОГДА** добавлять варианты в `alternative_answers`

### **Структура JSON**

```json
{
  "sentence": "Я хочу ___",
  "correct_answers": ["есть"],
  "alternative_answers": ["кушать"],
  "word_info": "Глагол, инфинитив"
}
```

**Поля:**
- **correct_answers** - основной правильный ответ (всегда один элемент)
- **alternative_answers** - дополнительные варианты для ЭТОГО предложения

### **Типы альтернативных ответов**

#### **1. Синонимы, подходящие к предложению**
```json
{
  "sentence": "Я хочу ___",
  "correct_answers": ["есть"],
  "alternative_answers": ["кушать"]
}
```
- ✅ "есть" и "кушать" - оба подходят к "Я хочу ___"
- ❌ "жрать" - грубо, не A0 уровень

#### **2. Формальные/неформальные варианты**
```json
{
  "sentence": "___!",
  "correct_answers": ["извини"],
  "alternative_answers": ["извините"]
}
```
- ✅ Оба варианта подходят к восклицанию "___!"

#### **3. Региональные варианты**
```json
{
  "sentence": "___ работает",
  "correct_answers": ["мама"],
  "alternative_answers": ["мать"]
}
```
- ✅ Оба варианта подходят к "___ работает"
- ✅ "мама" и "мать" переводятся одинаково: mother/mère/madre

### **СТРОГИЕ ПРАВИЛА отбора альтернативных ответов**

#### **✅ ВКЛЮЧАТЬ ТОЛЬКО:**
- **ТОЧНЫЕ ПЕРЕВОДЫ**: все варианты должны переводиться ОДИНАКОВО на другие языки
- **РАВНОЗНАЧНЫЕ ВАРИАНТЫ**: все альтернативы имеют точно тот же смысл
- **ОДИНАКОВЫЕ ЗНАЧЕНИЯ**: взаимозаменяемы в данном предложении
- **Формальные варианты**: если предложение допускает и ты-, и вы-формы
- **Естественные синонимы**: то, что носитель языка использовал бы

#### **❌ СТРОГО НЕ ВКЛЮЧАТЬ:**
- **Близкие по смыслу**: помощь ≠ поддержка, содействие
- **Похожие значения**: быстро ≠ скоро, срочно
- **Разные части речи**: прилагательные вместо наречий
- **Разные формы для разных контекстов**: "хорошо/хорошая/хороший"
- **Сленг и жаргон**: "жрать" вместо "есть"

#### **ЗОЛОТОЕ ПРАВИЛО:**
✅ **ТОЛЬКО если можно перевести ОДИНАКОВО**: mistake = error = ошибка
❌ **НЕ включать, если переводы разные**: help ≠ support (помощь ≠ поддержка)

### **Примеры с обоснованием**

#### **✅ ПРАВИЛЬНЫЕ примеры:**

**Пример 1: Еда**
```json
{
  "sentence": "Я хочу ___",
  "correct_answers": ["есть"],
  "alternative_answers": ["кушать"]
}
```
**Обоснование:**
- ✅ Оба переводятся одинаково: eat/manger/comer
- ✅ Оба естественны в "Я хочу ___"
- ❌ "жрать" - грубо, не A0 уровень

**Пример 2: Извинения**
```json
{
  "sentence": "___!",
  "correct_answers": ["извини"],
  "alternative_answers": ["извините", "прости", "простите"]
}
```
**Обоснование:**
- ✅ Все переводятся как sorry/excuse-me/forgive-me
- ✅ Контекст допускает и ты-, и вы-формы
- ✅ "прости/простите" - тоже естественны в восклицании

**Пример 3: Согласие**
```json
{
  "sentence": "___, я понимаю",
  "correct_answers": ["да"],
  "alternative_answers": ["ага"]
}
```
**Обоснование:**
- ✅ Оба означают согласие: yes/oui/sí
- ✅ Оба естественны в данном контексте

**Пример 4: Ошибка (английский)**
```json
{
  "sentence": "This is ___",
  "correct_answers": ["mistake"],
  "alternative_answers": ["error"]
}
```
**Обоснование:**
- ✅ "mistake" и "error" - точные синонимы
- ✅ Оба подходят к "This is ___"
- ✅ Переводятся одинаково: ошибка/erreur/error

### **Исключения и особые случаи**

#### **Когда НЕ создавать допустимые ответы:**
1. **Единственный естественный вариант**: если есть только один правильный перевод
2. **Слишком много вариантов**: если синонимов больше 3-4
3. **Контекст ограничивает**: предложение требует конкретную форму
4. **Неуверенность**: если сомневаетесь в правильности варианта

#### **Спорные случаи:**
- **Диалектные варианты**: включать только общепринятые
- **Заимствования**: только если они стали частью языка
- **Сокращения**: обычно не включать в A0

#### **❌ НЕПРАВИЛЬНЫЕ примеры:**

**Пример 1: Близкие по смыслу**
```json
{
  "sentence": "Мне нужна ___",
  "correct_answers": ["помощь"],
  "alternative_answers": ["поддержка", "содействие"]  // ❌ НЕПРАВИЛЬНО!
}
```
**Почему неправильно:**
- ❌ help ≠ support ≠ assistance (разные переводы)
- ❌ Близкие, но не одинаковые значения

**Пример 2: Похожие значения**
```json
{
  "sentence": "Он идет ___",
  "correct_answers": ["быстро"],
  "alternative_answers": ["скоро", "срочно"]  // ❌ НЕПРАВИЛЬНО!
}
```
**Почему неправильно:**
- ❌ fast ≠ soon ≠ urgently (разные значения)
- ❌ Не взаимозаменяемы в предложении

**Пример 3: Разные формы**
```json
{
  "sentence": "Погода ___",
  "correct_answers": ["хорошая"],
  "alternative_answers": ["хорошо", "хороший"]  // ❌ НЕПРАВИЛЬНО!
}
```
**Почему неправильно:**
- ❌ "хорошо" не подходит к "Погода ___"
- ❌ "хороший" не подходит к "Погода ___"
- ✅ Только "хорошая" грамматически правильна

### **ТЕСТ НА ВЗАИМОЗАМЕНЯЕМОСТЬ**

**Главный тест - подстановка:**
1. Подставьте каждый вариант из `alternative_answers` в предложение
2. Прочитайте получившееся предложение
3. Звучит ли оно естественно?
4. Имеет ли тот же смысл, что и с основным словом?
5. Переводится ли одинаково на другие языки?

**Контрольные вопросы:**
1. ✅ Все варианты созданы ПОСЛЕ успешного тестирования всех языков?
2. ✅ Каждый вариант подходит именно к этому предложению?
3. ✅ Все варианты переводятся одинаково?
4. ✅ Варианты подходят для уровня A0?
5. ✅ Количество вариантов разумно (максимум 2-3)?
6. ✅ При сомнениях - лучше не добавлять?

### **Технические требования**

**Формат в JSON:**
```json
{
  "sentence": "Я хочу ___",
  "correct_answers": ["есть"],
  "alternative_answers": ["кушать"],
  "word_info": "Глагол, инфинитив"
}
```

**Важные правила:**
- `correct_answers` - всегда массив с одним элементом (основной ответ)
- `alternative_answers` - массив с дополнительными вариантами (может быть пустым)
- Все варианты должны подходить к конкретному предложению
- Максимум 2-3 альтернативы на предложение
- Если точных вариантов нет - оставить `alternative_answers` пустым
- Все варианты должны быть естественны для носителей языка

**ПРОВЕРКА КОРРЕКТНОСТИ:**
✅ Альтернативные ответы созданы ПОСЛЕ успешного тестирования всех языков
✅ Все варианты естественны для носителей языка
✅ Варианты соответствуют контексту предложения
✅ Все варианты переводятся одинаково на другие языки

---

Еще можно сделать краткие рекомендации по уровням.