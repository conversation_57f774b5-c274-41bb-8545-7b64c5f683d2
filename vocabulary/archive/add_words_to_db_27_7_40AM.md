# 📚 Руководство по созданию и тестированию слов

## 🚀 **КРАТКИЙ ОБЗОР WORKFLOW**
1. **Определение уровня и выбор слов** - работа со списками слов, создание новых уровней
2. **Подготовка JSON файла** - заполнение базовых данных, анализ семантики3. 
3. **Создание JSON с данными** - создание файла с переводами на все 37 языков
4. **Тестирование и исправление проблем** - проверка по критериям A0 + исправления в чате LLM
5. **Создание MD лога результатов** - таблица с результатами тестирования как архив процесса
6. **Валидация JSON** - техническая проверка формата и структуры
7. **Импорт в базу данных** - подготовка окружения + загрузка в MongoDB
8. **Обновление статусов** - отметка завершенного слова

---

**РОЛИ**
- Ты, LLM, который проходит по этой инструкции и помогает составлять слова. Ты лучший лингвист в мире, носитель каждого языка из списка языков.
- Юзер, пользователь, который будет проверять результаты

Цель: Создать систему добавления новых слов максимально эфективно. Быстро и при этом высокого качества. 


## ПРИНЦИПЫ

## 💪 **ПРИНЦИП НЕПРЕРЫВНОГО УЛУЧШЕНИЯ**

> **🌟 КАЖДАЯ ПРОБЛЕМА СО СЛОВОМ - ЭТО ВОЗМОЖНОСТЬ УЛУЧШИТЬ СИСТЕМУ!**

**НЕ БОЙТЕСЬ ПРОБЛЕМ** - они делают систему сильнее:
- **Каждая проблема** → новое правило в документации
- **Каждое исключение** → улучшение процесса
- **Каждый сложный случай** → опыт для следующих слов

### **🔧 Как проблемы становятся решениями:**
- **Проблема**: "быть/be" не работает в азиатских языках
- **Решение**: Правило семантического соответствия
- **Результат**: Теперь можем работать со сложными концептами!

## ГОТОВНОСТЬ К БОЛЬШИМ ОБЪЕМАМ**

- Будет 5-6 тыс слов. 
- Тратить время на вытачивание одного слова мы не будем
- Все эти шаги детальное описание процесса создания слов. Но в идеале этот процесс проходит очень быстро и в идеале 1 шаг - 1 слово. Просто тогда эти шаги станвятся подшагами, чтобы не сбиться с пути. И быть увереным в результате.
- Просто будь готов быстро добавлять и тестирровать 1 слово за несколько секунд, потом переходить к следующему
- Давай рекомендации как ускорить процесс создания слов.
- И рекомендации как улучшить каество предложений




## 🔍 **ШАГ 1: ИЗУЧЕНИЕ ДОКУМЕНТАЦИИ И ПОДГОТОВКА** 
Тщательно изучить всю инструкцию и запомнить ее полностью. Включая все детали. 
Также изучить все необходимые файлы ниже.

Изучить этот файл полностью

### **3.1  Изучить принципы создания слов**
- Изучить этот файл `create_words_rules.md`

- Используй MCP Context7 Нужно ли? 

### **3.2 Изучение саммари языковых справочников** Особенности языков
`data/words/language_guides/language_summary.md`
- [ ] Задача создать этот файл
  - Положить туда все инфу по артиклям и другим вещам на оснвое этого файла
  - Разбить на язоковые группы
  - Сказать здесь в разделе тестирвоания что если сталкиваемся с проблемой, то добавляем правила либо в `language_summary.md` либо в `create_words_rules.md`. Если язковые нбюансы, то первый. Общие правила, то второй.


### **3.3  **Изучить список проблемных языков**
где он? 



---
**Используй MCP (перед созданием слов):**
1. **📚 Context7** → изучить краткие саммари всех language_guides
2. **📋 Context7** → изучить _WORDS_CREATION_GUIDE_WORKFLOW.md (эту инструкцию)
3. **🧠 Sequential Thinking** → проанализировать потенциальные сложности


**РЕЗУЛЬТАТ ШАГА**: Изученные принципы создания слов, языковые справочники и список проблемных языков. 



## 🧲 **ШАГ 2: ОПРЕДЕЛЕНИЕ УРОВНЯ И ВЫБОР СЛОВ**

### **1.1 Определить текущий уровень**
**Для A0 (текущий уровень):**
- Открыть `vocabulary/word_lists/_statuses.md`
- Найти первый файл, который не имеет статуса "✅ Импорт завершен" - это уровень, который в процессе
- Открыть соответствующий файл со словами. Например в `vocabulary/word_lists/A0_word_list.md`

Если доступного файла со словами нет, то предложить пользователю создать его? 
- Создать файл со словами, на основании последнего файла со статусом "✅ Импорт завершен"
- Утвердить список слов для уровня
- Создать UUID для каждого слова
- Создать концепты для каждого слова уровня. Правила для создания концептов описаны в `/vocabulary/concepts/rules.md`. 
- Заполнить таблицу для всех слов, с колонками №, "Русское", "Английское", "Концепт", "Приоритет", "UUID", "Файлы (.md/.json)". 
  - убедиться что UUID уникальный и не повторяется в других файлах (в том числе и в предыдущих файлах)
  - колонку с файлами можно оставить пустой
  - начальный статус = `⏳ TO_BE_CREATED`
- Вернуться к началу этого шага и выбрать файл для дальнейшей работы

### ** 1.2 Выбрать слово**
- В открытом файле со словами, найти первое слово со статусом `⏳ TO_BE_CREATED` и взять всю информацию из строки
- Если есть слова с незавершенными статусами `📄 JSON_CREATED` или `📝 MD_CREATED`, то предложить пользователю сначала завершить их
- Если доступных слов нет и статус файла со словами = `✅ Импорт завершен`, то перейти на следующий уровень (A1 > A2). Если уровня нет, то предложить создать. По инструкции в предыдущем шаге.
- Проверить, есть ли концепт для данного слова в папке `vocabulary/concepts/[level]`. Файл с концептом имеет такое же название как и другие файлы для слова + `_concept.json`. Например `0001_A0_ultra_core_01_concept.json`
  - Если концепта нет, то предложить создать его. Но обычно концепты создаются пакетно для слов всего уровня, после создания UUID. 
  - Правила создания концептов описаны в `/vocabulary/concepts/rules.md`. 

**РЕЗУЛЬТАТ ШАГА**: Мы имеем слово для дальнейшей работы и краткую информацию по нему. UUID, Русское слово, Английское слово, Концепт?, Приоритет.


## 🧠 **ШАГ 3: ПОДГОТОВКА JSON ФАЙЛА**

### ** 2.1 Создать файл**
- Создать JSON, на основе шаблона `vocabulary/json/example.json`, скопировав файл в папку с уровнем. Например `vocabulary/json/A0/` для A0. 
- Название файла должно соответствовать структуре `[общий_порядковый_номер_слова]_[уровень]_[приоритет_если_есть]_[номер_в_приоритете_или_если_нет_приоритета_уровне].json`. Например `0008_A0_ultra_core_08.json`

### ** 2.2 Заполнить базовые данные**
- Заполнить все данные для 2-х языков (EN и RU)
- Изучить концепт для слова, чтобы понять его смысл и контекст
- Заполнить краткое описание концепта в поле "_comment": "[CONCEPT_SHORT_DESCRIPTION]", на русском языке. Например "eating_action - базовое действие приема пищи, выражение желания есть"


**РЕЗУЛЬТАТ ШАГА**: JSON файл с базовыми данными для 2-х языков (EN и RU) и краткое описание концепта.


## 🧪 **ШАГ 4: ТЕСТИРОВАНИЕ** 

Короче, весь процесс тестирования описан здесь. Но шаг будет огромным. С кучей вложенных шагов и принципами. 

Напомню, ты, LLM-модель. Поэтому берешь данные из json и тестируешь в чате. Выводя таблицу в чат.

- Сначала проблемные языки
- Потом остальные языки
- Если все ок, то следующий шаг
- Если нет, то целый отдельный сложный процесс

**⚠️ КРИТИЧЕСКИ ВАЖНО - ТЕСТИРОВАНИЕ НА ОСНОВЕ JSON:**
- Здесь и во всех тестированиях далее 
- Тестирование всегда только на основе JSON
- Сначала добавляем инфу в JSON, потом ее тестируем. Не наобот.
- Исключение. Поиск рабочего решения, когда нужно быстро перебрать варианты. Но и после нахождения нужного решения мы заполняем JSON, а потом тестируем из него
- Это важно чтобы быть увереным что данные корректны


### ПРИНЦИПЫ ТЕСТИРОВАНИЯ**
**РЕКОМЕНДАЦИЯ**: Использовать MCP Server Sequential Thinking для комплексного анализа проблем и системного подхода к тестированию.
- **Источник данных**: JSON файл с переводами на все 37 языков
- **Процесс**: тестирование в чате LLM + исправление проблем

- ПРИ ОБНАРУЖЕНИИ ПРОБЛЕМЫ (здесь непонятно на каком это этапе - при первичном тесте или при вторичном или при полном? Или на любом этапе?)
  - Наверное при первом тесте не надо. У нас же там легкий тест. Естественно ли звучит

---


### **4.1 Создание фразы на основе концепта**
- На основе инструкций по созданию фразы `create_words_rules.md` 
- ПЕРВЫМ ДЕЛОМ НАМ НУЖНО Создать фразу с целевым словом для всех языков. 
  1. **Понять основное значение** - что означает концепт
  2. **Изучить контексты использования** - в каких ситуациях применяется
  3. **Определить сложность** - есть ли особенности в разных языках
  4. **Найти универсальную конструкцию** - какое предложение будет работать везд




**ОПТИМАЛЬНЫЙ АЛГОРИТМ:**
1. **Создать базовую фразу** для EN и RU, убедиться что она:
   - Соответствует концепту
   - Проста и естественна
   - Содержит целевое слово в правильном контексте

2. **Проверить на критичных языках** (FI, HU, JA, AR):
   - Перевести базовую фразу на эти языки
   - Проверить естественность и соответствие A0
   - Если возникают проблемы - модифицировать базовую фразу для всех языков

3. **Только после успешной проверки** на критичных языках:
   - Распространить на остальные языки
   - Заполнить JSON файл

**ПРЕИМУЩЕСТВА ЭТОГО ПОДХОДА:**
- Экономия времени (не нужно переделывать 37 переводов при обнаружении проблемы)
- Выявление структурных проблем на ранней стадии
- Фокус на самых проблемных языках сразу

### **4.2 Заполнение на 37 языков по естественности**

**РЕКОМЕНДАЦИЯ**: Использовать MCP Server Sequential Thinking для анализа грамматических конструкций и выбора оптимальных предложений.

ЕСЛИ ОК, ТО ПЕРЕХОДИМ К СЛЕДУБЩЕМУ шагу. Если нет, то 

### **4.3 Тестирование на оставшиеся параметры**
По той же схеме. Сначала проблемные. Если они ок, то остальные. 







### **4._ ПРОЦЕДУРА ТЕСТИРОВАНИЯ**

**Правила создания предложений находятся в `create_words_rules.md`**

### **4._ ТЕСТИРОВАНИЕ СЛОВА**

**ДЛЯ ВЫБРАННОГО СЛОВА ВЫПОЛНИТЬ:**

1. **Взять предложение** из соответствующего MD файла
2. **Подставить correct_answers[0]** вместо `___`
3. **Проверить итоговое предложение** по всем критериям A0 (из шага 3.3)
4. **ОБЯЗАТЕЛЬНО заполнить полную таблицу тестирования для ВСЕХ 37 языков** (не сокращать!)
  - (!!!) Это не просто заполнить таблицу, а проверить действительно предложение по каждому языку и каждому параметру в таблице.
5. **При обнаружении "проблемы" - ОБЯЗАТЕЛЬНЫЙ АЛГОРИТМ АНАЛИЗА**:

   **ШАГ 1: АНАЛИЗ** - действительно ли это проблема?
   - Сравнить с правилами A0 из шага 3.3
   - Подумать: создает ли это реальную сложность для A0?
   - Естественно ли звучит на этом языке?

   **ЧАСТЫЕ "ПРОБЛЕМЫ", КОТОРЫЕ НЕ ЯВЛЯЮТСЯ ПРОБЛЕМАМИ:**

   **1. ИМЕНИТЕЛЬНЫЙ ПАДЕЖ:**
   - ⚠️ "Слово не в именительном падеже"
   - ✅ НЕ ПРОБЛЕМА если: слово в естественной базовой форме для данной конструкции
   - Примеры: FI "apua" (партитив с "tarvita"), PL "pomocy" (родительный с "potrzebować")
   - Алгоритм: попробовать именительный → если неестественно → принять исключение

   **2. АРТИКЛИ:**
   - ⚠️ "Разные артикли в похожих языках"
   - ✅ НЕ ПРОБЛЕМА если: каждый язык использует свои правила артиклей естественно
   - Примеры: DE "die Hilfe" vs EN "help" (без артикля)

   **3. ДЛИННЫЕ СЛОВА:**
   - ⚠️ "Составное слово слишком длинное"
   - ✅ НЕ ПРОБЛЕМА если: это стандартный перевод в данном языке
   - Примеры: TH "ความช่วย", MY "အကူအညီ"

   **ШАГ 2А: ЕСЛИ НЕ ПРОБЛЕМА**
   - **Самостоятельно решить**: продолжать с текущим вариантом или слегка адаптировать
   - **Комплексный анализ**: где еще может проявиться похожая "проблема"?
     * Смежные языки (французские артикли → испанские/итальянские)
     * Похожие грамматические конструкции (падежи с определенными глаголами)
     * Другие части речи с похожими особенностями
   - **Документировать исключение**: ⚠️ → анализ → обоснование → ✅ с комментарием
   - **Примеры документирования**:
     * "FI: исключение - партитив естественнее именительного с глаголом 'tarvita'"
     * "DE: артикль 'die' стандартен для женского рода, не проблема"
   - **Предложить обновление правил в документации** (добавить в сводку для подтверждения)
   - **Продолжить тестирование** с выбранным вариантом

   **ШАГ 2Б: ЕСЛИ ДЕЙСТВИТЕЛЬНО ПРОБЛЕМА**
   - **Попытаться самостоятельно пересоздать** предложение для ВСЕГО концепта
   - **Если получилось**: внести изменения и продолжить тестирование
   - **Если не получается**: применить **АЛГОРИТМ РАЗУМНОГО КОМПРОМИССА**:

   **АЛГОРИТМ РЕШЕНИЯ ПРОБЛЕМ (СТРОГАЯ ПОСЛЕДОВАТЕЛЬНОСТЬ):**
   1. **СНАЧАЛА**: Попробовать 3-5 РАЗНЫХ предложений для ВСЕГО концепта
   2. **ЗАТЕМ**: Проверить каждое альтернативное предложение на естественность
   3. **ЗАТЕМ**: Выбрать лучшее решение, которое работает для всех языков
   4. **ТОЛЬКО ЕСЛИ ВСЕ ПОПЫТКИ НЕУДАЧНЫ** → применить алгоритм компромисса
   5. **ПРЕДЛОЖИТЬ УЛУЧШЕНИЕ ДОКУМЕНТАЦИИ** Для предотвращения подобной проблемы в будущем

   **АЛГОРИТМ КОМПРОМИССА (ТОЛЬКО В КРАЙНЕМ СЛУЧАЕ):**
   1. **Убедиться, что испробованы ВСЕ разумные альтернативы**
   2. **Проверить естественность** каждой альтернативы
   3. **Если ДЕЙСТВИТЕЛЬНО ВСЕ альтернативы неестественны** → задать пользователю СИСТЕМНЫЙ ВОПРОС:

      ```
      ВОПРОС: Может ли целевое слово "[слово]" быть в [проблемная форма]
      в [язык], если это единственная естественная форма?
      Или нужно найти другую конструкцию?

      КОНТЕКСТ:
      - Естественная форма: "[естественное предложение]"
      - Альтернативы звучат неестественно: "[список попыток]"
      - Влияет только на [количество] языков из 37
      ```

   4. **Предложить РАЗУМНЫЙ КОМПРОМИСС**: "Рекомендую принять исключение для [язык], так как альтернативы разрушают естественность"

6. **Отметить статус**: ✅ ГОТОВ / ❌ ТРЕБУЕТ ИСПРАВЛЕНИЯ / ⚠️ НУЖЕН ВЫБОР ПОЛЬЗОВАТЕЛЯ





### **4._ ОБЯЗАТЕЛЬНАЯ ТАБЛИЦА ТЕСТИРОВАНИЯ В MARKDOWN**

**КРИТИЧЕСКИ ВАЖНО**: Каждое слово тестируется через заполнение таблицы в Markdown файле.

**ЕДИНАЯ ТАБЛИЦА ТЕСТИРОВАНИЯ (используется и в процессе, и в Markdown логах):**

| Язык | Слово | Предложение | Смысл | Падеж | Целевое слово | Допустимые ответы | Грамматика | Естественность | A0-уровень | Статус |

**КОЛОНКИ ТАБЛИЦЫ:**
1. **Язык** - код языка (RU, EN, DE...)
2. **Слово** - целевое слово на этом языке
3. **Предложение** - итоговое предложение после подстановки слова
4. **Смысл** - итоговое предложение имеет тот же смысл на всех языках (✅/❌/⚠️)
5. **Падеж** - целевое слово в именительном падеже (✅/❌/⚠️)
6. **Целевое слово** - слово соответствует концепту в базовой узнаваемой форме (✅/❌/⚠️)
7. **Допустимые ответы** - синонимы и альтернативные формы (если есть)
8. **Грамматика** - простая конструкция (4-5 слов), правильные артикли/заглавные (✅/❌/⚠️)
9. **Естественность** - предложение звучит как живая речь носителя языка (✅/❌/⚠️)
10. **A0-уровень** - понятно новичку, нет культурных/сложных моментов (✅/❌/⚠️)
11. **Статус** - итоговый статус строки (✅/❌/⚠️)

**ПРОЦЕСС ЗАПОЛНЕНИЯ ТАБЛИЦЫ:**
- ✅ = критерий выполнен
- ❌ = критерий не выполнен (проблема)
- ⚠️ = требует внимания/обсуждения
- Строка считается готовой только при всех ✅ в статусе

**КРИТЕРИЙ "ЦЕЛЕВОЕ СЛОВО" - КРИТИЧЕСКИ ВАЖНО:**
**ПРИМЕРЫ ПРОБЛЕМ:**
- EN: "I am a doctor" - ✅ am (быть) vs RU: "Я врач" - ❌ Нет слова "быть"! 

**СУТЬ ПРОБЛЕМЫ**:
Пользователь изучает слово "be/быть", но в предложении "Я врач" нет пропуска "___" для этого слова.
Что он должен вводить как перевод "be/быть"? "Я" или "Врач"? Это совершенно другие слова. 
приложение построено так, что не все используют предлоэения. Они скорее вспомогательные. Некоторые юхеры используют первеод по словам. Видит "to be" (eng) > вводить "быть" (ru). 

**ПРАВИЛО ПРОВЕРКИ:**
- ✅ **ПРЯМОЕ СООТВЕТСТВИЕ**: Слово в узнаваемой форме концепта (идти→иду, go→go, essen→esse)
- ✅ **СЕМАНТИЧЕСКОЕ СООТВЕТСТВИЕ**: Тот же концепт, другое слово (be→стать в контексте профессии)
- ❌ **ДРУГОЙ КОНЦЕПТ**: Совершенно другое значение (врач вместо быть, делать вместо есть)
- ⚠️ **СПОРНЫЕ СЛУЧАИ**: Требует анализа и обоснования

**ПРИМЕРЫ СООТВЕТСТВИЙ:**
- ✅ **ПРЯМОЕ**: "go/идти": "I go home" (go) ↔ "Я иду домой" (иду)
- ✅ **СЕМАНТИЧЕСКОЕ**: "be/быть": "I want to be doctor" (be) ↔ "我想当医生" (当=стать)
- ❌ **НЕПРАВИЛЬНОЕ**: "be/быть": "I am doctor" (am) ↔ "Я врач" (врач)

**КОГДА ИСПОЛЬЗОВАТЬ СЕМАНТИЧЕСКОЕ СООТВЕТСТВИЕ:**
- Только если прямое соответствие невозможно найти
- Смысл концепта полностью сохраняется
- Естественность языка важнее формального совпадения
- Обязательно документировать обоснование

**КРИТЕРИЙ "ДОПУСТИМЫЕ ОТВЕТЫ" - ОБЯЗАТЕЛЬНЫЙ ЭТАП:**

**КОГДА ЗАПОЛНЯТЬ:**
- ⚠️ **ТОЛЬКО ПОСЛЕ** успешного тестирования всех 37 языков
- ⚠️ **ТОЛЬКО ПОСЛЕ** получения кодовой фразы валидации
- ⚠️ **НЕ ДО** завершения тестирования

**ЧТО ВКЛЮЧАТЬ:**
- **ТОЛЬКО ТОЧНЫЕ ПЕРЕВОДЫ**: понятно = clear, understood, got it (все = "понятно")
- **ТОЛЬКО РАВНОЗНАЧНЫЕ ВАРИАНТЫ**: да = yes, yeah, yep (все = "да")
- **ТОЛЬКО ОДИНАКОВЫЕ ЗНАЧЕНИЯ**: ошибка = mistake, error (оба = "ошибка")

**СТРОГИЕ ПРАВИЛА:**
- ❌ **НЕ ВКЛЮЧАТЬ близкие по смыслу**: помощь ≠ поддержка, содействие
- ❌ **НЕ ВКЛЮЧАТЬ похожие значения**: быстро ≠ скоро, срочно
- ✅ **ТОЛЬКО если можно перевести ОДИНАКОВО**: mistake = error = ошибка
- ✅ **ТОЛЬКО естественные варианты перевода одного слова**
- Максимум 3-5 вариантов на язык
- Если точных вариантов нет - оставить пустым

**ПРОВЕРКА КОРРЕКТНОСТИ:**
✅ Допустимые ответы созданы ПОСЛЕ успешного тестирования всех языков
✅ Все варианты естественны для носителей языка
✅ Варианты соответствуют контексту предложения

**ПРИ ПРОБЛЕМАХ:**
1. Попробовать другие предложения
2. Если во многих языках не соответствует → сменить концепцию
3. Документировать исключения

**ПРАВИЛА ИСКЛЮЧЕНИЙ ДЛЯ ИМЕНИТЕЛЬНОГО ПАДЕЖА:**
1. **СТРОГИЙ КРИТЕРИЙ**: В таблице проверяем именительный падеж (⚠️ если не именительный)
2. **АНАЛИЗ ИСКЛЮЧЕНИЙ**: При ⚠️ обязательно анализировать:
   - Попробовать 2-3 разных предложения с именительным падежом
   - Если ВСЕ варианты неестественны → можно принять исключение
   - Обязательно документировать причину исключения
3. **ОГРАНИЧЕНИЯ**:
   - Основные языки (RU, EN, DE, FR, ES, IT, PT, NL, SV, DA, NO, PL, UK) - максимум 1-2 исключения
   - Всего исключений: максимум 3-5 языков из 37
4. **ПРИНЯТИЕ ИСКЛЮЧЕНИЯ**: ⚠️ → анализ → обоснование → ✅ с комментарием


**КРИТИЧЕСКИ ВАЖНО - MARKDOWN ФАЙЛЫ КАК ЛОГИ ТЕСТИРОВАНИЯ:**
- **Тестирование проводится в чате** по полному процессу из шага 4.2
- **Markdown файл = лог результатов** проведенного тестирования
- **НЕ тестируем В файле** - только сохраняем результаты тестирования
- **Последовательность**: Тест в чате → Анализ проблем → Исправления → Сохранение финального результата в MD

**ДЕТАЛЬНОЕ ОБЪЯСНЕНИЕ КРИТЕРИЕВ:**
**ВАЖНО**: Все критерии применяются к **ИТОГОВОМУ ПРЕДЛОЖЕНИЮ** (после подстановки слова), кроме "Падеж" - он применяется только к целевому слову.

- **Смысл**: итоговое предложение переводится с тем же значением на всех языках
- **Падеж**: ТОЛЬКО целевое слово (изучаемое) в именительном падеже, не склоняется
- **Грамматика**: простая конструкция (4-5 слов), правильные артикли/заглавные буквы
- **Естественность**: итоговое предложение звучит как живая речь носителя языка
- **A0-уровень**: понятно новичку, нет культурных/сложных моментов

**ПРИМЕР ЗАПОЛНЕНИЯ ТАБЛИЦЫ:**

| Язык | Слово | Предложение | Смысл | Падеж | Грамматика | Естественность | A0-уровень | Статус |
|------|-------|-------------|-------|-------|------------|----------------|------------|--------|
| RU   | есть  | Я хочу есть | ✅    | ✅    | ✅         | ✅             | ✅         | ✅     |
| EN   | eat   | I want to eat | ✅  | ✅    | ✅         | ✅             | ✅         | ✅     |
| DE   | essen | Ich will essen | ✅ | ✅    | ✅         | ✅             | ✅         | ✅     |
| FR   | manger| Je veux manger | ✅ | ✅    | ✅         | ✅             | ✅         | ✅     |
| ... | ...   | ...         | ...   | ...   | ...        | ...            | ...        | ...    |
| **ИТОГО: 37 языков** | | | | | | | | |

**ВАЖНО**: Таблица должна содержать ВСЕ 37 языков, особенно проблемные из списка 5.3!

Результаты тестирования выдать в виде таблицы, сохраняя следующую структуру:
- итоги таблицы по слову по всем языкам
- краткая сводка по слову по формату ниже
- итоговая сводка по результатам тестирования

После каждого слова дать краткую сводку по языку. Например:
```
=== СЛОВО X: [русское]/[английское] ===
Концепт: [описание]
Паттерн: [тип паттерна]
Предложение: "[русское предложение]" / "[английское предложение]"

📊 ПОЛНАЯ ТАБЛИЦА ТЕСТИРОВАНИЯ (37 ЯЗЫКОВ):
[полная таблица со всеми языками и критериями]

АНАЛИЗ ПРОБЛЕМ:
✅ Проблем не найдено
ИЛИ
❌ НАЙДЕНЫ ПРОБЛЕМЫ:
- [описание проблем и предлагаемые решения]

СТАТУС: ✅ ГОТОВ / ❌ ТРЕБУЕТ ИСПРАВЛЕНИЯ
```

### **4._ NEW-ПРИОРИТЕТНОЕ ТЕСТИРОВАНИЕ (ОПТИМИЗАЦИЯ)** (раскидать по предыдущим пунктам)
Я думаю можно сделать раздел наверху, описать принцип приоритеного тестирования


> **🚀 НОВАЯ ЛОГИКА**: Сначала тестируем проблемные языки, если они ✅ → остальные скорее всего тоже ✅
> **⏱️ ЭКОНОМИЯ ВРЕМЕНИ**: 60-70% от полного тестирования

#### **🔥 ЭТАП 0: СУПЕР-ДИАГНОСТИКА (4 языка)**

**🎯 ЦЕЛЬ**: Быстро определить сложность слова

**СУПЕР-КРИТИЧНЫЕ ЯЗЫКИ-ИНДИКАТОРЫ (4 языка):**
- **fi** - Финский (15 падежей + агглютинация)
- **hu** - Венгерский (18 падежей + агглютинация)
- **ja** - Японский (частицы меняют смысл)
- **ar** - Арабский (корневая система + падежи)
- **+ любые языки с обнаруженными проблемами**

**⏱️ Время**: 10-15% от полного тестирования

#### **🎯 РАЗВИЛКА ПОСЛЕ СУПЕР-ДИАГНОСТИКИ:**

**ЕСЛИ все супер-критичные ✅:**
→ **СЛОВО "ПРОСТОЕ"** → сразу к ЭТАПУ 2 (полное тестирование)

**ЕСЛИ есть проблемы:**
→ **СЛОВО "СЛОЖНОЕ"** → исправляем → ЭТАП 1 (проверка всех проблемных)

#### **🔥 ЭТАП 1: ПОЛНАЯ ПРОВЕРКА ПРОБЛЕМНЫХ (18 языков)**

**Выполняется ТОЛЬКО для сложных слов после исправления:**

**🔴 ВСЕ КРИТИЧЕСКИ ПРОБЛЕМНЫЕ (18 языков - меняют форму целевого слова):**

> **🎯 КРИТЕРИЙ ПРОБЛЕМНОСТИ**: язык **МЕНЯЕТ ФОРМУ ЦЕЛЕВОГО СЛОВА** в предложениях
> - ❌ "Я вижу собак**у**" (не "собака") - **ПРОБЛЕМА**
> - ✅ "Собака лает" - **НЕ ПРОБЛЕМА**

**Падежные языки (7):**
- **Финский (fi)** - 15 падежей + агглютинация: talo→talossa, äiti→äitini
- **Венгерский (hu)** - 18 падежей + агглютинация: ház→házban, ember→emberek
- **Русский (ru)** - 6 падежей: собака→собаку, дом→дома
- **Польский (pl)** - 7 падежей: dom→domu, mama→mamę
- **Немецкий (de)** - 4 падежа + артикли: der Hund→den Hund
- **Турецкий (tr)** - агглютинация: ev→evimiz, anne→annem
- **Украинский (uk)** - 7 падежей: мама→маму, дім→дому

**Сложные конструкции (6):**
- **Японский (ja)** - частицы は/を/に меняют смысл
- **Корейский (ko)** - агглютинация + вежливость
- **Арабский (ar)** - корневая система + падежи
- **Хинди (hi)** - падежи + постпозиции: लड़का→लड़के को
- **Китайский (zh)** - иероглифы, тональность, порядок слов
- **Тайский (th)** - тональный, без пробелов, сложная письменность

**Романские языки (5) - артикли + род:**
- **Итальянский (it)**, **Испанский (es)**, **Французский (fr)**, **Португальский (pt)**, **Нидерландский (nl)**

#### **🎯 ДЕТАЛЬНАЯ ЛОГИКА ПРИНЯТИЯ РЕШЕНИЙ:**

**ПОСЛЕ ЭТАПА 0 (СУПЕР-ДИАГНОСТИКА):**

**ЕСЛИ все 4 супер-критичных ✅:**
- **→ СЛОВО "ПРОСТОЕ"** - переходим к ЭТАПУ 2A
- **→ ЭТАП 2A**: Тестируем **оставшиеся 33 языка** (37 - 4 уже протестированных)
- **→ Пропускаем ЭТАП 1** (экономия 40% времени)
- **→ Общее тестирование**: 4 + 33 = 37 языков (без дублей)

**ЕСЛИ есть проблемы в супер-критичных:**
- **→ СЛОВО "СЛОЖНОЕ"** - требует особого внимания
- **→ ИСПРАВЛЯЕМ ПРЕДЛОЖЕНИЕ** для всего концепта
- **→ ЭТАП 0.1: ПОВТОРНАЯ СУПЕР-ДИАГНОСТИКА** (те же 4 языка с исправленным предложением)

**ПОСЛЕ ЭТАПА 0.1 (повторная супер-диагностика):**

**ЕСЛИ все 4 супер-критичных ✅ после исправления:**
- **→ ИСПРАВЛЕНИЕ СРАБОТАЛО** - переходим к ЭТАПУ 1
- **→ ЭТАП 1**: Тестируем **остальные 14 проблемных языков** (18 - 4 уже протестированных)
- **→ Если Этап 1 ✅** → ЭТАП 2B (19 оставшихся языков)

**ЕСЛИ все еще есть проблемы в супер-критичных:**
- **→ ИСПРАВЛЕНИЕ НЕ СРАБОТАЛО** - нужно новое исправление
- **→ ПОВТОРЯЕМ ИСПРАВЛЕНИЕ** предложения
- **→ ВОЗВРАЩАЕМСЯ К ЭТАПУ 0.1** (цикл до успеха)

**ПОСЛЕ ЭТАПА 1 (остальные проблемные языки):**

**ЕСЛИ все остальные проблемные языки ✅:**
- **→ ПЕРЕХОДИМ К ЭТАПУ 2B** (тестирование оставшихся языков)
- **→ ЭТАП 2B**: Тестируем **оставшиеся языки** (37 - уже протестированные)

**ЕСЛИ есть проблемы в остальных проблемных языках:**
- **→ 📝 ДОБАВЛЯЕМ** новые проблемные языки в динамический список
- **→ 🔄 ИСПРАВЛЯЕМ** предложение для всего концепта
- **→ ВОЗВРАЩАЕМСЯ К ЭТАПУ 0.1** с расширенным списком проблемных языков

**ПОСЛЕ ЭТАПА 2B (оставшиеся языки):**

**ЕСЛИ все оставшиеся языки ✅:**
- **→ ✅ СЛОВО ГОТОВО** - все 37 языков протестированы

**ЕСЛИ есть проблемы в оставшихся языках:**
- **→ 📝 ДОБАВЛЯЕМ** новые проблемные языки в динамический список
- **→ 🔄 ИСПРАВЛЯЕМ** предложение для всего концепта
- **→ ВОЗВРАЩАЕМСЯ К ЭТАПУ 0.1** с полным списком всех обнаруженных проблемных языков

**ПРИНЦИП**: При любых проблемах на любом этапе → добавляем в список → исправляем → начинаем с Этапа 0.1

#### **🚨 ДИНАМИЧЕСКОЕ УПРАВЛЕНИЕ ПРОБЛЕМНЫМИ ЯЗЫКАМИ:**

**ПРИНЦИП**: Любой язык, показавший проблему, добавляется в приоритетный список

**ДИНАМИЧЕСКИЙ СПИСОК ДЛЯ СУПЕР-ДИАГНОСТИКИ:**
```
БАЗОВЫЕ СУПЕР-КРИТИЧНЫЕ: fi, hu, ja, ar (4 языка)
+
ОБНАРУЖЕННЫЕ ПРОБЛЕМНЫЕ: любые языки с проблемами в процессе тестирования
=
АКТУАЛЬНЫЙ СПИСОК для Этапа 0/0.1
```

**ПРОЦЕДУРА ПРИ ОБНАРУЖЕНИИ ПРОБЛЕМ:**

1. **📝 ЗАПИСАТЬ** все языки с проблемами в динамический список
2. **🔄 ИСПРАВИТЬ** предложение для всего концепта
3. **🎯 ЭТАП 0.1** тестирует: 4 базовых + ВСЕ обнаруженные проблемные
4. **Цикл** до успеха всех языков в динамическом списке

**Пример эволюции списка:**
```
Итерация 1: fi, hu, ja, ar (базовые 4)
Проблемы в: fi, sv → Список: fi, hu, ja, ar, sv (5 языков)
Итерация 2: fi, hu, ja, ar, sv
Проблемы в: sv, de → Список: fi, hu, ja, ar, sv, de (6 языков)
Итерация 3: fi, hu, ja, ar, sv, de ✅ → переходим дальше
```

#### **📋 ЭТАП 2A: ДЛЯ ПРОСТЫХ СЛОВ (33 языка)**

**После успешного прохождения Этапа 0:**

**✅ ОСТАВШИЕСЯ НЕ ПРОБЛЕМНЫЕ ЯЗЫКИ (33 языка):**
- **Простые (3)**: en, id, ms (если не тестировались в Этапе 0)
- **Средние (16)**: vi, my, sv, da, no, kk, uz, pa, mr, bn, ur, ne, fa, tl, cb, ka, ro
- **Проблемные (14)**: ru, pl, uk, de, tr, ko, hi, zh, th, it, es, fr, pt, nl

#### **📋 ЭТАП 2B: ДЛЯ СЛОЖНЫХ СЛОВ (19 языков)**

**После успешного прохождения Этапа 1:**

**✅ НЕ ПРОБЛЕМНЫЕ ЯЗЫКИ (19 языков) - слово НЕ меняет форму:**
- **Простые (3)**: en, id, ms - минимальная морфология
- **Средние (16)**: vi, my, sv, da, no, kk, uz, pa, mr, bn, ur, ne, fa, tl, cb, ka, ro

**🔄 + ПОВТОРНАЯ ПРОВЕРКА ПРОБЛЕМНЫХ (18+ языков):**
- 18 базовых проблемных языков
- + любые языки с обнаруженными проблемами
- Для полной уверенности и заполнения таблицы

### **4._ ИТОГОВАЯ СВОДКА ТЕСТИРОВАНИЯ** (УБРАТЬ ИЛИ ПЕРЕМЕСТИТЬ В ПУНКТ ПРО СОХРАНЕНИЕ ЛОГА В MD ФАЙЛЕ)

После тестирования слова создать **ПОЛНУЮ СВОДКУ**:
**ВАЖНО**: Предложить обновление документации согласно п. 5.5 "Принципы непрерывного улучшения". Обновления добавляются в шаг 3.3 "Правила создания предложений A0 уровня".

Пример сводки:
```
📈 ИТОГИ ТЕСТИРОВАНИЯ:
✅ Готово к импорту: слово прошло все проверки
❌ Требует исправления: найдены проблемы

🔧 ПРОБЛЕМЫ - ЧТО НУЖНО РЕШИТЬ:
  - Слово 3 "хочу/want": падежи в винительном падеже в ru/de/pl
    → ВАРИАНТЫ: 1) "Хочу есть" 2) "Хочу спать" 3) "Хочу играть"
    → РЕКОМЕНДАЦИЯ: вариант 1

  - Слово 7 "большой/big": артикли в немецком
    → ВАРИАНТЫ: 1) "Дом большой" 2) "Большой дом" 3) "Это большой"
    → РЕКОМЕНДАЦИЯ: вариант 1

📋 КРИТЕРИИ ТЕСТИРОВАНИЯ (нужны ли обновления правил в шаге 4.3):
  ❌ КРИТЕРИИ ТЕСТИРОВАНИЯ: обновления НЕ требуются
  ИЛИ
  ⚠️ КРИТЕРИИ ТЕСТИРОВАНИЯ: предлагаю добавить правило "[конкретное правило]" в шаг 4.3
  → См. пункт 5.5 "Принципы непрерывного улучшения" для процедуры обновления

**ВАЖНО**: Предлагать конкретные формулировки правил для добавления в документацию.

### **4.6 КОДОВАЯ ФРАЗА ВАЛИДАЦИИ**
**КРИТИЧЕСКИ ВАЖНО - ОБЯЗАТЕЛЬНАЯ ПРОВЕРКА ГОТОВНОСТИ:**

После успешного тестирования слова по всем 37 языкам и всем критериям, в разделе "РЕЗУЛЬТАТ ТЕСТИРОВАНИЯ" MD файла ОБЯЗАТЕЛЬНО добавить:

```
ВАЛИДАЦИЯ: ✅ ДАННОЕ СЛОВО БЫЛО ПРОТЕСТИРОВАНО ПО ВСЕМ 37 ЯЗЫКАМ ПО ВСЕМ ПАРАМЕТРАМ ТАБЛИЦЫ
```

**УСЛОВИЯ ДОБАВЛЕНИЯ КОДОВОЙ ФРАЗЫ:**
- ✅ Показана таблица тестирования по всем 37 языкам
- ✅ Проверены все критерии из таблицы тестирования (шаг 4.3)
- ✅ Не обнаружено критических проблем
- ✅ Предложение работает естественно на всех языках
- ✅ Пользователь подтвердил готовность слова

**БЕЗ ЭТОЙ ФРАЗЫ СЛОВО СЧИТАЕТСЯ НЕГОТОВЫМ!**

**ДОПОЛНИТЕЛЬНО**: Эту фразу также выводить в краткой сводке по слову в чате для подтверждения.

🎯 ЧТО ПОЛЬЗОВАТЕЛЮ НУЖНО РЕШИТЬ:
1. По слову 3: выбрать вариант (1, 2 или 3) или предложить свой
2. По слову 7: выбрать вариант (1, 2 или 3) или предложить свой
3. Подтвердить обновления правил: да/нет/изменить (если предложены)
4. Подтвердить изменения критериев: да/нет (если нужны)

🎯 СЛЕДУЮЩИЙ ШАГ: Ожидание указаний пользователя по проблемным словам
```

### **4._ Workflow после тестирования**
**КРИТИЧЕСКИ ВАЖНО - ЧЕТКАЯ ПОСЛЕДОВАТЕЛЬНОСТЬ:**

**ПОСЛЕ ТЕСТИРОВАНИЯ JSON ФАЙЛА:**
1. **Исправить проблемы** (если есть) согласно результатам тестирования
2. **Создать MD лог результатов** (шаг 5)
3. **Валидация JSON** (шаг 6)
4. **Импорт в базу данных** (шаг 7)
5. **Обновить статус в списке слов**: `⏳ TO_BE_CREATED` → `✅ COMPLETED`
6. **Перейти к следующему слову** или завершить работу

**ЕДИНИЧНАЯ ОБРАБОТКА:**
- Обрабатываем слова по одному: JSON → тестирование → исправления → импорт → следующее слово
- Это обеспечивает максимальное качество и контроль

**КРИТИЧЕСКИ ВАЖНО**: JSON файл должен содержать переводы на все 37 языков!

### **4._ Принципы непрерывного улучшения**
**Каждая "ошибка" → улучшение системы:**
1. **Анализировать корень проблемы**: почему возникла "ошибка"?
2. **Предлагать улучшение правил**: как предотвратить в будущем?
3. **Думать системно**: какие смежные случаи могут быть?
4. **Спрашивать подтверждение**: "Давайте обновим инструкцию?"
5. **Документировать**: добавлять новые правила в шаг 4.3

**Шаблоны для сводки тестирования:**
- **Если НЕ проблема**: "Особенность [описание] НЕ является проблемой, потому что [объяснение]. Предлагаю добавить в правила шага 4.3: '[новое правило]' - подтвердить?"
- **Если РЕАЛЬНАЯ проблема**: "Найдена проблема: [описание] создает сложность для A0. Варианты решения: 1) [вариант] 2) [вариант] 3) [вариант]. Рекомендация: [номер]"

### **4._ Главное правило тестирования** (перенести в `create_words_rules.md`, но выборочно)
**⚠️ КРИТИЧЕСКИ ВАЖНО**: Если слово меняет форму в ЛЮБОМ из проблемных языков - нужно придумать другое предложение для ВСЕГО концепта (всех 37 языков).

**ИСКЛЮЧЕНИЕ - ПРИНЦИП РАЗУМНОГО КОМПРОМИССА**:
Если ВСЕ альтернативные конструкции звучат неестественно, можно принять исключение для 1-2 языков, сохранив естественность для остальных 35-36 языков.

**ПРИОРИТЕТЫ**:
1. **Естественность** (звучит как живая речь) - ВЫСШИЙ ПРИОРИТЕТ
2. **Именительный падеж** целевого слова - важно, но не критично
3. **Единообразие** всех языков - желательно, но не обязательно

**ФОРМУЛА**: Лучше 1 исключение + 36 естественных предложений, чем 37 неестественных предложений.





## 📥 **ШАГ 5: СОХРАНЕНИЕ РЕЗУЛЬТАТОВ ТЕСТИРОВАНИЯ В MD ФАЙЛЕ** 
После успешного тестирования в чате на предыдущем шаге, результаты сохраняешь в md файле. 

По сути md файл это лог тестирования. C результатами. И имеют похожий вид. Его не надо генерировать заново. Его надо вставить в  md файл. 

Но он имеет требования. Примерно такой вид: 


---


### **5.1 Создание MD файла с результатами тестирования**
После завершения тестирования в JSON создать **MD файл как лог процесса** в папке `vocabulary/md/A0/`:

**ФОРМАТ MD ЛОГА (например, `0008_A0_ultra_core_08_log.md`):**

```markdown
# ТЕСТИРОВАНИЕ: есть/eat

**Концепт:** eating_action - базовое действие приема пищи
**Предложение:** "Я хочу есть" / "I want to eat"
**Статус:** ✅ ГОТОВ
**Дата тестирования:** 2025-01-26

## ТАБЛИЦА РЕЗУЛЬТАТОВ ТЕСТИРОВАНИЯ

| Язык | Код | Слово | Предложение | Смысл | Падеж | Целевое слово | Грамматика | Естественность | A0-уровень | Статус |
|------|-----|-------|-------------|-------|-------|---------------|------------|----------------|------------|--------|
| Русский | ru | есть | Я хочу есть | ✅ | ✅ | ✅ есть=eat | ✅ | ✅ | ✅ | ✅ |
| English | en | eat | I want to eat | ✅ | ✅ | ✅ eat=есть | ✅ | ✅ | ✅ | ✅ |
| Deutsch | de | essen | Ich will essen | ✅ | ✅ | ✅ essen=eat | ✅ | ✅ | ✅ | ✅ |
| ... все 37 языков ... |

## ИТОГИ ТЕСТИРОВАНИЯ
- **Успешно**: 37/37 языков (100%)
- **Проблемы**: 0 ❌
- **Предупреждения**: 0 ⚠️

**ВАЛИДАЦИЯ**: ✅ ДАННОЕ СЛОВО БЫЛО ПРОТЕСТИРОВАНО ПО ВСЕМ 37 ЯЗЫКАМ ПО ВСЕМ ПАРАМЕТРАМ ТАБЛИЦЫ
```

### **5.2 Обновление JSON с результатами**
Обновить `testing_log` в JSON файле:
```json
"testing_log": {
  "status": "completed",
  "progress": "37/37",
  "issues": "",
  "updated": "2025-01-26",
  "validation": "ДАННОЕ СЛОВО БЫЛО ПРОТЕСТИРОВАНО ПО ВСЕМ 37 ЯЗЫКАМ ПО ВСЕМ ПАРАМЕТРАМ ТАБЛИЦЫ"
}
```

---


## ✅ **ШАГ 6: ВАЛИДАЦИЯ JSON**

### **� ТЕХНИЧЕСКАЯ ПРОВЕРКА JSON ФАЙЛА**

**ПРОВЕРКА ГОТОВНОСТИ**: Убедиться что JSON файл корректен и готов для импорта в базу данных.

#### **✅ КРИТЕРИИ ВАЛИДАЦИИ:**
- **Структура JSON**: Корректный синтаксис, все обязательные поля
- **Данные по всем языкам**: 37 языков с переводами
- **Testing_log**: Статус "completed" и валидационная фраза
- **Уникальность concept_id**: Не дублируется с существующими

#### **� КОМАНДЫ ВАЛИДАЦИИ:**
```bash
# Проверка структуры JSON
python scripts/validate_json.py vocabulary/json/A0/0008_A0_ultra_core_08.json

# Проверка готовности к импорту
python scripts/check_ready_for_import.py vocabulary/json/A0/0008_A0_ultra_core_08.json

# 4. Просмотр прогресса
python scripts/json_to_md_table.py vocabulary/json/A0/новое_слово.json --progress
```

#### **📋 ПРИМЕР ВАЛИДАЦИИ:**
```json
{
  "testing_log": {
    "status": "completed",
    "progress": "37/37",
    "validation": "ДАННОЕ СЛОВО БЫЛО ПРОТЕСТИРОВАНО ПО ВСЕМ 37 ЯЗЫКАМ ПО ВСЕМ ПАРАМЕТРАМ ТАБЛИЦЫ"
  }
}







- ✅ Ошибки: минимум (шаблон проверен)

**ОБЯЗАТЕЛЬНО**: Всегда используйте шаблонный подход для создания JSON файлов!

### **6.7 СОСТАВНЫЕ СЛОВА**
**✅ ПОДДЕРЖИВАЮТСЯ**: Фразовые глаголы (`give up`), составные слова (`сдаваться`), разделяемые глаголы (`aufstehen`).
**ПРИНЦИП**: Один пропуск `___` заменяется любым количеством слов из `correct_answers`.
**ПРИМЕР**: `"Never ___"` + `["give up"]` = `"Never give up"`.

### **6.8 КОНТРОЛЬ КАЧЕСТВА ПРИ ПЕРЕНОСЕ**

**ПРОБЛЕМА**: При переносе из MD в JSON может теряться информация (заглавные буквы, пунктуация, смысл).

**РЕШЕНИЕ - ДВОЙНАЯ ПРОВЕРКА:**
```bash
# 1. Создаем JSON из MD
sed -i '' 's/\[RU_WORD\]/есть/g' файл.json

# 2. БЫСТРАЯ ПРОВЕРКА КАЧЕСТВА
./scripts/check_json_quality.sh файл.json

# 3. Если ошибка - исправляем точечно
sed -i '' 's/"Я хочу есть"/"Я хочу ___"/g' файл.json

# 4. Валидация
python -m scripts.words.validate_word файл.json
```

**ПРИНЦИП**: Лучше потратить 2 минуты на проверку, чем переделывать весь файл.

### **6.9 СПЕЦИАЛЬНАЯ ПРОВЕРКА CB (СЕБУАНО)**
**ОБЯЗАТЕЛЬНО** для каждого слова проверить CB фразы по справочнику:
```bash
# Проверка CB фраз
grep -A 5 '"language": "cb"' файл.json

# Сверка с правилами
cat backend/data/languages/cb_cebuano_guide.md
```
**Частые ошибки**: "kong" → "ko", "katulog" → "tulog", буквальный перевод.

---

## 📥 **ШАГ 7: ИМПОРТ В БАЗУ ДАННЫХ**

### **7.1 Подготовка окружения**
```bash
# Активация виртуального окружения (для пользователя Rona)
cd backend && source venv_rona/bin/activate

# Проверка подключения к MongoDB
python -m scripts.words check_db
```

### **7.2 Импорт JSON файла**
```bash
# Импорт готового JSON файла
python -m scripts.words import vocabulary/json/A0/0008_A0_ultra_core_08.json
```

### **7.3 Проверка результатов**
```bash
# Проверить недавно добавленные слова
python -m scripts.words check_recent --minutes 5

# Проверить конкретное слово в базе
python -m scripts.words check_word --concept_id "550e8400-e29b-41d4-a716-446655440008"
```

### **7.4 Возможные проблемы**
- **Дублирование**: Скрипт автоматически проверяет существующие слова
- **Ошибки формата**: Проверьте логи в `backend/logs/import_words.log`
- **Подключение к БД**: Убедитесь что MongoDB доступна

---

## � **ШАГ 8: ОБНОВЛЕНИЕ СТАТУСОВ**

### **8.1 Обновление статуса в списке слов**
После успешного импорта обновить статус слова в соответствующем файле списка слов:

```bash
# Обновить статус в A0_word_list.md
# Изменить: ⏳ TO_BE_CREATED → ✅ COMPLETED
```

### **8.2 Система статусов**
**СТАТУСЫ СЛОВ:**
- `⏳ TO_BE_CREATED` - слово не начато
- `📄 JSON_CREATED` - создан JSON файл, прошел валидацию
- `✅ COMPLETED` - импортировано в базу данных

### **8.3 Процедура обновления**
1. **После создания JSON**: `⏳ TO_BE_CREATED` → `📄 JSON_CREATED`
2. **После успешного импорта**: `📄 JSON_CREATED` → `✅ COMPLETED`
3. **Добавить название концепта** в колонку "Концепт" в списке слов

---


> **📋 ВАЖНО**: Этот файл содержит процедуру и workflow. Техническое описание JSON формата находится в `vocabulary/json/EXAMPLE.md`.





## ** ШАГ 9: ПЕРЕЙТИ К СЛЕДУЮЩЕМУ СЛОВУ, НА ШАГ 2 И ПОВТОРИТЬ ШАГИ 2-9
Повторить цикл 10 раз. Для 10 слов из списка.

Затем вывести итоговое саммари по 10 словам. 

И предложить создать новый тред, и начать заново с первого шага. Опять изучить инструкцию, снова 10 слов. Потому что кол-во токенов 200 000 хватит только на 10-12 слов. 

# РАЗОБРАТЬ ОТДЕЛЬНО


## 🤖 **MCP WORKFLOW: УМНОЕ ИЗУЧЕНИЕ СПРАВОЧНИКОВ** 

Решить куда это вообще поместить? 
- Часть в тестирование
- Точечно в создание слов (хотя это и есть тестирвоание)

### **🎯 СТРАТЕГИЯ ПОЭТАПНОГО ИЗУЧЕНИЯ**

**УГЛУБЛЕННЫЙ УРОВЕНЬ (при обнаружении проблем):**
4. **📚 Context7** → изучить подробные справочники проблемных языков. Нужно ли? Насколкьо я знаю Context7 Работает только с известной популярной документацией, а не с файлами документации в проекте.
5. **🧠 Sequential Thinking** → найти решение с учетом языковых особенностей
6. **✍️ Применение** → обновить переводы на основе найденных решений
7. **📈 Улучшение** → обновить справочники с новыми правилами

### **🔄 ПРИНЦИП НЕПРЕРЫВНОГО УЛУЧШЕНИЯ**

**При столкновении с проблемой:**
1. **Изучить подробный справочник** проблемного языка
2. **Найти решение** используя Sequential Thinking и всю свободу творчества, до тех пор пкоа решение не будет найдено
3. **Обновить справочник** с найденным решением
4. **Добавить в краткое саммари** если правило критично
5. **Документировать** для будущих слов

**Пример workflow:**
```
Проблема: Китайский язык не принимает перевод "врач"
↓
Context7 → изучить language_guides/zh_chinese_guide.md
↓
Sequential Thinking → проанализировать культурные особенности
↓
Решение: Использовать более формальный термин
↓
Обновить справочник с правилом для медицинских терминов
```

### **📋 ЧЕКЛИСТ MCP ИСПОЛЬЗОВАНИЯ**

**Перед созданием каждого слова:**
- [ ] Context7: Изучены краткие саммари language_guides
- [ ] Context7: Изучена инструкция _WORDS_CREATION_GUIDE_WORKFLOW.md
- [ ] Sequential Thinking: Проанализированы потенциальные сложности

**При обнаружении проблем:**
- [ ] Context7: Изучены подробные справочники проблемных языков
- [ ] Sequential Thinking: Найдено решение
- [ ] Справочники обновлены с новыми правилами
- [ ] Краткие саммари дополнены при необходимости



