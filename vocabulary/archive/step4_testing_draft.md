# 🧪 **ШАГ 4: ПОЭТАПНОЕ ТЕСТИРОВАНИЕ ПО ЯЗЫКАМ**

## Задачи:
- [x] Сократить таблицу тестирования. Слишком много параметров. Их можно сократить.
- [x] Изменить порядок начиная с 7 шага
- [ ] Навести порядок в доп файлах-инструкциях по созданию слов
   - [ ] `_language_summary.md` 
   - [ ] `create_words_rules.md`
      - критерии базовой формы
         - именительный падеж для существительных 
         - ед. число для существительных
         - исходная форма для глаголов
         - исходная форма для прилагательных
         - исходная форма для наречий
         - исходная форма для других частей речи
      - критерии естественности
         - звучит как живая речь
         - понятно новичку
- [ ] Разобрать ⏭️ ПОСЛЕ ТЕСТИРОВАНИЯ
- [ ] Sequential thinking для тестирвоания гипотезы предложений. И соответственно на любой стадии при обнаружении проблем с предложениями
- [ ] Вернуть в основной файл

---
## Важные примечания: 
> Важно: Все предложения создаются и пересоздаются (если нашли проблему) на основе правил из `vocabulary/create_words_rules.md` и саммари язковых справочников из `vocabulary/language_guides/_language_summary.md`. А также описания этого шага прямо здесь. Обязательно изучить полностью все эти файлы. Они небольшие. Но крайне важные. Без них создание слов невозможно.
- Важно: Реально тестировать каждое слово по каждому языку, по каждому необходимому параметру (в зависимости от шага). А не просто ставить галочку, что все ок.  
- Важно: Тестирование на любом этапе (4.1-4.4) должно быть из JSON файла. Не из JSON допускается только если мы обнаружили проблему и происходит подбор других вариантов. Но и то, после успешной замены вариантов нужно сохранить их в JSON. 
- Использовать MCP Server Sequential Thinking составления фраз, для анализа проблем и поиска решений. 


## **📋 БЛОК-СХЕМА ТЕСТИРОВАНИЯ:**

```
НАЧАЛО: Предложение из JSON файла (сырое)
    ↓
4.1 ПОДГОТОВИТЬ ФРАЗУ-ГИПОТЕЗУ (создать качественное предложение)
    ↓
4.2 БАЗОВОЕ ТЕСТИРОВАНИЕ (5 языков, естественность)
    ├─ НЕТ → Возврат к 4.1 (новое предложение) → Повтор 4.2
    └─ ДА → Переход к 4.3
         ↓
4.3 РАСШИРЕННОЕ ТЕСТИРОВАНИЕ (18 языков, 3 критерия)
    ├─ НЕТ → Анализ (см. "Работа с проблемами") → Возврат к 4.1 ИЛИ Повтор 4.3
    └─ ДА → Переход к 4.4
         ↓
4.4 ПОЛНОЕ ТЕСТИРОВАНИЕ (37 языков, все критерии)
    ├─ НЕТ → Анализ → Возврат к 4.1 ИЛИ Повтор 4.4
    └─ ДА → Переход к 4.5
         ↓
4.5 СОХРАНЕНИЕ ЛОГА → ГОТОВО
```


---

## **🎯 ПОЭТАПНЫЙ АЛГОРИТМ ТЕСТИРОВАНИЯ**

### **4.1 ПОДГОТОВИТЬ ФРАЗУ-ГИПОТЕЗУ**

**Цель:** Создать качественную фразу-гипотезу, которая будет работать на всех языках.

**Требования к фразе:**
- Основана на концепте слова
- Звучит естественно на разных языках
- Соответствует уровню (A0 = понятно новичку)
- Следует правилам из `vocabulary/create_words_rules.md`
- Учитывает особенности языков из `vocabulary/language_guides/_language_summary.md`

**Процедура:**
1. Взять исходное предложение из JSON файла
2. Проанализировать потенциальные проблемы на разных языках
3. При необходимости создать улучшенную версию предложения
4. **Обязательно обновить JSON файл** с новой фразой


### **4.2 БАЗОВОЕ ТЕСТИРОВАНИЕ**

**Цель:** Быстро отбраковать неудачные предложения на самых критичных языках.

**Языки для тестирования (5):**
- **RU, EN** - базовые языки
- **FI, DE, JA** - самые проблемные из разных групп

**Критерии:** только **ЕСТЕСТВЕННОСТЬ**

**Процедура:**
1. Взять подготовленное предложение из пункта 4.1 (из JSON файла)
2. Подставить целевое слово в предложение (см. `vocabulary/create_words_rules.md`)
3. Проверить естественность итогового предложения на 5 языках
4. **Результат:**
   - ❌ **НЕТ** → Возврат к 4.1 (создать новое предложение)
   - ✅ **ДА** → Переход к 4.3

---

### **4.3 РАСШИРЕННОЕ ТЕСТИРОВАНИЕ**

**Цель:** Проверить на всех проблемных языках по основным критериям.

**Языки для тестирования:** добавляем еще 13 языков к уже протестированным
- **Падежные:** PL, TR, UK
- **Сложные:** KO, AR, HI  
- **Романские:** IT, ES, FR, PT, NL
- **Итого:** 18 проблемных языков

**Критерии:** СМЫСЛ + ЕСТЕСТВЕННОСТЬ + БАЗОВАЯ ФОРМА (колонки 4, 5, 6)

**Процедура:**
1. Тестируем только **НОВЫЕ** языки (не повторяем уже протестированные из 4.2)
2. Проверяем по 3 критериям (колонки 4, 5, 6)
3. **При проблемах:** изучить языковые справочники для проблемных языков
4. **Результат:**
   - ❌ **НЕТ** → Анализ по алгоритму (см. "Работа с проблемами") → Возврат к 4.1 ИЛИ Повтор 4.2
   - ✅ **ДА** → Переход к 4.3

---

### **4.4 ПОЛНОЕ ТЕСТИРОВАНИЕ**

**Цель:** Финальная проверка по всем языкам и критериям.

**Языки для тестирования:** добавляем оставшиеся 19 непроблемных языков
- **Простые:** ID, MS, ZH, TH, VI, MY, SV, DA, NO, KK, UZ, PA, MR, BN, UR, NE, FA, TL, CB, KA, RO
- **Итого:** 37 языков

**Критерии:** ВСЕ критерии таблицы тестирования (колонки 4-7: Смысл + Естественность + Базовая форма + Уровень A0)

**Процедура:**
1. Тестируем только **НОВЫЕ** языки и критерии
2. Заполняем полную таблицу тестирования для всех 37 языков
3. **Результат:**
   - ❌ **НЕТ** → Анализ → Возврат к 4.1 ИЛИ Повтор 4.3
   - ✅ **ДА** → Переход к 4.4

Формат итоговой сводки тестирвоания: 

```
# СЛОВО X: [русское]/[английское] - ТЕСТИРОВАНИЕ

**Метаданные:**
- concept_id: [UUID]
- файлы: [номер]_A0_[приоритет]_[номер].md и .json
- приоритет: [ultra_core/core/extended]

**КОНЦЕПТ**: [краткое описание концепта]

**Предложение-идея:** "[русское]" / "[английское]"

## ПОЛНАЯ ТАБЛИЦА ТЕСТИРОВАНИЯ (все языки)

| Язык | Слово | Предложение | Смысл | Естественность | Базовая форма | Уровень A0 | Статус |
|------|-------|-------------|-------|----------------|---------------|------------|--------|
| RU   | [слово] | [предложение] | ✅ | ✅ | ✅ | ✅ | ✅ |
| ... все языки ... |

## РЕЗУЛЬТАТ ТЕСТИРОВАНИЯ
- **Всего языков**: [количество язков]
- **Успешно**: [количество] ✅
- **Проблемы**: [количество] ❌
- **Предупреждения**: [количество] ⚠️
- **Предложения для улучшения документации**: [улучшение] в [файл] на [строка]

**ВАЛИДАЦИЯ**: ✅ ДАННОЕ СЛОВО БЫЛО ПРОТЕСТИРОВАНО ПО ВСЕМ 37 ЯЗЫКАМ ПО ВСЕМ ПАРАМЕТРАМ ТАБЛИЦЫ

## РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ СИСТЕМЫ
- [Конкретные предложения по улучшению процесса]
- [Новые правила для документации]
- [Идеи для языковых справочников]
- [Предложения по оптимизации тестирования, в т.ч. скорости и экономия токенов]
```

**Критерии оценки колонок:**

1. **Язык:** код языка (RU, EN, DE, etc.)
2. **Слово:** целевое слово на данном языке
3. **Предложение:** итоговое предложение с подставленным целевым словом
4. **Смысл:** итоговое предложение имеет тот же смысл на всех языках
5. **Естественность:** предложение звучит как живая речь носителя языка
6. **Базовая форма:** целевое слово представлено в правильной базовой форме:
   - Существительные: именительный падеж единственного числа
   - Глаголы: инфинитив (неопределенная форма)
   - Прилагательные: мужской род единственного числа именительного падежа (где применимо)
   - Наречия: базовая форма без степеней сравнения
   - Другие части речи: словарная форма
7. **Уровень A0:** предложение по грамматике и лексике соответствует уровню A0 (понятно новичку, простая конструкция 4-5 слов)
8. **Статус:** итоговая оценка результата тестирования для данного языка

**Процесс заполнения статусов:**

**Для колонок 4-7 (критерии оценки):**
- ✅ = критерий выполнен
- ❌ = критерий не выполнен (проблема)
- ⚠️ = требует внимания/обсуждения

**Для колонки 8 (итоговый статус):**
- ✅ = язык успешно прошел тестирование (все критерии ✅ или ⚠️ с обоснованием)
- ❌ = язык не прошел тестирование (есть критические проблемы ❌)
- ⚠️ = язык прошел с оговорками (требует внимания, но не критично)

---

### **4.5 СОХРАНЕНИЕ ЛОГА В MD ФАЙЛ**

**Цель:** Создать MD файл с результатами тестирования и рекомендациями по улучшению системы.

**Процедура:**
1. Создать MD файл с полной таблицей всех 37 языков (с таким же названием). В папке `vocabulary/md/[A0/A1/...]`
2. В том же формате сводки что и в предыдущем пункте мы выводили результаты полного тестирования в чате
3. **ЖЕЛАТЕЛЬНО добавить блок "РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ СИСТЕМЫ"** с конкретными предложениями по улучшению системы добавления новых слов. Если таковые имеются.

**Что включать в рекомендации:**
- Новые "ложные проблемы" для справочника
- Улучшения правил создания слов
- Оптимизации процесса тестирования
- Идеи для языковых справочников
- Предложения по автоматизации
- Паттерны, которые хорошо работают


---

# **📚 СПРАВОЧНАЯ ИНФОРМАЦИЯ**


## **🔧 РАБОТА С ПРОБЛЕМАМИ**

#### **Использование языковых справочников**

**При проблемах обязательно изучить:**
```bash
# Проверить краткие саммари для проблемных языков
cat vocabulary/language_guides/[ЯЗЫК]_language_summary.md
```

**Рекомендация:** Использовать MCP Server Sequential Thinking для комплексного анализа проблем.


### **Алгоритм анализа проблем**

#### ШАГ 1: АНАЛИЗ** - действительно ли это проблема?
- Сравнить с правилами A0 из CreateWordsRules.md
- Подумать: создает ли это реальную сложность для A0?
- Естественно ли звучит на этом языке?

##### Частые "проблемы", которые НЕ являются проблемами:**

1. **ИМЕНИТЕЛЬНЫЙ ПАДЕЖ:**
   - ⚠️ "Слово не в именительном падеже"
   - ✅ НЕ ПРОБЛЕМА если: слово в естественной базовой форме
   - Алгоритм: попробовать именительный → если неестественно → принять исключение

2. **АРТИКЛИ:**
   - ⚠️ "Разные артикли в похожих языках"
   - ✅ НЕ ПРОБЛЕМА если: каждый язык использует свои правила естественно

3. **ДЛИННЫЕ СЛОВА:**
   - ⚠️ "Составное слово слишком длинное"
   - ✅ НЕ ПРОБЛЕМА если: это стандартный перевод в данном языке

#### ШАГ 2А: ЕСЛИ НЕ ПРОБЛЕМА**
- Документировать исключение: ⚠️ → анализ → обоснование → ✅ с комментарием
- Предложить обновление правил в документации
- Продолжить тестирование с выбранным вариантом

####  ШАГ 2Б: ЕСЛИ ДЕЙСТВИТЕЛЬНО ПРОБЛЕМА**
- Попытаться пересоздать предложение для ВСЕГО концепта
- Если не получается → применить алгоритм решения проблем

### **Алгоритм решения проблем**

**СТРОГАЯ ПОСЛЕДОВАТЕЛЬНОСТЬ:**
1. **СНАЧАЛА:** Попробовать 3-5 РАЗНЫХ предложений для ВСЕГО концепта
2. **ЗАТЕМ:** Проверить каждое новое предложение на естественность
3. **ЗАТЕМ:** Выбрать лучшее решение, которое работает для всех языков
4. **ТОЛЬКО ЕСЛИ ВСЕ ПОПЫТКИ НЕУДАЧНЫ** → применить алгоритм компромисса
5. **ПОСЛЕ НАХОЖДЕНИЯ РЕШЕНИЯ** предложить обновление правил в документации (в сообщении в сводке далее). Также нужно предложить где именно нужно изменить правила. Например, в `vocabulary/create_words_rules.md` или в `vocabulary/language_guides/_language_summary.md`. Или в п. 4.2 данной инструкции.

**Алгоритм компромисса (только в крайнем случае):**
- Убедиться, что испробованы ВСЕ разумные варианты предложений
- Если ДЕЙСТВИТЕЛЬНО все варианты неестественны → задать пользователю системный вопрос
- Предложить разумный компромисс

**Принципы непрерывного улучшения:**
1. Анализировать корень проблемы
2. Предлагать улучшение правил
3. Думать системно
4. Использовать MCP Server Sequential Thinking для анализа проблем и составления фраз
5. Предлагать документировать новые правила, чтобы избежать подобных проблем в будущем
6. Иметь свободу принятия решений, если есть уверенность. Если сомнения - спросить юзера (оператора)
7. **ВСЕГДА добавлять рекомендации по улучшению системы** в блок MD файла

---

## **⏭️ ПОСЛЕ ТЕСТИРОВАНИЯ**

### **Workflow после пакетной обработки**

**ПОСЛЕ ТЕСТИРОВАНИЯ СЛОВ:**
1. Исправить проблемные слова согласно указаниям пользователя
2. Обновить статусы в списке слов: `⏳ TO_BE_CREATED` → `📝 MD_CREATED`
3. Создать JSON файлы для всех слов (шаг 6)
4. Валидация и импорт JSON файлов (шаги 7-8)
5. Обновить статусы: `📄 JSON_CREATED` → `✅ COMPLETED`

### **Итоговая сводка пакета - НЕ В ЭТОТ ШАГ ТОЧНО**

**Формат сводки:**
```
📈 ИТОГИ ПАКЕТА:
✅ Готовы к импорту: [количество] слов
❌ Требуют исправления: [количество] слов

🔧 ПРОБЛЕМНЫЕ СЛОВА - ЧТО НУЖНО РЕШИТЬ:
- Слово X: [описание проблемы]
  → ВАРИАНТЫ: 1) [вариант] 2) [вариант] 3) [вариант]
  → РЕКОМЕНДАЦИЯ: вариант [номер]

📋 ОБНОВЛЕНИЯ ПРАВИЛ:
- Предлагаю добавить правило "[конкретное правило]" в CreateWordsRules.md
```

**Главное правило тестирования:**
Если слово меняет форму в ЛЮБОМ из проблемных языков - нужно придумать другое предложение для ВСЕГО концепта (всех 37 языков).

**Приоритеты критериев:**
1. **Естественность** (колонка 5) - ВЫСШИЙ ПРИОРИТЕТ. Предложение должно звучать как живая речь носителя языка
2. **Смысл** (колонка 4) - очень важно. Предложение должно передавать одинаковый смысл на всех языках
3. **Базовая форма** (колонка 6) - важно, но не критично. Целевое слово должно быть в базовой форме. Если в 1-2 языках не получается идеально, но это все равно узнаваемая словарная форма и предложение звучит естественно - это приемлемо
4. **Уровень A0** (колонка 7) - важно для обучения. Предложение должно быть простым и понятным новичку
