[{"concept_id": "550e8400-e29b-41d4-a716-446655440007", "level": "A0", "priority": "ultra_core", "category": "desires", "concept_name": "want", "description": {"en": "Expression of desire or need - want", "ru": "Выражение желания или потребности - хочу"}, "semantic_field": "basic_needs", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'want'", "ru": "Базовое использование 'хочу'"}, "translation_notes": {"general": "Use the most common and simple translation for 'want' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440008", "level": "A0", "priority": "ultra_core", "category": "actions", "concept_name": "eat", "description": {"en": "Basic life action - eat", "ru": "Базовое жизненное действие - есть"}, "semantic_field": "basic_needs", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'eat'", "ru": "Базовое использование 'есть'"}, "translation_notes": {"general": "Use the most common and simple translation for 'eat' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440009", "level": "A0", "priority": "ultra_core", "category": "actions", "concept_name": "drink", "description": {"en": "Basic life action - drink", "ru": "Базовое жизненное действие - пить"}, "semantic_field": "basic_needs", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'drink'", "ru": "Базовое использование 'пить'"}, "translation_notes": {"general": "Use the most common and simple translation for 'drink' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440010", "level": "A0", "priority": "ultra_core", "category": "assistance", "concept_name": "help", "description": {"en": "Request for assistance - help", "ru": "Просьба о помощи - помощь"}, "semantic_field": "social_interaction", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'help'", "ru": "Базовое использование 'помощь'"}, "translation_notes": {"general": "Use the most common and simple translation for 'help' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440011", "level": "A0", "priority": "ultra_core", "category": "politeness", "concept_name": "thank_you", "description": {"en": "Polite social expression - thank you", "ru": "Вежливое социальное выражение - спасибо"}, "semantic_field": "social_interaction", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'thank you'", "ru": "Базовое использование 'спасибо'"}, "translation_notes": {"general": "Use the most common and simple translation for 'thank you' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440012", "level": "A0", "priority": "ultra_core", "category": "politeness", "concept_name": "sorry", "description": {"en": "Polite social expression - sorry", "ru": "Вежливое социальное выражение - извините"}, "semantic_field": "social_interaction", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'sorry'", "ru": "Базовое использование 'извините'"}, "translation_notes": {"general": "Use the most common and simple translation for 'sorry' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440013", "level": "A0", "priority": "ultra_core", "category": "people", "concept_name": "doctor", "description": {"en": "Person or profession - doctor", "ru": "Человек или профессия - врач"}, "semantic_field": "emergency", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'doctor'", "ru": "Базовое использование 'врач'"}, "translation_notes": {"general": "Use the most common and simple translation for 'doctor' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440014", "level": "A0", "priority": "ultra_core", "category": "places", "concept_name": "home", "description": {"en": "Location or place - home", "ru": "Место или локация - дом"}, "semantic_field": "basic_locations", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'home'", "ru": "Базовое использование 'дом'"}, "translation_notes": {"general": "Use the most common and simple translation for 'home' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440015", "level": "A0", "priority": "ultra_core", "category": "substances", "concept_name": "water", "description": {"en": "Basic substance essential for life - water", "ru": "Базовое вещество, необходимое для жизни - вода"}, "semantic_field": "basic_needs", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'water'", "ru": "Базовое использование 'вода'"}, "translation_notes": {"general": "Use the most common and simple translation for 'water' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440016", "level": "A0", "priority": "core", "category": "verbs", "concept_name": "be", "description": {"en": "Basic verb - be", "ru": "Базовый глагол - быть"}, "semantic_field": "existence", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'be'", "ru": "Базовое использование 'быть'"}, "translation_notes": {"general": "Use the most common and simple translation for 'be' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440017", "level": "A0", "priority": "core", "category": "verbs", "concept_name": "want", "description": {"en": "Basic verb - want", "ru": "Базовый глагол - хотеть"}, "semantic_field": "desires", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'want'", "ru": "Базовое использование 'хотеть'"}, "translation_notes": {"general": "Use the most common and simple translation for 'want' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440018", "level": "A0", "priority": "core", "category": "verbs", "concept_name": "sleep", "description": {"en": "Basic verb - sleep", "ru": "Базовый глагол - спать"}, "semantic_field": "daily_activities", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'sleep'", "ru": "Базовое использование 'спать'"}, "translation_notes": {"general": "Use the most common and simple translation for 'sleep' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440019", "level": "A0", "priority": "core", "category": "verbs", "concept_name": "see", "description": {"en": "Basic verb - see", "ru": "Базовый глагол - видеть"}, "semantic_field": "perception", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'see'", "ru": "Базовое использование 'видеть'"}, "translation_notes": {"general": "Use the most common and simple translation for 'see' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440020", "level": "A0", "priority": "core", "category": "verbs", "concept_name": "speak", "description": {"en": "Basic verb - speak", "ru": "Базовый глагол - говорить"}, "semantic_field": "communication", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'speak'", "ru": "Базовое использование 'говорить'"}, "translation_notes": {"general": "Use the most common and simple translation for 'speak' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440021", "level": "A0", "priority": "core", "category": "verbs", "concept_name": "understand", "description": {"en": "Basic verb - understand", "ru": "Базовый глагол - понимать"}, "semantic_field": "cognition", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'understand'", "ru": "Базовое использование 'понимать'"}, "translation_notes": {"general": "Use the most common and simple translation for 'understand' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440022", "level": "A0", "priority": "core", "category": "verbs", "concept_name": "know", "description": {"en": "Basic verb - know", "ru": "Базовый глагол - знать"}, "semantic_field": "cognition", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'know'", "ru": "Базовое использование 'знать'"}, "translation_notes": {"general": "Use the most common and simple translation for 'know' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440023", "level": "A0", "priority": "core", "category": "verbs", "concept_name": "help", "description": {"en": "Basic verb - help", "ru": "Базовый глагол - помогать"}, "semantic_field": "social_interaction", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'help'", "ru": "Базовое использование 'помогать'"}, "translation_notes": {"general": "Use the most common and simple translation for 'help' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440024", "level": "A0", "priority": "core", "category": "verbs", "concept_name": "live", "description": {"en": "Basic verb - live", "ru": "Базовый глагол - жить"}, "semantic_field": "existence", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'live'", "ru": "Базовое использование 'жить'"}, "translation_notes": {"general": "Use the most common and simple translation for 'live' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440025", "level": "A0", "priority": "core", "category": "verbs", "concept_name": "do", "description": {"en": "Basic verb - do", "ru": "Базовый глагол - делать"}, "semantic_field": "actions", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'do'", "ru": "Базовое использование 'делать'"}, "translation_notes": {"general": "Use the most common and simple translation for 'do' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440026", "level": "A0", "priority": "core", "category": "family", "concept_name": "mother", "description": {"en": "Family member - mother", "ru": "Член семьи - мама"}, "semantic_field": "family_relations", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'mother'", "ru": "Базовое использование 'мама'"}, "translation_notes": {"general": "Use the most common and simple translation for 'mother' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440027", "level": "A0", "priority": "core", "category": "family", "concept_name": "father", "description": {"en": "Family member - father", "ru": "Член семьи - папа"}, "semantic_field": "family_relations", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'father'", "ru": "Базовое использование 'папа'"}, "translation_notes": {"general": "Use the most common and simple translation for 'father' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440028", "level": "A0", "priority": "core", "category": "people", "concept_name": "friend", "description": {"en": "Person or profession - friend", "ru": "Человек или профессия - друг"}, "semantic_field": "social_relations", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'friend'", "ru": "Базовое использование 'друг'"}, "translation_notes": {"general": "Use the most common and simple translation for 'friend' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440029", "level": "A0", "priority": "core", "category": "people", "concept_name": "person", "description": {"en": "Person or profession - person", "ru": "Человек или профессия - человек"}, "semantic_field": "basic_identity", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'person'", "ru": "Базовое использование 'человек'"}, "translation_notes": {"general": "Use the most common and simple translation for 'person' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440030", "level": "A0", "priority": "core", "category": "family", "concept_name": "child", "description": {"en": "Family member - child", "ru": "Член семьи - ребенок"}, "semantic_field": "family_relations", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'child'", "ru": "Базовое использование 'ребенок'"}, "translation_notes": {"general": "Use the most common and simple translation for 'child' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440031", "level": "A0", "priority": "core", "category": "time", "concept_name": "now", "description": {"en": "Time concept - now", "ru": "Временное понятие - сейчас"}, "semantic_field": "temporal", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'now'", "ru": "Базовое использование 'сейчас'"}, "translation_notes": {"general": "Use the most common and simple translation for 'now' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440032", "level": "A0", "priority": "core", "category": "time", "concept_name": "today", "description": {"en": "Time concept - today", "ru": "Временное понятие - сегодня"}, "semantic_field": "temporal", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'today'", "ru": "Базовое использование 'сегодня'"}, "translation_notes": {"general": "Use the most common and simple translation for 'today' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440033", "level": "A0", "priority": "core", "category": "time", "concept_name": "tomorrow", "description": {"en": "Time concept - tomorrow", "ru": "Временное понятие - завтра"}, "semantic_field": "temporal", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'tomorrow'", "ru": "Базовое использование 'завтра'"}, "translation_notes": {"general": "Use the most common and simple translation for 'tomorrow' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440034", "level": "A0", "priority": "core", "category": "location", "concept_name": "here", "description": {"en": "Location reference - here", "ru": "Указание на место - здесь"}, "semantic_field": "spatial", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'here'", "ru": "Базовое использование 'здесь'"}, "translation_notes": {"general": "Use the most common and simple translation for 'here' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440035", "level": "A0", "priority": "core", "category": "location", "concept_name": "there", "description": {"en": "Location reference - there", "ru": "Указание на место - там"}, "semantic_field": "spatial", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'there'", "ru": "Базовое использование 'там'"}, "translation_notes": {"general": "Use the most common and simple translation for 'there' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440036", "level": "A0", "priority": "core", "category": "places", "concept_name": "home", "description": {"en": "Location or place - home", "ru": "Место или локация - дом"}, "semantic_field": "basic_locations", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'home'", "ru": "Базовое использование 'дом'"}, "translation_notes": {"general": "Use the most common and simple translation for 'home' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440037", "level": "A0", "priority": "core", "category": "questions", "concept_name": "when", "description": {"en": "Interrogative word asking when", "ru": "Вопросительное слово когда"}, "semantic_field": "temporal_seeking", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'when'", "ru": "Базовое использование 'когда'"}, "translation_notes": {"general": "Use the most common and simple translation for 'when' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440038", "level": "A0", "priority": "core", "category": "questions", "concept_name": "how", "description": {"en": "Interrogative word asking how", "ru": "Вопросительное слово как"}, "semantic_field": "method_seeking", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'how'", "ru": "Базовое использование 'как'"}, "translation_notes": {"general": "Use the most common and simple translation for 'how' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440039", "level": "A0", "priority": "core", "category": "time", "concept_name": "soon", "description": {"en": "Time concept - soon", "ru": "Временное понятие - скоро"}, "semantic_field": "temporal", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'soon'", "ru": "Базовое использование 'скоро'"}, "translation_notes": {"general": "Use the most common and simple translation for 'soon' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440040", "level": "A0", "priority": "core", "category": "food", "concept_name": "bread", "description": {"en": "Food item - bread", "ru": "Продукт питания - хлеб"}, "semantic_field": "basic_needs", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'bread'", "ru": "Базовое использование 'хлеб'"}, "translation_notes": {"general": "Use the most common and simple translation for 'bread' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440041", "level": "A0", "priority": "core", "category": "food", "concept_name": "food", "description": {"en": "Food item - food", "ru": "Продукт питания - еда"}, "semantic_field": "basic_needs", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'food'", "ru": "Базовое использование 'еда'"}, "translation_notes": {"general": "Use the most common and simple translation for 'food' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440042", "level": "A0", "priority": "core", "category": "states", "concept_name": "hungry", "description": {"en": "Physical or emotional state - hungry", "ru": "Физическое или эмоциональное состояние - голодный"}, "semantic_field": "physical_states", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'hungry'", "ru": "Базовое использование 'голодный'"}, "translation_notes": {"general": "Use the most common and simple translation for 'hungry' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440043", "level": "A0", "priority": "core", "category": "states", "concept_name": "sick", "description": {"en": "Physical or emotional state - sick", "ru": "Физическое или эмоциональное состояние - больной"}, "semantic_field": "physical_states", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'sick'", "ru": "Базовое использование 'больной'"}, "translation_notes": {"general": "Use the most common and simple translation for 'sick' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440044", "level": "A0", "priority": "core", "category": "objects", "concept_name": "money", "description": {"en": "Important object - money", "ru": "Важный предмет - деньги"}, "semantic_field": "basic_needs", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'money'", "ru": "Базовое использование 'деньги'"}, "translation_notes": {"general": "Use the most common and simple translation for 'money' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440045", "level": "A0", "priority": "core", "category": "activities", "concept_name": "work", "description": {"en": "Daily activity - work", "ru": "Повседневная деятельность - работа"}, "semantic_field": "daily_life", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'work'", "ru": "Базовое использование 'работа'"}, "translation_notes": {"general": "Use the most common and simple translation for 'work' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440046", "level": "A0", "priority": "core", "category": "places", "concept_name": "hospital", "description": {"en": "Location or place - hospital", "ru": "Место или локация - больница"}, "semantic_field": "emergency", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'hospital'", "ru": "Базовое использование 'больница'"}, "translation_notes": {"general": "Use the most common and simple translation for 'hospital' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440047", "level": "A0", "priority": "core", "category": "objects", "concept_name": "phone", "description": {"en": "Important object - phone", "ru": "Важный предмет - телефон"}, "semantic_field": "communication", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'phone'", "ru": "Базовое использование 'телефон'"}, "translation_notes": {"general": "Use the most common and simple translation for 'phone' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440048", "level": "A0", "priority": "core", "category": "qualities", "concept_name": "dangerous", "description": {"en": "Quality or characteristic - dangerous", "ru": "Качество или характеристика - опасно"}, "semantic_field": "safety", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'dangerous'", "ru": "Базовое использование 'опасно'"}, "translation_notes": {"general": "Use the most common and simple translation for 'dangerous' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440049", "level": "A0", "priority": "core", "category": "qualities", "concept_name": "good", "description": {"en": "Quality or characteristic - good", "ru": "Качество или характеристика - хорошо"}, "semantic_field": "evaluation", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'good'", "ru": "Базовое использование 'хорошо'"}, "translation_notes": {"general": "Use the most common and simple translation for 'good' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440050", "level": "A0", "priority": "core", "category": "qualities", "concept_name": "bad", "description": {"en": "Quality or characteristic - bad", "ru": "Качество или характеристика - плохо"}, "semantic_field": "evaluation", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'bad'", "ru": "Базовое использование 'плохо'"}, "translation_notes": {"general": "Use the most common and simple translation for 'bad' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440051", "level": "A0", "priority": "core", "category": "qualities", "concept_name": "big", "description": {"en": "Quality or characteristic - big", "ru": "Качество или характеристика - большой"}, "semantic_field": "size", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'big'", "ru": "Базовое использование 'большой'"}, "translation_notes": {"general": "Use the most common and simple translation for 'big' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440052", "level": "A0", "priority": "core", "category": "qualities", "concept_name": "small", "description": {"en": "Quality or characteristic - small", "ru": "Качество или характеристика - маленький"}, "semantic_field": "size", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'small'", "ru": "Базовое использование 'маленький'"}, "translation_notes": {"general": "Use the most common and simple translation for 'small' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440053", "level": "A0", "priority": "extended", "category": "pronouns", "concept_name": "we", "description": {"en": "Personal pronoun referring to we", "ru": "Личное местоимение мы"}, "semantic_field": "personal_identity", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'we'", "ru": "Базовое использование 'мы'"}, "translation_notes": {"general": "Use the most common and simple translation for 'we' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440054", "level": "A0", "priority": "extended", "category": "pronouns", "concept_name": "you_plural", "description": {"en": "Personal pronoun referring to you_plural", "ru": "Личное местоимение вы"}, "semantic_field": "personal_identity", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'you_plural'", "ru": "Базовое использование 'вы'"}, "translation_notes": {"general": "Use the most common and simple translation for 'you_plural' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440055", "level": "A0", "priority": "extended", "category": "pronouns", "concept_name": "they", "description": {"en": "Personal pronoun referring to they", "ru": "Личное местоимение они"}, "semantic_field": "personal_identity", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'they'", "ru": "Базовое использование 'они'"}, "translation_notes": {"general": "Use the most common and simple translation for 'they' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440056", "level": "A0", "priority": "extended", "category": "pronouns", "concept_name": "that", "description": {"en": "Personal pronoun referring to that", "ru": "Личное местоимение то"}, "semantic_field": "demonstrative", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'that'", "ru": "Базовое использование 'то'"}, "translation_notes": {"general": "Use the most common and simple translation for 'that' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440057", "level": "A0", "priority": "extended", "category": "verbs", "concept_name": "have", "description": {"en": "Basic verb - have", "ru": "Базовый глагол - иметь"}, "semantic_field": "possession", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'have'", "ru": "Базовое использование 'иметь'"}, "translation_notes": {"general": "Use the most common and simple translation for 'have' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440058", "level": "A0", "priority": "extended", "category": "verbs", "concept_name": "go", "description": {"en": "Basic verb - go", "ru": "Базовый глагол - идти"}, "semantic_field": "movement", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'go'", "ru": "Базовое использование 'идти'"}, "translation_notes": {"general": "Use the most common and simple translation for 'go' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440059", "level": "A0", "priority": "extended", "category": "verbs", "concept_name": "give", "description": {"en": "Basic verb - give", "ru": "Базовый глагол - давать"}, "semantic_field": "transfer", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'give'", "ru": "Базовое использование 'давать'"}, "translation_notes": {"general": "Use the most common and simple translation for 'give' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440060", "level": "A0", "priority": "extended", "category": "verbs", "concept_name": "take", "description": {"en": "Basic verb - take", "ru": "Базовый глагол - брать"}, "semantic_field": "acquisition", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'take'", "ru": "Базовое использование 'брать'"}, "translation_notes": {"general": "Use the most common and simple translation for 'take' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440061", "level": "A0", "priority": "extended", "category": "verbs", "concept_name": "work", "description": {"en": "Basic verb - work", "ru": "Базовый глагол - работать"}, "semantic_field": "activities", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'work'", "ru": "Базовое использование 'работать'"}, "translation_notes": {"general": "Use the most common and simple translation for 'work' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440062", "level": "A0", "priority": "extended", "category": "verbs", "concept_name": "love", "description": {"en": "Basic verb - love", "ru": "Базовый глагол - любить"}, "semantic_field": "emotions", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'love'", "ru": "Базовое использование 'любить'"}, "translation_notes": {"general": "Use the most common and simple translation for 'love' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440063", "level": "A0", "priority": "extended", "category": "verbs", "concept_name": "come", "description": {"en": "Basic verb - come", "ru": "Базовый глагол - приходить"}, "semantic_field": "movement", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'come'", "ru": "Базовое использование 'приходить'"}, "translation_notes": {"general": "Use the most common and simple translation for 'come' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440064", "level": "A0", "priority": "extended", "category": "verbs", "concept_name": "buy", "description": {"en": "Basic verb - buy", "ru": "Базовый глагол - покупать"}, "semantic_field": "commerce", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'buy'", "ru": "Базовое использование 'покупать'"}, "translation_notes": {"general": "Use the most common and simple translation for 'buy' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440065", "level": "A0", "priority": "extended", "category": "family", "concept_name": "son", "description": {"en": "Family member - son", "ru": "Член семьи - сын"}, "semantic_field": "family_relations", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'son'", "ru": "Базовое использование 'сын'"}, "translation_notes": {"general": "Use the most common and simple translation for 'son' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440066", "level": "A0", "priority": "extended", "category": "family", "concept_name": "daughter", "description": {"en": "Family member - daughter", "ru": "Член семьи - дочь"}, "semantic_field": "family_relations", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'daughter'", "ru": "Базовое использование 'дочь'"}, "translation_notes": {"general": "Use the most common and simple translation for 'daughter' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440067", "level": "A0", "priority": "extended", "category": "family", "concept_name": "brother", "description": {"en": "Family member - brother", "ru": "Член семьи - брат"}, "semantic_field": "family_relations", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'brother'", "ru": "Базовое использование 'брат'"}, "translation_notes": {"general": "Use the most common and simple translation for 'brother' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440068", "level": "A0", "priority": "extended", "category": "family", "concept_name": "sister", "description": {"en": "Family member - sister", "ru": "Член семьи - сестра"}, "semantic_field": "family_relations", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'sister'", "ru": "Базовое использование 'сестра'"}, "translation_notes": {"general": "Use the most common and simple translation for 'sister' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440069", "level": "A0", "priority": "extended", "category": "time", "concept_name": "later", "description": {"en": "Time concept - later", "ru": "Временное понятие - потом"}, "semantic_field": "temporal", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'later'", "ru": "Базовое использование 'потом'"}, "translation_notes": {"general": "Use the most common and simple translation for 'later' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440070", "level": "A0", "priority": "extended", "category": "time", "concept_name": "yesterday", "description": {"en": "Time concept - yesterday", "ru": "Временное понятие - вчера"}, "semantic_field": "temporal", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'yesterday'", "ru": "Базовое использование 'вчера'"}, "translation_notes": {"general": "Use the most common and simple translation for 'yesterday' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440071", "level": "A0", "priority": "extended", "category": "time", "concept_name": "morning", "description": {"en": "Time concept - morning", "ru": "Временное понятие - утром"}, "semantic_field": "temporal", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'morning'", "ru": "Базовое использование 'утром'"}, "translation_notes": {"general": "Use the most common and simple translation for 'morning' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440072", "level": "A0", "priority": "extended", "category": "time", "concept_name": "afternoon", "description": {"en": "Time concept - afternoon", "ru": "Временное понятие - днем"}, "semantic_field": "temporal", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'afternoon'", "ru": "Базовое использование 'днем'"}, "translation_notes": {"general": "Use the most common and simple translation for 'afternoon' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440073", "level": "A0", "priority": "extended", "category": "time", "concept_name": "evening", "description": {"en": "Time concept - evening", "ru": "Временное понятие - вечером"}, "semantic_field": "temporal", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'evening'", "ru": "Базовое использование 'вечером'"}, "translation_notes": {"general": "Use the most common and simple translation for 'evening' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440074", "level": "A0", "priority": "extended", "category": "location", "concept_name": "far", "description": {"en": "Location reference - far", "ru": "Указание на место - далеко"}, "semantic_field": "spatial", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'far'", "ru": "Базовое использование 'далеко'"}, "translation_notes": {"general": "Use the most common and simple translation for 'far' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440075", "level": "A0", "priority": "extended", "category": "location", "concept_name": "close", "description": {"en": "Location reference - close", "ru": "Указание на место - близко"}, "semantic_field": "spatial", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'close'", "ru": "Базовое использование 'близко'"}, "translation_notes": {"general": "Use the most common and simple translation for 'close' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440076", "level": "A0", "priority": "extended", "category": "food", "concept_name": "milk", "description": {"en": "Food item - milk", "ru": "Продукт питания - молоко"}, "semantic_field": "basic_needs", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'milk'", "ru": "Базовое использование 'молоко'"}, "translation_notes": {"general": "Use the most common and simple translation for 'milk' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440077", "level": "A0", "priority": "extended", "category": "food", "concept_name": "meat", "description": {"en": "Food item - meat", "ru": "Продукт питания - мясо"}, "semantic_field": "basic_needs", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'meat'", "ru": "Базовое использование 'мясо'"}, "translation_notes": {"general": "Use the most common and simple translation for 'meat' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440078", "level": "A0", "priority": "extended", "category": "qualities", "concept_name": "hot", "description": {"en": "Quality or characteristic - hot", "ru": "Качество или характеристика - горячий"}, "semantic_field": "temperature", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'hot'", "ru": "Базовое использование 'горячий'"}, "translation_notes": {"general": "Use the most common and simple translation for 'hot' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440079", "level": "A0", "priority": "extended", "category": "qualities", "concept_name": "cold", "description": {"en": "Quality or characteristic - cold", "ru": "Качество или характеристика - холодный"}, "semantic_field": "temperature", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'cold'", "ru": "Базовое использование 'холодный'"}, "translation_notes": {"general": "Use the most common and simple translation for 'cold' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440080", "level": "A0", "priority": "extended", "category": "food", "concept_name": "rice", "description": {"en": "Food item - rice", "ru": "Продукт питания - рис"}, "semantic_field": "basic_needs", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'rice'", "ru": "Базовое использование 'рис'"}, "translation_notes": {"general": "Use the most common and simple translation for 'rice' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440081", "level": "A0", "priority": "extended", "category": "food", "concept_name": "tea", "description": {"en": "Food item - tea", "ru": "Продукт питания - чай"}, "semantic_field": "beverages", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'tea'", "ru": "Базовое использование 'чай'"}, "translation_notes": {"general": "Use the most common and simple translation for 'tea' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440082", "level": "A0", "priority": "extended", "category": "food", "concept_name": "coffee", "description": {"en": "Food item - coffee", "ru": "Продукт питания - кофе"}, "semantic_field": "beverages", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'coffee'", "ru": "Базовое использование 'кофе'"}, "translation_notes": {"general": "Use the most common and simple translation for 'coffee' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440083", "level": "A0", "priority": "extended", "category": "objects", "concept_name": "table", "description": {"en": "Important object - table", "ru": "Важный предмет - стол"}, "semantic_field": "furniture", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'table'", "ru": "Базовое использование 'стол'"}, "translation_notes": {"general": "Use the most common and simple translation for 'table' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440084", "level": "A0", "priority": "extended", "category": "objects", "concept_name": "chair", "description": {"en": "Important object - chair", "ru": "Важный предмет - стул"}, "semantic_field": "furniture", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'chair'", "ru": "Базовое использование 'стул'"}, "translation_notes": {"general": "Use the most common and simple translation for 'chair' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440085", "level": "A0", "priority": "extended", "category": "objects", "concept_name": "bed", "description": {"en": "Important object - bed", "ru": "Важный предмет - кровать"}, "semantic_field": "furniture", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'bed'", "ru": "Базовое использование 'кровать'"}, "translation_notes": {"general": "Use the most common and simple translation for 'bed' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440086", "level": "A0", "priority": "extended", "category": "transport", "concept_name": "car", "description": {"en": "Basic A0 concept - car", "ru": "Базовое понятие A0 - машина"}, "semantic_field": "transportation", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'car'", "ru": "Базовое использование 'машина'"}, "translation_notes": {"general": "Use the most common and simple translation for 'car' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440087", "level": "A0", "priority": "extended", "category": "places", "concept_name": "road", "description": {"en": "Location or place - road", "ru": "Место или локация - дорога"}, "semantic_field": "infrastructure", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'road'", "ru": "Базовое использование 'дорога'"}, "translation_notes": {"general": "Use the most common and simple translation for 'road' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440088", "level": "A0", "priority": "extended", "category": "places", "concept_name": "shop", "description": {"en": "Location or place - shop", "ru": "Место или локация - магазин"}, "semantic_field": "commerce", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'shop'", "ru": "Базовое использование 'магазин'"}, "translation_notes": {"general": "Use the most common and simple translation for 'shop' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440089", "level": "A0", "priority": "extended", "category": "places", "concept_name": "school", "description": {"en": "Location or place - school", "ru": "Место или локация - школа"}, "semantic_field": "education", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'school'", "ru": "Базовое использование 'школа'"}, "translation_notes": {"general": "Use the most common and simple translation for 'school' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440090", "level": "A0", "priority": "extended", "category": "objects", "concept_name": "door", "description": {"en": "Important object - door", "ru": "Важный предмет - дверь"}, "semantic_field": "architecture", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'door'", "ru": "Базовое использование 'дверь'"}, "translation_notes": {"general": "Use the most common and simple translation for 'door' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440091", "level": "A0", "priority": "extended", "category": "objects", "concept_name": "window", "description": {"en": "Important object - window", "ru": "Важный предмет - окно"}, "semantic_field": "architecture", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'window'", "ru": "Базовое использование 'окно'"}, "translation_notes": {"general": "Use the most common and simple translation for 'window' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440092", "level": "A0", "priority": "extended", "category": "objects", "concept_name": "book", "description": {"en": "Important object - book", "ru": "Важный предмет - книга"}, "semantic_field": "education", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'book'", "ru": "Базовое использование 'книга'"}, "translation_notes": {"general": "Use the most common and simple translation for 'book' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440093", "level": "A0", "priority": "extended", "category": "objects", "concept_name": "clothes", "description": {"en": "Important object - clothes", "ru": "Важный предмет - одежда"}, "semantic_field": "personal_items", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'clothes'", "ru": "Базовое использование 'одежда'"}, "translation_notes": {"general": "Use the most common and simple translation for 'clothes' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440094", "level": "A0", "priority": "extended", "category": "qualities", "concept_name": "new", "description": {"en": "Quality or characteristic - new", "ru": "Качество или характеристика - новый"}, "semantic_field": "temporal_quality", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'new'", "ru": "Базовое использование 'новый'"}, "translation_notes": {"general": "Use the most common and simple translation for 'new' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440095", "level": "A0", "priority": "extended", "category": "qualities", "concept_name": "old", "description": {"en": "Quality or characteristic - old", "ru": "Качество или характеристика - старый"}, "semantic_field": "temporal_quality", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'old'", "ru": "Базовое использование 'старый'"}, "translation_notes": {"general": "Use the most common and simple translation for 'old' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440096", "level": "A0", "priority": "extended", "category": "qualities", "concept_name": "beautiful", "description": {"en": "Quality or characteristic - beautiful", "ru": "Качество или характеристика - красивый"}, "semantic_field": "aesthetic", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'beautiful'", "ru": "Базовое использование 'красивый'"}, "translation_notes": {"general": "Use the most common and simple translation for 'beautiful' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440097", "level": "A0", "priority": "extended", "category": "qualities", "concept_name": "fast", "description": {"en": "Quality or characteristic - fast", "ru": "Качество или характеристика - быстрый"}, "semantic_field": "speed", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'fast'", "ru": "Базовое использование 'быстрый'"}, "translation_notes": {"general": "Use the most common and simple translation for 'fast' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440098", "level": "A0", "priority": "extended", "category": "qualities", "concept_name": "slow", "description": {"en": "Quality or characteristic - slow", "ru": "Качество или характеристика - медленный"}, "semantic_field": "speed", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'slow'", "ru": "Базовое использование 'медленный'"}, "translation_notes": {"general": "Use the most common and simple translation for 'slow' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440099", "level": "A0", "priority": "extended", "category": "qualities", "concept_name": "easy", "description": {"en": "Quality or characteristic - easy", "ru": "Качество или характеристика - легкий"}, "semantic_field": "difficulty", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'easy'", "ru": "Базовое использование 'легкий'"}, "translation_notes": {"general": "Use the most common and simple translation for 'easy' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440100", "level": "A0", "priority": "extended", "category": "qualities", "concept_name": "heavy", "description": {"en": "Quality or characteristic - heavy", "ru": "Качество или характеристика - тяжелый"}, "semantic_field": "weight", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'heavy'", "ru": "Базовое использование 'тяжелый'"}, "translation_notes": {"general": "Use the most common and simple translation for 'heavy' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440101", "level": "A0", "priority": "extended", "category": "qualities", "concept_name": "expensive", "description": {"en": "Quality or characteristic - expensive", "ru": "Качество или характеристика - дорогой"}, "semantic_field": "value", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'expensive'", "ru": "Базовое использование 'дорогой'"}, "translation_notes": {"general": "Use the most common and simple translation for 'expensive' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440102", "level": "A0", "priority": "extended", "category": "qualities", "concept_name": "cheap", "description": {"en": "Quality or characteristic - cheap", "ru": "Качество или характеристика - дешевый"}, "semantic_field": "value", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'cheap'", "ru": "Базовое использование 'дешевый'"}, "translation_notes": {"general": "Use the most common and simple translation for 'cheap' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440103", "level": "A0", "priority": "extended", "category": "qualities", "concept_name": "correct", "description": {"en": "Quality or characteristic - correct", "ru": "Качество или характеристика - правильный"}, "semantic_field": "accuracy", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'correct'", "ru": "Базовое использование 'правильный'"}, "translation_notes": {"general": "Use the most common and simple translation for 'correct' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440104", "level": "A0", "priority": "extended", "category": "qualities", "concept_name": "important", "description": {"en": "Quality or characteristic - important", "ru": "Качество или характеристика - важный"}, "semantic_field": "significance", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'important'", "ru": "Базовое использование 'важный'"}, "translation_notes": {"general": "Use the most common and simple translation for 'important' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440105", "level": "A0", "priority": "extended", "category": "qualities", "concept_name": "first", "description": {"en": "Quality or characteristic - first", "ru": "Качество или характеристика - первый"}, "semantic_field": "order", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'first'", "ru": "Базовое использование 'первый'"}, "translation_notes": {"general": "Use the most common and simple translation for 'first' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440106", "level": "A0", "priority": "extended", "category": "qualities", "concept_name": "last", "description": {"en": "Quality or characteristic - last", "ru": "Качество или характеристика - последний"}, "semantic_field": "order", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'last'", "ru": "Базовое использование 'последний'"}, "translation_notes": {"general": "Use the most common and simple translation for 'last' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440107", "level": "A0", "priority": "extended", "category": "qualities", "concept_name": "best", "description": {"en": "Quality or characteristic - best", "ru": "Качество или характеристика - лучший"}, "semantic_field": "comparison", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'best'", "ru": "Базовое использование 'лучший'"}, "translation_notes": {"general": "Use the most common and simple translation for 'best' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440108", "level": "A0", "priority": "extended", "category": "qualities", "concept_name": "worst", "description": {"en": "Quality or characteristic - worst", "ru": "Качество или характеристика - худший"}, "semantic_field": "comparison", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'worst'", "ru": "Базовое использование 'худший'"}, "translation_notes": {"general": "Use the most common and simple translation for 'worst' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440109", "level": "A0", "priority": "extended", "category": "numbers", "concept_name": "one", "description": {"en": "Number - one", "ru": "Число - один"}, "semantic_field": "quantity", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'one'", "ru": "Базовое использование 'один'"}, "translation_notes": {"general": "Use the most common and simple translation for 'one' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440110", "level": "A0", "priority": "extended", "category": "numbers", "concept_name": "two", "description": {"en": "Number - two", "ru": "Число - два"}, "semantic_field": "quantity", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'two'", "ru": "Базовое использование 'два'"}, "translation_notes": {"general": "Use the most common and simple translation for 'two' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440111", "level": "A0", "priority": "extended", "category": "numbers", "concept_name": "three", "description": {"en": "Number - three", "ru": "Число - три"}, "semantic_field": "quantity", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'three'", "ru": "Базовое использование 'три'"}, "translation_notes": {"general": "Use the most common and simple translation for 'three' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440112", "level": "A0", "priority": "extended", "category": "numbers", "concept_name": "ten", "description": {"en": "Number - ten", "ru": "Число - десять"}, "semantic_field": "quantity", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'ten'", "ru": "Базовое использование 'десять'"}, "translation_notes": {"general": "Use the most common and simple translation for 'ten' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440113", "level": "A0", "priority": "extended", "category": "numbers", "concept_name": "hundred", "description": {"en": "Number - hundred", "ru": "Число - сто"}, "semantic_field": "quantity", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'hundred'", "ru": "Базовое использование 'сто'"}, "translation_notes": {"general": "Use the most common and simple translation for 'hundred' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440114", "level": "A0", "priority": "extended", "category": "numbers", "concept_name": "thousand", "description": {"en": "Number - thousand", "ru": "Число - тысяча"}, "semantic_field": "quantity", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'thousand'", "ru": "Базовое использование 'тысяча'"}, "translation_notes": {"general": "Use the most common and simple translation for 'thousand' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440115", "level": "A0", "priority": "extended", "category": "quantities", "concept_name": "many", "description": {"en": "Quantity expression - many", "ru": "Выражение количества - много"}, "semantic_field": "amount", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'many'", "ru": "Базовое использование 'много'"}, "translation_notes": {"general": "Use the most common and simple translation for 'many' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440116", "level": "A0", "priority": "extended", "category": "quantities", "concept_name": "few", "description": {"en": "Quantity expression - few", "ru": "Выражение количества - мало"}, "semantic_field": "amount", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'few'", "ru": "Базовое использование 'мало'"}, "translation_notes": {"general": "Use the most common and simple translation for 'few' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440117", "level": "A0", "priority": "extended", "category": "quantities", "concept_name": "all", "description": {"en": "Quantity expression - all", "ru": "Выражение количества - все"}, "semantic_field": "totality", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'all'", "ru": "Базовое использование 'все'"}, "translation_notes": {"general": "Use the most common and simple translation for 'all' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440118", "level": "A0", "priority": "extended", "category": "quantities", "concept_name": "nothing", "description": {"en": "Quantity expression - nothing", "ru": "Выражение количества - ничего"}, "semantic_field": "absence", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'nothing'", "ru": "Базовое использование 'ничего'"}, "translation_notes": {"general": "Use the most common and simple translation for 'nothing' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440119", "level": "A0", "priority": "extended", "category": "questions", "concept_name": "how_much", "description": {"en": "Interrogative word asking how_much", "ru": "Вопросительное слово сколько"}, "semantic_field": "quantity_seeking", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'how_much'", "ru": "Базовое использование 'сколько'"}, "translation_notes": {"general": "Use the most common and simple translation for 'how_much' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440120", "level": "A0", "priority": "extended", "category": "politeness", "concept_name": "please", "description": {"en": "Polite social expression - please", "ru": "Вежливое социальное выражение - пожалуйста"}, "semantic_field": "social_interaction", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'please'", "ru": "Базовое использование 'пожалуйста'"}, "translation_notes": {"general": "Use the most common and simple translation for 'please' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440121", "level": "A0", "priority": "extended", "category": "greetings", "concept_name": "hello", "description": {"en": "Greeting expression - hello", "ru": "Приветствие - привет"}, "semantic_field": "social_interaction", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'hello'", "ru": "Базовое использование 'привет'"}, "translation_notes": {"general": "Use the most common and simple translation for 'hello' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440122", "level": "A0", "priority": "extended", "category": "greetings", "concept_name": "goodbye", "description": {"en": "Greeting expression - goodbye", "ru": "Приветствие - пока"}, "semantic_field": "social_interaction", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'goodbye'", "ru": "Базовое использование 'пока'"}, "translation_notes": {"general": "Use the most common and simple translation for 'goodbye' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440123", "level": "A0", "priority": "extended", "category": "questions", "concept_name": "why", "description": {"en": "Interrogative word asking why", "ru": "Вопросительное слово почему"}, "semantic_field": "reason_seeking", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'why'", "ru": "Базовое использование 'почему'"}, "translation_notes": {"general": "Use the most common and simple translation for 'why' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440124", "level": "A0", "priority": "extended", "category": "colors", "concept_name": "white", "description": {"en": "Color - white", "ru": "Цвет - белый"}, "semantic_field": "visual_properties", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'white'", "ru": "Базовое использование 'белый'"}, "translation_notes": {"general": "Use the most common and simple translation for 'white' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440125", "level": "A0", "priority": "extended", "category": "colors", "concept_name": "black", "description": {"en": "Color - black", "ru": "Цвет - черный"}, "semantic_field": "visual_properties", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'black'", "ru": "Базовое использование 'черный'"}, "translation_notes": {"general": "Use the most common and simple translation for 'black' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440126", "level": "A0", "priority": "extended", "category": "colors", "concept_name": "red", "description": {"en": "Color - red", "ru": "Цвет - красный"}, "semantic_field": "visual_properties", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'red'", "ru": "Базовое использование 'красный'"}, "translation_notes": {"general": "Use the most common and simple translation for 'red' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440127", "level": "A0", "priority": "extended", "category": "colors", "concept_name": "blue", "description": {"en": "Color - blue", "ru": "Цвет - синий"}, "semantic_field": "visual_properties", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'blue'", "ru": "Базовое использование 'синий'"}, "translation_notes": {"general": "Use the most common and simple translation for 'blue' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440128", "level": "A0", "priority": "extended", "category": "directions", "concept_name": "left", "description": {"en": "Direction - left", "ru": "Направление - налево"}, "semantic_field": "spatial_orientation", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'left'", "ru": "Базовое использование 'налево'"}, "translation_notes": {"general": "Use the most common and simple translation for 'left' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440129", "level": "A0", "priority": "extended", "category": "directions", "concept_name": "right", "description": {"en": "Direction - right", "ru": "Направление - направо"}, "semantic_field": "spatial_orientation", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'right'", "ru": "Базовое использование 'направо'"}, "translation_notes": {"general": "Use the most common and simple translation for 'right' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440130", "level": "A0", "priority": "extended", "category": "directions", "concept_name": "straight", "description": {"en": "Direction - straight", "ru": "Направление - прямо"}, "semantic_field": "spatial_orientation", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'straight'", "ru": "Базовое использование 'прямо'"}, "translation_notes": {"general": "Use the most common and simple translation for 'straight' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440131", "level": "A0", "priority": "extended", "category": "directions", "concept_name": "up", "description": {"en": "Direction - up", "ru": "Направление - вверх"}, "semantic_field": "spatial_orientation", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'up'", "ru": "Базовое использование 'вверх'"}, "translation_notes": {"general": "Use the most common and simple translation for 'up' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440132", "level": "A0", "priority": "extended", "category": "directions", "concept_name": "down", "description": {"en": "Direction - down", "ru": "Направление - вниз"}, "semantic_field": "spatial_orientation", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'down'", "ru": "Базовое использование 'вниз'"}, "translation_notes": {"general": "Use the most common and simple translation for 'down' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440133", "level": "A0", "priority": "extended", "category": "states", "concept_name": "hurt", "description": {"en": "Physical or emotional state - hurt", "ru": "Физическое или эмоциональное состояние - больно"}, "semantic_field": "physical_states", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'hurt'", "ru": "Базовое использование 'больно'"}, "translation_notes": {"general": "Use the most common and simple translation for 'hurt' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440134", "level": "A0", "priority": "extended", "category": "states", "concept_name": "tired", "description": {"en": "Physical or emotional state - tired", "ru": "Физическое или эмоциональное состояние - устал"}, "semantic_field": "physical_states", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'tired'", "ru": "Базовое использование 'устал'"}, "translation_notes": {"general": "Use the most common and simple translation for 'tired' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440135", "level": "A0", "priority": "extended", "category": "transport", "concept_name": "bus", "description": {"en": "Basic A0 concept - bus", "ru": "Базовое понятие A0 - автобус"}, "semantic_field": "transportation", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'bus'", "ru": "Базовое использование 'автобус'"}, "translation_notes": {"general": "Use the most common and simple translation for 'bus' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440136", "level": "A0", "priority": "extended", "category": "transport", "concept_name": "train", "description": {"en": "Basic A0 concept - train", "ru": "Базовое понятие A0 - поезд"}, "semantic_field": "transportation", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'train'", "ru": "Базовое использование 'поезд'"}, "translation_notes": {"general": "Use the most common and simple translation for 'train' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440137", "level": "A0", "priority": "extended", "category": "transport", "concept_name": "airplane", "description": {"en": "Basic A0 concept - airplane", "ru": "Базовое понятие A0 - самолет"}, "semantic_field": "transportation", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'airplane'", "ru": "Базовое использование 'самолет'"}, "translation_notes": {"general": "Use the most common and simple translation for 'airplane' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440138", "level": "A0", "priority": "extended", "category": "body_parts", "concept_name": "head", "description": {"en": "Body part - head", "ru": "Часть тела - голова"}, "semantic_field": "anatomy", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'head'", "ru": "Базовое использование 'голова'"}, "translation_notes": {"general": "Use the most common and simple translation for 'head' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440139", "level": "A0", "priority": "extended", "category": "body_parts", "concept_name": "heart", "description": {"en": "Body part - heart", "ru": "Часть тела - сердце"}, "semantic_field": "anatomy", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'heart'", "ru": "Базовое использование 'сердце'"}, "translation_notes": {"general": "Use the most common and simple translation for 'heart' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440140", "level": "A0", "priority": "extended", "category": "body_parts", "concept_name": "stomach", "description": {"en": "Body part - stomach", "ru": "Часть тела - живот"}, "semantic_field": "anatomy", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'stomach'", "ru": "Базовое использование 'живот'"}, "translation_notes": {"general": "Use the most common and simple translation for 'stomach' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440141", "level": "A0", "priority": "extended", "category": "body_parts", "concept_name": "hand", "description": {"en": "Body part - hand", "ru": "Часть тела - рука"}, "semantic_field": "anatomy", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'hand'", "ru": "Базовое использование 'рука'"}, "translation_notes": {"general": "Use the most common and simple translation for 'hand' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440142", "level": "A0", "priority": "extended", "category": "body_parts", "concept_name": "leg", "description": {"en": "Body part - leg", "ru": "Часть тела - нога"}, "semantic_field": "anatomy", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'leg'", "ru": "Базовое использование 'нога'"}, "translation_notes": {"general": "Use the most common and simple translation for 'leg' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440143", "level": "A0", "priority": "extended", "category": "emergency", "concept_name": "police", "description": {"en": "Emergency concept - police", "ru": "Экстренная ситуация - полиция"}, "semantic_field": "safety", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'police'", "ru": "Базовое использование 'полиция'"}, "translation_notes": {"general": "Use the most common and simple translation for 'police' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440144", "level": "A0", "priority": "extended", "category": "states", "concept_name": "lost", "description": {"en": "Physical or emotional state - lost", "ru": "Физическое или эмоциональное состояние - потерялся"}, "semantic_field": "emergency", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'lost'", "ru": "Базовое использование 'потерялся'"}, "translation_notes": {"general": "Use the most common and simple translation for 'lost' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440145", "level": "A0", "priority": "extended", "category": "communication", "concept_name": "dont_understand", "description": {"en": "Communication concept - dont_understand", "ru": "Понятие общения - не понимаю"}, "semantic_field": "comprehension", "usage_context": "basic_communication", "examples": {"en": "Basic usage of 'dont_understand'", "ru": "Базовое использование 'не понимаю'"}, "translation_notes": {"general": "Use the most common and simple translation for 'dont_understand' in each language"}, "created_at": "2025-06-23T12:00:00Z", "updated_at": "2025-06-23T12:00:00Z"}]