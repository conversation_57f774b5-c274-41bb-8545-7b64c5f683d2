[{"concept_id": "550e8400-e29b-41d4-a716-446655440001", "level": "A0", "priority": "ultra_core", "category": "pronouns", "concept_name": "first_person_pronoun", "description": {"en": "First person singular pronoun - refers to the speaker/writer themselves", "ru": "Местоимение первого лица единственного числа - обозначает говорящего"}, "semantic_field": "personal_identity", "usage_context": "basic_communication", "examples": {"en": "I work, I am happy, I want", "ru": "Я работаю, я счастлив, я хочу"}, "translation_notes": {"general": "Should be the most common first-person pronoun in each language", "specific": {"ja": "Use 私 (watashi) as most neutral form", "ar": "Use أنا (ana) for both masculine and feminine", "th": "Use ฉัน (chan) as polite neutral form"}}, "created_at": "2025-06-23T10:00:00Z", "updated_at": "2025-06-23T10:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440002", "level": "A0", "priority": "ultra_core", "category": "pronouns", "concept_name": "second_person_pronoun", "description": {"en": "Second person singular pronoun - refers to the person being addressed", "ru": "Местоимение второго лица единственного числа - обозначает собеседника"}, "semantic_field": "personal_identity", "usage_context": "basic_communication", "examples": {"en": "You work, you are happy, you want", "ru": "Ты работае<PERSON>ь, ты счастлив, ты хочешь"}, "translation_notes": {"general": "Use informal/familiar form where applicable", "specific": {"de": "Use 'du' (informal) not 'Sie' (formal)", "fr": "Use 'tu' (informal) not 'vous' (formal)", "es": "Use 'tú' (informal) not 'usted' (formal)"}}, "created_at": "2025-06-23T10:00:00Z", "updated_at": "2025-06-23T10:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440003", "level": "A0", "priority": "ultra_core", "category": "affirmation", "concept_name": "positive_response", "description": {"en": "Affirmative response - expresses agreement, confirmation, or positive answer", "ru": "Утвердительный ответ - выражает согласие, подтверждение или положительный ответ"}, "semantic_field": "agreement", "usage_context": "basic_communication", "examples": {"en": "Yes, I understand. Yes, that's correct.", "ru": "Да, я понимаю. Да, это правильно."}, "translation_notes": {"general": "Use the most common affirmative word in each language", "specific": {"ja": "Use はい (hai) for polite affirmation", "zh": "Use 是 (shì) for general affirmation"}}, "created_at": "2025-06-23T10:00:00Z", "updated_at": "2025-06-23T10:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440004", "level": "A0", "priority": "ultra_core", "category": "negation", "concept_name": "negative_response", "description": {"en": "Negative response - expresses disagreement, denial, or negative answer", "ru": "Отрицательный ответ - выражает несогласие, отрицание или отрицательный ответ"}, "semantic_field": "disagreement", "usage_context": "basic_communication", "examples": {"en": "No, I don't understand. No, that's wrong.", "ru": "Нет, я не понимаю. Нет, это неправильно."}, "translation_notes": {"general": "Use the most common negative word in each language", "specific": {"ja": "Use いいえ (iie) for polite negation", "zh": "Use 不 (bù) or 没有 (méiyǒu) depending on context"}}, "created_at": "2025-06-23T10:00:00Z", "updated_at": "2025-06-23T10:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440005", "level": "A0", "priority": "ultra_core", "category": "questions", "concept_name": "what_question", "description": {"en": "Interrogative pronoun asking about things, objects, or concepts", "ru": "Вопросительное местоимение для вопроса о предметах, объектах или понятиях"}, "semantic_field": "information_seeking", "usage_context": "basic_questions", "examples": {"en": "What is this? What do you want?", "ru": "Что это? Что ты хочешь?"}, "translation_notes": {"general": "Use the most basic 'what' equivalent in each language", "specific": {"zh": "Use 什么 (shénme) for general 'what' questions", "ar": "Use ما (mā) or ماذا (mādhā) for 'what'"}}, "created_at": "2025-06-23T10:00:00Z", "updated_at": "2025-06-23T10:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440006", "level": "A0", "priority": "ultra_core", "category": "questions", "concept_name": "where_question", "description": {"en": "Interrogative adverb asking about location or place", "ru": "Вопросительное наречие для вопроса о местоположении или месте"}, "semantic_field": "location_seeking", "usage_context": "basic_questions", "examples": {"en": "Where is home? Where are you?", "ru": "Где дом? Где ты?"}, "translation_notes": {"general": "Use the most basic 'where' equivalent in each language", "specific": {"zh": "Use 哪里 (nǎlǐ) or 在哪里 (zài nǎlǐ) for 'where'", "ar": "Use أين (ayna) for 'where'"}}, "created_at": "2025-06-23T10:00:00Z", "updated_at": "2025-06-23T10:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-************", "level": "A0", "priority": "ultra_core", "category": "actions", "concept_name": "eating_action", "description": {"en": "Basic action of consuming food - fundamental survival activity", "ru": "Базовое действие приема пищи - фундаментальная деятельность для выживания"}, "semantic_field": "basic_needs", "usage_context": "expressing desire or action of food consumption", "not_confused_with": {"drinking": "consuming liquids, not solid food", "cooking": "preparing food, not consuming it"}, "situation_examples": ["feeling hungry and wanting food", "sitting at table with meal", "expressing basic need for nutrition"], "emotional_tone": "neutral, basic need", "examples": {"en": "I want to eat", "ru": "Я хочу есть"}, "translation_notes": {"general": "Use most basic verb for food consumption in each language", "specific": {"en": "Use 'eat' not 'consume' or 'dine'", "ru": "Use 'есть' not 'кушать' or 'питаться'", "de": "Use 'essen' as most basic form"}}, "created_at": "2025-06-24T10:00:00Z", "updated_at": "2025-06-24T10:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-************", "level": "A0", "priority": "ultra_core", "category": "emotions", "concept_name": "apology_regret", "description": {"en": "Expression of regret or sorrow for one's mistake or wrongdoing", "ru": "Выражение сожаления или раскаяния за свою ошибку или проступок"}, "semantic_field": "emotional_expression", "usage_context": "when person made mistake and wants to apologize", "not_confused_with": {"excuse_me": "polite attention-getting, not apology for wrongdoing", "pardon": "request for repetition, not expression of regret"}, "situation_examples": ["accidentally bumped into someone", "arrived late to meeting", "made an error in work"], "emotional_tone": "regret, remorse, sorrow", "examples": {"en": "I'm very sorry for being late", "ru": "Мне очень жаль, что опоздал"}, "translation_notes": {"general": "Use words expressing regret/sorrow, not attention-getting", "specific": {"en": "Use 'sorry' not 'excuse me'", "de": "Use 'es tut mir leid' not 'entschuldigung'", "fr": "Use 'd<PERSON><PERSON>é' not 'excusez-moi'"}}, "created_at": "2025-06-24T10:00:00Z", "updated_at": "2025-06-24T10:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-************", "level": "A0", "priority": "ultra_core", "category": "actions", "concept_name": "drinking_action", "description": {"en": "Basic action of consuming liquids - fundamental survival activity", "ru": "Базовое действие приема жидкости - фундаментальная деятельность для выживания"}, "semantic_field": "basic_needs", "usage_context": "expressing desire or action of liquid consumption", "not_confused_with": {"eating": "consuming solid food, not liquids", "pouring": "transferring liquid, not consuming it"}, "situation_examples": ["feeling thirsty and wanting liquid", "holding a glass of water", "expressing basic need for hydration"], "emotional_tone": "neutral, basic need", "examples": {"en": "I want to drink", "ru": "Я хочу пить"}, "translation_notes": {"general": "Use most basic verb for liquid consumption in each language", "specific": {"en": "Use 'drink' not 'consume' or 'sip'", "ru": "Use 'пить' as most basic form", "de": "Use 'trinken' as most basic form"}}, "created_at": "2025-06-24T10:00:00Z", "updated_at": "2025-06-24T10:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-************", "level": "A0", "priority": "ultra_core", "category": "assistance", "concept_name": "help_request", "description": {"en": "Request for assistance or support from others", "ru": "Просьба о помощи или поддержке от других людей"}, "semantic_field": "social_interaction", "usage_context": "when person needs assistance and asks for support", "not_confused_with": {"helping_others": "giving help to someone, not requesting it", "emergency": "urgent crisis situation, not general help request"}, "situation_examples": ["lost and need directions", "struggling with heavy object", "need assistance with task"], "emotional_tone": "neutral, request", "examples": {"en": "I need help", "ru": "Мне нужна помощь"}, "translation_notes": {"general": "Use basic word for assistance/support in each language", "specific": {"en": "Use 'help' not 'assistance' or 'aid'", "ru": "Use 'помощь' as most basic form", "de": "Use 'Hilfe' not 'Unterstützung'"}}, "created_at": "2025-06-24T10:00:00Z", "updated_at": "2025-06-24T10:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440011", "level": "A0", "priority": "ultra_core", "category": "gratitude", "concept_name": "gratitude_expression", "description": {"en": "Expression of gratitude and appreciation for something received", "ru": "Выражение благодарности и признательности за что-то полученное"}, "semantic_field": "social_politeness", "usage_context": "when person wants to express gratitude for help, gift, or kindness", "not_confused_with": {"please": "polite request, not gratitude expression", "you_welcome": "response to thanks, not giving thanks"}, "situation_examples": ["received help from someone", "got a gift or favor", "someone did something kind"], "emotional_tone": "positive, grateful", "examples": {"en": "Thank you very much", "ru": "Спасибо большое"}, "translation_notes": {"general": "Use most common gratitude expression, compound phrases acceptable", "specific": {"en": "Use 'thank you' as standard form", "ru": "Use 'спасибо' as most basic form", "de": "Use 'danke' or 'dankeschön'"}}, "created_at": "2025-06-24T10:00:00Z", "updated_at": "2025-06-24T10:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440013", "level": "A0", "priority": "ultra_core", "category": "professions", "concept_name": "medical_doctor", "description": {"en": "Medical professional who treats sick people and provides healthcare", "ru": "Медицинский специалист, который лечит больных людей и оказывает медицинскую помощь"}, "semantic_field": "healthcare", "usage_context": "when person needs medical help or talking about medical professional", "not_confused_with": {"nurse": "medical assistant, not primary doctor", "dentist": "tooth specialist, not general doctor", "teacher": "education professional, not medical"}, "situation_examples": ["feeling sick and need medical help", "going to hospital or clinic", "talking about medical profession"], "emotional_tone": "neutral, professional", "examples": {"en": "I need a doctor", "ru": "Мне нужен врач"}, "translation_notes": {"general": "Use most basic word for medical doctor in each language", "specific": {"en": "Use 'doctor' not 'physician' or 'medic'", "ru": "Use 'врач' as most basic form", "de": "Use 'Arzt' not 'Do<PERSON><PERSON>'"}}, "created_at": "2025-06-24T10:00:00Z", "updated_at": "2025-06-24T10:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-************", "level": "A0", "priority": "ultra_core", "category": "substances", "concept_name": "water_liquid", "description": {"en": "Clear liquid essential for life - most basic drink", "ru": "Прозрачная жидкость, необходимая для жизни - самый базовый напиток"}, "semantic_field": "basic_needs", "usage_context": "when person needs hydration or talking about basic liquid", "not_confused_with": {"juice": "flavored drink, not pure water", "milk": "dairy liquid, not water", "tea": "prepared beverage, not pure water"}, "situation_examples": ["feeling thirsty", "asking for drink in restaurant", "basic survival need"], "emotional_tone": "neutral, basic need", "examples": {"en": "I need water", "ru": "Мне нужна вода"}, "translation_notes": {"general": "Use most basic word for water in each language", "specific": {"en": "Use 'water' as standard form", "ru": "Use 'вода' as most basic form", "de": "Use 'Wasser' as basic form"}}, "created_at": "2025-06-24T10:00:00Z", "updated_at": "2025-06-24T10:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-************", "level": "A0", "priority": "ultra_core", "category": "demonstratives", "concept_name": "proximal_demonstrative", "description": {"en": "Demonstrative pronoun pointing to something near or present", "ru": "Указательное местоимение, указывающее на что-то близкое или присутствующее"}, "semantic_field": "reference", "usage_context": "when pointing to or referring to something close or visible", "not_confused_with": {"that": "distant demonstrative, not close", "it": "neutral pronoun, not demonstrative", "here": "location adverb, not demonstrative pronoun"}, "situation_examples": ["pointing to nearby object", "referring to current situation", "indicating something visible"], "emotional_tone": "neutral, demonstrative", "examples": {"en": "This is good", "ru": "Это хорошо"}, "translation_notes": {"general": "Use proximal (near) demonstrative pronoun in each language", "specific": {"en": "Use 'this' not 'that'", "ru": "Use 'это' as neutral demonstrative", "de": "Use 'das' or 'dies' depending on context"}}, "created_at": "2025-06-24T10:00:00Z", "updated_at": "2025-06-24T10:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440016", "level": "A0", "priority": "core", "category": "existence", "concept_name": "existence_state", "description": {"en": "Fundamental verb expressing existence, identity, or state of being", "ru": "Фундаментальный глагол, выражающий существование, идентичность или состояние бытия"}, "semantic_field": "existence", "usage_context": "expressing existence, location, identity, or temporary state", "not_confused_with": {"have": "possession, not existence", "do": "action, not state of being", "live": "residing somewhere, not general existence"}, "situation_examples": ["expressing desire to be somewhere", "stating identity or profession", "describing temporary state"], "emotional_tone": "neutral, fundamental", "examples": {"en": "I want to be home", "ru": "Я хочу быть дома"}, "translation_notes": {"general": "Use most basic copula/existence verb in each language", "specific": {"en": "Use 'be' as infinitive form", "ru": "Use 'быть' as infinitive", "es": "Use 'ser' or 'estar' depending on context"}}, "created_at": "2025-06-24T10:00:00Z", "updated_at": "2025-06-24T10:00:00Z"}, {"concept_id": "550e8400-e29b-41d4-a716-446655440017", "level": "A0", "priority": "core", "category": "actions", "concept_name": "sleep_action", "description": {"en": "Basic action of resting with closed eyes - fundamental biological need", "ru": "Базовое действие отдыха с закрытыми глазами - фундаментальная биологическая потребность"}, "semantic_field": "basic_needs", "usage_context": "expressing desire or need for rest and sleep", "not_confused_with": {"rest": "general relaxation, not necessarily sleep", "lie_down": "position, not sleep action", "tired": "feeling, not action of sleeping"}, "situation_examples": ["feeling tired and wanting sleep", "bedtime routine", "expressing basic biological need"], "emotional_tone": "neutral, basic need", "examples": {"en": "I want to sleep", "ru": "Я хочу спать"}, "translation_notes": {"general": "Use most basic verb for sleep action in each language", "specific": {"en": "Use 'sleep' not 'slumber' or 'rest'", "ru": "Use 'спать' as most basic form", "de": "Use 'schlafen' as basic form"}}, "created_at": "2025-06-24T10:00:00Z", "updated_at": "2025-06-24T10:00:00Z"}]