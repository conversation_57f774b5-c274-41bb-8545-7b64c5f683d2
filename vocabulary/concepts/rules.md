**Концепт** - это семантическое описание идеи, которую выражает слово. Каждый концепт имеет уникальный `concept_id` и связывает переводы одного слова на всех языках.

**КРИТИЧЕСКИ ВАЖНО**: Концепты должны соответствовать UUID слова в файлах со списками слов `vocabulary/word_lists/*.md`.

**ПРОЦЕДУРА ВЕРИФИКАЦИИ:**
1. **Проверить наличие концепта** для каждого выбранного слова в `backend/data/concepts/A0_concepts.json`
2. **Если концепт отсутствует** - создать новый по расширенному формату
3. **Если концепт неполный** - дополнить недостающими элементами
4. **Проверить качество** - соответствие критериям детального концепта

**РАСШИРЕННЫЙ ФОРМАТ КОНЦЕПТА:**
`/vocabulary/concepts/example_concept.json`

**КРИТЕРИИ КАЧЕСТВЕННОГО КОНЦЕПТА:**
- ✅ **Однозначность**: четкое описание без двусмысленности
- ✅ **Контекстность**: ясно указано когда используется
- ✅ **Различимость**: отличается от похожих концептов
- ✅ **Примеры ситуаций**: конкретные случаи использования
- ✅ **Translation notes**: указания для правильного перевода
- ✅ **Эмоциональная окраска**: тон и настроение слова


Используй **📚 Context7** → изучить подробные справочники проблемных языков