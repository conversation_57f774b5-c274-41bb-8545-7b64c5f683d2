# Word Master

Приложение для изучения английских слов с использованием метода интервального повторения. Помогает запоминать слова в контексте предложений с адаптивной системой повторений.

## 📄 Описание проекта

Подробное описание проекта, концепции, маркетинга и планов развития: **[PROJECT.md](PROJECT.md)**

## 🚀 Быстрый старт

Для первоначальной настройки проекта следуйте инструкциям в [документации по настройке](docs/unsorted/SETUP.md).

## 🏗️ Структура проекта

Подробная структура проекта описана в [PROJECT_STRUCTURE.md](PROJECT_STRUCTURE.md).

## 🛠️ Частые команды

> **Важно**: Перед выполнением команд, связанных с Python и mongoDB, всегда активируйте виртуальное окружение:

### 🔄 Виртуальное окружение
- Emir: `cd backend && source venv/bin/activate` - Активировать виртуальное окружение (Linux/Mac)
- Zak: `cd backend && source venv_zak/bin/activate` - Активировать виртуальное окружение (Linux/Mac)
- Rona: `cd backend && source venv_rona/bin/activate` - Активировать виртуальное окружение (Linux/Mac)
- Eric: `cd backend && source venv_eric/bin/activate` - Активировать виртуальное окружение (Linux/Mac)


### 🚀 Запуск приложений
- `cd frontend && npx expo start --tunnel` - Запустить фронтенд
- `cd backend && source venv_rona/bin/activate &&  uvicorn app.main:app --reload --port 8000` - Запустить бэкенд
   - Обязательно! Для тестирования через телефон expo go, то:
      - в одном окне запускаем бэкенд
      - `ngrok http 8000` - из папки backend (тот же порт что и бэкенд)
      - копируешь адрес ngrok и вставляешь в файл `frontend/config/api.ts` в константу `NGROK_URL`

### 🗃️ Управление базой данных
- `python backend/temp_scripts/check/check_mongodb_connection.py` - Проверить подключение к MongoDB
- `python -m scripts.words clear --force` - Очистить коллекцию слов
- `python -m scripts.words import` - Импортировать слова из файла (положить файл в `vocabulary/json/new/`)
- `python -m scripts.words recent --minutes 5` - Показать недавно добавленные слова
- `python -m scripts.words recent --level A1 --lang en` - Показать слова по уровню и языку

## 📁 Важные файлы

### Backend
- `spaced_repetition.py` - основной файл интервального повторения
- **[📖 Система Spaced Repetition](backend/docs/SPACED_REPETITION_SYSTEM.md)** - полная документация по алгоритму интервального повторения, предзагрузке карточек и фронтенд-бэкенд интеграции

### Frontend
- `frontend/config/api.ts` - **конфигурация IP-адреса** (изменяйте при смене IP)
- `frontend/constants/languages.ts` - централизованный список всех поддерживаемых языков
- `frontend/contexts/AuthContext.tsx` - контекст аутентификации
- `frontend/services/api.ts` - API сервисы для работы с карточками

### 🐳 Docker
- `docker-compose up --build` - Собрать и запустить контейнеры
- `docker-compose down` - Остановить контейнеры

## 📚 Документация

### 🏗️ Структура проекта
- **[PROJECT_STRUCTURE.md](PROJECT_STRUCTURE.md)** - полная структура проекта

### 🆕 Словарные данные
- **[📋 Создание слов](vocabulary/_WORDS_CREATION_GUIDE_WORKFLOW.md)** - главная инструкция по созданию и тестированию новых слов
- [📝 Список слов A0](vocabulary/word_lists/A0_word_list.md) - все слова уровня A0
- [📚 Структура vocabulary](vocabulary/README.md) - описание организации словарных данных

### Backend
- [Настройка проекта](docs/unsorted/SETUP.md) - полное руководство по начальной настройке
- **[📖 Система Spaced Repetition](backend/docs/SPACED_REPETITION_SYSTEM.md)** - полная документация по алгоритму интервального повторения
- [API документация](docs/api/API_DOCS.md) - полная документация по API (эндпоинты, аутентификация)
- [Развертывание](docs/deployment/DEPLOYMENT_LINUX.md) - инструкции по развертыванию
- [Тестирование](docs/development/TESTING_STRATEGY.md) - стратегия тестирования

### Frontend
- [🧭 Руководство по навигации](frontend/NAVIGATION_GUIDE.md) - лучшие практики навигации, решение проблем со стеком


## ⚙️ Конфигурация API

IP-адрес бэкенда централизованно управляется в файле `frontend/config/api.ts`:

Все остальные URL генерируются автоматически:
- `API_URL` - для аутентификации
- `API_ENDPOINT` - для API запросов
- `API_BASE_URL` - базовый URL

При смене IP-адреса изменяйте только эту строку:
`export const API_HOST = '***********';`


## 🚧 Частые проблемы

### Проблема: Не работает авторизация (крутится индикатор)
**Причина:** IP-адрес бэкенда изменился после перезагрузки интернета

**Быстрое решение:**
1. **Узнать ЛОКАЛЬНЫЙ IP** (НЕ внешний!): `ifconfig | grep 'inet ' | grep -v 127.0.0.1`
   - ✅ **Правильно**: `***********` (локальный IP для локальной сети)
   - ❌ **Неправильно**: `curl ifconfig.me` (внешний IP, не работает для локальной разработки)
2. Открыть `frontend/config/api.ts`
3. Изменить **только** строку `API_HOST = 'новый_локальный_IP'`
4. Перезапустить приложение


### 2. 



## 🧪 Тестирование
- `test_spaced_repetition.py` - Тестирование системы интервального повторения (новые слова, расчет интервалов, прогресс пользователя)

- `test_api_flow.py` - Тестирование API интервального повторения

## 🌍 Поддерживаемые языки

Приложение поддерживает **22 языка** для изучения:

> 💡 **Централизованное управление**: Все языки хранятся в `frontend/constants/languages.ts` для удобного добавления новых языков.

### Уровни владения языком
Поддерживаются стандартные уровни CEFR + дополнительный начальный:
- **A0** - Начальный (100 базовых слов)
- **A1** - Элементарный
- **A2** - Предпороговый
- **B1** - Пороговый
- **B2** - Пороговый продвинутый
- **C1** - Уровень профессионального владения
- **C2** - Уровень владения в совершенстве



## 📦 Технологический стек

- **Frontend**: React Native, TypeScript, Expo
- **Backend**: Python FastAPI
- **База данных**: MongoDB Atlas
- **Аутентификация**: JWT

## 📄 Лицензия

MIT
