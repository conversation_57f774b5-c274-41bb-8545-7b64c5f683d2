# Word Master - Приложение для изучения английских слов

> 📖 [Назад к README](README.md)

---

# Word Master: Приложение для изучения английских слов

## Обзор проекта

**Название**: Word Master — приложение для изучения английских слов с интервальным повторением  
**Цель**: Создать мобильное приложение для изучения английского языка с использованием интервального повторения по кривой забывания Эббингауза.  

### О продукте
Пользователи изучают 5–6 тыс. частотных английских слов (95–97% языка) через карточки, вводя пропущенные слова в английских предложениях с переводом. Слова разделены на уровни (начинающий, средний). Интервалы повторения (1 мин → 5 мин → 10 мин → 1 ч → 1 день → 3 дня → 7 дней → 14 дней → 30 дней → 2 мес → 4 мес → 8 мес → 1 год → 2 года) закрепляют знания. Приложение работает без регистрации первый месяц, погружая в тренировку с первой карточки.

### Цены
Россия: 129 руб./месяц, 1290 руб./год (скидка ~25%)
Цены для других стран: `docs/future/prices.md`

### Целевая аудитория
- Обычные пользователи: изучающие английский язык (начинающий и средний уровень, Россия, СНГ).  
- Партнёры: обычные люди, инфлюенсеры, блогеры, школы, репетиторы, рекомендуют приложение, зарабатывают 30% от подписок.  
- Агенты: самозанятые, ищут партнёров (инфлюенсеры, блогеры, школы, репетиторы).  
- Рекруты: самозанятые, ищут агентов и других рекрутов.

### Дизайн
Современный стильный интерфейс используя лучшие практики и тренды 2025 года с тёмной темой и стекломорфизмом glassmorphism, со слегка прозрачными слоями и мягкими тенями. Дизайн минималистичный, интуитивный

- Динамические смешанные размытые радиальные градиенты в фонах карточек, блоков и просто в фоне. Грандиенты смешиваются между собой, образуя красивые мягкие сочетания на темном фоне. Но они приглушенные, темные, и не бросаются в глаза. 
- Акцентный цвет - пурпурный и все что с ним связано. Но не яркий, а приглушенный, слегка затемненный. Чтобы смотрелось гармонично. Ацкентного цвета не много. Чтобы небыло сильно пестро. 
- Градиентные шрифты для заголовков (градиент цвета слева неправо), читаемые шрифты для текста. Плавные переходы, микроанимации (свечение кнопок, пульсация прогресс-бара). 
- Кнопки имеют анимации, свечение, градиенты
- Также нужно использовать frosted glass effect.
- Примеры дизайнов, которые нравятся по стилю находятся в папке `design/`

### Финансовая цель
$60,000/год чистой прибыли через 12,000 годовых подписок (1290 руб./год).

### Образовательная цель
Выучить 5–6 тыс. слов за год с прогресс-баром, мотивацией и геймификацией.

## Концепции проекта

### Концепция №1: Максимальная автономность
Проект работает и развивается без участия владельца. После запуска одного рекрута он находит агентов и других рекрутов, которые привлекают партнёров и пользователей. Пользователи мотивируются становиться агентами или рекрутами через приложение (баннеры, уведомления) и Партнёрский портал, обеспечивая автономный рост сети.

## Ключевые функции

- **Тренировка**: Ежедневные карточки (20–50, выбирает пользователь) с английским предложением, пропущенным словом и переводом. Пользователь вводит слово.  
- **Интервальное повторение**:  
  - Первый правильный ввод → слово в "изученные" (без интервалов).  
  - Первый неправильный ввод → повтор до правильного, затем первый интервал (1 минута).  
  - Последующие показы:  
    - Правильный ввод → следующий интервал.  
    - Неправильный → снижение интервала на один уровень (например, с 30 дней до 14 дней), повтор до правильного.  
- **Очереди**:  
  - Очередь 1 (активная): новые слова или слова с наступившим интервалом.  
  - Очередь 2 (неактивная): слова, ожидающие интервала.  
  - Список "изученные":  
    - Слова, введенные правильно с первого раза  
    - Слова, дошедшие до 13-го интервала (1 год)  
    - Через год после последнего повторения показывается уведомление для закрепления.  
- **Гостевой режим**:  
  - Доступно прохождение 20 карточек без регистрации  
  - После 20 карточек предлагается зарегистрироваться для сохранения прогресса
- **Прогресс**: Прогресс-бар (+1 за карточку), статистика выученных слов.  
- **Уровни**: 5–6 тыс. слов (например, 50 уровней по 100 слов).  
- **Мотивация**: Фразы при запуске ("На Английском говорят половина пленеты!"), визуализация ("500/6000 слов").  
- **Обратная связь**: После ошибки — правильный ответ и объяснение (например, "Написано 'runed' вместо 'ran'. Это Past Simple, правильная форма — 'ran'.").

## Экраны

### Экран тренировки (экран по умолчанию)
В верхней части экрана находиться прогресс бар. С визуальной индикацией текущей карточки (например 7 из 20).

Затем идет блок тренировки. Сначала предложение на английском, затем, чуть ниже на русском. 
В английском предложении (там, где нужно ввести слово) есть блок инпута, на котором по умолчанию гнаходится курсор при переходе на карточку. Ширина инпута составляет кол-во букв в слове. 

В русском предложении необходимое слово (или словосочетание) выделяется акцентным цветом и жирным шрифтом. Чтобы было видно какое слово нужно вводить. А предложение нужно для понимания контекста. 

Кнопка "Проверить" не нужна. Так как проверка происходит по нажатию Enter на клавиатуре.

После нажатия Enter на клавиатуре, происходит проверка. Если введено верно, то инпут окрашивается в зеленый цвет и происходит переход к следующей карточке. Если введено неверно, то инпут окрашивается в красный цвет и карточка повторяется (вне очереди), до тех пор пока не будет введено корректно. 

### Логика интервального повторения
- При первом правильном ответе слово переходит в статус "изучено" (без интервалов) и попадает в список изученных
- Если слово было введено неправильно хотя бы раз, оно проходит через все интервалы повторения
- При ошибке интервал сбрасывается на предыдущий уровень (например, с 14 дней до 7 дней)
- При двух ошибках подряд интервал сбрасывается на начальный (1 минута)
- Слова считаются выученными, если они:
  - Были введены правильно с первого раза, ИЛИ
  - Прошли все 13 интервалов (1 год)
- Через год после последнего повторения показывается уведомление для закрепления

### Индикация прогресса
Перед предложениями на русском и английском отображается индикатор прогресса слова:
- Новое слово - оранжевый цвет, иконка 1 из 5
- Базовые интервалы (1, 5, 10 минут) - синий цвет, иконка 2 из 5
- Короткие интервалы (1 час - 1 день) - голубой цвет, иконка 3 из 5
- Средние интервалы (3 дня - 1 неделя) - зеленый цвет, иконка 4 из 5
- Длинные интервалы (2 недели и более) - темно-зеленый цвет, иконка 5 из 5

### Гостевой режим
- Доступно прохождение 20 карточек без регистрации
- После 20 карточек предлагается зарегистрироваться для сохранения прогресса
- При регистрации прогресс по изученным словам сохраняется

### Подсказки
- Если пользователь ничего не печатает 10 секунд на открытой карточке, появляется подсказка (первая буква слова)


## Технологический стек

- **Backend**:  
  - Python (FastAPI): API.  
  - Node.js (Express): дополнительный API для рефералов и выплат.  
- **Frontend**: React Native + TypeScript (Expo).  
- **База данных**:  
  - MongoDB Atlas: слова, предложения, пользователи, подписки, выплаты.  
- **Аналитика и трекинг**:  
  - Google Analytics: поведение пользователей.  
  - Пиксели соцсетей (Meta, VK): конверсии, ретаргетинг.  
  - Grok API (xAI): генерация предложений, AI-объяснения.  
- **Инструменты**:  
  - [Expo](https://expo.dev/)  
  - [MongoDB Atlas](https://www.mongodb.com/cloud/atlas)  
  - [Tatoeba](https://tatoeba.org/eng/downloads) (предложения)  
  - [EF Top 1000](https://www.ef.edu/english-resources/english-vocabulary/top-1000-words/) (слова)  
  - Firebase Dynamic Links (deep links)  
  - Branch.io (рефералы)  
  - Tapfiliate (трекинг)  
  - Elementor (сайты)  
  - ЮKassa (РФ), Stripe (глобально)

## Маркетинг

Основной канал продвижения — партнёрская программа, которая в соответствии с **Концепцией №1: Максимальная автономность** обеспечивает автономный рост в России и за рубежом (СНГ, Индия, Бразилия, Мексика, Турция).

### Партнёрская программа

#### Обычные пользователи
- **Кто?** Пользователи приложения, изучающие английский язык.  
- **Доход**: 30% от подписок (38.7 руб./месяц или 387 руб./год).  
- **Как работает?**  
  - Регистрация в приложении или на Партнёрском портале (`portal.yourapp.com`).  
  - Получают реферальную ссылку (`yourapp.com/ref/userX`), промокод (`USERX10`), персональную страницу (`userX.page.yourapp.com`) через систему сайтов.  
  - Привлекают новых пользователей через личные рекомендации (друзья, знакомые) в соцсетях, WhatsApp, Telegram.  
  - Новые пользователи оплачивают подписку через приложение (In-app Billing) или веб (ЮKassa/Stripe).  
  - Выплаты: ежемесячно через ЮKassa/Stripe, налоги платят сами (РФ: 13% НДФЛ или 4–6% НПД; за рубежом: местные налоги).  
  - Пример: пользователь привлёк 10 новых пользователей (годовые) → 10 × 387 = 3870 руб./год.  
- **Привязка новых пользователей**:  
  - Ссылка (`yourapp.com/ref/userX`), QR-код, Deep Links (Firebase/Branch).  
  - Промокод (`USERX10`) в «Настройках».  
  - Аналитика: `portal.yourapp.com` (базовая статистика).  
- **Мотивация**:  
  - Если нет денег на подписку, продвигайте приложение (во время или после триального периода), зарабатывайте на подписку (например, 1–3 подписчика = годовая подписка).  
  - Призывы в приложении ("Стань агентом!", "Зарабатывай с нами!") для перехода в агенты или рекруты.  
  - Доступ к Партнёрскому порталу для обучения и роста.  
- **Обучение**: Курс "Как продвигать приложение через рекомендации" на Партнёрском портале, см. `docs/affiliate/courses.md`.

#### Партнёры
- **Кто?** Обычные люди, инфлюенсеры, блогеры, школы, репетиторы.  
- **Доход**: 30% от подписок (38.7 руб./месяц или 387 руб./год).  
- **Как работает?**  
  - Регистрация на Партнёрском портале (`portal.yourapp.com`).  
  - Получают реферальную ссылку (`yourapp.com/ref/partnerX`), промокод (`PARTNERX10`), персональный сайт (`partnerX.yourapp.com`) через систему сайтов.  
  - Распространяют через соцсети, WhatsApp, Telegram, YouTube, классы, листовки.  
  - Ученики оплачивают подписку через приложение (In-app Billing) или веб (ЮKassa/Stripe).  
  - Выплаты: ежемесячно через ЮKassa/Stripe, налоги платят сами (РФ: 13% НДФЛ или 4–6% НПД; за рубежом: местные налоги).  
  - Пример: блогер с 100 учениками (годовые) → 100 × 387 = 38,700 руб./год.  
- **Привязка учеников**:  
  - Ссылка (`yourapp.com/ref/partnerX`), QR-код, Deep Links (Firebase/Branch).  
  - Промокод (`PARTNERX10`) в «Настройках».  
  - Аналитика: `portal.yourapp.com`.  
- **Инструкции**:
  - Курс на Партнёрском портале: "Как продавать приложение" (видео, шаблоны, скрипты), см. `docs/affiliate/courses.md`.
- **Особенности**:  
  - Лендинги, кабинеты, обучение бесплатны.  
  - Автоматизация: аналитика и выплаты на портале.

#### Самозанятые агенты
- **Кто?** Самозанятые, привлекающие партнёров (инфлюенсеров, блогеров, репетиторов, школы).  
- **Доход**: 10% от подписок (12.9 руб./месяц или 129 руб./год).  
- **Как работает?**  
  - Регистрация на Партнёрском портале (`portal.yourapp.com`).  
  - Получают кабинет с курсом "Простой маркетинг" (видео, скрипты, книга "5 правил успешного маркетинга"), промокод (`AGENTX10`), ссылку (`yourapp.com/agentX`), персональные лenдинги (`agentX.yourapp.com/school`, `agentX.yourapp.com/tutor`, `agentX.yourapp.com/blogger`) через систему сайтов.  
  - Ищут партнёров через соцсети, офлайн, рекламу.  
  - Выплаты: через ЮKassa после чека (РФ: НПД; за рубеж: декларация).  
  - Бонусы: 5000 руб. за 100 учеников, 10,000 руб. за 1000, 15,000 руб. за 10 школ.  
  - Пример: агент привлёк школу с 100 учениками (годовые) → 100 × 129 = 12,900 руб./год.  
- **Легальность**:  
  - РФ: НПД (ФЗ №422-ФЗ), лимит 2.4 млн руб./год.  
  - За рубеж: Индия (GST), Бразилия (MEI), Турция (ИП).  
  - Оферта: «10% за подписки, чек/декларация обязательны».  
- **Инструкции**: Курс "Простой маркетинг" на Партнёрском портале, см. `docs/affiliate/courses.md`.
- **Подробности**: `docs/affiliate/agents.md`.

#### Самозанятые рекруты
- **Кто?** Самозанятые, привлекающие агентов и других рекрутов.  
- **Доход**: 1000 руб. за агента, приведшего 1 партнёра с 10 учениками.  
- **Как работает?**  
  - Регистрация на Партнёрском портале (`portal.yourapp.com`) с подтверждением статуса (РФ: НПД; за рубеж: фриланс/ИП).  
  - Получают кабинет с курсом "Как привлекать агентов и рекрутов" (см. `docs/affiliate/courses.md`), персональный лендинг (`recruitX.yourapp.com`) через систему сайтов.
  - Продвигают через соцсети, мессенджеры, офлайн.  
  - Выплаты: через ЮKassa после чека/декларации.  
  - Пример: рекрут привлёк 10 агентов → 10 × 1000 = 10,000 руб.  
- **Легальность**:  
  - РФ: НПД, чеки через «Мой налог».  
  - За рубеж: Турция (ИП), Мексика (SAT).  
  - Оферта: «1000 руб. за агента, чек/декларация обязательны».  
- **Почему не MLM?** Фиксированная плата за прямой результат, без «нижележащих» уровней.  
- **Подробности**: `docs/affiliate/agents.md`.

### Система сайтов
- **Как работает?**  
  - Шаблоны создаются через Elementor (единый дизайн для пользователей, партнёров, агентов, рекрутов).  
  - Каждый участник получает персональную страницу или сайт:  
    - Пользователи: `userX.page.yourapp.com`.  
    - Партнёры: `partnerX.yourapp.com`.  
    - Агенты: `agentX.yourapp.com/school`, `agentX.yourapp.com/tutor`, `agentX.yourapp.com/blogger`.  
    - Рекруты: `recruitX.yourapp.com`.  
  - Реферальный код (например, `userX`, `partnerX`, `agentX`) подставляется в кнопки «Скачать» или «Зарегистрироваться».  
  - Изменения в шаблоне синхронизируются через API (Elementor Pro + Tapfiliate).  
  - Распространение: соцсети, WhatsApp, листовки, QR-коды.  
- **Примеры**:  
  - Пользователи: `userX.page.yourapp.com` с QR-кодом и кнопкой «Скачать».  
  - Партнёры: `partnerX.yourapp.com` с QR-кодом и кнопкой «Скачать».  
  - Агенты: `agentX.yourapp.com/school`, `agentX.yourapp.com/tutor`, `agentX.yourapp.com/blogger`.  
  - Рекруты: `recruitX.yourapp.com` для привлечения агентов.

### Другие элементы продвижения
- **Призывы в приложении**: Баннеры и уведомления ("Стань агентом!", "Зарабатывай с нами!") для перехода пользователей в агенты или рекруты.  
- **Оптимизация для Google Play и App Store**:  
  - Ключевые слова: "learn English", "English vocabulary", "spaced repetition" (английский, испанский, хинди, турецкий).  
  - Скриншоты: прогресс-бар, карточки, геймификация, 5 языков.  
  - Видео-трейлер (30 сек): онбординг, тренировка, прогресс.  
  - Ретаргетинг: Meta Ads, Google Ads.  
- **Метрики роста**:  
  - DAU/MAU: активные пользователи.  
  - Retention: 1/7/30 дней.  
  - Конверсия: freemium → платная подписка.  
  - Среднее время в приложении.

## Цели и цифры

### Финансовая цель
$60,000/год чистой прибыли через 12,000 годовых подписок.

### Доход с подписки
- **Цена**: 1290 руб./год (~$15).  
- **Чистый доход**: ~$5/подписка после комиссий (15% Google/Apple, 2% ЮKassa), налогов (6% УСН), выплат (30% партнёры, 10% агенты, ~$0.47 рекруты).

### Подписки
60,000 ÷ 5 = 12,000 годовых подписок (~1000/месяц).

## Международная структура

### Локализация
- **Приложение**: Английский, русский, испанский, португальский, хинди, турецкий.  
- **Оферта**: Перевод на 5 языков, адаптация под GDPR (ЕС), LGPD (Бразилия).

### Платежи
- **РФ**: ЮKassa (карты, СБП).  
- **За рубеж**: Stripe (PayPal, UPI в Индии, Pix в Бразилии).  
- **In-app Billing**: Все страны.

### Налоги
- **РФ**: Физлица — 13% НДФЛ, самозанятые — 4–6% НПД, ИП — 6% УСН.  
- **За рубеж**: Индия (10–30%), Бразилия (MEI до 27.5%), Турция (15–40%).

### Легальность
- **РФ**: НПД (ФЗ №422-ФЗ), ФЗ №2300-1.  
- **За рубеж**: США (FTC), ЕС (GDPR), Индия (Consumer Protection Act).  
- **App Store/Play Market**: Одноуровневая реферальная программа (App Store 3.1.2, Google Play).

## Структура проекта
Подробности: [PROJECT_STRUCTURE.md](PROJECT_STRUCTURE.md).

## 

## Этапы разработки

### Продукт
1. **MVP** (1–2 месяца):  
   - 1000 слов (по 100 на уровень. То есть есть супер простые, и супер сложные. Нужны все уровни).  
   - Тренировка, интервальное повторение, прогресс-бар.  
   - Простая система аутентификации и личный кабинет, чтобы сохранять прогресс по словам для пользователя (простая регистрация через email). 
   - Без оплаты пока
   - Подробный план хранится в `docs/future/mvp.md`.
2. **Слова** (1–2 месяца): 5–6 тыс. слов, улучшение уровней.  
3. **Оплата** (1 месяц): Подписка через ЮKassa, Stripe, In-app.  
4. **Геймификация** (1 месяц): V1 (стрики, баллы), детали в `docs/future/gamification.md`.
5. **Голос и виджет** (1–2 месяца): Озвучка, виджет прогресса.  
6. **Мультиязычность и грамматика** (2–3 месяца): Языки, грамматические карточки.  
7. **AI и персонализация** (2–3 месяца): AI-объяснения, переводчик.

### Маркетинг
1. **Партнёрка** (1–2 месяца): Партнёры, агенты, рекруты, кабинеты, лендинги.
   - `docs/affiliate/affiliate.md`, `docs/affiliate/agents.md`.



## Идеи на будущее

### Ближайшее будущее
- **Адаптивное количество карточек**: Корректировка числа карточек (от 20/день) по прогрессу.  
- **Голосовое произношение**: Озвучка (Google Text-to-Speech, 5 языков).  
- **Поддержка языков**: Испанский, португальский, хинди, турецкий.  
- **Модульная архитектура**: Подготовка для добавления языков.  
- [x] **Отслеживание прогресса**: Визуализация ("500/6000 слов").  
- **Мотивационные фразы**: Фразы при запуске.  
- **Геймификация (V1)**: Баллы, стрики, бонусы. Детали: `docs/future/gamification.md`.
- **Виджет**: Прогресс на главном экране.  
- **Тест уровня**: 20 карточек для калибровки.  
- **Онбординг**: 3–5 экранов с объяснением механики.  
- [x] **Анимации**: Для прогресс-бара, переходов.  
- **Оффлайн-режим**: Кэш 20–50 карточек.  
- **Контекстные примеры**: 2–3 предложения (Tatoeba).  
- **Синонимы и фразы**: Карточки для синонимов, фразовых глаголов.  
- **Freemium**: Бесплатный месяц, 7-дневный премиум.  
- **Подсказка**: Первая буква слова через 30 секунд.

### Долгосрочное будущее
- **Грамматика**: Карточки по темам, карта ошибок, мини-уроки.  
- **Переводчик**: Ввод слов с добавлением в повторение.  
- **Персонализация**: Генерация предложений из выученных слов.  
- **AI-объяснения**: Разбор ошибок через Grok API.  
- **Напоминания**: Настраиваемые уведомления.  
- **Аналитика**: Анонимизированные данные для улучшений.  
- **Кэширование**: Redis для ускорения API.  
- **A/B-тесты**: Firebase для тестирования функций.  
- **Партнёрства**: Интеграция с Duolingo, Quizlet.