# Система Spaced Repetition - Полная Документация

## 📋 Оглавление
1. [Обзор системы](#обзор-системы)
2. [Архитектура](#архитектура)
3. [Интервалы повторения](#интервалы-повторения)
4. [Логика немедленного изучения](#логика-немедленного-изучения)
5. [Система предзагрузки](#система-предзагрузки)
6. [Обработка ошибок](#обработка-ошибок)
7. [База данных](#база-данных)
8. [Фронтенд интеграция](#фронтенд-интеграция)
9. [Отладка и мониторинг](#отладка-и-мониторинг)
10. [Известные проблемы и решения](#известные-проблемы-и-решения)

---

## 🎯 Обзор системы

Система spaced repetition реализует алгоритм интервального повторения для эффективного изучения слов. Основные принципы:

- **Новые слова** изучаются немедленно при правильном ответе с первого раза
- **Интервалы повторения** увеличиваются экспоненциально при правильных ответах
- **Неправильные ответы** сбрасывают прогресс или уменьшают интервал
- **Предзагрузка карточек** обеспечивает мгновенные переходы

---

## 🏗️ Архитектура

### Backend (Python/FastAPI)
- `app/services/spaced_repetition.py` - основная логика
- `app/routers/spaced.py` - API эндпоинты
- MongoDB коллекции: `words`, `user_progress`

### Frontend (React Native/TypeScript)
- `screens/TrainingScreen.tsx` - основной экран обучения
- `services/cardPreloader.ts` - система предзагрузки
- `services/cardDataProcessor.ts` - обработка данных карточек

### Ключевая особенность
**Часть логики обрабатывается на фронтенде:**
- Предзагрузка следующих карточек
- Фильтрация дублирующихся карточек
- Локальная обработка неправильных ответов
- Мгновенные переходы между карточками

---

## ⏰ Интервалы повторения

### Константы (backend/app/services/spaced_repetition.py)
```python
# Интервалы в минутах для каждого уровня
REPETITION_INTERVALS = [
    0.5,    # Уровень 0: 30 секунд
    2,      # Уровень 1: 2 минуты
    10,     # Уровень 2: 10 минут
    60,     # Уровень 3: 1 час
    1440,   # Уровень 4: 1 день
    4320,   # Уровень 5: 3 дня
    10080,  # Уровень 6: 1 неделя
    20160,  # Уровень 7: 2 недели
    43200,  # Уровень 8: 1 месяц
    86400,  # Уровень 9: 2 месяца
    172800, # Уровень 10: 4 месяца
    345600, # Уровень 11: 8 месяцев
    525600, # Уровень 12: 1 год
    788400, # Уровень 13: 1.5 года
    1051200,# Уровень 14: 2 года
    1576800 # Уровень 15: 3 года (выученное слово)
]

MAX_INTERVAL_LEVEL = 15  # Максимальный уровень (выученное слово)
```

### Логика прогрессии
- **Правильный ответ**: `interval_level += 1`
- **Неправильный ответ (первый)**: `interval_level -= 1` (но не меньше -1)
- **Неправильный ответ (второй подряд)**: `interval_level = -1` (сброс)

---

## 🚀 Логика немедленного изучения

### Принцип
Новые слова, отвеченные правильно с первого раза, сразу считаются выученными.

### Условие (backend/app/services/spaced_repetition.py:225-229)
```python
is_truly_new_word = (
    current_level == -1 and                    # Новое слово (interval_level = -1)
    progress.get("correct_answers", 0) == 0 and # Никогда не было правильных ответов
    progress.get("incorrect_answers", 0) == 0   # Никогда не было неправильных ответов
)
```

### Результат для новых слов
```python
if is_truly_new_word:
    new_level = MAX_INTERVAL_LEVEL              # 15
    progress["interval_level"] = new_level      # 15
    progress["is_learned"] = True               # True
    progress["next_review"] = now + timedelta(days=365)  # Через год
```

### Важно!
- Слова с предыдущими ошибками НЕ считаются "новыми"
- Только первый правильный ответ без предыдущих попыток активирует эту логику

---

## 🔄 Система предзагрузки

### Цель
Обеспечить мгновенные переходы между карточками без задержек загрузки.

### Компоненты

#### 1. CardPreloader (frontend/services/cardPreloader.ts)
- Загружает следующую карточку в фоне
- Кэширует результат для мгновенного использования
- Фильтрует дублирующиеся карточки

#### 2. Ключевые методы
```typescript
startPreloading(userId, nativeLang, targetLang, currentCardId)
getPreloadedCard()
waitForPreload(timeout)
```

#### 3. Фильтрация дублирования
```typescript
// 🎯 НОВОЕ РЕШЕНИЕ: Исключение на уровне API
const params = new URLSearchParams({
    user_id: userId,
    native_lang: nativeLang,
    target_lang: targetLang,
    preload: 'true',
    exclude_card_id: currentCardId  // ← Исключаем текущую карточку
});

// Проверка при использовании (fallback)
if (currentCardId && preloadedCardId === currentCardId) {
    // Игнорируем предзагруженную карточку, загружаем новую
    nextCardPromise = fetchNewCard();
}
```

### Backend поддержка
```python
# В get_next_word()
if preload:
    # Для предзагрузки НЕ добавляем слово в прогресс
    prepared = await self._prepare_word_data(new_word, is_new=True, is_forced=False)
    return prepared
```

### Важно!
- Предзагрузка НЕ создает записи в `user_progress`
- Используется параметр `preload=true` в API запросах
- Альтернативные карточки тоже запрашиваются с `preload=true`

---

## ❌ Обработка ошибок

### Неправильные ответы
1. **Первый неправильный ответ**: `interval_level -= 1`
2. **Второй неправильный ответ подряд**: `interval_level = -1`
3. **Форсированная очередь**: `force_review = True`

### Локальная обработка на фронтенде
```typescript
// При неправильном ответе - повторяем текущую карточку
if (!isCorrect) {
    // Показываем ту же карточку снова
    // НЕ запрашиваем новую с сервера
}
```

---

## 💾 База данных

### Коллекция: user_progress
```javascript
{
    user_id: ObjectId,
    word_id: ObjectId,
    interval_level: Number,     // -1 до 15
    correct_answers: Number,
    incorrect_answers: Number,
    last_reviewed: Date,
    next_review: Date,
    is_learned: Boolean,        // true при interval_level >= 15
    force_review: Boolean,      // true для немедленного повторения
    created_at: Date,
    updated_at: Date
}
```

### Индексы
- `{user_id: 1, word_id: 1}` - уникальный
- `{user_id: 1, next_review: 1}` - для поиска активных слов
- `{user_id: 1, force_review: 1}` - для форсированной очереди

---

## 🎨 Фронтенд интеграция

### TrainingScreen.tsx - Основные моменты

#### 1. Запуск предзагрузки
```typescript
// После загрузки карточки
setTimeout(() => {
    if (currentCard) {
        const currentCardId = currentCard.word_id || currentCard.id || currentCard._id;
        startPreloading(user._id, nativeLang, actualTargetLang, currentCardId);
    }
}, 200);
```

#### 2. Использование предзагруженных карточек
```typescript
// При переходе к следующей карточке
if (preloadedCard && !isDuplicate) {
    nextCardPromise = Promise.resolve(preloadedCard);
} else {
    nextCardPromise = fetchNewCard();
}
```

#### 3. Обработка немедленного изучения
```typescript
if (isNewWordCorrect) {
    // Мгновенно обновляем UI на 5 зеленых полосок
    // Сервер вернет interval_level: 15, is_learned: true
}
```

### Важные детали
- Предзагрузка запускается ДВА раза: после ответа и после загрузки карточки
- Фокус автоматически устанавливается на поле ввода
- Анимации длятся ~400ms для плавности
- Логирование помогает отслеживать производительность

---

## 🔍 Отладка и мониторинг

### Логирование
Система использует централизованное логирование с категориями:
- `CARD_LOADING` - загрузка карточек
- `PRELOADER` - предзагрузка
- `USER_INPUT` - ввод пользователя
- `PROGRESS` - прогресс изучения
- `API` - API запросы
- `ANIMATION` - анимации

### Ключевые метрики
- Время загрузки карточек
- Время предзагрузки
- Успешность предзагрузки
- Дублирование карточек
- Время ответа пользователя

### Тестирование
- `test_immediate_learning_fix.py` - тест немедленного изучения
- `test_preload_progress_fix.py` - тест предзагрузки
- Автоматические тесты покрывают основные сценарии

---

## ⚠️ Известные проблемы и решения

### 1. Дублирование карточек
**Проблема**: Предзагрузка возвращала ту же карточку из-за случайной выборки
**Решение**: Исключение текущей карточки на уровне API через параметр `exclude_card_id`

### 2. Предзагрузка создавала прогресс
**Проблема**: Предзагруженные слова не считались "новыми"
**Решение**: Флаг `preload=true` + отдельная логика в `get_next_word()`

### 3. Неправильный interval_level для новых слов
**Проблема**: `process_answer` создавал записи с `interval_level: 0`
**Решение**: Изменено на `interval_level: -1` для новых записей

### 4. Перезапись is_learned
**Проблема**: Логика в конце `process_answer` перезаписывала `is_learned`
**Решение**: Удалена строка `progress["is_learned"] = progress["interval_level"] >= MAX_INTERVAL_LEVEL`

---

## 🚀 Производительность

### Достигнутые результаты
- **Мгновенные переходы**: ~400ms (только анимация)
- **Предзагрузка**: 1.3-1.9s в фоне
- **Нет дублирования**: 100% фильтрация
- **Немедленное изучение**: работает для всех новых слов

### Оптимизации
- Кэширование слов и прогресса
- Агрегационные запросы MongoDB
- Предзагрузка в фоне
- Локальная обработка ошибок

---

## 📚 Дополнительные ресурсы

- [API документация](../app/routers/spaced.py)
- [Схемы данных](../app/schemas/)
- [Тесты](../tests/)
- [Конфигурация](../app/config.py)

---

**Последнее обновление**: 16 июня 2025
**Версия системы**: 2.1 (с исключением карточек в предзагрузке)
