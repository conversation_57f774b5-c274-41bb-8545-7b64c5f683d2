# Документация Backend

Эта папка содержит техническую документацию по бэкенду приложения Word Master.

## 📚 Доступная документация

### [📖 Система Spaced Repetition](SPACED_REPETITION_SYSTEM.md)
**Полная документация по алгоритму интервального повторения**

Включает:
- Архитектуру системы (бэкенд + фронтенд)
- Интервалы повторения (15 уровней)
- Логику немедленного изучения новых слов
- Систему предзагрузки карточек
- Обработку ошибок и дублирования
- Структуру базы данных
- Фронтенд интеграцию
- Отладку и мониторинг
- Известные проблемы и их решения

**⚠️ ВАЖНО:** Эта документация критически важна для понимания того, как работает система обучения. Часть логики обрабатывается на фронтенде!

---

## 🎯 Для разработчиков

### При работе с системой spaced repetition:
1. **Всегда читайте документацию** перед внесением изменений
2. **Помните о гибридной архитектуре** - логика разделена между фронтендом и бэкендом
3. **Тестируйте изменения** с помощью существующих тестов
4. **Обновляйте документацию** при внесении изменений

### При отладке проблем:
1. Проверьте раздел "Известные проблемы и решения"
2. Используйте централизованное логирование
3. Тестируйте как бэкенд, так и фронтенд компоненты

---

## 📝 Обновление документации

При внесении изменений в систему spaced repetition:
1. Обновите соответствующие разделы в `SPACED_REPETITION_SYSTEM.md`
2. Добавьте новые проблемы и решения в раздел "Известные проблемы"
3. Обновите примеры кода, если они изменились
4. Проверьте актуальность ссылок и констант

---

**Последнее обновление**: 16 июня 2025
