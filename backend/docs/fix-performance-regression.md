# Исправление регрессии производительности

## Критическая проблема
После изменения буфера времени с 5 минут на 2 минуты, времена загрузки карточек стали катастрофическими:
- **15247ms** (15.2 секунды!) для "ikaw"
- **10737ms** (10.7 секунд!) для "unsa"

## Причина регрессии
**Буфер 2 минуты оказался слишком строгим** для карточек с интервалом 30 секунд:

### Временная линия проблемы:
1. **17:47:57** - "oo" получила `next_review: 09:48:28` (30 секунд)
2. **17:48:50** - Предзагрузчик ищет карточки готовые **2 минуты назад** (`<= 09:46:50`)
3. **"oo" НЕ подходит** под критерий (готова только через 30 секунд)
4. **Система переключается на медленную обычную загрузку** → **15+ секунд!**

## Анализ буферов времени

### Интервалы spaced repetition:
- **30 секунд** (уровень 0) - самый частый интервал после неправильного ответа
- **2 минуты** (уровень 1) - второй по частоте
- **10 минут** (уровень 2) - реже
- **1+ час** (уровень 3+) - редко

### Проблема с буфером 2 минуты:
- ❌ **30 секунд** - НЕ предзагружается (слишком свежие)
- ✅ **2 минуты** - предзагружается
- ✅ **10+ минут** - предзагружается

**Результат**: Большинство карточек (уровень 0) НЕ предзагружаются → медленная загрузка

## Решение: Буфер 1 минута

```python
# ОПТИМАЛЬНЫЙ БАЛАНС:
if preload:
    # Карточка должна быть готова минимум 1 минуту назад
    # Баланс между производительностью и безопасностью от race conditions
    buffer_time = datetime.utcnow() - timedelta(minutes=1)
    query["next_review"] = {"$lte": buffer_time}
```

### Что это означает:
- ❌ **30 секунд** - НЕ предзагружается (безопасность)
- ✅ **2 минуты** - предзагружается (производительность)
- ✅ **10+ минут** - предзагружается (производительность)

## Ожидаемый результат
1. ✅ **Карточки с интервалом 2+ минут** предзагружаются быстро
2. ✅ **Безопасность от race conditions** сохранена
3. ✅ **Производительность восстановлена** - нет 15-секундных загрузок
4. ⚠️ **Карточки с интервалом 30 секунд** все еще не предзагружаются (компромисс)

## Логика компромисса
**Почему именно 1 минута:**
- **Слишком мало (30 сек)**: риск race conditions
- **Слишком много (2+ мин)**: плохая производительность для частых интервалов
- **1 минута**: оптимальный баланс безопасности и производительности

## Альтернативное решение (для будущего)
Если нужна предзагрузка карточек с интервалом 30 секунд, можно:
1. Добавить специальную логику для исключения текущей карточки
2. Использовать более сложную систему блокировок
3. Реализовать queue-based подход вместо time-based буфера
