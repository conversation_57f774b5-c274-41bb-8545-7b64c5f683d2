# 📋 Система логирования WordMaster

## 🎯 Общие принципы

Все логи в приложении должны соответствовать единой системе логирования для обеспечения читаемости и удобства отладки.

### ✅ Основные требования:

1. **Временные метки**: Формат `YYYY-MM-DD HH:MM:SS` (без миллисекунд)
2. **Уровни логирования**: INFO, SUCCESS, WARNING, ERROR, DEBUG, TIMING
3. **Категории**: Четкое разделение по функциональным областям
4. **Язык**: Русский язык для сообщений
5. **Эмодзи**: Визуальные индикаторы для быстрого понимания
6. **Краткость**: Без избыточной информации, только ключевые данные

## 📝 Формат логов

```
YYYY-MM-DD HH:MM:SS LEVEL 🔥 🧠 Сообщение (дополнительные данные)
```

**Пример:**
```
2025-06-17 14:30:45 INFO ℹ️ 🧠 Поиск слова для 3b5c0d62 (основной запрос) (cb)
2025-06-17 14:30:46 SUCCESS ✅ 🧠 Найдено слово "unsa" из новые слова (1378.5ms)
2025-06-17 14:30:47 INFO ℹ️ 🧠 3b5c0d62: "unsa" - правильный (new_level: 15)
```

## 🏷️ Уровни логирования

| Уровень | Эмодзи | Описание | Когда использовать |
|---------|--------|----------|-------------------|
| `DEBUG` | 🔍 | Отладочная информация | Детальная диагностика |
| `INFO` | ℹ️ | Информационные сообщения | Обычный ход выполнения |
| `SUCCESS` | ✅ | Успешные операции | Завершение операций |
| `WARNING` | ⚠️ | Предупреждения | Потенциальные проблемы |
| `ERROR` | ❌ | Ошибки | Критические проблемы |
| `TIMING` | ⏱️ | Измерение производительности | Время выполнения |

## 📂 Категории логирования

| Категория | Эмодзи | Описание |
|-----------|--------|----------|
| `SYSTEM` | 🚀 | Системные события (запуск, остановка) |
| `SPACED_REP` | 🧠 | Система интервального повторения |
| `DATABASE` | 🗄️ | Операции с базой данных |
| `API` | 🌐 | HTTP API запросы и ответы |
| `CACHE` | 💾 | Кэширование данных |
| `AUTH` | 🔐 | Аутентификация и авторизация |
| `PERFORMANCE` | ⚡ | Производительность и оптимизация |

## 🛠️ Использование

### Импорт
```python
from app.utils.logger import logger, LogCategory
```

### Основные методы
```python
# Общие методы
logger.info(LogCategory.SYSTEM, "Сообщение")
logger.success(LogCategory.DATABASE, "Операция завершена")
logger.warning(LogCategory.API, "Медленный запрос")
logger.error(LogCategory.SPACED_REP, "Ошибка обработки")

# Специализированные методы
logger.spaced_rep_start(user_id, target_lang, preload=False)
logger.spaced_rep_found(word, source, duration_ms)
logger.api_request("GET", "/endpoint", user_id)
logger.api_response("/endpoint", 200, duration_ms)
```

### Дополнительные данные
```python
# С одним параметром
logger.info(LogCategory.SPACED_REP, "Операция", {"duration": "150ms"})

# С несколькими параметрами  
logger.info(LogCategory.API, "Запрос", {"method": "POST", "status": 200})
```

## 📊 Примеры логов по категориям

### 🚀 SYSTEM
```
2025-06-17 14:30:45 INFO ℹ️ 🚀 🚀 Запуск приложения...
2025-06-17 14:30:46 INFO ℹ️ 🚀 ✅ MongoDB подключена: word_master (слова), users_db (пользователи)
2025-06-17 14:30:46 INFO ℹ️ 🚀 🎉 Приложение готово к работе!
```

### 🧠 SPACED_REP
```
2025-06-17 14:31:15 INFO ℹ️ 🧠 Поиск слова для 3b5c0d62 (основной запрос) (cb)
2025-06-17 14:31:16 SUCCESS ✅ 🧠 Найдено слово "unsa" из новые слова (1378.5ms)
2025-06-17 14:31:25 INFO ℹ️ 🧠 3b5c0d62: "unsa" - правильный (new_level: 15)
```

### 🌐 API
```
2025-06-17 14:31:15 INFO ℹ️ 🌐 GET /spaced/next от 3b5c0d62
2025-06-17 14:31:16 SUCCESS ✅ 🌐 Ответ /spaced/next (200) (1379.2ms)
2025-06-17 14:31:20 WARNING ⚠️ 🌐 🐌 Очень медленный запрос: POST /spaced/response (2150ms)
```

## 🚫 Что НЕ логировать

- ❌ Избыточные промежуточные шаги
- ❌ Повторяющиеся операции без ошибок
- ❌ Детали внутренней реализации
- ❌ Персональные данные пользователей
- ❌ Пароли и токены

## ⚡ Производительность

- Логируем медленные операции >500ms для spaced repetition
- Логируем очень медленные запросы >2000ms для API
- Используем кэширование для часто запрашиваемых данных
- Группируем связанные операции

## 🔧 Настройка

Логирование можно отключить через переменную окружения:
```bash
ENABLE_LOGS=false
```

По умолчанию логирование включено (`ENABLE_LOGS=true`).
