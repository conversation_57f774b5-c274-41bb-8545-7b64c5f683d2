# Исправление дублирования карточек в spaced repetition

## Проблема
Карточка "kaon" появилась дважды в одной тренировке:
1. 1-я карточка: "kaon" → `intervalLevel: 15`, `is_learned: true`
2. 4-я карточка: "kaon" → `intervalLevel: 16`, `is_learned: true`

## Причина
**Неполная логика установки `is_learned` в обычной ветке алгоритма.**

### Логика до исправления:
```python
if is_truly_new_word:
    # Новое слово → interval_level = 15, is_learned = true
else:
    # Обычная логика → interval_level++, is_learned НЕ УСТАНАВЛИВАЕТСЯ
```

### Что происходило:
1. **1-й ответ**: "kaon" (новое слово) → `interval_level: 15`, `is_learned: true`
2. **Система считала слово выученным**, но при втором показе:
3. **2-й ответ**: "kaon" (уже НЕ новое) → попало в обычную логику → `interval_level: 16`, `is_learned` осталось `true`

## Исправление
Добавлена проверка максимального уровня в обычной логике:

```python
else:
    # Обычная логика для уже изучаемых слов
    new_level = current_level + 1
    progress["interval_level"] = new_level

    # Проверяем, достигли ли максимального уровня (слово выучено)
    if new_level >= MAX_INTERVAL_LEVEL:
        progress["is_learned"] = True
        # Устанавливаем далекую дату следующего повторения
        progress["next_review"] = now + timedelta(days=365)
        logger.info(LogCategory.SPACED_REP, f"Слово достигло максимального уровня и считается выученным! Уровень: {current_level} -> {new_level}")
    else:
        # Вычисляем следующий интервал для неполностью выученных слов
        level = max(0, min(new_level, len(REPETITION_INTERVALS) - 1))
        interval_minutes = REPETITION_INTERVALS[level]
        progress["next_review"] = now + timedelta(minutes=interval_minutes)
```

## Результат
✅ Слова с `is_learned: true` больше не попадают в активную очередь  
✅ Дублирование карточек устранено  
✅ Новые слова правильно отмечаются как выученные с первого раза  
✅ Старые слова правильно отмечаются как выученные при достижении максимального уровня  

## Логи для отладки
- `Новое слово выучено с первого раза! Уровень: -1 -> 15`
- `Слово достигло максимального уровня и считается выученным! Уровень: 14 -> 15`
