# Исправление игнорирования активной очереди предзагрузчиком

## Проблема
Карточка "ikaw" должна была появиться через 30 секунд после правильного ответа, но появилась только через 34 секунды с опозданием. Предзагрузчик игнорировал активную очередь и загружал только новые слова.

## Временная линия проблемы:
1. **17:32:03** - "ikaw" получила `interval_level: 0`, `next_review: 09:32:34` (30 секунд)
2. **17:32:04** - Предзагрузчик ищет карточки готовые **5 минут назад** (`<= 09:27:04`)
3. **"ikaw" НЕ подходит** под критерий (готова только через 30 секунд)
4. **Предзагрузчик берет новые слова** вместо активной очереди
5. **17:33:08** - "ikaw" наконец появляется (с опозданием на 34 секунды)

## Причина
**Слишком строгий буфер времени в предзагрузчике:**

```python
# БЫЛО (строгие критерии):
if preload:
    # Карточка должна быть готова минимум 5 минут назад
    buffer_time = datetime.utcnow() - timedelta(minutes=5)
    query["next_review"] = {"$lte": buffer_time}
```

**Логика была неправильной:**
- Карточки с интервалом 30 секунд никогда не попадали в предзагрузку
- Карточки с интервалом 2 минуты тоже не попадали
- Только карточки старше 5 минут предзагружались

## Исправление
**Уменьшили буфер времени с 5 минут до 2 минут:**

```python
# СТАЛО (умеренно строгие критерии):
if preload:
    # Карточка должна быть готова минимум 2 минуты назад
    # Это позволяет предзагружать карточки с интервалом 2+ минут, но исключает карточки с интервалом 30 секунд
    # Компромисс между производительностью и безопасностью от race conditions
    buffer_time = datetime.utcnow() - timedelta(minutes=2)
    query["next_review"] = {"$lte": buffer_time}
```

## Почему 2 минуты, а не 30 секунд?

**Потенциальные риски 30 секунд:**
1. **Race conditions** между обычной загрузкой и предзагрузкой
2. **Дублирование карточек** при быстрых переходах
3. **Конфликты** в многопользовательской среде

**Компромисс 2 минуты:**
- ✅ Карточки с интервалом 2+ минут предзагружаются
- ✅ Карточки с интервалом 30 секунд исключены (безопасность)
- ✅ Большинство активных карточек все равно попадают в предзагрузку
- ✅ Снижен риск race conditions

## Результат
✅ **Карточки из активной очереди** с интервалом 2+ минут предзагружаются
✅ **Приоритет активной очереди** частично восстановлен над новыми словами
✅ **Безопасность от race conditions** сохранена
✅ **Большинство карточек** появляются вовремя без задержек

⚠️ **Компромисс**: Карточки с интервалом 30 секунд все еще НЕ предзагружаются (для безопасности)

## Логика приоритетов (частично восстановлена):
1. **Форсированная очередь** (неправильные ответы) - высший приоритет
2. **Активная очередь** (готовые 2+ минут назад) - средний приоритет
3. **Новые слова** - низший приоритет

## Тестирование
- Карточка с интервалом 30 секунд: появится когда придет время (не предзагружается)
- Карточка с интервалом 2+ минут: должна предзагружаться и появляться быстро

## Интервалы и предзагрузка:
- **30 секунд** (уровень 0): НЕ предзагружается ⚠️
- **2 минуты** (уровень 1): предзагружается ✅
- **10 минут** (уровень 2): предзагружается ✅
- **1+ час** (уровень 3+): предзагружается ✅
