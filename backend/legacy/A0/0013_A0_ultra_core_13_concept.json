{"concept_id": "550e8400-e29b-41d4-a716-446655440013", "level": "A0", "priority": "ultra_core", "category": "professions", "concept_name": "medical_professional", "description": {"en": "Medical professional who treats illnesses and injuries", "ru": "Медицинский специалист, который лечит болезни и травмы"}, "semantic_field": "healthcare", "usage_context": "medical_emergency", "examples": {"en": "I want doctor, I need doctor", "ru": "Я хочу врач, Мне нужно врач"}, "translation_notes": {"general": "Use the most common and basic form of 'doctor' in each language", "specific": {"asian_languages": "Focus on natural expression that works across different grammatical structures", "european_languages": "Use nominative case for the target word when possible"}}, "difficulty_level": "beginner", "frequency": "high", "cultural_notes": "Universal concept across all cultures", "related_concepts": [], "antonyms": [], "created_date": "2024-06-26", "last_updated": "2024-06-26"}