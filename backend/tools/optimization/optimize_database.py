#!/usr/bin/env python3
"""
Скрипт для оптимизации базы данных MongoDB.
Создает необходимые индексы для улучшения производительности.
"""

import asyncio
import os
from pathlib import Path
from dotenv import load_dotenv
from motor.motor_asyncio import AsyncIOMotorClient

# Загружаем переменные окружения
env_path = Path(__file__).parent / '.env'
if env_path.exists():
    load_dotenv(dotenv_path=env_path)

async def create_indexes():
    """Создает все необходимые индексы для оптимизации производительности."""
    
    mongo_uri = os.getenv("MONGODB_URL")
    if not mongo_uri:
        print("❌ Ошибка: MONGODB_URL не найден в переменных окружения")
        return False
    
    try:
        # Подключаемся к MongoDB
        client = AsyncIOMotorClient(mongo_uri)
        
        # Получаем базы данных
        words_db = client.get_database("word_master")
        users_db = client.get_database("users_db")
        
        print("🔗 Подключение к MongoDB установлено")
        
        # === Индексы для коллекции words ===
        print("\n📚 Создание индексов для коллекции 'words'...")
        
        words_collection = words_db.words
        
        # Уникальный индекс для слова + язык
        await words_collection.create_index(
            [("word", 1), ("language", 1)], 
            unique=True, 
            background=True,
            name="word_language_unique"
        )
        print("✅ Создан уникальный индекс: word + language")
        
        # Индекс для concept_id
        await words_collection.create_index(
            "concept_id", 
            background=True,
            name="concept_id_idx"
        )
        print("✅ Создан индекс: concept_id")
        
        # Индекс для языка (для быстрой фильтрации)
        await words_collection.create_index(
            "language", 
            background=True,
            name="language_idx"
        )
        print("✅ Создан индекс: language")
        
        # Индекс для уровня сложности
        await words_collection.create_index(
            "level", 
            background=True,
            name="level_idx"
        )
        print("✅ Создан индекс: level")
        
        # Составной индекс для A0 приоритета
        await words_collection.create_index(
            [("language", 1), ("priority", 1)], 
            background=True,
            name="language_priority_idx"
        )
        print("✅ Создан составной индекс: language + priority")
        
        # === Индексы для коллекции user_progress ===
        print("\n📈 Создание индексов для коллекции 'user_progress'...")
        
        user_progress_collection = words_db.user_progress
        
        # Уникальный индекс для user_id + word_id
        await user_progress_collection.create_index(
            [("user_id", 1), ("word_id", 1)], 
            unique=True, 
            background=True,
            name="user_word_unique"
        )
        print("✅ Создан уникальный индекс: user_id + word_id")
        
        # Индекс для поиска слов на повторение
        await user_progress_collection.create_index(
            [("user_id", 1), ("next_review", 1), ("force_review", 1)], 
            background=True,
            name="user_next_review_force_idx"
        )
        print("✅ Создан составной индекс: user_id + next_review + force_review")
        
        # Индекс для поиска выученных слов
        await user_progress_collection.create_index(
            [("user_id", 1), ("is_learned", 1)], 
            background=True,
            name="user_learned_idx"
        )
        print("✅ Создан составной индекс: user_id + is_learned")
        
        # Индекс для форсированной очереди
        await user_progress_collection.create_index(
            [("user_id", 1), ("force_review", 1), ("next_review", 1)], 
            background=True,
            name="user_force_review_idx"
        )
        print("✅ Создан составной индекс: user_id + force_review + next_review")
        
        # Индекс для активной очереди
        await user_progress_collection.create_index(
            [("user_id", 1), ("next_review", 1), ("is_learned", 1)], 
            background=True,
            name="user_active_queue_idx"
        )
        print("✅ Создан составной индекс: user_id + next_review + is_learned")
        
        # === Индексы для коллекции users ===
        print("\n👥 Создание индексов для коллекции 'users'...")
        
        users_collection = users_db.users
        
        # Уникальный индекс для email
        await users_collection.create_index(
            "email", 
            unique=True, 
            background=True,
            name="email_unique"
        )
        print("✅ Создан уникальный индекс: email")
        
        # === Проверка созданных индексов ===
        print("\n🔍 Проверка созданных индексов...")
        
        # Проверяем индексы words
        words_indexes = await words_collection.index_information()
        print(f"\n📚 Индексы коллекции 'words' ({len(words_indexes)}):")
        for name, info in words_indexes.items():
            print(f"  - {name}: {info.get('key', 'N/A')}")
        
        # Проверяем индексы user_progress
        progress_indexes = await user_progress_collection.index_information()
        print(f"\n📈 Индексы коллекции 'user_progress' ({len(progress_indexes)}):")
        for name, info in progress_indexes.items():
            print(f"  - {name}: {info.get('key', 'N/A')}")
        
        # Проверяем индексы users
        users_indexes = await users_collection.index_information()
        print(f"\n👥 Индексы коллекции 'users' ({len(users_indexes)}):")
        for name, info in users_indexes.items():
            print(f"  - {name}: {info.get('key', 'N/A')}")
        
        print("\n✅ Оптимизация базы данных завершена успешно!")
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при создании индексов: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if 'client' in locals():
            client.close()

async def analyze_performance():
    """Анализирует производительность запросов."""
    
    mongo_uri = os.getenv("MONGODB_URL")
    if not mongo_uri:
        print("❌ Ошибка: MONGODB_URL не найден в переменных окружения")
        return
    
    try:
        client = AsyncIOMotorClient(mongo_uri)
        words_db = client.get_database("word_master")
        
        print("\n📊 Анализ производительности...")
        
        # Статистика коллекций
        words_count = await words_db.words.count_documents({})
        progress_count = await words_db.user_progress.count_documents({})
        
        print(f"📚 Слов в базе: {words_count:,}")
        print(f"📈 Записей прогресса: {progress_count:,}")
        
        # Анализ размера коллекций
        words_stats = await words_db.command("collStats", "words")
        progress_stats = await words_db.command("collStats", "user_progress")
        
        print(f"💾 Размер коллекции words: {words_stats.get('size', 0) / 1024 / 1024:.2f} MB")
        print(f"💾 Размер коллекции user_progress: {progress_stats.get('size', 0) / 1024 / 1024:.2f} MB")
        
        # Анализ индексов
        words_index_stats = await words_db.command("collStats", "words", indexDetails=True)
        progress_index_stats = await words_db.command("collStats", "user_progress", indexDetails=True)
        
        print(f"🔍 Размер индексов words: {words_index_stats.get('totalIndexSize', 0) / 1024 / 1024:.2f} MB")
        print(f"🔍 Размер индексов user_progress: {progress_index_stats.get('totalIndexSize', 0) / 1024 / 1024:.2f} MB")
        
    except Exception as e:
        print(f"❌ Ошибка при анализе производительности: {e}")
    finally:
        if 'client' in locals():
            client.close()

async def main():
    """Главная функция."""
    print("🚀 Запуск оптимизации базы данных MongoDB")
    print("=" * 50)
    
    # Создаем индексы
    success = await create_indexes()
    
    if success:
        # Анализируем производительность
        await analyze_performance()
        
        print("\n" + "=" * 50)
        print("🎉 Оптимизация завершена!")
        print("\n💡 Рекомендации:")
        print("  - Регулярно мониторьте производительность запросов")
        print("  - Используйте explain() для анализа планов выполнения")
        print("  - Рассмотрите возможность шардинга при росте данных")
    else:
        print("\n❌ Оптимизация не удалась")

if __name__ == "__main__":
    asyncio.run(main())
