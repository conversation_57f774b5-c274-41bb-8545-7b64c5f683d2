#!/usr/bin/env python3
"""
Скрипт для очистки кэша и тестирования новых интервалов.
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta
from bson import ObjectId

# Добавляем путь к корню проекта
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.spaced_repetition import SpacedRepetitionService, REPETITION_INTERVALS

async def clear_cache_and_test():
    """Очищает кэш и тестирует новые интервалы."""
    print("🧹 ОЧИСТКА КЭША И ТЕСТИРОВАНИЕ ИНТЕРВАЛОВ")
    print("=" * 50)
    
    # Очищаем весь кэш
    try:
        from app.services.cache_service import cache_service
        await cache_service.clear()
        print("✅ Кэш очищен")
    except Exception as e:
        print(f"⚠️ Не удалось очистить кэш: {e}")
        print("Продолжаем без очистки кэша...")
    
    # Выводим текущие интервалы
    print("\n📋 Текущие интервалы из REPETITION_INTERVALS:")
    for i, interval in enumerate(REPETITION_INTERVALS[:5]):  # Первые 5
        if interval < 1:
            print(f"   Уровень {i}: {interval * 60:.0f} секунд")
        elif interval < 60:
            print(f"   Уровень {i}: {interval} минут")
        else:
            print(f"   Уровень {i}: {interval / 60:.1f} часов")
    
    # Создаем сервис
    service = SpacedRepetitionService()
    
    # Тестируем создание нового прогресса
    print("\n🧪 Тестируем создание нового прогресса:")
    
    # Создаем тестовые ID
    test_user_id = ObjectId()
    test_word_id = ObjectId()
    
    print(f"Тестовый пользователь: {test_user_id}")
    print(f"Тестовое слово: {test_word_id}")
    
    # Симулируем неправильный ответ (создание записи)
    print("\n1️⃣ Симулируем неправильный ответ (создание записи):")
    try:
        progress1 = await service.process_answer(test_user_id, test_word_id, False)
        print(f"   interval_level: {progress1.get('interval_level')}")
        print(f"   force_review: {progress1.get('force_review')}")
        print(f"   next_review: {progress1.get('next_review')}")
    except Exception as e:
        print(f"   ❌ Ошибка: {e}")
    
    # Симулируем правильный ответ
    print("\n2️⃣ Симулируем правильный ответ (переход на уровень 0):")
    try:
        progress2 = await service.process_answer(test_user_id, test_word_id, True)
        print(f"   interval_level: {progress2.get('interval_level')}")
        print(f"   force_review: {progress2.get('force_review')}")
        print(f"   next_review: {progress2.get('next_review')}")
        
        # Вычисляем интервал
        if progress2.get('next_review'):
            now = datetime.utcnow()
            diff_minutes = (progress2['next_review'] - now).total_seconds() / 60
            print(f"   Интервал до следующего повторения: {diff_minutes:.2f} минут")
            
            if abs(diff_minutes - 0.5) < 0.1:  # 30 секунд = 0.5 минуты
                print("   ✅ ПРАВИЛЬНО: Интервал 30 секунд!")
            else:
                print(f"   ❌ НЕПРАВИЛЬНО: Ожидался интервал 0.5 мин, получен {diff_minutes:.2f} мин")
    except Exception as e:
        print(f"   ❌ Ошибка: {e}")
    
    # Очищаем тестовые данные
    try:
        collection = await service.user_progress_collection
        await collection.delete_many({"user_id": test_user_id})
        print("\n🧹 Тестовые данные очищены")
    except Exception as e:
        print(f"❌ Ошибка при очистке: {e}")
    
    print("\n" + "=" * 50)
    print("🏁 Тест завершен")

if __name__ == "__main__":
    asyncio.run(clear_cache_and_test())
