#!/usr/bin/env python3
"""
Скрипт для очистки кэша через API.
"""

import requests
import json

def clear_cache_via_api(base_url="http://localhost:8000"):
    """Очищает кэш через API."""
    print("🧹 ОЧИСТКА КЭША ЧЕРЕЗ API")
    print("=" * 40)
    
    try:
        # Получаем статистику до очистки
        print("\n📊 Статистика кэша до очистки:")
        stats_response = requests.get(f"{base_url}/api/cache/stats")
        if stats_response.status_code == 200:
            stats = stats_response.json()
            print(f"   Всего записей: {stats['stats']['total_entries']}")
            print(f"   Активных записей: {stats['stats']['active_entries']}")
            print(f"   Устаревших записей: {stats['stats']['expired_entries']}")
        else:
            print(f"   ❌ Ошибка получения статистики: {stats_response.status_code}")
        
        # Очищаем кэш
        print("\n🧹 Очищаем кэш...")
        clear_response = requests.post(f"{base_url}/api/cache/clear")
        
        if clear_response.status_code == 200:
            result = clear_response.json()
            print("✅ Кэш успешно очищен!")
            print(f"   Очищено записей: {result.get('cleared_entries', 'N/A')}")
            
            # Показываем статистику после очистки
            if 'stats_after' in result:
                stats_after = result['stats_after']
                print(f"   Записей после очистки: {stats_after['total_entries']}")
        else:
            print(f"❌ Ошибка очистки кэша: {clear_response.status_code}")
            print(f"   Ответ: {clear_response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Не удалось подключиться к серверу")
        print("   Убедитесь, что сервер запущен на http://localhost:8000")
    except Exception as e:
        print(f"❌ Ошибка: {e}")
    
    print("\n" + "=" * 40)
    print("🏁 Завершено")

if __name__ == "__main__":
    clear_cache_via_api()
