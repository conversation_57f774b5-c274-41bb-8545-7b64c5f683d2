"""
Анализ производительности запросов MongoDB для оптимизации $sample
"""
import asyncio
import os
import time
from dotenv import load_dotenv
from motor.motor_asyncio import AsyncIOMotorClient
from bson import ObjectId

# Загружаем переменные окружения
load_dotenv()

async def analyze_query_performance():
    """Анализируем производительность различных подходов к получению случайных слов"""
    try:
        # Подключаемся к MongoDB
        client = AsyncIOMotorClient(os.getenv("MONGODB_URL"))
        db = client.get_database("word_master")
        words_collection = db.words
        user_progress_collection = db.user_progress
        
        print("🔗 Подключение к MongoDB установлено")
        
        # Получаем статистику коллекций
        words_count = await words_collection.count_documents({})
        progress_count = await user_progress_collection.count_documents({})
        
        print(f"📊 Статистика коллекций:")
        print(f"  - words: {words_count:,} документов")
        print(f"  - user_progress: {progress_count:,} документов")
        
        # Проверяем индексы
        print(f"\n📋 Индексы коллекции words:")
        words_indexes = await words_collection.index_information()
        for name, info in words_indexes.items():
            print(f"  - {name}: {info.get('key', [])}")
            
        print(f"\n📋 Индексы коллекции user_progress:")
        progress_indexes = await user_progress_collection.index_information()
        for name, info in progress_indexes.items():
            print(f"  - {name}: {info.get('key', [])}")
        
        # Тестируем производительность различных подходов
        test_user_id = ObjectId("684c7900e9e41d7a3b5c0d62")  # Тестовый пользователь
        target_lang = "cb"
        
        print(f"\n🧪 Тестирование производительности запросов для пользователя {test_user_id}")
        print(f"   Целевой язык: {target_lang}")
        
        # 1. Текущий подход с $sample
        await test_current_approach(words_collection, user_progress_collection, test_user_id, target_lang)
        
        # 2. Альтернативный подход с skip + limit
        await test_skip_limit_approach(words_collection, user_progress_collection, test_user_id, target_lang)
        
        # 3. Подход с предварительным подсчетом
        await test_count_based_approach(words_collection, user_progress_collection, test_user_id, target_lang)
        
    except Exception as e:
        print(f"❌ Ошибка: {e}")
    finally:
        client.close()

async def test_current_approach(words_collection, user_progress_collection, user_id, target_lang):
    """Тестируем текущий подход с $sample"""
    print(f"\n🔍 1. Текущий подход с $sample")
    
    # Получаем список изученных слов (как в текущем коде)
    start_time = time.time()
    
    # Сначала находим все слова нужного языка
    target_lang_words = await words_collection.find(
        {"language": target_lang.lower()},
        {"_id": 1}
    ).to_list(None)
    target_lang_word_ids = [doc["_id"] for doc in target_lang_words]
    
    # Затем находим прогресс только по словам этого языка
    cursor = user_progress_collection.find(
        {
            "user_id": user_id,
            "word_id": {"$in": target_lang_word_ids}
        },
        {"word_id": 1, "_id": 0}
    )
    learned_word_ids = [doc["word_id"] async for doc in cursor]
    
    prep_time = time.time() - start_time
    
    # Выполняем основной запрос с $sample
    start_time = time.time()
    
    match_conditions = {
        "_id": {"$nin": learned_word_ids},
        "language": target_lang.lower(),
        "level": "A0",
        "priority": "ultra_core"
    }
    
    pipeline = [
        {"$match": match_conditions},
        {"$sample": {"size": 1}}
    ]
    
    cursor = words_collection.aggregate(pipeline)
    words = await cursor.to_list(length=1)
    
    query_time = time.time() - start_time
    total_time = prep_time + query_time
    
    print(f"   ⏱️  Подготовка данных: {prep_time*1000:.2f}ms")
    print(f"   ⏱️  Запрос $sample: {query_time*1000:.2f}ms")
    print(f"   ⏱️  Общее время: {total_time*1000:.2f}ms")
    print(f"   📊 Исключено слов: {len(learned_word_ids)}")
    print(f"   ✅ Найдено слов: {len(words)}")
    if words:
        print(f"   📝 Слово: {words[0].get('word', 'N/A')}")

async def test_skip_limit_approach(words_collection, user_progress_collection, user_id, target_lang):
    """Тестируем альтернативный подход с skip + limit"""
    print(f"\n🔍 2. Альтернативный подход с skip + limit")
    
    start_time = time.time()
    
    # Получаем список изученных слов (тот же код)
    target_lang_words = await words_collection.find(
        {"language": target_lang.lower()},
        {"_id": 1}
    ).to_list(None)
    target_lang_word_ids = [doc["_id"] for doc in target_lang_words]
    
    cursor = user_progress_collection.find(
        {
            "user_id": user_id,
            "word_id": {"$in": target_lang_word_ids}
        },
        {"word_id": 1, "_id": 0}
    )
    learned_word_ids = [doc["word_id"] async for doc in cursor]
    
    prep_time = time.time() - start_time
    
    # Альтернативный подход: сначала считаем, потом skip
    start_time = time.time()
    
    match_conditions = {
        "_id": {"$nin": learned_word_ids},
        "language": target_lang.lower(),
        "level": "A0",
        "priority": "ultra_core"
    }
    
    # Считаем общее количество доступных слов
    total_count = await words_collection.count_documents(match_conditions)
    
    if total_count > 0:
        import random
        skip_count = random.randint(0, total_count - 1)
        
        # Получаем одно случайное слово через skip
        cursor = words_collection.find(match_conditions).skip(skip_count).limit(1)
        words = await cursor.to_list(length=1)
    else:
        words = []
    
    query_time = time.time() - start_time
    total_time = prep_time + query_time
    
    print(f"   ⏱️  Подготовка данных: {prep_time*1000:.2f}ms")
    print(f"   ⏱️  Запрос skip+limit: {query_time*1000:.2f}ms")
    print(f"   ⏱️  Общее время: {total_time*1000:.2f}ms")
    print(f"   📊 Доступно слов: {total_count}")
    print(f"   ✅ Найдено слов: {len(words)}")
    if words:
        print(f"   📝 Слово: {words[0].get('word', 'N/A')}")

async def test_count_based_approach(words_collection, user_progress_collection, user_id, target_lang):
    """Тестируем подход с предварительным подсчетом и кэшированием"""
    print(f"\n🔍 3. Подход с кэшированием и оптимизацией")

    start_time = time.time()

    # Оптимизированный подход: используем агрегацию для получения только нужных ID
    pipeline = [
        {
            "$match": {
                "user_id": user_id,
                "word_id": {"$exists": True}
            }
        },
        {
            "$lookup": {
                "from": "words",
                "localField": "word_id",
                "foreignField": "_id",
                "as": "word_info"
            }
        },
        {
            "$match": {
                "word_info.language": target_lang.lower()
            }
        },
        {
            "$project": {
                "word_id": 1,
                "_id": 0
            }
        }
    ]

    cursor = user_progress_collection.aggregate(pipeline)
    learned_word_ids = [doc["word_id"] async for doc in cursor]

    prep_time = time.time() - start_time

    # Основной запрос
    start_time = time.time()

    match_conditions = {
        "_id": {"$nin": learned_word_ids},
        "language": target_lang.lower(),
        "level": "A0",
        "priority": "ultra_core"
    }

    # Используем $sample как в оригинале
    pipeline = [
        {"$match": match_conditions},
        {"$sample": {"size": 1}}
    ]

    cursor = words_collection.aggregate(pipeline)
    words = await cursor.to_list(length=1)

    query_time = time.time() - start_time
    total_time = prep_time + query_time

    print(f"   ⏱️  Подготовка (агрегация): {prep_time*1000:.2f}ms")
    print(f"   ⏱️  Запрос $sample: {query_time*1000:.2f}ms")
    print(f"   ⏱️  Общее время: {total_time*1000:.2f}ms")
    print(f"   📊 Исключено слов: {len(learned_word_ids)}")
    print(f"   ✅ Найдено слов: {len(words)}")
    if words:
        print(f"   📝 Слово: {words[0].get('word', 'N/A')}")

async def analyze_sample_performance():
    """Специальный анализ производительности $sample в разных условиях"""
    try:
        client = AsyncIOMotorClient(os.getenv("MONGODB_URL"))
        db = client.get_database("word_master")
        words_collection = db.words

        print(f"\n🔬 Детальный анализ производительности $sample")

        # Тест 1: $sample на всей коллекции
        start_time = time.time()
        pipeline = [{"$sample": {"size": 1}}]
        cursor = words_collection.aggregate(pipeline)
        words = await cursor.to_list(length=1)
        time1 = (time.time() - start_time) * 1000
        print(f"   1️⃣ $sample на всей коллекции: {time1:.2f}ms")

        # Тест 2: $sample с простым фильтром
        start_time = time.time()
        pipeline = [
            {"$match": {"language": "cb"}},
            {"$sample": {"size": 1}}
        ]
        cursor = words_collection.aggregate(pipeline)
        words = await cursor.to_list(length=1)
        time2 = (time.time() - start_time) * 1000
        print(f"   2️⃣ $sample с фильтром по языку: {time2:.2f}ms")

        # Тест 3: $sample с $nin (маленький список)
        start_time = time.time()
        small_exclude_list = [ObjectId() for _ in range(5)]  # 5 случайных ID
        pipeline = [
            {"$match": {"language": "cb", "_id": {"$nin": small_exclude_list}}},
            {"$sample": {"size": 1}}
        ]
        cursor = words_collection.aggregate(pipeline)
        words = await cursor.to_list(length=1)
        time3 = (time.time() - start_time) * 1000
        print(f"   3️⃣ $sample с $nin (5 элементов): {time3:.2f}ms")

        # Тест 4: $sample с $nin (большой список)
        start_time = time.time()
        large_exclude_list = [ObjectId() for _ in range(100)]  # 100 случайных ID
        pipeline = [
            {"$match": {"language": "cb", "_id": {"$nin": large_exclude_list}}},
            {"$sample": {"size": 1}}
        ]
        cursor = words_collection.aggregate(pipeline)
        words = await cursor.to_list(length=1)
        time4 = (time.time() - start_time) * 1000
        print(f"   4️⃣ $sample с $nin (100 элементов): {time4:.2f}ms")

        # Тест 5: Альтернатива - count + skip
        start_time = time.time()
        count = await words_collection.count_documents({"language": "cb"})
        if count > 0:
            import random
            skip_count = random.randint(0, count - 1)
            cursor = words_collection.find({"language": "cb"}).skip(skip_count).limit(1)
            words = await cursor.to_list(length=1)
        time5 = (time.time() - start_time) * 1000
        print(f"   5️⃣ count + skip + limit: {time5:.2f}ms")

        print(f"\n📈 Сравнение производительности:")
        print(f"   - Базовый $sample: {time1:.2f}ms (базовая линия)")
        print(f"   - С фильтром: {time2:.2f}ms ({time2/time1:.1f}x)")
        print(f"   - С $nin (5): {time3:.2f}ms ({time3/time1:.1f}x)")
        print(f"   - С $nin (100): {time4:.2f}ms ({time4/time1:.1f}x)")
        print(f"   - count+skip: {time5:.2f}ms ({time5/time1:.1f}x)")

    except Exception as e:
        print(f"❌ Ошибка в анализе $sample: {e}")
    finally:
        client.close()

if __name__ == "__main__":
    asyncio.run(analyze_query_performance())
    print("\n" + "="*50)
    asyncio.run(analyze_sample_performance())
