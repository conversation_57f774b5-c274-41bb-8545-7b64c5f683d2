"""
Скрипт для защиты коллекций от удаления.
Запускать при инициализации приложения.
"""
import os
from pymongo import MongoClient
from dotenv import load_dotenv

def protect_collections():
    load_dotenv()
    client = MongoClient(os.getenv("MONGODB_URL"))
    db = client[os.getenv("WORDS_DATABASE_NAME", "word_master")]
    
    # Создаем пользовательскую роль, если её нет
    try:
        admin_db = client.admin
        admin_db.command('createRole', 
                       'wordMasterRole',
                       privileges=[],
                       roles=['readWrite'])
        
        # Запрещаем удаление коллекции words
        admin_db.command({
            'revokePrivilegesFromRole': 'wordMasterRole',
            'privileges': [
                {
                    'resource': {'db': db.name, 'collection': 'words'},
                    'actions': ['dropCollection']
                }
            ]
        })
        print("✅ Защита коллекции 'words' активирована")
    except Exception as e:
        if 'already exists' in str(e):
            print("✅ Роль wordMasterRole уже существует")
        else:
            print(f"⚠️ Ошибка при настройке защиты: {e}")

if __name__ == "__main__":
    protect_collections()
