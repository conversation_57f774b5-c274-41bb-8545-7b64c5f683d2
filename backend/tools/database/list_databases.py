"""
Скрипт для вывода списка всех баз данных в MongoDB.
"""
import os
from pymongo import MongoClient
from dotenv import load_dotenv
from pathlib import Path

def main():
    # Загружаем переменные окружения
    env_path = Path(__file__).parent / '.env'
    if env_path.exists():
        load_dotenv(dotenv_path=env_path, override=True)
    
    # Получаем настройки подключения
    mongo_uri = os.getenv("MONGODB_URL")
    if not mongo_uri:
        print("Ошибка: Не задана переменная MONGODB_URL в .env файле")
        return
    
    print(f"Подключение к MongoDB: {mongo_uri}")
    
    try:
        # Подключаемся к MongoDB
        client = MongoClient(mongo_uri, serverSelectionTimeoutMS=5000)
        
        # Проверяем соединение
        client.admin.command('ping')
        print("✅ Успешное подключение к MongoDB")
        
        # Получаем список всех баз данных
        databases = client.list_database_names()
        
        print("\nДоступные базы данных:")
        for db_name in sorted(databases):
            if db_name not in ['admin', 'local', 'config']:  # Пропускаем служебные БД
                db = client[db_name]
                collections = db.list_collection_names()
                print(f"\n📁 {db_name}:")
                for col_name in collections:
                    count = db[col_name].count_documents({})
                    print(f"  └─ {col_name} ({count} документов)")
        
        print("\nСлужебные базы данных:")
        for db_name in sorted(databases):
            if db_name in ['admin', 'local', 'config']:
                print(f"- {db_name}")
        
    except Exception as e:
        print(f"❌ Ошибка: {e}")
    finally:
        if 'client' in locals():
            client.close()

if __name__ == "__main__":
    main()
