#!/usr/bin/env python3
"""
Скрипт для переименования базы данных в MongoDB.
Использование: python rename_database.py <старое_имя> <новое_имя>
"""
import os
import sys
from pymongo import MongoClient
from pymongo.errors import OperationFailure
from dotenv import load_dotenv
from pathlib import Path

def rename_database(old_name: str, new_name: str) -> bool:
    """
    Переименовывает базу данных в MongoDB.
    
    Args:
        old_name: Текущее имя базы данных
        new_name: Новое имя базы данных
        
    Returns:
        bool: True, если переименование прошло успешно, иначе False
    """
    # Загружаем переменные окружения
    env_path = Path(__file__).parent / '.env'
    if env_path.exists():
        load_dotenv(dotenv_path=env_path, override=True)
    
    mongo_uri = os.getenv("MONGODB_URL")
    if not mongo_uri:
        print("Ошибка: Не задана переменная MONGODB_URL в .env файле")
        return False
    
    try:
        # Подключаемся к MongoDB
        client = MongoClient(mongo_uri, serverSelectionTimeoutMS=5000)
        
        # Проверяем соединение
        client.admin.command('ping')
        print(f"✅ Успешное подключение к MongoDB")
        
        # Получаем список всех баз данных
        dbs = client.list_database_names()
        print(f"\nДоступные базы данных: {', '.join(dbs)}")
        
        # Проверяем существование старой базы данных
        if old_name not in dbs:
            print(f"\n❌ Ошибка: База данных '{old_name}' не найдена")
            return False
            
        # Проверяем, не существует ли уже новая база данных
        if new_name in dbs:
            print(f"\n❌ Ошибка: База данных '{new_name}' уже существует")
            return False
        
        print(f"\nНачинаем переименование базы данных '{old_name}' в '{new_name}'...")
        
        # Копируем базу данных
        client.admin.command('copydb',
                           fromdb=old_name,
                           todb=new_name)
        
        # Проверяем, что копия создана успешно
        if new_name in client.list_database_names():
            # Удаляем старую базу данных
            client.drop_database(old_name)
            print(f"✅ База данных успешно переименована из '{old_name}' в '{new_name}'")
            return True
        else:
            print("❌ Ошибка при создании копии базы данных")
            return False
            
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        return False
    finally:
        if 'client' in locals():
            client.close()

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Использование: python rename_database.py <старое_имя> <новое_имя>")
        sys.exit(1)
        
    old_name = sys.argv[1]
    new_name = sys.argv[2]
    
    if old_name == new_name:
        print("❌ Ошибка: Старое и новое имена совпадают")
        sys.exit(1)
        
    success = rename_database(old_name, new_name)
    sys.exit(0 if success else 1)
