# MongoDB Connection
MONGODB_URL=mongodb+srv://<username>:<password>@<cluster>.mongodb.net/?retryWrites=true&w=majority

# База данных для слов
WORDS_DATABASE_NAME=word_master

# База данных для пользователей
USERS_DATABASE_NAME=users_db

# JWT Settings
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=1440  # 24 hours

# App Settings
DEBUG=True
ENVIRONMENT=development
API_PREFIX=/api

# Email Settings (if needed)
# SMTP_SERVER=smtp.example.com
# SMTP_PORT=587
# EMAIL_USER=<EMAIL>
# EMAIL_PASSWORD=your-email-password
