#!/usr/bin/env python3
"""
Комплексный скрипт для исправления кода языка Себуано с "ceb" на "cb" 
в JSON файлах и коллекции MongoDB.
"""

import os
import sys
import json
import glob
from pathlib import Path
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure

# Добавляем путь к корневой директории проекта
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def fix_json_files():
    """Исправляет код языка Себуано в JSON файлах."""
    print("🔧 ЭТАП 1: Исправление JSON файлов")
    print("-" * 40)
    
    # Путь к папке с JSON файлами
    json_dir = Path(__file__).parent.parent / "data" / "words" / "A0"
    
    if not json_dir.exists():
        print(f"❌ Папка {json_dir} не найдена!")
        return False
    
    # Находим все JSON файлы
    json_files = list(json_dir.glob("*.json"))
    
    if not json_files:
        print("❌ JSON файлы не найдены!")
        return False
    
    print(f"📁 Найдено {len(json_files)} JSON файлов")
    
    total_changes = 0
    modified_files = []
    
    for json_file in json_files:
        try:
            # Читаем файл
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            file_changes = 0
            
            # Проверяем и исправляем каждую концепцию
            for concept in data.get('concepts', []):
                for word_key in ['words', 'translations']:
                    if word_key in concept:
                        for word in concept[word_key]:
                            if word.get('language') == 'ceb':
                                word['language'] = 'cb'
                                file_changes += 1
            
            # Если были изменения, сохраняем файл
            if file_changes > 0:
                with open(json_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                
                modified_files.append(json_file.name)
                total_changes += file_changes
                
        except Exception as e:
            print(f"❌ Ошибка при обработке файла {json_file.name}: {e}")
            return False
    
    print(f"✅ JSON файлы: {total_changes} изменений в {len(modified_files)} файлах")
    return True

def fix_mongodb():
    """Исправляет код языка Себуано в коллекции MongoDB."""
    print("\n🔧 ЭТАП 2: Исправление MongoDB")
    print("-" * 40)
    
    try:
        # Подключение к MongoDB
        client = MongoClient('mongodb://localhost:27017/')
        db = client['word_master']
        collection = db['words']
        
        # Проверяем количество документов с кодом "ceb"
        ceb_count = collection.count_documents({
            "$or": [
                {"native_word.language": "ceb"},
                {"target_word.language": "ceb"}
            ]
        })
        
        print(f"📊 Найдено документов с кодом 'ceb': {ceb_count}")
        
        if ceb_count == 0:
            print("✅ MongoDB: исправления не требуются")
            return True
        
        # Исправляем native_word.language
        result1 = collection.update_many(
            {"native_word.language": "ceb"},
            {"$set": {"native_word.language": "cb"}}
        )
        
        # Исправляем target_word.language
        result2 = collection.update_many(
            {"target_word.language": "ceb"},
            {"$set": {"target_word.language": "cb"}}
        )
        
        total_modified = result1.modified_count + result2.modified_count
        print(f"✅ MongoDB: {total_modified} документов обновлено")
        
        return True
        
    except ConnectionFailure:
        print("❌ Ошибка подключения к MongoDB")
        return False
    except Exception as e:
        print(f"❌ Ошибка MongoDB: {e}")
        return False
    finally:
        if 'client' in locals():
            client.close()

def reload_words_to_mongodb():
    """Перезагружает исправленные JSON файлы в MongoDB."""
    print("\n🔧 ЭТАП 3: Перезагрузка слов в MongoDB")
    print("-" * 40)
    
    try:
        # Импортируем и запускаем скрипт загрузки
        from load_words import main as load_words_main
        load_words_main()
        print("✅ Слова успешно перезагружены в MongoDB")
        return True
    except Exception as e:
        print(f"❌ Ошибка при перезагрузке слов: {e}")
        print("💡 Попробуйте запустить вручную: python backend/scripts/load_words.py")
        return False

def main():
    """Основная функция."""
    print("🔧 Комплексное исправление кода языка Себуано")
    print("=" * 60)
    
    # Этап 1: Исправление JSON файлов
    if not fix_json_files():
        print("❌ Ошибка на этапе исправления JSON файлов")
        return
    
    # Этап 2: Исправление MongoDB
    if not fix_mongodb():
        print("❌ Ошибка на этапе исправления MongoDB")
        return
    
    # Этап 3: Перезагрузка слов (опционально)
    print("\n❓ Хотите перезагрузить слова в MongoDB? (y/N): ", end="")
    response = input().strip().lower()
    
    if response == 'y':
        reload_words_to_mongodb()
    
    print("\n🎉 Исправление завершено!")
    print("💡 Теперь можно проверить работу с языком Себуано в приложении")

if __name__ == "__main__":
    main()
