#!/usr/bin/env python3
"""
Создание НАСТОЯЩИХ языковых справочников на основе лингвистических знаний.
"""

from pathlib import Path
from typing import Dict, List

# Языковые семьи и их особенности
LANGUAGE_DATA = {
    # ГЕРМАНСКИЕ ЯЗЫКИ
    'en': {
        'name': 'English',
        'family': 'Germanic',
        'features': ['simple_grammar', 'no_gender', 'minimal_cases'],
        'complexity': 'simple',
        'articles': ['a', 'an', 'the'],
        'cases': 0,
        'genders': 0
    },
    'de': {
        'name': 'Deutsch', 
        'family': 'Germanic',
        'features': ['4_cases', 'gender', 'complex_articles'],
        'complexity': 'problematic',
        'articles': ['der', 'die', 'das', 'den', 'dem', 'des'],
        'cases': 4,
        'genders': 3
    },
    'nl': {
        'name': 'Nederlands',
        'family': 'Germanic', 
        'features': ['gender', 'articles'],
        'complexity': 'problematic',
        'articles': ['de', 'het'],
        'cases': 0,
        'genders': 2
    },
    'sv': {
        'name': 'Svenska',
        'family': 'Germanic',
        'features': ['gender', 'articles'],
        'complexity': 'medium',
        'articles': ['en', 'ett'],
        'cases': 0,
        'genders': 2
    },
    'da': {
        'name': 'Dansk',
        'family': 'Germanic',
        'features': ['gender', 'articles'],
        'complexity': 'medium',
        'articles': ['en', 'et'],
        'cases': 0,
        'genders': 2
    },
    'no': {
        'name': 'Norsk',
        'family': 'Germanic',
        'features': ['gender', 'articles'],
        'complexity': 'medium',
        'articles': ['en', 'ei', 'et'],
        'cases': 0,
        'genders': 3
    },
    
    # РОМАНСКИЕ ЯЗЫКИ
    'es': {
        'name': 'Español',
        'family': 'Romance',
        'features': ['gender', 'articles', 'verb_conjugation'],
        'complexity': 'problematic',
        'articles': ['el', 'la', 'los', 'las'],
        'cases': 0,
        'genders': 2
    },
    'fr': {
        'name': 'Français',
        'family': 'Romance',
        'features': ['gender', 'articles', 'verb_conjugation'],
        'complexity': 'problematic',
        'articles': ['le', 'la', 'les'],
        'cases': 0,
        'genders': 2
    },
    'it': {
        'name': 'Italiano',
        'family': 'Romance',
        'features': ['gender', 'articles', 'verb_conjugation'],
        'complexity': 'problematic',
        'articles': ['il', 'la', 'lo', 'gli', 'le'],
        'cases': 0,
        'genders': 2
    },
    'pt': {
        'name': 'Português',
        'family': 'Romance',
        'features': ['gender', 'articles', 'verb_conjugation'],
        'complexity': 'problematic',
        'articles': ['o', 'a', 'os', 'as'],
        'cases': 0,
        'genders': 2
    },
    'ro': {
        'name': 'Română',
        'family': 'Romance',
        'features': ['gender', 'articles', 'cases'],
        'complexity': 'problematic',
        'articles': ['un', 'o'],
        'cases': 5,
        'genders': 3
    },
    
    # СЛАВЯНСКИЕ ЯЗЫКИ
    'ru': {
        'name': 'Русский',
        'family': 'Slavic',
        'features': ['6_cases', 'gender', 'aspect'],
        'complexity': 'problematic',
        'articles': [],
        'cases': 6,
        'genders': 3
    },
    'pl': {
        'name': 'Polski',
        'family': 'Slavic',
        'features': ['7_cases', 'gender', 'aspect'],
        'complexity': 'problematic',
        'articles': [],
        'cases': 7,
        'genders': 3
    },
    'uk': {
        'name': 'Українська',
        'family': 'Slavic',
        'features': ['7_cases', 'gender', 'aspect'],
        'complexity': 'problematic',
        'articles': [],
        'cases': 7,
        'genders': 3
    },
    
    # ФИННО-УГОРСКИЕ ЯЗЫКИ
    'fi': {
        'name': 'Suomi',
        'family': 'Finno-Ugric',
        'features': ['15_cases', 'agglutination', 'no_gender'],
        'complexity': 'problematic',
        'articles': [],
        'cases': 15,
        'genders': 0
    },
    
    # ТЮРКСКИЕ ЯЗЫКИ
    'tr': {
        'name': 'Türkçe',
        'family': 'Turkic',
        'features': ['agglutination', 'vowel_harmony', 'no_gender'],
        'complexity': 'problematic',
        'articles': [],
        'cases': 6,
        'genders': 0
    },
    'kk': {
        'name': 'Қазақша',
        'family': 'Turkic',
        'features': ['agglutination', 'vowel_harmony', 'cases'],
        'complexity': 'medium',
        'articles': [],
        'cases': 7,
        'genders': 0
    },
    'uz': {
        'name': 'Oʻzbekcha',
        'family': 'Turkic',
        'features': ['agglutination', 'no_gender'],
        'complexity': 'medium',
        'articles': [],
        'cases': 6,
        'genders': 0
    },
    
    # ИНДОЕВРОПЕЙСКИЕ (ИНДИЙСКИЕ)
    'hi': {
        'name': 'हिन्दी',
        'family': 'Indo-European',
        'features': ['cases', 'gender', 'postpositions'],
        'complexity': 'problematic',
        'articles': [],
        'cases': 3,
        'genders': 2
    },
    'pa': {
        'name': 'ਪੰਜਾਬੀ',
        'family': 'Indo-European',
        'features': ['cases', 'gender', 'tones'],
        'complexity': 'medium',
        'articles': [],
        'cases': 3,
        'genders': 2
    },
    'mr': {
        'name': 'मराठी',
        'family': 'Indo-European',
        'features': ['cases', 'gender'],
        'complexity': 'medium',
        'articles': [],
        'cases': 8,
        'genders': 3
    },
    'bn': {
        'name': 'বাংলা',
        'family': 'Indo-European',
        'features': ['cases', 'no_gender'],
        'complexity': 'medium',
        'articles': [],
        'cases': 6,
        'genders': 0
    },
    'ur': {
        'name': 'اردو',
        'family': 'Indo-European',
        'features': ['cases', 'gender', 'arabic_script'],
        'complexity': 'medium',
        'articles': [],
        'cases': 3,
        'genders': 2
    },
    'ne': {
        'name': 'नेपाली',
        'family': 'Indo-European',
        'features': ['cases', 'gender'],
        'complexity': 'medium',
        'articles': [],
        'cases': 6,
        'genders': 2
    },
    'fa': {
        'name': 'فارسی',
        'family': 'Indo-European',
        'features': ['ezafe', 'no_gender', 'arabic_script'],
        'complexity': 'medium',
        'articles': [],
        'cases': 0,
        'genders': 0
    },
    
    # СЕМИТСКИЕ ЯЗЫКИ
    'ar': {
        'name': 'العربية',
        'family': 'Semitic',
        'features': ['root_system', 'cases', 'gender'],
        'complexity': 'problematic',
        'articles': ['ال'],
        'cases': 3,
        'genders': 2
    },
    
    # ВОСТОЧНОАЗИАТСКИЕ
    'zh': {
        'name': '中文',
        'family': 'Sino-Tibetan',
        'features': ['tones', 'classifiers', 'no_inflection'],
        'complexity': 'medium',
        'articles': [],
        'cases': 0,
        'genders': 0
    },
    'ja': {
        'name': '日本語',
        'family': 'Japonic',
        'features': ['particles', 'politeness', 'counters'],
        'complexity': 'problematic',
        'articles': [],
        'cases': 0,
        'genders': 0
    },
    'ko': {
        'name': '한국어',
        'family': 'Koreanic',
        'features': ['agglutination', 'politeness', 'particles'],
        'complexity': 'problematic',
        'articles': [],
        'cases': 0,
        'genders': 0
    },
    
    # ЮГО-ВОСТОЧНАЯ АЗИЯ
    'th': {
        'name': 'ไทย',
        'family': 'Tai-Kadai',
        'features': ['tones', 'no_spaces', 'classifiers'],
        'complexity': 'medium',
        'articles': [],
        'cases': 0,
        'genders': 0
    },
    'vi': {
        'name': 'Tiếng Việt',
        'family': 'Austroasiatic',
        'features': ['tones', 'isolating'],
        'complexity': 'medium',
        'articles': [],
        'cases': 0,
        'genders': 0
    },
    'my': {
        'name': 'မြန်မာ',
        'family': 'Sino-Tibetan',
        'features': ['tones', 'complex_script'],
        'complexity': 'medium',
        'articles': [],
        'cases': 0,
        'genders': 0
    },
    
    # АВСТРОНЕЗИЙСКИЕ
    'id': {
        'name': 'Bahasa Indonesia',
        'family': 'Austronesian',
        'features': ['affixation', 'no_gender', 'simple_grammar'],
        'complexity': 'simple',
        'articles': [],
        'cases': 0,
        'genders': 0
    },
    'ms': {
        'name': 'Bahasa Malaysia',
        'family': 'Austronesian',
        'features': ['affixation', 'no_gender', 'simple_grammar'],
        'complexity': 'simple',
        'articles': [],
        'cases': 0,
        'genders': 0
    },
    'tl': {
        'name': 'Tagalog',
        'family': 'Austronesian',
        'features': ['focus_system', 'aspect', 'affixation'],
        'complexity': 'medium',
        'articles': ['ang', 'ng', 'sa'],
        'cases': 0,
        'genders': 0
    },
    'cb': {
        'name': 'Cebuano',
        'family': 'Austronesian',
        'features': ['focus_system', 'aspect', 'affixation'],
        'complexity': 'medium',
        'articles': ['ang', 'sa'],
        'cases': 0,
        'genders': 0
    },
    
    # КАРТВЕЛЬСКИЕ
    'ka': {
        'name': 'ქართული',
        'family': 'Kartvelian',
        'features': ['ergativity', 'complex_morphology'],
        'complexity': 'medium',
        'articles': [],
        'cases': 7,
        'genders': 0
    }
}

def create_language_guide(lang_code: str, lang_data: Dict) -> str:
    """Создает настоящий языковой справочник"""
    
    name = lang_data['name']
    family = lang_data['family']
    features = lang_data['features']
    complexity = lang_data['complexity']
    
    # Определяем основные грамматические особенности
    grammar_notes = []
    if 'gender' in features:
        grammar_notes.append(f"- Gender system ({lang_data['genders']} genders)")
    if lang_data['cases'] > 0:
        grammar_notes.append(f"- Case system ({lang_data['cases']} cases)")
    if 'agglutination' in features:
        grammar_notes.append("- Agglutinative morphology")
    if 'tones' in features:
        grammar_notes.append("- Tonal language")
    if lang_data['articles']:
        grammar_notes.append(f"- Articles: {', '.join(lang_data['articles'])}")
    
    # Специальные правила для A0
    a0_rules = []
    if complexity == 'problematic':
        a0_rules.append("⚠️ **PROBLEMATIC LANGUAGE** - Requires special attention for A0 words")
    
    if lang_data['cases'] > 0:
        a0_rules.append("- Use ONLY nominative case for target words")
        a0_rules.append("- Avoid case-changing constructions")
    
    if lang_data['articles']:
        if lang_code == 'de':
            a0_rules.append("- AVOID German articles (der/die/das) in A0 sentences")
        else:
            a0_rules.append("- Articles allowed if natural")
    
    if 'agglutination' in features:
        a0_rules.append("- Use base forms without suffixes")
    
    if 'tones' in features:
        a0_rules.append("- Ensure correct tone marking")
    
    # Создаем справочник
    guide = f"""# {name} Language Guide

## Language Information
- **Family**: {family}
- **Complexity for A0**: {complexity.upper()}
- **Writing System**: {_get_writing_system(lang_code)}

## Key Grammatical Features
{chr(10).join(grammar_notes) if grammar_notes else "- Simple grammar structure"}

## A0 Level Guidelines
{chr(10).join(a0_rules) if a0_rules else "- Standard A0 rules apply"}

## Parts of Speech Format

### Noun
**Format:** `noun{_get_noun_format(lang_data)}`
**Examples:**
{_get_noun_examples(lang_code, lang_data)}

### Verb  
**Format:** `verb{_get_verb_format(lang_data)}`
**Examples:**
{_get_verb_examples(lang_code, lang_data)}

### Adjective
**Format:** `adjective{_get_adj_format(lang_data)}`
**Examples:**
{_get_adj_examples(lang_code, lang_data)}

## Special Notes for A0
{_get_special_notes(lang_code, lang_data)}

## Common Patterns
{_get_common_patterns(lang_code, lang_data)}
"""
    
    return guide

def _get_writing_system(lang_code: str) -> str:
    """Определяет систему письма"""
    scripts = {
        'ar': 'Arabic script (right-to-left)',
        'fa': 'Persian script (right-to-left)', 
        'ur': 'Arabic script (right-to-left)',
        'hi': 'Devanagari',
        'pa': 'Gurmukhi',
        'mr': 'Devanagari',
        'bn': 'Bengali script',
        'ne': 'Devanagari',
        'th': 'Thai script',
        'my': 'Myanmar script',
        'ka': 'Georgian script',
        'ja': 'Hiragana/Katakana/Kanji',
        'ko': 'Hangul',
        'zh': 'Chinese characters',
        'ru': 'Cyrillic',
        'uk': 'Cyrillic',
        'kk': 'Cyrillic'
    }
    return scripts.get(lang_code, 'Latin script')

def _get_noun_format(lang_data: Dict) -> str:
    """Формат для существительных"""
    parts = []
    if lang_data['genders'] > 0:
        parts.append(', gender')
    if lang_data['cases'] > 0:
        parts.append(', case')
    parts.append(', number')
    return ''.join(parts)

def _get_noun_examples(lang_code: str, lang_data: Dict) -> str:
    """Примеры существительных"""
    examples = {
        'en': '- `noun, singular` (house)\n- `noun, plural` (houses)',
        'ru': '- `существительное, мужской, именительный, единственное` (дом)\n- `существительное, женский, именительный, множественное` (мамы)',
        'de': '- `Substantiv, maskulin, Nominativ, Singular` (Haus)\n- `Substantiv, feminin, Nominativ, Plural` (Häuser)',
        'es': '- `sustantivo, masculino, singular` (libro)\n- `sustantivo, femenino, plural` (casas)',
        'ja': '- `名詞, 単数` (家)\n- `名詞, 複数` (家々)',
        'ar': '- `اسم, مذكر, مفرد` (بيت)\n- `اسم, مؤنث, جمع` (بيوت)'
    }
    return examples.get(lang_code, '- `noun, basic form`\n- `noun, plural form`')

def _get_verb_format(lang_data: Dict) -> str:
    """Формат для глаголов"""
    return ', tense, person, number'

def _get_verb_examples(lang_code: str, lang_data: Dict) -> str:
    """Примеры глаголов"""
    examples = {
        'en': '- `verb, present, 1st person, singular` (work)\n- `verb, past` (worked)',
        'ru': '- `глагол, настоящее, 1 лицо, единственное` (работаю)\n- `глагол, прошедшее, мужской, единственное` (работал)',
        'es': '- `verbo, presente, 1a persona, singular` (trabajo)\n- `verbo, pretérito, 3a persona, singular` (trabajó)'
    }
    return examples.get(lang_code, '- `verb, present tense`\n- `verb, past tense`')

def _get_adj_format(lang_data: Dict) -> str:
    """Формат для прилагательных"""
    parts = []
    if lang_data['genders'] > 0:
        parts.append(', gender')
    if lang_data['cases'] > 0:
        parts.append(', case')
    parts.append(', number')
    return ''.join(parts)

def _get_adj_examples(lang_code: str, lang_data: Dict) -> str:
    """Примеры прилагательных"""
    examples = {
        'en': '- `adjective, positive` (beautiful)\n- `adjective, comparative` (more beautiful)',
        'ru': '- `прилагательное, мужской, именительный, единственное` (красивый)\n- `прилагательное, женский, именительный, единственное` (красивая)'
    }
    return examples.get(lang_code, '- `adjective, basic form`\n- `adjective, comparative`')

def _get_special_notes(lang_code: str, lang_data: Dict) -> str:
    """Специальные заметки"""
    notes = []
    
    if lang_data['complexity'] == 'problematic':
        notes.append("⚠️ This language requires extra attention for A0 level")
    
    if lang_code in ['fi', 'tr', 'ko']:
        notes.append("- Agglutinative language - use base forms only")
    
    if lang_code in ['zh', 'th', 'vi']:
        notes.append("- Tonal language - ensure correct tone representation")
    
    if lang_code == 'ar':
        notes.append("- Root-based morphology - use simple forms")
        notes.append("- Right-to-left writing")
    
    return '\n'.join(notes) if notes else "- Follow standard A0 guidelines"

def _get_common_patterns(lang_code: str, lang_data: Dict) -> str:
    """Общие паттерны"""
    patterns = {
        'en': '- Simple word order: Subject-Verb-Object\n- No case changes needed',
        'ru': '- Use nominative case for all target words\n- Avoid complex case constructions',
        'de': '- Avoid articles in A0 sentences\n- Use nominative case only',
        'ja': '- Use polite forms when appropriate\n- Avoid complex particle constructions'
    }
    return patterns.get(lang_code, '- Keep constructions simple\n- Use most basic forms')

def main():
    """Создает справочники для всех языков"""
    
    guides_dir = Path("data/words/language_guides")
    guides_dir.mkdir(exist_ok=True)
    
    created_count = 0
    
    print("📚 Создание НАСТОЯЩИХ языковых справочников...")
    
    for lang_code, lang_data in LANGUAGE_DATA.items():
        guide_file = guides_dir / f"{lang_code.upper()}.md"
        
        # Проверяем, существует ли уже хороший справочник
        if guide_file.exists():
            with open(guide_file, 'r', encoding='utf-8') as f:
                content = f.read()
                # Если файл содержательный (больше 1000 символов), не перезаписываем
                if len(content) > 1000 and 'Parts of Speech' in content:
                    print(f"⏭️  {lang_code.upper()}: уже существует качественный справочник")
                    continue
        
        # Создаем справочник
        guide_content = create_language_guide(lang_code, lang_data)
        
        with open(guide_file, 'w', encoding='utf-8') as f:
            f.write(guide_content)
        
        complexity_emoji = {
            'simple': '🟢',
            'medium': '🟡', 
            'problematic': '🔴'
        }
        
        emoji = complexity_emoji.get(lang_data['complexity'], '⚪')
        print(f"{emoji} {lang_code.upper()}: создан ({lang_data['name']}) - {lang_data['complexity']}")
        created_count += 1
    
    print(f"\n📊 Статистика:")
    print(f"  Создано новых справочников: {created_count}")
    print(f"  Всего языков: {len(LANGUAGE_DATA)}")
    
    print(f"\n✅ Все языковые справочники готовы!")

if __name__ == "__main__":
    main()
