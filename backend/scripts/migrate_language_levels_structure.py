#!/usr/bin/env python3
"""
Миграция для обновления структуры language_level -> language_levels
Преобразует старое поле language_level в новую структуру language_levels
"""

import asyncio
import os
import sys
from motor.motor_asyncio import AsyncIOMotorClient
from pymongo import UpdateOne
from dotenv import load_dotenv

# Добавляем путь к корневой директории проекта
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Загружаем переменные окружения из .env файла
load_dotenv(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env'))

async def migrate_language_levels_structure():
    """
    Миграция структуры уровней языков:
    - Преобразует settings.language_level в settings.language_levels[target_language]
    - Добавляет target_language если его нет
    - Обеспечивает обратную совместимость
    """
    
    # Подключение к базе данных
    MONGODB_URL = os.getenv("MONGODB_URL")
    if not MONGODB_URL:
        print("❌ Переменная окружения MONGODB_URL не найдена")
        return
    
    DATABASE_NAME = "users_db"
    
    client = AsyncIOMotorClient(MONGODB_URL)
    db = client[DATABASE_NAME]
    users_collection = db.users
    
    try:
        print("🔍 Поиск пользователей для миграции структуры language_levels...")
        
        # Находим всех пользователей
        all_users = await users_collection.find({}).to_list(length=None)
        
        if not all_users:
            print("✅ Пользователи не найдены")
            return
        
        print(f"📝 Найдено {len(all_users)} пользователей для проверки")
        
        # Подготавливаем операции обновления
        update_operations = []
        updated_count = 0
        
        for user in all_users:
            user_id = user["_id"]
            email = user.get("email", "unknown")
            
            # Получаем текущие настройки или создаем пустые
            current_settings = user.get("settings", {})
            needs_update = False
            
            # 1. Добавляем target_language если его нет
            if "target_language" not in current_settings:
                # Берем первый язык из learning_languages или "en" по умолчанию
                learning_languages = user.get("learning_languages", ["en"])
                current_settings["target_language"] = learning_languages[0] if learning_languages else "en"
                needs_update = True
                print(f"  📧 {email} -> добавляем target_language: {current_settings['target_language']}")
            
            # 2. Преобразуем language_level в language_levels
            if "language_level" in current_settings and "language_levels" not in current_settings:
                old_level = current_settings["language_level"]
                target_lang = current_settings.get("target_language", "en")
                
                # Создаем новую структуру language_levels
                current_settings["language_levels"] = {
                    target_lang: old_level
                }
                
                # Удаляем старое поле
                del current_settings["language_level"]
                needs_update = True
                print(f"  📧 {email} -> мигрируем language_level '{old_level}' -> language_levels['{target_lang}']")
            
            # 3. Добавляем language_levels если его вообще нет
            elif "language_levels" not in current_settings:
                target_lang = current_settings.get("target_language", "en")
                current_settings["language_levels"] = {
                    target_lang: "A0"  # Значение по умолчанию
                }
                needs_update = True
                print(f"  📧 {email} -> добавляем language_levels['{target_lang}'] = 'A0'")
            
            # Добавляем операцию обновления если нужно
            if needs_update:
                update_operations.append(
                    UpdateOne(
                        {"_id": user_id},
                        {"$set": {"settings": current_settings}}
                    )
                )
                updated_count += 1
        
        # Выполняем массовое обновление
        if update_operations:
            result = await users_collection.bulk_write(update_operations)
            print(f"✅ Обновлено {result.modified_count} пользователей")
        else:
            print("✅ Все пользователи уже имеют правильную структуру language_levels")
        
        print("🎉 Миграция структуры language_levels завершена успешно!")
        
        # Показываем примеры обновленных данных
        print("\n📊 Примеры обновленных настроек:")
        sample_users = await users_collection.find({}).limit(2).to_list(length=2)
        for user in sample_users:
            settings = user.get("settings", {})
            print(f"  📧 {user.get('email')}: target_language={settings.get('target_language')}, language_levels={settings.get('language_levels')}")
        
    except Exception as e:
        print(f"❌ Ошибка при выполнении миграции: {e}")
        raise
    finally:
        client.close()

async def main():
    """Главная функция"""
    print("🚀 Запуск миграции структуры language_levels...")
    await migrate_language_levels_structure()

if __name__ == "__main__":
    asyncio.run(main())
