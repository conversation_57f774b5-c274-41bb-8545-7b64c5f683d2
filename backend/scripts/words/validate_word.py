#!/usr/bin/env python3
"""
Скрипт для валидации JSON-файлов со словами.

Перед запуском убедитесь, что активировано виртуальное окружение:
  source venv/bin/activate

Проверяет корректность структуры и данных, обязательные поля и форматы.

Использование:
    python -m scripts.words validate path/to/your/file.json
"""

import json
import sys
import uuid
import re
from pathlib import Path
from typing import Dict, List, Any, Optional, Set

# Настройка логирования
import logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Обязательные поля для каждого слова
REQUIRED_FIELDS = {
    'concept_id': str,
    'word': str,
    'language': str,
    'level': str,
    'part_of_speech': str,
    'examples': list,
}

# Допустимые уровни сложности
VALID_LEVELS = {'A0', 'A1', 'A2', 'B1', 'B2', 'C1', 'C2'}

# Допустимые приоритеты для A0 уровня
VALID_A0_PRIORITIES = {'ultra_core', 'core', 'extended'}

# Допустимые приоритеты для A0 уровня
VALID_A0_PRIORITIES = {'ultra_core', 'core', 'extended'}

def load_languages_from_typescript() -> Set[str]:
    """
    Динамически загружает список языков из frontend/constants/languages.ts.

    Парсит TypeScript файл и извлекает коды языков из массива ALL_LANGUAGES.
    Это гарантирует, что валидатор всегда использует актуальный список языков.
    """
    # Путь к файлу с языками относительно корня проекта
    script_dir = Path(__file__).parent  # backend/scripts/words/
    project_root = script_dir.parent.parent.parent  # корень проекта
    languages_file = project_root / "frontend" / "constants" / "languages.ts"

    if not languages_file.exists():
        logger.warning(f"Файл с языками не найден: {languages_file}")
        logger.warning("Используется резервный список языков")
        # Резервный список на случай, если файл не найден
        return {
            'en', 'es', 'de', 'fr', 'it', 'pt', 'zh', 'ja', 'ko', 'ar', 'hi', 'th', 'vi', 'tr', 'pl', 'nl',
            'sv', 'no', 'da', 'fi', 'cb', 'tl', 'id', 'ms', 'uk', 'ka', 'ru'
        }

    try:
        with open(languages_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # Ищем массив ALL_LANGUAGES и извлекаем коды языков
        # Паттерн для поиска: { code: 'xx', ... }
        pattern = r"{\s*code:\s*['\"]([a-z]{2,4})['\"]"
        matches = re.findall(pattern, content)

        if not matches:
            logger.warning("Не удалось найти коды языков в languages.ts")
            logger.warning("Используется резервный список языков")
            # Резервный список
            return {
                'en', 'es', 'de', 'fr', 'it', 'pt', 'zh', 'ja', 'ko', 'ar', 'hi', 'th', 'vi', 'tr', 'pl', 'nl',
                'sv', 'no', 'da', 'fi', 'cb', 'tl', 'id', 'ms', 'uk', 'ka', 'ru'
            }

        languages = set(matches)
        logger.info(f"Загружено {len(languages)} языков из {languages_file}")
        logger.debug(f"Языки: {sorted(languages)}")
        return languages

    except Exception as e:
        logger.error(f"Ошибка при чтении файла {languages_file}: {e}")
        logger.warning("Используется резервный список языков")
        # Резервный список на случай ошибки
        return {
            'en', 'es', 'de', 'fr', 'it', 'pt', 'zh', 'ja', 'ko', 'ar', 'hi', 'th', 'vi', 'tr', 'pl', 'nl',
            'sv', 'no', 'da', 'fi', 'cb', 'tl', 'id', 'ms', 'uk', 'ka', 'ru'
        }

# Динамически загружаем список языков из TypeScript файла
# ИСТОЧНИК ИСТИНЫ: frontend/constants/languages.ts - ALL_LANGUAGES
VALID_LANGUAGES = load_languages_from_typescript()

def validate_uuid(uuid_str: str) -> bool:
    """Проверяет, является ли строка валидным UUID v4."""
    try:
        if not isinstance(uuid_str, str):
            logger.debug(f"UUID не является строкой: {uuid_str} ({type(uuid_str)})")
            return False
            
        # Проверяем длину (36 символов для UUID с дефисами)
        if len(uuid_str) != 36:
            logger.debug(f"Некорректная длина UUID: {uuid_str} (длина: {len(uuid_str)})")
            return False
            
        # Проверяем наличие дефисов в правильных позициях
        if uuid_str[8] != '-' or uuid_str[13] != '-' or uuid_str[18] != '-' or uuid_str[23] != '-':
            logger.debug(f"Неправильные дефисы в UUID: {uuid_str}")
            return False
            
        # Пытаемся создать UUID объект
        uuid_obj = uuid.UUID(uuid_str, version=4)
        
        # Проверяем, что строка не изменилась при преобразовании
        result = str(uuid_obj) == uuid_str
        if not result:
            logger.debug(f"UUID изменился при преобразовании: '{uuid_str}' -> '{uuid_obj}'")
        return result
        
    except (ValueError, AttributeError) as e:
        logger.debug(f"Ошибка при валидации UUID '{uuid_str}': {e}")
        return False

def validate_example(example: Dict[str, Any], example_index: int) -> List[str]:
    """Проверяет валидность примера использования."""
    errors = []
    
    if not isinstance(example.get('sentence'), str):
        errors.append(f"Пример {example_index + 1}: отсутствует или неверный формат поля 'sentence'")
    elif '___' not in example['sentence']:
        errors.append(f"Пример {example_index + 1}: в предложении отсутствует пропуск '___'")
    
    if not isinstance(example.get('correct_answers'), list) or not example['correct_answers']:
        errors.append(f"Пример {example_index + 1}: отсутствует или пустой массив 'correct_answers'")
    elif not all(isinstance(ans, str) for ans in example['correct_answers']):
        errors.append(f"Пример {example_index + 1}: 'correct_answers' должен содержать только строки")
    
    return errors

def validate_word(word_data: Dict[str, Any], index: int, concept_ids: Dict[str, List[int]]) -> List[str]:
    """Проверяет валидность данных одного слова."""
    errors = []
    
    # Проверка обязательных полей
    for field, field_type in REQUIRED_FIELDS.items():
        if field not in word_data:
            errors.append(f"Слово {index + 1}: отсутствует обязательное поле '{field}'")
        elif not isinstance(word_data[field], field_type):
            errors.append(
                f"Слово {index + 1}: поле '{field}' должно быть типа {field_type.__name__}, "
                f"получено {type(word_data[field]).__name__}"
            )
    
    # Проверка concept_id (должен быть валидным UUID v4)
    if 'concept_id' in word_data and not validate_uuid(word_data['concept_id']):
        errors.append(f"Слово {index + 1}: неверный формат concept_id, ожидается UUID v4")
    
    # Проверка уровня
    if 'level' in word_data and word_data['level'] not in VALID_LEVELS:
        errors.append(
            f"Слово {index + 1}: недопустимый уровень '{word_data['level']}'. "
            f"Допустимые значения: {', '.join(sorted(VALID_LEVELS))}"
        )
    
    # Проверка кода языка
    if 'language' in word_data and word_data['language'] not in VALID_LANGUAGES:
        errors.append(
            f"Слово {index + 1}: недопустимый код языка '{word_data['language']}'. "
            f"Используйте коды ISO 639-1 (en, ru, es, и т.д.)"
        )

    # Проверка поля priority для A0 уровня
    if 'level' in word_data:
        if word_data['level'] == 'A0':
            # Для A0 поле priority ОБЯЗАТЕЛЬНО
            if 'priority' not in word_data:
                errors.append(f"Слово {index + 1}: для уровня A0 обязательно поле 'priority'")
            elif word_data['priority'] not in VALID_A0_PRIORITIES:
                errors.append(
                    f"Слово {index + 1}: недопустимый приоритет '{word_data['priority']}' для A0. "
                    f"Допустимые значения: {', '.join(sorted(VALID_A0_PRIORITIES))}"
                )
        else:
            # Для других уровней поле priority НЕ ДОЛЖНО присутствовать
            if 'priority' in word_data:
                errors.append(
                    f"Слово {index + 1}: поле 'priority' допустимо только для уровня A0, "
                    f"но найдено для уровня '{word_data['level']}'"
                )
    
    # Проверка примеров
    if 'examples' not in word_data or not isinstance(word_data['examples'], list):
        errors.append(f"Слово {index + 1}: отсутствует или неверный формат поля 'examples'")
    else:
        # Выводим отладочную информацию
        print(f"\nОтладка - Слово {index + 1} (язык: {word_data.get('language', 'не указан')}):")
        print(f"Количество примеров: {len(word_data['examples'])}")
        for i, example in enumerate(word_data['examples'], 1):
            print(f"  Пример {i}: {example.get('sentence', 'нет sentence')}")
        
        # Проверяем количество примеров (должен быть ровно один)
        if len(word_data['examples']) != 1:
            errors.append(
                f"Слово {index + 1} (язык: {word_data.get('language', 'не указан')}): "
                f"должно быть ровно 1 пример использования, найдено {len(word_data['examples'])}"
            )
        
        # Проверяем каждый пример
        for i, example in enumerate(word_data['examples'], 1):
            errors.extend(validate_example(example, i))
    
    # Сохраняем concept_id для проверки переводов
    if 'concept_id' in word_data:
        concept_id = word_data['concept_id']
        if concept_id not in concept_ids:
            concept_ids[concept_id] = []
        concept_ids[concept_id].append(index + 1)
    
    return errors

def validate_translations(concept_ids: Dict[str, List[int]]) -> List[str]:
    """Проверяет согласованность переводов (одинаковые concept_id)."""
    errors = []

    for concept_id, indices in concept_ids.items():
        if len(indices) == 1:
            errors.append(
                f"Слово {indices[0]}: concept_id '{concept_id}' встречается только один раз. "
                "Каждое слово должно иметь хотя бы один перевод."
            )

    return errors

def validate_language_completeness(data: List[Dict]) -> List[str]:
    """Проверяет, что для каждого concept_id присутствуют ВСЕ языки из VALID_LANGUAGES."""
    errors = []

    # Группируем слова по concept_id
    concepts = {}
    for i, word_data in enumerate(data):
        if 'concept_id' not in word_data or 'language' not in word_data:
            continue  # Эти ошибки будут обнаружены в validate_word

        concept_id = word_data['concept_id']
        language = word_data['language']

        if concept_id not in concepts:
            concepts[concept_id] = {
                'languages': set(),
                'word_indices': []
            }

        concepts[concept_id]['languages'].add(language)
        concepts[concept_id]['word_indices'].append(i + 1)

    # Проверяем полноту языков для каждого концепта
    for concept_id, concept_data in concepts.items():
        present_languages = concept_data['languages']
        missing_languages = VALID_LANGUAGES - present_languages
        extra_languages = present_languages - VALID_LANGUAGES

        if missing_languages:
            errors.append(
                f"Концепт '{concept_id}': отсутствуют переводы для языков: {', '.join(sorted(missing_languages))}. "
                f"Требуется {len(VALID_LANGUAGES)} языков, найдено {len(present_languages)}"
            )

        if extra_languages:
            errors.append(
                f"Концепт '{concept_id}': найдены недопустимые языки: {', '.join(sorted(extra_languages))}"
            )

        # Проверяем дубликаты языков
        language_counts = {}
        for i, word_data in enumerate(data):
            if word_data.get('concept_id') == concept_id:
                lang = word_data.get('language')
                if lang:
                    if lang not in language_counts:
                        language_counts[lang] = []
                    language_counts[lang].append(i + 1)

        for lang, indices in language_counts.items():
            if len(indices) > 1:
                errors.append(
                    f"Концепт '{concept_id}': дублирование языка '{lang}' в словах {', '.join(map(str, indices))}"
                )

    return errors

def validate_file(file_path: Path) -> bool:
    """Проверяет JSON-файл со словами."""
    if not file_path.exists():
        logger.error(f"Файл не найден: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except json.JSONDecodeError as e:
        logger.error(f"Ошибка разбора JSON: {e}")
        return False
    
    if not isinstance(data, list):
        logger.error("Корневой элемент должен быть массивом")
        return False
    
    all_errors = []
    concept_ids = {}
    
    # Проверяем каждое слово
    for i, word_data in enumerate(data):
        all_errors.extend(validate_word(word_data, i, concept_ids))

    # Проверяем согласованность переводов
    all_errors.extend(validate_translations(concept_ids))

    # Проверяем полноту языков для каждого концепта
    all_errors.extend(validate_language_completeness(data))
    
    # Выводим ошибки
    if all_errors:
        logger.error(f"Найдено {len(all_errors)} ошибок в файле {file_path}:")
        for error in all_errors:
            logger.error(f"- {error}")
        return False

    logger.info(f"Файл {file_path} прошел валидацию успешно!")

    # Определяем относительный путь файла
    try:
        relative_path = Path(file_path).relative_to(Path.cwd())
    except ValueError:
        relative_path = Path(file_path)

    # Проверяем, находится ли файл в папке A0 (мастер-файлы)
    if "A0/" in str(relative_path):
        logger.info("=" * 60)
        logger.info("🎯 ФАЙЛ ГОТОВ К ИМПОРТУ!")
        logger.info("=" * 60)
        logger.info("📋 Следующие шаги:")
        logger.info(f"1. Скопируйте файл в папку new/:")

        # Извлекаем имя файла
        filename = Path(file_path).name
        logger.info(f"   cp {relative_path} data/words/new/{filename}")

        logger.info("2. Импортируйте в базу данных:")
        logger.info(f"   python3 -m scripts.words import data/words/new/{filename}")
        logger.info("=" * 60)
    elif "new/" in str(relative_path):
        logger.info("=" * 60)
        logger.info("✅ ФАЙЛ ГОТОВ К ИМПОРТУ!")
        logger.info("=" * 60)
        logger.info("📋 Импортируйте в базу данных:")
        logger.info(f"   python3 -m scripts.words import {relative_path}")
        logger.info("=" * 60)

    return True

def main():
    if len(sys.argv) != 2:
        print("Использование: python -m scripts.validate_word_file path/to/your/file.json")
        sys.exit(1)
    
    file_path = Path(sys.argv[1]).resolve()
    if not validate_file(file_path):
        sys.exit(1)

if __name__ == "__main__":
    main()
