#!/usr/bin/env python3
"""
Скрипт для импорта слов из JSON-файлов в MongoDB.

Файлы должны находиться в директории backend/data/words/new/
После успешного импорта файлы перемещаются в backend/data/words/imported/
При ошибках файлы перемещаются в backend/data/words/invalid/
"""

import json
import logging
import os
import shutil
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple

# Добавляем корень проекта в PYTHONPATH
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from motor.motor_asyncio import AsyncIOMotorClient
from dotenv import load_dotenv

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(project_root / 'logs' / 'import_words.log')
    ]
)
logger = logging.getLogger(__name__)

# Загрузка переменных окружения
env_path = project_root / '.env'  # project_root указывает на backend, поэтому .env находится там же
if env_path.exists():
    load_dotenv(env_path)
    print(f"✅ Загружен .env файл: {env_path}")
else:
    # Если файл не найден, попробуем загрузить из текущей директории
    load_dotenv()
    print(f"⚠️ Файл .env не найден по пути {env_path}, используем переменные окружения")

# Настройки подключения к MongoDB
MONGODB_URL = os.getenv('MONGODB_URL')
DATABASE_NAME = os.getenv('WORDS_DATABASE_NAME', 'word_master')

# Проверяем наличие обязательных переменных
if not MONGODB_URL:
    print("❌ ОШИБКА: Не задана переменная окружения MONGODB_URL")
    print(f"Проверьте файл .env по пути: {env_path}")
    logger.error("ОШИБКА: Не задана переменная окружения MONGODB_URL")
    sys.exit(1)

print(f"🔗 Подключение к MongoDB: {MONGODB_URL[:50]}...")
print(f"🗄️ Используется база данных: {DATABASE_NAME}")
logger.info(f"Подключение к MongoDB: {MONGODB_URL}")
logger.info(f"Используется база данных: {DATABASE_NAME}")

# Пути к директориям
# Корень проекта (где находится backend/)
PROJECT_ROOT = project_root.parent.parent

# Пути к директориям относительно корня проекта
DATA_DIR = PROJECT_ROOT / 'backend' / 'data' / 'words'
NEW_WORDS_DIR = DATA_DIR / 'new'
IMPORTED_DIR = DATA_DIR / 'imported'
INVALID_DIR = DATA_DIR / 'invalid'

# Создаем необходимые директории, если их нет
for directory in [NEW_WORDS_DIR, IMPORTED_DIR, INVALID_DIR]:
    directory.mkdir(parents=True, exist_ok=True)
    logger.debug(f"Директория {directory} {'создана' if directory.exists() else 'не создана'}")

class ImportResult:
    """Класс для хранения результатов импорта."""
    def __init__(self, processed: int = 0, imported: int = 0, errors: List[str] = None):
        self.processed = processed
        self.imported = imported
        self.errors = errors or []
    
    def add_error(self, error: str):
        """Добавляет сообщение об ошибке."""
        self.errors.append(error)
    
    def to_dict(self) -> Dict[str, Any]:
        """Возвращает результат в виде словаря."""
        return {
            'processed': self.processed,
            'imported': self.imported,
            'errors': self.errors,
            'success': not bool(self.errors)
        }

async def process_file(file_path: Path, db) -> ImportResult:
    """Обработка одного файла с словами."""
    result = ImportResult()
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            words = json.load(f)
        
        if not isinstance(words, list):
            error_msg = f"Файл {file_path.name} должен содержать массив слов"
            logger.error(error_msg)
            result.add_error(error_msg)
            return result
        
        current_time = datetime.utcnow()
        
        for word_data in words:
            try:
                # Добавляем или обновляем слово в базе
                word = word_data.get('word', '').lower().strip()
                language = word_data.get('language', '').lower().strip()
                
                if not word or not language:
                    error_msg = f"Пропущено слово с пустым значением word или language: {word_data}"
                    logger.warning(error_msg)
                    result.add_error(error_msg)
                    continue
                
                # Добавляем метаданные
                word_data['created_at'] = current_time
                word_data['updated_at'] = current_time
                
                # Преобразуем ObjectId, если он есть (для обновления)
                if '_id' in word_data and isinstance(word_data['_id'], str):
                    try:
                        from bson import ObjectId
                        word_data['_id'] = ObjectId(word_data['_id'])
                    except Exception as e:
                        error_msg = f"Неверный формат _id в слове {word} ({language}): {e}"
                        logger.warning(error_msg)
                        result.add_error(error_msg)
                        continue
                
                # Подготавливаем данные для обновления
                update_data = {k: v for k, v in word_data.items() if k != 'examples'}
                
                # Обрабатываем примеры, если они есть
                if 'examples' in word_data and isinstance(word_data['examples'], list):
                    # Получаем текущие примеры из базы, если они есть
                    existing_word = await db.words.find_one(
                        {'word': word, 'language': language},
                        {'examples': 1}
                    )
                    
                    if existing_word and 'examples' in existing_word:
                        # Создаем словарь для быстрого поиска примеров по предложению
                        existing_examples = {ex['sentence']: ex for ex in existing_word['examples']}
                        
                        # Обновляем существующие примеры и добавляем новые
                        updated_examples = []
                        for new_example in word_data['examples']:
                            if not isinstance(new_example, dict) or 'sentence' not in new_example:
                                continue
                                
                            sentence = new_example['sentence']
                            if sentence in existing_examples:
                                # Обновляем существующий пример, сохраняя word_info, если он не указан
                                existing_example = existing_examples[sentence]
                                if 'word_info' not in new_example and 'word_info' in existing_example:
                                    new_example['word_info'] = existing_example['word_info']
                                
                                # Обновляем только указанные поля
                                existing_example.update({
                                    k: v for k, v in new_example.items()
                                    if k != 'sentence'  # Не обновляем предложение, так как это ключ
                                })
                                updated_examples.append(existing_example)
                            else:
                                # Добавляем новый пример
                                updated_examples.append(new_example)
                        
                        # Добавляем оставшиеся существующие примеры, которые не были обновлены
                        updated_sentences = {ex['sentence'] for ex in updated_examples}
                        for ex in existing_word['examples']:
                            if ex['sentence'] not in updated_sentences:
                                updated_examples.append(ex)
                        
                        word_data['examples'] = updated_examples
                    
                    # Убедимся, что все примеры имеют правильную структуру
                    for example in word_data['examples']:
                        if 'word_info' not in example:
                            example['word_info'] = None
                
                # Обновляем или вставляем слово
                update_result = await db.words.update_one(
                    {'word': word, 'language': language},
                    {'$set': word_data},
                    upsert=True
                )
                
                result.processed += 1
                if update_result.upserted_id or update_result.modified_count > 0:
                    result.imported += 1
                
            except Exception as e:
                error_msg = f"Ошибка при обработке слова {word_data.get('word', 'unknown')}: {e}"
                logger.error(error_msg, exc_info=True)
                result.add_error(error_msg)
        
        logger.info(f"Обработано {result.processed} слов, добавлено/обновлено {result.imported} слов из {file_path.name}")
        return result
        
    except json.JSONDecodeError as e:
        error_msg = f"Ошибка при разборе JSON в файле {file_path.name}: {e}"
        logger.error(error_msg)
        result.add_error(error_msg)
        return result
    except Exception as e:
        error_msg = f"Непредвиденная ошибка при обработке файла {file_path.name}: {e}"
        logger.error(error_msg, exc_info=True)
        result.add_error(error_msg)
        return result

def move_file(source_path: Path, target_dir: Path) -> Path:
    """Перемещает файл в целевую директорию, добавляя временную метку при необходимости."""
    if not source_path.exists():
        return None
    
    target_dir.mkdir(parents=True, exist_ok=True)
    target_path = target_dir / source_path.name
    
    # Если файл с таким именем уже существует, добавляем временную метку
    counter = 1
    while target_path.exists():
        target_path = target_dir / f"{source_path.stem}_{counter}{''.join(source_path.suffixes)}"
        counter += 1
    
    shutil.move(str(source_path), str(target_path))
    return target_path

async def import_words():
    """Основная функция импорта слов."""
    logger.info("Запуск процесса импорта слов...")
    
    # Создаем подключение к MongoDB
    client = AsyncIOMotorClient(MONGODB_URL)
    db = client[DATABASE_NAME]
    
    try:
        # Создаем индексы для быстрого поиска
        await db.words.create_index([("word", 1), ("language", 1)], unique=True)
        await db.words.create_index("concept_id")
        
        # Получаем список JSON-файлов в директории new
        word_files = list(NEW_WORDS_DIR.glob('*.json'))
        
        if not word_files:
            logger.warning(f"Не найдено новых JSON-файлов в директории {NEW_WORDS_DIR}")
            return
        
        total_result = ImportResult()
        
        for file_path in word_files:
            # Пропускаем скрытые файлы и файлы, начинающиеся с подчеркивания
            if file_path.name.startswith(('.', '_')):
                continue
                
            logger.info(f"Обработка файла: {file_path.name}")
            
            # Обрабатываем файл
            file_result = await process_file(file_path, db)
            
            # Обновляем общую статистику
            total_result.processed += file_result.processed
            total_result.imported += file_result.imported
            total_result.errors.extend(file_result.errors)
            
            # Перемещаем файл в соответствующую директорию
            if file_result.errors:
                target_dir = INVALID_DIR
                logger.warning(f"Обнаружены ошибки в файле {file_path.name}, перемещение в {target_dir}")
            else:
                target_dir = IMPORTED_DIR
                logger.info(f"Файл {file_path.name} успешно обработан, перемещение в {target_dir}")
            
            # Перемещаем файл
            try:
                new_path = move_file(file_path, target_dir)
                if new_path:
                    logger.debug(f"Файл перемещен: {file_path} -> {new_path}")
            except Exception as e:
                error_msg = f"Ошибка при перемещении файла {file_path}: {e}"
                logger.error(error_msg)
                total_result.add_error(error_msg)
        
        # Выводим итоговый отчет
        logger.info("=" * 50)
        logger.info("ОТЧЕТ ОБ ИМПОРТЕ")
        logger.info("=" * 50)
        logger.info(f"Всего обработано файлов: {len(word_files)}")
        logger.info(f"Всего обработано слов: {total_result.processed}")
        logger.info(f"Добавлено/обновлено слов: {total_result.imported}")
        
        if total_result.errors:
            logger.warning("\nБыли обнаружены ошибки:")
            for i, error in enumerate(total_result.errors, 1):
                logger.warning(f"{i}. {error}")
        
        logger.info("=" * 50)
        logger.info("Импорт завершен" if not total_result.errors else "Импорт завершен с ошибками")
        
    except Exception as e:
        logger.error(f"Критическая ошибка при импорте: {e}", exc_info=True)
        raise
    finally:
        client.close()

if __name__ == "__main__":
    import asyncio
    asyncio.run(import_words())
