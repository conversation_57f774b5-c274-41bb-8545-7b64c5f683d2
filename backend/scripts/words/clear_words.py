#!/usr/bin/env python3
"""
Скрипт для полной очистки коллекции words в базе данных.

Перед запуском убедитесь, что активировано виртуальное окружение:
  source venv/bin/activate

Использование: python -m scripts.words clear
"""

import asyncio
import os
from motor.motor_asyncio import AsyncIOMotorClient
from dotenv import load_dotenv
from pathlib import Path

# Настройка логирования
import logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Загружаем переменные окружения из .env файла
dotenv_path = Path(__file__).parent.parent.parent / '.env'
if dotenv_path.exists():
    load_dotenv(dotenv_path=dotenv_path, override=True)
else:
    logger.error(f"Файл .env не найден по пути: {dotenv_path}")
    exit(1)

async def clear_words_collection():
    """Очистка коллекции words"""
    client = None
    try:
        # Подключаемся к MongoDB
        mongo_uri = os.getenv("MONGODB_URL")
        db_name = os.getenv("DATABASE_NAME")
        
        if not mongo_uri or not db_name:
            logger.error("Не заданы MONGODB_URL и/или DATABASE_NAME в .env файле")
            return False
        
        client = AsyncIOMotorClient(mongo_uri)
        db = client[db_name]
        
        # Проверяем, существует ли коллекция
        collections = await db.list_collection_names()
        if 'words' not in collections:
            logger.warning("Коллекция 'words' не существует")
            return False
        
        # Удаляем все документы из коллекции
        result = await db.words.delete_many({})
        logger.info(f"Удалено {result.deleted_count} документов из коллекции 'words'")
        
        return True
        
    except Exception as e:
        logger.error(f"Ошибка при очистке коллекции: {str(e)}")
        return False
    finally:
        if client:
            client.close()

if __name__ == "__main__":
    import sys
    
    print("Начинаем очистку коллекции 'words'...")
    
    # Запускаем асинхронную функцию
    success = asyncio.run(clear_words_collection())
    
    if success:
        print("\n✅ Коллекция 'words' успешно очищена.")
        sys.exit(0)
    else:
        print("\n❌ Не удалось очистить коллекцию. Проверьте логи для получения подробностей.", file=sys.stderr)
        sys.exit(1)
