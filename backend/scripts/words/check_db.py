#!/usr/bin/env python3
"""
Скрипт для проверки подключения к MongoDB Atlas.

Перед запуском убедитесь, что активировано виртуальное окружение:
  source venv/bin/activate

Проверяет:
1. Подключение к серверу
2. Наличие прав доступа
3. Доступ к коллекциям
"""

import os
import sys
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, OperationFailure
from dotenv import load_dotenv
from pathlib import Path

def print_status(message: str, status: bool):
    """Выводит сообщение с цветным статусом"""
    status_text = "✓" if status else "✗"
    status_color = "\033[92m" if status else "\033[91m"
    print(f"{status_color}{status_text} \033[0m {message}")
    return status

def check_mongodb_connection():
    """Проверяет подключение к MongoDB"""
    try:
        # Загружаем переменные окружения
        # Сначала пробуем загрузить .env.test для тестового окружения
        env_path = Path(__file__).parent.parent.parent / '.env.test'
        if not env_path.exists():
            # Если .env.test не найден, загружаем .env
            env_path = Path(__file__).parent.parent.parent / '.env'
        
        if env_path.exists():
            load_dotenv(dotenv_path=env_path, override=True)
        else:
            print(f"Файл конфигурации не найден: {env_path}")
            return False
        
        mongo_uri = os.getenv("MONGODB_URL")
        db_name = os.getenv("WORDS_DATABASE_NAME")
        
        if not mongo_uri or not db_name:
            print("Ошибка: Не заданы MONGODB_URL и/или WORDS_DATABASE_NAME в .env файле")
            return False
        
        # Проверяем подключение
        client = MongoClient(mongo_uri, serverSelectionTimeoutMS=5000)
        client.admin.command('ping')
        print_status("Успешное подключение к MongoDB", True)
        
        # Проверяем доступ к базе данных
        db = client[db_name]
        
        # Проверяем права доступа
        try:
            db.command('usersInfo')
            print_status("Есть права администратора", True)
        except OperationFailure:
            print_status("Нет прав администратора (но это не обязательно)", False)
        
        # Проверяем доступ к коллекциям
        try:
            db.words.find_one()
            print_status("Есть доступ к коллекции words", True)
        except OperationFailure:
            print_status("Нет доступа к коллекции words", False)
        
        # Выводим информацию о базе данных
        print("\nИнформация о базе данных:")
        print(f"- Имя базы: {db_name}")
        print(f"- Коллекции: {db.list_collection_names()}")
        
        # Проверяем наличие коллекции words
        if 'words' in db.list_collection_names():
            count = db.words.count_documents({})
            print(f"- Количество документов в коллекции words: {count}")
        
        return True
        
    except ConnectionFailure as e:
        print_status(f"Ошибка подключения к MongoDB: {str(e)}", False)
        return False
    except Exception as e:
        print_status(f"Неизвестная ошибка: {str(e)}", False)
        return False
    finally:
        if 'client' in locals():
            client.close()

if __name__ == "__main__":
    print("Проверка подключения к MongoDB...\n")
    success = check_mongodb_connection()
    
    if not success:
        print("\nУстранение неполадок:")
        print("1. Проверьте правильность строки подключения в .env файле")
        print("2. Убедитесь, что ваш IP-адрес добавлен в белый список в MongoDB Atlas")
        print("3. Проверьте имя пользователя и пароль")
        print("4. Убедитесь, что кластер запущен")
    
    sys.exit(0 if success else 1)
