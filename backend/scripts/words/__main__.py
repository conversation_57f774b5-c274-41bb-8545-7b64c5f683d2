#!/usr/bin/env python3
"""
Главный модуль для работы со словами.

Доступные команды:
  import     - Импорт слов из файлов
  clear      - Очистка коллекции слов
  validate   - Валидация файлов со словами
  check-db   - Проверка подключения к БД
  recent     - Просмотр недавно добавленных слов

Примеры использования:
  python -m scripts.words import
  python -m scripts.words validate path/to/file.json
"""

import sys
import argparse
from pathlib import Path
from typing import List, Optional

# Добавляем родительскую директорию в PYTHONPATH
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

def main():
    parser = argparse.ArgumentParser(description='Утилиты для работы со словами')
    subparsers = parser.add_subparsers(dest='command', help='Доступные команды')
    
    # Команда import
    import_parser = subparsers.add_parser('import', help='Импорт слов из файлов')
    
    # Команда clear
    clear_parser = subparsers.add_parser('clear', help='Очистка коллекции слов')
    clear_parser.add_argument('--force', action='store_true', help='Подтвердить удаление без подтверждения')
    
    # Команда validate
    validate_parser = subparsers.add_parser('validate', help='Валидация файлов со словами')
    validate_parser.add_argument('file', help='Путь к файлу для валидации')
    
    # Команда check-db
    subparsers.add_parser('check-db', help='Проверка подключения к БД')
    
    # Команда recent
    recent_parser = subparsers.add_parser('recent', help='Просмотр недавно добавленных слов')
    recent_parser.add_argument('--minutes', type=int, help='Искать слова, добавленные за последние N минут')
    recent_parser.add_argument('--level', type=str, help='Фильтр по уровню (A1, A2, B1, и т.д.)')
    recent_parser.add_argument('--lang', type=str, help='Фильтр по языку (например, en, ru)')
    recent_parser.add_argument('--tag', type=str, help='Фильтр по тегу')

    # Команда concept
    concept_parser = subparsers.add_parser('concept', help='Просмотр концепта и его слов')
    concept_parser.add_argument('concept_id', help='ID концепта для просмотра')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    try:
        if args.command == 'import':
            from . import import_words
            import asyncio
            asyncio.run(import_words.import_words())
            
        elif args.command == 'clear':
            if not args.force:
                confirm = input('Вы уверены, что хотите очистить коллекцию слов? (y/N): ')
                if confirm.lower() != 'y':
                    print('Отменено')
                    return
            from . import clear_words
            import asyncio
            asyncio.run(clear_words.clear_words_collection())
            
        elif args.command == 'validate':
            from . import validate_word
            validate_word.validate_file(Path(args.file))
            
        elif args.command == 'check-db':
            from . import check_db
            check_db.check_mongodb_connection()
            
        elif args.command == 'recent':
            from . import check_recent
            import asyncio
            asyncio.run(check_recent.get_recent_words(
                minutes=args.minutes,
                level=args.level,
                lang=args.lang,
                tag=args.tag
            ))

        elif args.command == 'concept':
            try:
                from ..concepts import ConceptsManager
                manager = ConceptsManager()
                data = manager.get_concept_with_words(args.concept_id)
                if data:
                    concept = data['concept']
                    words = data['words']
                    print(f"\n🧠 Концепт: {concept['concept_name']}")
                    print(f"📋 ID: {concept['_id']}")
                    print(f"🎯 Уровень: {concept['level']} / {concept['priority']}")
                    print(f"📂 Категория: {concept['category']}")
                    print(f"🌍 Языков: {len(words)}")
                    print(f"\n📝 Описание:")
                    print(f"  EN: {concept['description']['en']}")
                    print(f"  RU: {concept['description']['ru']}")
                    print(f"\n🔤 Слова по языкам:")
                    for word in sorted(words, key=lambda x: x['language']):
                        print(f"  {word['language']}: {word['word']}")
                else:
                    print(f"❌ Концепт {args.concept_id} не найден")
            except ImportError:
                print("❌ Модуль концептов недоступен. Установите зависимости.")
            except Exception as e:
                print(f"❌ Ошибка при получении концепта: {e}")
            
    except ImportError as e:
        print(f'Ошибка: {e}')
        sys.exit(1)
    except Exception as e:
        print(f'Ошибка при выполнении команды: {e}')
        sys.exit(1)

if __name__ == '__main__':
    main()
