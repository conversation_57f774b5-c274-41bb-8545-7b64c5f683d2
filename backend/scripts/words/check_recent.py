#!/usr/bin/env python3
"""
Скрипт для проверки недавно добавленных слов в базе данных.

Перед запуском убедитесь, что активировано виртуальное окружение:
  source venv/bin/activate

Использование:
    python -m scripts.words recent [--minutes MINUTES] [--level LEVEL] [--lang LANG] [--tag TAG]

Примеры:
    # Показать слова, добавленные за последние 60 минут
    python -m scripts.words recent --minutes 60
    
    # Показать слова уровня A1
    python -m scripts.words recent --level A1
    
    # Показать русские слова
    python -m scripts.words recent --lang ru
    
    # Показать слова с тегом 'животное'
    python -m scripts.words recent --tag "животное"
"""

import asyncio
import os
import argparse
from datetime import datetime, timedelta, timezone
from motor.motor_asyncio import AsyncIOMotorClient
from dotenv import load_dotenv
from pathlib import Path
from bson import ObjectId

# Настройка логирования
import logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Загружаем переменные окружения из .env файла
env_paths = [
    Path(__file__).parent.parent.parent / '.env',  # Корень проекта
    Path(__file__).parent.parent / '.env',         # Родительская директория
    Path(__file__).parent / '.env'                  # Текущая директория
]

loaded = False
for path in env_paths:
    if path.exists():
        logger.info(f"Загружаем переменные окружения из: {path}")
        load_dotenv(dotenv_path=path, override=True)
        loaded = True
        break

if not loaded:
    logger.error("Не найден .env файл ни в одной из проверенных директорий")
    exit(1)

async def get_recent_words(minutes=None, level=None, lang=None, tag=None):
    """Получить недавно добавленные слова с возможностью фильтрации"""
    client = None
    try:
        # Подключаемся к MongoDB
        mongo_uri = os.getenv("MONGODB_URL")
        db_name = os.getenv("DATABASE_NAME")
        
        if not mongo_uri or not db_name:
            logger.error("Не заданы MONGODB_URL и/или DATABASE_NAME в .env файле")
            return []
        
        client = AsyncIOMotorClient(mongo_uri)
        db = client[db_name]
        
        # Собираем фильтры
        query = {}
        
        # Фильтр по времени
        if minutes is not None:
            time_threshold = datetime.now(timezone.utc) - timedelta(minutes=minutes)
            query["updated_at"] = {"$gte": time_threshold}
        
        # Фильтр по уровню
        if level:
            query["level"] = level.upper()
        
        # Фильтр по языку
        if lang:
            query["language"] = lang.lower()
        
        # Фильтр по тегу
        if tag:
            query["tags"] = {"$in": [tag]}
        
        logger.info(f"Выполняем запрос с фильтрами: {query}")
        
        # Выполняем запрос
        cursor = db.words.find(query).sort("updated_at", -1)
        results = await cursor.to_list(length=100)  # Ограничиваем 100 результатами
        
        return results
        
    except Exception as e:
        logger.error(f"Ошибка при получении слов: {str(e)}")
        return []
    finally:
        if client:
            client.close()

def format_word(word_data):
    """Форматирование вывода информации о слове"""
    word = word_data.get('word', 'Без названия')
    lang = word_data.get('language', '??').upper()
    pos = word_data.get('part_of_speech', '??')
    level = word_data.get('level', '??')
    
    # Форматируем дату
    updated = word_data.get('updated_at', datetime.utcnow())
    if isinstance(updated, dict) and '$date' in updated:
        updated = updated['$date']
    if isinstance(updated, str):
        updated = datetime.fromisoformat(updated.replace('Z', '+00:00'))
    
    time_ago = datetime.now(timezone.utc) - updated.replace(tzinfo=timezone.utc)
    minutes_ago = int(time_ago.total_seconds() / 60)
    
    # Формируем основную информацию
    result = [
        f"Слово: {word}",
        f"Язык: {lang}",
        f"Часть речи: {pos}",
        f"Уровень: {level}",
        f"Добавлено: {updated.strftime('%Y-%m-%d %H:%M:%S')} ({minutes_ago} мин. назад)",
        ""
    ]
    
    return "\n".join(result)

def main():
    parser = argparse.ArgumentParser(description='Поиск недавно добавленных слов')
    parser.add_argument('--minutes', type=int, help='Искать слова, добавленные за последние N минут')
    parser.add_argument('--level', type=str, help='Фильтр по уровню (A1, A2, B1, и т.д.)')
    parser.add_argument('--lang', type=str, help='Фильтр по языку (например, en, ru)')
    parser.add_argument('--tag', type=str, help='Фильтр по тегу')
    
    args = parser.parse_args()
    
    # Если не задано ни одного фильтра, показываем за последние 60 минут
    if not any([args.minutes, args.level, args.lang, args.tag]):
        args.minutes = 60
        print("Показываем слова, добавленные за последний час. Используйте --help для других опций.")
    
    # Запускаем асинхронную функцию
    loop = asyncio.get_event_loop()
    words = loop.run_until_complete(
        get_recent_words(
            minutes=args.minutes,
            level=args.level,
            lang=args.lang,
            tag=args.tag
        )
    )
    
    # Выводим результаты
    if not words:
        print("Слова не найдены по заданным критериям.")
        return
    
    # Удаляем дубликаты по полю word
    unique_words = {}
    for word in words:
        word_text = word.get('word', '').lower()
        if word_text not in unique_words:
            unique_words[word_text] = word
    
    print(f"\nНайдено уникальных слов: {len(unique_words)}")
    print("=" * 70)
    
    for word in unique_words.values():
        print(format_word(word))
        
        # Показываем дополнительную информацию, если есть
        if 'examples' in word and len(word['examples']) > 0:
            print("Примеры использования:")
            for i, example in enumerate(word['examples'], 1):
                print(f"  {i}. {example.get('sentence', '')}")
                if 'correct_answers' in example:
                    print(f"     Возможные ответы: {', '.join(example['correct_answers'])}")
        
        if 'tags' in word and word['tags']:
            print(f"Теги: {', '.join(word['tags'])}")
            
        print("-" * 70)

if __name__ == "__main__":
    main()
