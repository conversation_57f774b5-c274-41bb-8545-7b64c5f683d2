#!/usr/bin/env python3
"""
Скрипт для исправления кода языка Себуано с "ceb" на "cb" в JSON файлах.
"""

import os
import json
import glob
from pathlib import Path

def fix_cebuano_in_json_files():
    """Исправляет код языка Себуано с 'ceb' на 'cb' во всех JSON файлах в папке A0."""
    
    # Путь к папке с JSON файлами
    json_dir = Path(__file__).parent.parent / "data" / "words" / "A0"
    
    if not json_dir.exists():
        print(f"❌ Папка {json_dir} не найдена!")
        return
    
    print(f"🔍 Поиск JSON файлов в папке: {json_dir}")
    
    # Находим все JSON файлы
    json_files = list(json_dir.glob("*.json"))
    
    if not json_files:
        print("❌ JSON файлы не найдены!")
        return
    
    print(f"📁 Найдено {len(json_files)} JSON файлов")
    
    total_changes = 0
    modified_files = []
    
    for json_file in json_files:
        print(f"\n🔧 Обрабатываем файл: {json_file.name}")
        
        try:
            # Читаем файл
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            file_changes = 0
            
            # Проверяем и исправляем каждую концепцию
            for concept in data.get('concepts', []):
                for word_key in ['words', 'translations']:
                    if word_key in concept:
                        for word in concept[word_key]:
                            if word.get('language') == 'ceb':
                                print(f"  ✏️  Исправляем язык с 'ceb' на 'cb' для слова: {word.get('word', 'N/A')}")
                                word['language'] = 'cb'
                                file_changes += 1
            
            # Если были изменения, сохраняем файл
            if file_changes > 0:
                with open(json_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                
                modified_files.append(json_file.name)
                total_changes += file_changes
                print(f"  ✅ Файл сохранен с {file_changes} изменениями")
            else:
                print(f"  ℹ️  Изменения не требуются")
                
        except Exception as e:
            print(f"  ❌ Ошибка при обработке файла {json_file.name}: {e}")
    
    print(f"\n📊 Итоги:")
    print(f"Всего изменений: {total_changes}")
    print(f"Измененных файлов: {len(modified_files)}")
    
    if modified_files:
        print(f"Список измененных файлов:")
        for filename in modified_files:
            print(f"  - {filename}")
    
    if total_changes > 0:
        print(f"\n✅ Исправление JSON файлов завершено!")
    else:
        print(f"\n✅ Все JSON файлы уже корректны!")

if __name__ == "__main__":
    print("🔧 Скрипт исправления кода языка Себуано в JSON файлах")
    print("=" * 60)
    fix_cebuano_in_json_files()
