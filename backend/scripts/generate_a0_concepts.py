#!/usr/bin/env python3
"""
Скрипт для генерации UUID и создания концептов для всех A0 слов.
"""

import json
import uuid
from pathlib import Path
from typing import Dict, List, Any

# Полный список A0 слов с категориями
A0_WORDS = {
    # ULTRA-CORE (1-15) - первые слова ребенка
    "ultra_core": [
        {"ru": "я", "en": "I", "category": "pronouns", "semantic_field": "personal_identity"},
        {"ru": "ты", "en": "you", "category": "pronouns", "semantic_field": "personal_identity"},
        {"ru": "да", "en": "yes", "category": "affirmation", "semantic_field": "agreement"},
        {"ru": "нет", "en": "no", "category": "negation", "semantic_field": "disagreement"},
        {"ru": "что", "en": "what", "category": "questions", "semantic_field": "information_seeking"},
        {"ru": "где", "en": "where", "category": "questions", "semantic_field": "location_seeking"},
        {"ru": "хочу", "en": "want", "category": "desires", "semantic_field": "basic_needs"},
        {"ru": "есть", "en": "eat", "category": "actions", "semantic_field": "basic_needs"},
        {"ru": "пить", "en": "drink", "category": "actions", "semantic_field": "basic_needs"},
        {"ru": "помощь", "en": "help", "category": "assistance", "semantic_field": "social_interaction"},
        {"ru": "спасибо", "en": "thank you", "category": "politeness", "semantic_field": "social_interaction"},
        {"ru": "извините", "en": "sorry", "category": "politeness", "semantic_field": "social_interaction"},
        {"ru": "врач", "en": "doctor", "category": "people", "semantic_field": "emergency"},
        {"ru": "дом", "en": "home", "category": "places", "semantic_field": "basic_locations"},
        {"ru": "вода", "en": "water", "category": "substances", "semantic_field": "basic_needs"}
    ],
    
    # CORE (16-50) - базовые слова для выживания
    "core": [
        {"ru": "мама", "en": "mother", "category": "family", "semantic_field": "family_relations"},
        {"ru": "папа", "en": "father", "category": "family", "semantic_field": "family_relations"},
        {"ru": "ребенок", "en": "child", "category": "family", "semantic_field": "family_relations"},
        {"ru": "человек", "en": "person", "category": "people", "semantic_field": "basic_identity"},
        {"ru": "мужчина", "en": "man", "category": "people", "semantic_field": "basic_identity"},
        {"ru": "женщина", "en": "woman", "category": "people", "semantic_field": "basic_identity"},
        {"ru": "друг", "en": "friend", "category": "people", "semantic_field": "social_relations"},
        {"ru": "имя", "en": "name", "category": "identity", "semantic_field": "personal_identity"},
        {"ru": "работа", "en": "work", "category": "activities", "semantic_field": "daily_life"},
        {"ru": "деньги", "en": "money", "category": "objects", "semantic_field": "basic_needs"},
        {"ru": "время", "en": "time", "category": "concepts", "semantic_field": "temporal"},
        {"ru": "день", "en": "day", "category": "time", "semantic_field": "temporal"},
        {"ru": "ночь", "en": "night", "category": "time", "semantic_field": "temporal"},
        {"ru": "сегодня", "en": "today", "category": "time", "semantic_field": "temporal"},
        {"ru": "вчера", "en": "yesterday", "category": "time", "semantic_field": "temporal"},
        {"ru": "завтра", "en": "tomorrow", "category": "time", "semantic_field": "temporal"},
        {"ru": "год", "en": "year", "category": "time", "semantic_field": "temporal"},
        {"ru": "месяц", "en": "month", "category": "time", "semantic_field": "temporal"},
        {"ru": "неделя", "en": "week", "category": "time", "semantic_field": "temporal"},
        {"ru": "час", "en": "hour", "category": "time", "semantic_field": "temporal"},
        {"ru": "минута", "en": "minute", "category": "time", "semantic_field": "temporal"},
        {"ru": "страна", "en": "country", "category": "places", "semantic_field": "geography"},
        {"ru": "город", "en": "city", "category": "places", "semantic_field": "geography"},
        {"ru": "улица", "en": "street", "category": "places", "semantic_field": "geography"},
        {"ru": "магазин", "en": "shop", "category": "places", "semantic_field": "daily_life"},
        {"ru": "больница", "en": "hospital", "category": "places", "semantic_field": "emergency"},
        {"ru": "школа", "en": "school", "category": "places", "semantic_field": "education"},
        {"ru": "машина", "en": "car", "category": "transport", "semantic_field": "transportation"},
        {"ru": "автобус", "en": "bus", "category": "transport", "semantic_field": "transportation"},
        {"ru": "поезд", "en": "train", "category": "transport", "semantic_field": "transportation"},
        {"ru": "самолет", "en": "airplane", "category": "transport", "semantic_field": "transportation"},
        {"ru": "еда", "en": "food", "category": "substances", "semantic_field": "basic_needs"},
        {"ru": "хлеб", "en": "bread", "category": "food", "semantic_field": "basic_needs"},
        {"ru": "молоко", "en": "milk", "category": "food", "semantic_field": "basic_needs"},
        {"ru": "мясо", "en": "meat", "category": "food", "semantic_field": "basic_needs"}
    ]
}

def generate_concept_id() -> str:
    """Генерирует UUID для концепта"""
    return str(uuid.uuid4())

def create_concept_description(word_data: Dict[str, str], concept_id: str, priority: str, index: int) -> Dict[str, Any]:
    """Создает описание концепта"""
    
    # Базовые описания по категориям
    category_descriptions = {
        "pronouns": {
            "en": f"Personal pronoun - {word_data['en']}",
            "ru": f"Личное местоимение - {word_data['ru']}"
        },
        "affirmation": {
            "en": f"Affirmative response - {word_data['en']}",
            "ru": f"Утвердительный ответ - {word_data['ru']}"
        },
        "negation": {
            "en": f"Negative response - {word_data['en']}",
            "ru": f"Отрицательный ответ - {word_data['ru']}"
        },
        "questions": {
            "en": f"Interrogative word - {word_data['en']}",
            "ru": f"Вопросительное слово - {word_data['ru']}"
        },
        "desires": {
            "en": f"Expression of desire - {word_data['en']}",
            "ru": f"Выражение желания - {word_data['ru']}"
        },
        "actions": {
            "en": f"Basic action - {word_data['en']}",
            "ru": f"Базовое действие - {word_data['ru']}"
        },
        "assistance": {
            "en": f"Request for assistance - {word_data['en']}",
            "ru": f"Просьба о помощи - {word_data['ru']}"
        },
        "politeness": {
            "en": f"Polite expression - {word_data['en']}",
            "ru": f"Вежливое выражение - {word_data['ru']}"
        },
        "people": {
            "en": f"Person or profession - {word_data['en']}",
            "ru": f"Человек или профессия - {word_data['ru']}"
        },
        "places": {
            "en": f"Location or place - {word_data['en']}",
            "ru": f"Место или локация - {word_data['ru']}"
        },
        "substances": {
            "en": f"Basic substance - {word_data['en']}",
            "ru": f"Базовое вещество - {word_data['ru']}"
        },
        "family": {
            "en": f"Family member - {word_data['en']}",
            "ru": f"Член семьи - {word_data['ru']}"
        },
        "identity": {
            "en": f"Identity concept - {word_data['en']}",
            "ru": f"Понятие идентичности - {word_data['ru']}"
        },
        "activities": {
            "en": f"Daily activity - {word_data['en']}",
            "ru": f"Повседневная деятельность - {word_data['ru']}"
        },
        "objects": {
            "en": f"Basic object - {word_data['en']}",
            "ru": f"Базовый объект - {word_data['ru']}"
        },
        "concepts": {
            "en": f"Abstract concept - {word_data['en']}",
            "ru": f"Абстрактное понятие - {word_data['ru']}"
        },
        "time": {
            "en": f"Time concept - {word_data['en']}",
            "ru": f"Временное понятие - {word_data['ru']}"
        },
        "transport": {
            "en": f"Transportation - {word_data['en']}",
            "ru": f"Транспорт - {word_data['ru']}"
        },
        "food": {
            "en": f"Food item - {word_data['en']}",
            "ru": f"Продукт питания - {word_data['ru']}"
        }
    }
    
    category = word_data["category"]
    description = category_descriptions.get(category, {
        "en": f"Basic concept - {word_data['en']}",
        "ru": f"Базовое понятие - {word_data['ru']}"
    })
    
    # Создаем concept_name из английского слова
    concept_name = word_data["en"].lower().replace(" ", "_").replace("-", "_")
    
    return {
        "concept_id": concept_id,
        "level": "A0",
        "priority": priority,
        "category": category,
        "concept_name": concept_name,
        "description": description,
        "semantic_field": word_data["semantic_field"],
        "usage_context": "basic_communication",
        "examples": {
            "en": f"Example with '{word_data['en']}'",
            "ru": f"Пример с '{word_data['ru']}'"
        },
        "translation_notes": {
            "general": f"Use the most common translation for '{word_data['en']}' in each language"
        },
        "created_at": "2025-06-23T12:00:00Z",
        "updated_at": "2025-06-23T12:00:00Z"
    }

def generate_all_concepts():
    """Генерирует все концепты для A0 слов"""
    all_concepts = []
    concept_mapping = {}
    
    index = 1
    
    # Обрабатываем ULTRA-CORE
    for word_data in A0_WORDS["ultra_core"]:
        concept_id = generate_concept_id()
        concept = create_concept_description(word_data, concept_id, "ultra_core", index)
        all_concepts.append(concept)
        
        concept_mapping[f"{index:02d}"] = {
            "ru": word_data["ru"],
            "en": word_data["en"],
            "concept_id": concept_id,
            "priority": "ultra_core"
        }
        index += 1
    
    # Обрабатываем CORE
    for word_data in A0_WORDS["core"]:
        concept_id = generate_concept_id()
        concept = create_concept_description(word_data, concept_id, "core", index)
        all_concepts.append(concept)
        
        concept_mapping[f"{index:02d}"] = {
            "ru": word_data["ru"],
            "en": word_data["en"],
            "concept_id": concept_id,
            "priority": "core"
        }
        index += 1
    
    return all_concepts, concept_mapping

def main():
    """Основная функция"""
    print("🧠 Генерация концептов для всех A0 слов...")
    
    # Создаем директории
    concepts_dir = Path("data/concepts")
    concepts_dir.mkdir(exist_ok=True)
    
    # Генерируем концепты
    all_concepts, concept_mapping = generate_all_concepts()
    
    # Сохраняем полный файл концептов
    concepts_file = concepts_dir / "A0_all_concepts.json"
    with open(concepts_file, 'w', encoding='utf-8') as f:
        json.dump(all_concepts, f, ensure_ascii=False, indent=2)
    
    print(f"✅ Создан файл концептов: {concepts_file}")
    print(f"📊 Всего концептов: {len(all_concepts)}")
    
    # Сохраняем маппинг для обновления списка слов
    mapping_file = concepts_dir / "A0_concept_mapping.json"
    with open(mapping_file, 'w', encoding='utf-8') as f:
        json.dump(concept_mapping, f, ensure_ascii=False, indent=2)
    
    print(f"✅ Создан файл маппинга: {mapping_file}")
    
    # Статистика
    ultra_core_count = len(A0_WORDS["ultra_core"])
    core_count = len(A0_WORDS["core"])
    
    print(f"\n📈 Статистика:")
    print(f"  ULTRA-CORE: {ultra_core_count} концептов")
    print(f"  CORE: {core_count} концептов")
    print(f"  ВСЕГО: {len(all_concepts)} концептов")
    
    print(f"\n🎯 Следующие шаги:")
    print(f"  1. Проверить файл: {concepts_file}")
    print(f"  2. Импортировать: python -m scripts.concepts import {concepts_file}")
    print(f"  3. Обновить md/A0_word_list.md с новыми concept_id")

if __name__ == "__main__":
    main()
