#!/usr/bin/env python3
"""
Скрипт для работы с концептами в базе данных.
"""

import json
import sys
import logging
from pathlib import Path
from typing import List, Dict, Any
from pymongo import MongoClient
from pymongo.errors import DuplicateKeyError, BulkWriteError
import os
from dotenv import load_dotenv

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ConceptsManager:
    def __init__(self):
        """Инициализация менеджера концептов"""
        load_dotenv()
        
        # Подключение к MongoDB
        mongodb_url = os.getenv('MONGODB_URL')
        if not mongodb_url:
            raise ValueError("MONGODB_URL не найден в переменных окружения")
        
        self.client = MongoClient(mongodb_url)
        self.db = self.client[os.getenv('DATABASE_NAME', 'word_master')]
        self.concepts_collection = self.db.concepts  # ← НОВАЯ коллекция concepts
        self.words_collection = self.db.words        # ← Существующая коллекция words

        logger.info(f"Подключен к базе данных: {self.db.name}")
        logger.info(f"Коллекция концептов: {self.concepts_collection.name}")
        logger.info(f"Коллекция слов: {self.words_collection.name}")

    def import_concepts(self, file_path: str) -> Dict[str, Any]:
        """
        Импорт концептов из JSON файла
        
        Args:
            file_path: Путь к JSON файлу с концептами
            
        Returns:
            Статистика импорта
        """
        try:
            # Загрузка данных из файла
            with open(file_path, 'r', encoding='utf-8') as f:
                concepts_data = json.load(f)
            
            if not isinstance(concepts_data, list):
                raise ValueError("JSON файл должен содержать массив концептов")
            
            logger.info(f"Загружено {len(concepts_data)} концептов из {file_path}")
            
            # Подготовка данных для импорта
            concepts_to_insert = []
            for concept in concepts_data:
                # Используем concept_id как _id для связи с words
                concept_doc = {
                    '_id': concept['concept_id'],
                    **{k: v for k, v in concept.items() if k != 'concept_id'}
                }
                concepts_to_insert.append(concept_doc)
            
            # Импорт в базу данных
            result = self.concepts_collection.insert_many(
                concepts_to_insert, 
                ordered=False  # Продолжать при дублях
            )
            
            stats = {
                'total_concepts': len(concepts_data),
                'inserted': len(result.inserted_ids),
                'skipped': len(concepts_data) - len(result.inserted_ids),
                'file': file_path
            }
            
            logger.info(f"Импорт завершен: {stats}")
            return stats
            
        except BulkWriteError as e:
            # Обработка дублей
            inserted_count = e.details.get('nInserted', 0)
            duplicate_count = len([err for err in e.details.get('writeErrors', []) 
                                 if err.get('code') == 11000])
            
            stats = {
                'total_concepts': len(concepts_data),
                'inserted': inserted_count,
                'skipped': duplicate_count,
                'file': file_path
            }
            
            logger.info(f"Импорт завершен с дублями: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"Ошибка импорта концептов: {e}")
            raise

    def validate_concepts_file(self, file_path: str) -> Dict[str, Any]:
        """
        Валидация файла концептов
        
        Args:
            file_path: Путь к JSON файлу
            
        Returns:
            Результат валидации
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                concepts_data = json.load(f)
            
            if not isinstance(concepts_data, list):
                return {'valid': False, 'error': 'JSON должен содержать массив'}
            
            errors = []
            required_fields = ['concept_id', 'level', 'priority', 'category', 'concept_name', 'description']
            
            for i, concept in enumerate(concepts_data):
                # Проверка обязательных полей
                for field in required_fields:
                    if field not in concept:
                        errors.append(f"Концепт {i+1}: отсутствует поле '{field}'")
                
                # Проверка формата concept_id (UUID)
                concept_id = concept.get('concept_id', '')
                if not self._is_valid_uuid(concept_id):
                    errors.append(f"Концепт {i+1}: неверный формат concept_id '{concept_id}'")
                
                # Проверка описания
                description = concept.get('description', {})
                if not isinstance(description, dict) or 'en' not in description or 'ru' not in description:
                    errors.append(f"Концепт {i+1}: description должно содержать 'en' и 'ru'")
            
            result = {
                'valid': len(errors) == 0,
                'total_concepts': len(concepts_data),
                'errors': errors,
                'file': file_path
            }
            
            if result['valid']:
                logger.info(f"✅ Файл {file_path} прошел валидацию")
            else:
                logger.error(f"❌ Файл {file_path} содержит ошибки:")
                for error in errors:
                    logger.error(f"  - {error}")
            
            return result
            
        except Exception as e:
            return {
                'valid': False,
                'error': f"Ошибка чтения файла: {e}",
                'file': file_path
            }

    def get_concept_by_id(self, concept_id: str) -> Dict[str, Any]:
        """Получить концепт по ID"""
        return self.concepts_collection.find_one({'_id': concept_id})

    def get_words_by_concept(self, concept_id: str) -> List[Dict[str, Any]]:
        """Получить все слова концепта"""
        return list(self.words_collection.find({'concept_id': concept_id}))

    def get_concept_with_words(self, concept_id: str) -> Dict[str, Any]:
        """Получить концепт с его словами"""
        concept = self.get_concept_by_id(concept_id)
        if not concept:
            return None
        
        words = self.get_words_by_concept(concept_id)
        
        return {
            'concept': concept,
            'words': words,
            'languages_count': len(words),
            'languages': [word['language'] for word in words]
        }

    def list_concepts(self, level: str = None, priority: str = None) -> List[Dict[str, Any]]:
        """Список концептов с фильтрацией"""
        query = {}
        if level:
            query['level'] = level
        if priority:
            query['priority'] = priority
        
        return list(self.concepts_collection.find(query))

    def clear_concepts(self) -> int:
        """Очистить коллекцию концептов"""
        result = self.concepts_collection.delete_many({})
        logger.info(f"Удалено {result.deleted_count} концептов")
        return result.deleted_count

    def _is_valid_uuid(self, uuid_string: str) -> bool:
        """Проверка формата UUID"""
        import re
        uuid_pattern = re.compile(
            r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$',
            re.IGNORECASE
        )
        return bool(uuid_pattern.match(uuid_string))

def main():
    """Основная функция CLI"""
    if len(sys.argv) < 2:
        print("Использование:")
        print("  python -m scripts.concepts import <file_path>")
        print("  python -m scripts.concepts validate <file_path>")
        print("  python -m scripts.concepts list [level] [priority]")
        print("  python -m scripts.concepts get <concept_id>")
        print("  python -m scripts.concepts clear")
        sys.exit(1)
    
    command = sys.argv[1]
    manager = ConceptsManager()
    
    try:
        if command == 'import':
            if len(sys.argv) < 3:
                print("Укажите путь к файлу")
                sys.exit(1)
            file_path = sys.argv[2]
            stats = manager.import_concepts(file_path)
            print(f"✅ Импорт завершен: {stats}")
            
        elif command == 'validate':
            if len(sys.argv) < 3:
                print("Укажите путь к файлу")
                sys.exit(1)
            file_path = sys.argv[2]
            result = manager.validate_concepts_file(file_path)
            if result['valid']:
                print("✅ Файл валиден")
            else:
                print("❌ Файл содержит ошибки")
                sys.exit(1)
                
        elif command == 'list':
            level = sys.argv[2] if len(sys.argv) > 2 else None
            priority = sys.argv[3] if len(sys.argv) > 3 else None
            concepts = manager.list_concepts(level, priority)
            print(f"Найдено {len(concepts)} концептов:")
            for concept in concepts:
                print(f"  {concept['_id']}: {concept['concept_name']} ({concept['level']}/{concept['priority']})")
                
        elif command == 'get':
            if len(sys.argv) < 3:
                print("Укажите concept_id")
                sys.exit(1)
            concept_id = sys.argv[2]
            data = manager.get_concept_with_words(concept_id)
            if data:
                print(f"Концепт: {data['concept']['concept_name']}")
                print(f"Языков: {data['languages_count']}")
                print(f"Описание (en): {data['concept']['description']['en']}")
            else:
                print("Концепт не найден")
                
        elif command == 'clear':
            count = manager.clear_concepts()
            print(f"✅ Удалено {count} концептов")
            
        else:
            print(f"Неизвестная команда: {command}")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Ошибка выполнения команды: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
