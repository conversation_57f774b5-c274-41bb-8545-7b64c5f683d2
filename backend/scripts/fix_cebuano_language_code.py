#!/usr/bin/env python3
"""
Скрипт для исправления кода языка Себуано с "ceb" на "cb" в коллекции MongoDB.
"""

import os
import sys
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure

# Добавляем путь к корневой директории проекта
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def fix_cebuano_language_code():
    """Исправляет код языка Себуано с 'ceb' на 'cb' во всех документах коллекции words."""
    
    try:
        # Подключение к MongoDB
        client = MongoClient('mongodb://localhost:27017/')
        db = client['word_master']
        collection = db['words']
        
        print("Подключение к MongoDB успешно!")
        print(f"База данных: {db.name}")
        print(f"Коллекция: {collection.name}")
        
        # Проверяем количество документов с кодом "ceb"
        ceb_count = collection.count_documents({
            "$or": [
                {"native_word.language": "ceb"},
                {"target_word.language": "ceb"}
            ]
        })
        
        print(f"\nНайдено документов с кодом 'ceb': {ceb_count}")
        
        if ceb_count == 0:
            print("Документы с кодом 'ceb' не найдены. Исправление не требуется.")
            return
        
        # Подтверждение от пользователя
        response = input(f"\nВы хотите исправить {ceb_count} документов? (y/N): ")
        if response.lower() != 'y':
            print("Операция отменена.")
            return
        
        # Исправляем native_word.language
        result1 = collection.update_many(
            {"native_word.language": "ceb"},
            {"$set": {"native_word.language": "cb"}}
        )
        
        # Исправляем target_word.language
        result2 = collection.update_many(
            {"target_word.language": "ceb"},
            {"$set": {"target_word.language": "cb"}}
        )
        
        total_modified = result1.modified_count + result2.modified_count
        
        print(f"\nИсправление завершено!")
        print(f"Обновлено native_word.language: {result1.modified_count} документов")
        print(f"Обновлено target_word.language: {result2.modified_count} документов")
        print(f"Всего обновлено: {total_modified} документов")
        
        # Проверяем результат
        remaining_ceb = collection.count_documents({
            "$or": [
                {"native_word.language": "ceb"},
                {"target_word.language": "ceb"}
            ]
        })
        
        cb_count = collection.count_documents({
            "$or": [
                {"native_word.language": "cb"},
                {"target_word.language": "cb"}
            ]
        })
        
        print(f"\nПроверка результата:")
        print(f"Осталось документов с кодом 'ceb': {remaining_ceb}")
        print(f"Документов с кодом 'cb': {cb_count}")
        
        if remaining_ceb == 0:
            print("✅ Все коды языка Себуано успешно исправлены!")
        else:
            print("⚠️ Остались документы с кодом 'ceb'. Проверьте логику скрипта.")
            
    except ConnectionFailure:
        print("❌ Ошибка подключения к MongoDB. Убедитесь, что сервер запущен.")
    except Exception as e:
        print(f"❌ Произошла ошибка: {e}")
    finally:
        if 'client' in locals():
            client.close()
            print("\nСоединение с MongoDB закрыто.")

if __name__ == "__main__":
    print("🔧 Скрипт исправления кода языка Себуано")
    print("=" * 50)
    fix_cebuano_language_code()
