#!/usr/bin/env python3
"""
Скрипт для добавления поля language_level в настройки существующих пользователей
"""

import asyncio
import os
import sys
from motor.motor_asyncio import AsyncIOMotorClient
from pymongo import UpdateOne
from dotenv import load_dotenv

# Добавляем путь к корневой директории проекта
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Загружаем переменные окружения из .env файла
load_dotenv(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env'))

async def migrate_users_language_level():
    """Добавляет поле language_level в настройки всех пользователей, у которых его нет"""
    
    # Подключение к базе данных
    MONGODB_URL = os.getenv("MONGODB_URL")
    if not MONGODB_URL:
        print("❌ Переменная окружения MONGODB_URL не найдена")
        return

    DATABASE_NAME = "users_db"  # Используем правильное имя базы данных
    
    client = AsyncIOMotorClient(MONGODB_URL)
    db = client[DATABASE_NAME]
    users_collection = db.users
    
    try:
        print("🔍 Поиск пользователей без поля language_level...")
        
        # Находим всех пользователей, у которых нет поля language_level в настройках
        users_without_level = await users_collection.find({
            "$or": [
                {"settings.language_level": {"$exists": False}},
                {"settings.language_level": None}
            ]
        }).to_list(length=None)
        
        if not users_without_level:
            print("✅ Все пользователи уже имеют поле language_level")
            return
        
        print(f"📝 Найдено {len(users_without_level)} пользователей для обновления")
        
        # Подготавливаем операции обновления
        update_operations = []
        for user in users_without_level:
            user_id = user["_id"]
            
            # Получаем текущие настройки или создаем пустые
            current_settings = user.get("settings", {})
            
            # Добавляем language_level со значением по умолчанию A0
            current_settings["language_level"] = "A0"
            
            update_operations.append(
                UpdateOne(
                    {"_id": user_id},
                    {"$set": {"settings": current_settings}}
                )
            )
            
            print(f"  📧 {user.get('email', 'unknown')} -> добавляем language_level: A0")
        
        # Выполняем массовое обновление
        if update_operations:
            result = await users_collection.bulk_write(update_operations)
            print(f"✅ Обновлено {result.modified_count} пользователей")
        
        print("🎉 Миграция завершена успешно!")
        
    except Exception as e:
        print(f"❌ Ошибка при выполнении миграции: {e}")
        raise
    finally:
        client.close()

async def main():
    """Главная функция"""
    print("🚀 Запуск миграции для добавления language_level...")
    await migrate_users_language_level()

if __name__ == "__main__":
    asyncio.run(main())
