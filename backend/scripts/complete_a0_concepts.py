#!/usr/bin/env python3
"""
Скрипт для создания недостающих концептов A0 с учетом существующих UUID.
"""

import json
import uuid
from pathlib import Path
from typing import Dict, List, Any

# Существующие UUID (уже созданы)
EXISTING_CONCEPTS = {
    1: {"uuid": "550e8400-e29b-41d4-a716-************", "ru": "я", "en": "I"},
    2: {"uuid": "550e8400-e29b-41d4-a716-************", "ru": "ты", "en": "you"},
    3: {"uuid": "550e8400-e29b-41d4-a716-************", "ru": "да", "en": "yes"},
    4: {"uuid": "550e8400-e29b-41d4-a716-************", "ru": "нет", "en": "no"},
    5: {"uuid": "550e8400-e29b-41d4-a716-************", "ru": "что", "en": "what"},
    6: {"uuid": "550e8400-e29b-41d4-a716-************", "ru": "где", "en": "where"}
}

# Полный список A0 слов (исправленный на основе документации)
A0_WORDS = {
    # ULTRA-CORE (1-15) - первые слова ребенка
    1: {"ru": "я", "en": "I", "category": "pronouns", "semantic_field": "personal_identity"},
    2: {"ru": "ты", "en": "you", "category": "pronouns", "semantic_field": "personal_identity"},
    3: {"ru": "да", "en": "yes", "category": "affirmation", "semantic_field": "agreement"},
    4: {"ru": "нет", "en": "no", "category": "negation", "semantic_field": "disagreement"},
    5: {"ru": "что", "en": "what", "category": "questions", "semantic_field": "information_seeking"},
    6: {"ru": "где", "en": "where", "category": "questions", "semantic_field": "location_seeking"},
    7: {"ru": "хочу", "en": "want", "category": "desires", "semantic_field": "basic_needs"},
    8: {"ru": "есть", "en": "eat", "category": "actions", "semantic_field": "basic_needs"},
    9: {"ru": "пить", "en": "drink", "category": "actions", "semantic_field": "basic_needs"},
    10: {"ru": "помощь", "en": "help", "category": "assistance", "semantic_field": "social_interaction"},
    11: {"ru": "спасибо", "en": "thank you", "category": "politeness", "semantic_field": "social_interaction"},
    12: {"ru": "извините", "en": "sorry", "category": "politeness", "semantic_field": "social_interaction"},
    13: {"ru": "врач", "en": "doctor", "category": "people", "semantic_field": "emergency"},
    14: {"ru": "дом", "en": "home", "category": "places", "semantic_field": "basic_locations"},  # ИСПРАВЛЕНО
    15: {"ru": "вода", "en": "water", "category": "substances", "semantic_field": "basic_needs"},  # ИСПРАВЛЕНО
    
    # CORE (16-52) - базовые слова для выживания
    16: {"ru": "быть", "en": "be", "category": "verbs", "semantic_field": "existence"},
    17: {"ru": "хотеть", "en": "want", "category": "verbs", "semantic_field": "desires"},
    18: {"ru": "спать", "en": "sleep", "category": "verbs", "semantic_field": "daily_activities"},
    19: {"ru": "видеть", "en": "see", "category": "verbs", "semantic_field": "perception"},
    20: {"ru": "говорить", "en": "speak", "category": "verbs", "semantic_field": "communication"},
    21: {"ru": "понимать", "en": "understand", "category": "verbs", "semantic_field": "cognition"},
    22: {"ru": "знать", "en": "know", "category": "verbs", "semantic_field": "cognition"},
    23: {"ru": "помогать", "en": "help", "category": "verbs", "semantic_field": "social_interaction"},
    24: {"ru": "жить", "en": "live", "category": "verbs", "semantic_field": "existence"},
    25: {"ru": "делать", "en": "do", "category": "verbs", "semantic_field": "actions"},
    26: {"ru": "мама", "en": "mother", "category": "family", "semantic_field": "family_relations"},
    27: {"ru": "папа", "en": "father", "category": "family", "semantic_field": "family_relations"},
    28: {"ru": "друг", "en": "friend", "category": "people", "semantic_field": "social_relations"},
    29: {"ru": "человек", "en": "person", "category": "people", "semantic_field": "basic_identity"},
    30: {"ru": "ребенок", "en": "child", "category": "family", "semantic_field": "family_relations"},
    31: {"ru": "сейчас", "en": "now", "category": "time", "semantic_field": "temporal"},
    32: {"ru": "сегодня", "en": "today", "category": "time", "semantic_field": "temporal"},
    33: {"ru": "завтра", "en": "tomorrow", "category": "time", "semantic_field": "temporal"},
    34: {"ru": "здесь", "en": "here", "category": "location", "semantic_field": "spatial"},
    35: {"ru": "там", "en": "there", "category": "location", "semantic_field": "spatial"},
    36: {"ru": "дом", "en": "home", "category": "places", "semantic_field": "basic_locations"},
    37: {"ru": "когда", "en": "when", "category": "questions", "semantic_field": "temporal_seeking"},
    38: {"ru": "как", "en": "how", "category": "questions", "semantic_field": "method_seeking"},
    39: {"ru": "скоро", "en": "soon", "category": "time", "semantic_field": "temporal"},
    40: {"ru": "хлеб", "en": "bread", "category": "food", "semantic_field": "basic_needs"},
    41: {"ru": "еда", "en": "food", "category": "food", "semantic_field": "basic_needs"},
    42: {"ru": "голодный", "en": "hungry", "category": "states", "semantic_field": "physical_states"},
    43: {"ru": "больной", "en": "sick", "category": "states", "semantic_field": "physical_states"},
    44: {"ru": "деньги", "en": "money", "category": "objects", "semantic_field": "basic_needs"},
    45: {"ru": "работа", "en": "work", "category": "activities", "semantic_field": "daily_life"},
    46: {"ru": "больница", "en": "hospital", "category": "places", "semantic_field": "emergency"},
    47: {"ru": "телефон", "en": "phone", "category": "objects", "semantic_field": "communication"},
    48: {"ru": "опасно", "en": "dangerous", "category": "qualities", "semantic_field": "safety"},
    49: {"ru": "хорошо", "en": "good", "category": "qualities", "semantic_field": "evaluation"},
    50: {"ru": "плохо", "en": "bad", "category": "qualities", "semantic_field": "evaluation"},
    51: {"ru": "большой", "en": "big", "category": "qualities", "semantic_field": "size"},
    52: {"ru": "маленький", "en": "small", "category": "qualities", "semantic_field": "size"},

    # EXTENDED (53-145) - расширенный набор
    # Местоимения (4 слова)
    53: {"ru": "мы", "en": "we", "category": "pronouns", "semantic_field": "personal_identity"},
    54: {"ru": "вы", "en": "you_plural", "category": "pronouns", "semantic_field": "personal_identity"},
    55: {"ru": "они", "en": "they", "category": "pronouns", "semantic_field": "personal_identity"},
    56: {"ru": "то", "en": "that", "category": "pronouns", "semantic_field": "demonstrative"},

    # Глаголы (8 слов)
    57: {"ru": "иметь", "en": "have", "category": "verbs", "semantic_field": "possession"},
    58: {"ru": "идти", "en": "go", "category": "verbs", "semantic_field": "movement"},
    59: {"ru": "давать", "en": "give", "category": "verbs", "semantic_field": "transfer"},
    60: {"ru": "брать", "en": "take", "category": "verbs", "semantic_field": "acquisition"},
    61: {"ru": "работать", "en": "work", "category": "verbs", "semantic_field": "activities"},
    62: {"ru": "любить", "en": "love", "category": "verbs", "semantic_field": "emotions"},
    63: {"ru": "приходить", "en": "come", "category": "verbs", "semantic_field": "movement"},
    64: {"ru": "покупать", "en": "buy", "category": "verbs", "semantic_field": "commerce"},

    # Семья (4 слова)
    65: {"ru": "сын", "en": "son", "category": "family", "semantic_field": "family_relations"},
    66: {"ru": "дочь", "en": "daughter", "category": "family", "semantic_field": "family_relations"},
    67: {"ru": "брат", "en": "brother", "category": "family", "semantic_field": "family_relations"},
    68: {"ru": "сестра", "en": "sister", "category": "family", "semantic_field": "family_relations"},

    # Время и место (7 слов)
    69: {"ru": "потом", "en": "later", "category": "time", "semantic_field": "temporal"},
    70: {"ru": "вчера", "en": "yesterday", "category": "time", "semantic_field": "temporal"},
    71: {"ru": "утром", "en": "morning", "category": "time", "semantic_field": "temporal"},
    72: {"ru": "днем", "en": "afternoon", "category": "time", "semantic_field": "temporal"},
    73: {"ru": "вечером", "en": "evening", "category": "time", "semantic_field": "temporal"},
    74: {"ru": "далеко", "en": "far", "category": "location", "semantic_field": "spatial"},
    75: {"ru": "близко", "en": "close", "category": "location", "semantic_field": "spatial"},

    # Еда (7 слов)
    76: {"ru": "молоко", "en": "milk", "category": "food", "semantic_field": "basic_needs"},
    77: {"ru": "мясо", "en": "meat", "category": "food", "semantic_field": "basic_needs"},
    78: {"ru": "горячий", "en": "hot", "category": "qualities", "semantic_field": "temperature"},
    79: {"ru": "холодный", "en": "cold", "category": "qualities", "semantic_field": "temperature"},
    80: {"ru": "рис", "en": "rice", "category": "food", "semantic_field": "basic_needs"},
    81: {"ru": "чай", "en": "tea", "category": "food", "semantic_field": "beverages"},
    82: {"ru": "кофе", "en": "coffee", "category": "food", "semantic_field": "beverages"},

    # Предметы (11 слов)
    83: {"ru": "стол", "en": "table", "category": "objects", "semantic_field": "furniture"},
    84: {"ru": "стул", "en": "chair", "category": "objects", "semantic_field": "furniture"},
    85: {"ru": "кровать", "en": "bed", "category": "objects", "semantic_field": "furniture"},
    86: {"ru": "машина", "en": "car", "category": "transport", "semantic_field": "transportation"},
    87: {"ru": "дорога", "en": "road", "category": "places", "semantic_field": "infrastructure"},
    88: {"ru": "магазин", "en": "shop", "category": "places", "semantic_field": "commerce"},
    89: {"ru": "школа", "en": "school", "category": "places", "semantic_field": "education"},
    90: {"ru": "дверь", "en": "door", "category": "objects", "semantic_field": "architecture"},
    91: {"ru": "окно", "en": "window", "category": "objects", "semantic_field": "architecture"},
    92: {"ru": "книга", "en": "book", "category": "objects", "semantic_field": "education"},
    93: {"ru": "одежда", "en": "clothes", "category": "objects", "semantic_field": "personal_items"},

    # Описания (15 слов)
    94: {"ru": "новый", "en": "new", "category": "qualities", "semantic_field": "temporal_quality"},
    95: {"ru": "старый", "en": "old", "category": "qualities", "semantic_field": "temporal_quality"},
    96: {"ru": "красивый", "en": "beautiful", "category": "qualities", "semantic_field": "aesthetic"},
    97: {"ru": "быстрый", "en": "fast", "category": "qualities", "semantic_field": "speed"},
    98: {"ru": "медленный", "en": "slow", "category": "qualities", "semantic_field": "speed"},
    99: {"ru": "легкий", "en": "easy", "category": "qualities", "semantic_field": "difficulty"},
    100: {"ru": "тяжелый", "en": "heavy", "category": "qualities", "semantic_field": "weight"},
    101: {"ru": "дорогой", "en": "expensive", "category": "qualities", "semantic_field": "value"},
    102: {"ru": "дешевый", "en": "cheap", "category": "qualities", "semantic_field": "value"},
    103: {"ru": "правильный", "en": "correct", "category": "qualities", "semantic_field": "accuracy"},
    104: {"ru": "важный", "en": "important", "category": "qualities", "semantic_field": "significance"},
    105: {"ru": "первый", "en": "first", "category": "qualities", "semantic_field": "order"},
    106: {"ru": "последний", "en": "last", "category": "qualities", "semantic_field": "order"},
    107: {"ru": "лучший", "en": "best", "category": "qualities", "semantic_field": "comparison"},
    108: {"ru": "худший", "en": "worst", "category": "qualities", "semantic_field": "comparison"},

    # Числа (11 слов)
    109: {"ru": "один", "en": "one", "category": "numbers", "semantic_field": "quantity"},
    110: {"ru": "два", "en": "two", "category": "numbers", "semantic_field": "quantity"},
    111: {"ru": "три", "en": "three", "category": "numbers", "semantic_field": "quantity"},
    112: {"ru": "десять", "en": "ten", "category": "numbers", "semantic_field": "quantity"},
    113: {"ru": "сто", "en": "hundred", "category": "numbers", "semantic_field": "quantity"},
    114: {"ru": "тысяча", "en": "thousand", "category": "numbers", "semantic_field": "quantity"},
    115: {"ru": "много", "en": "many", "category": "quantities", "semantic_field": "amount"},
    116: {"ru": "мало", "en": "few", "category": "quantities", "semantic_field": "amount"},
    117: {"ru": "все", "en": "all", "category": "quantities", "semantic_field": "totality"},
    118: {"ru": "ничего", "en": "nothing", "category": "quantities", "semantic_field": "absence"},
    119: {"ru": "сколько", "en": "how_much", "category": "questions", "semantic_field": "quantity_seeking"},

    # Общение (4 слова)
    120: {"ru": "пожалуйста", "en": "please", "category": "politeness", "semantic_field": "social_interaction"},
    121: {"ru": "привет", "en": "hello", "category": "greetings", "semantic_field": "social_interaction"},
    122: {"ru": "пока", "en": "goodbye", "category": "greetings", "semantic_field": "social_interaction"},
    123: {"ru": "почему", "en": "why", "category": "questions", "semantic_field": "reason_seeking"},

    # Цвета (4 слова)
    124: {"ru": "белый", "en": "white", "category": "colors", "semantic_field": "visual_properties"},
    125: {"ru": "черный", "en": "black", "category": "colors", "semantic_field": "visual_properties"},
    126: {"ru": "красный", "en": "red", "category": "colors", "semantic_field": "visual_properties"},
    127: {"ru": "синий", "en": "blue", "category": "colors", "semantic_field": "visual_properties"},

    # Направления (5 слов)
    128: {"ru": "налево", "en": "left", "category": "directions", "semantic_field": "spatial_orientation"},
    129: {"ru": "направо", "en": "right", "category": "directions", "semantic_field": "spatial_orientation"},
    130: {"ru": "прямо", "en": "straight", "category": "directions", "semantic_field": "spatial_orientation"},
    131: {"ru": "вверх", "en": "up", "category": "directions", "semantic_field": "spatial_orientation"},
    132: {"ru": "вниз", "en": "down", "category": "directions", "semantic_field": "spatial_orientation"},

    # Состояния (2 слова)
    133: {"ru": "больно", "en": "hurt", "category": "states", "semantic_field": "physical_states"},
    134: {"ru": "устал", "en": "tired", "category": "states", "semantic_field": "physical_states"},

    # Транспорт (3 слова)
    135: {"ru": "автобус", "en": "bus", "category": "transport", "semantic_field": "transportation"},
    136: {"ru": "поезд", "en": "train", "category": "transport", "semantic_field": "transportation"},
    137: {"ru": "самолет", "en": "airplane", "category": "transport", "semantic_field": "transportation"},

    # Части тела (5 слов)
    138: {"ru": "голова", "en": "head", "category": "body_parts", "semantic_field": "anatomy"},
    139: {"ru": "сердце", "en": "heart", "category": "body_parts", "semantic_field": "anatomy"},
    140: {"ru": "живот", "en": "stomach", "category": "body_parts", "semantic_field": "anatomy"},
    141: {"ru": "рука", "en": "hand", "category": "body_parts", "semantic_field": "anatomy"},
    142: {"ru": "нога", "en": "leg", "category": "body_parts", "semantic_field": "anatomy"},

    # Критические ситуации (3 слова)
    143: {"ru": "полиция", "en": "police", "category": "emergency", "semantic_field": "safety"},
    144: {"ru": "потерялся", "en": "lost", "category": "states", "semantic_field": "emergency"},
    145: {"ru": "не понимаю", "en": "dont_understand", "category": "communication", "semantic_field": "comprehension"}
}

def generate_uuid_with_pattern(index: int) -> str:
    """Генерирует UUID в том же формате, что и существующие"""
    base = "550e8400-e29b-41d4-a716-44665544"
    return f"{base}{index:04d}"

def create_concept_description(index: int, word_data: Dict[str, str], concept_id: str) -> Dict[str, Any]:
    """Создает описание концепта"""

    # Определяем приоритет
    if index <= 15:
        priority = "ultra_core"
    elif index <= 52:
        priority = "core"
    else:
        priority = "extended"
    
    # Базовые описания по категориям
    category_descriptions = {
        "pronouns": {
            "en": f"Personal pronoun referring to {word_data['en']}",
            "ru": f"Личное местоимение {word_data['ru']}"
        },
        "affirmation": {
            "en": f"Affirmative response - {word_data['en']}",
            "ru": f"Утвердительный ответ - {word_data['ru']}"
        },
        "negation": {
            "en": f"Negative response - {word_data['en']}",
            "ru": f"Отрицательный ответ - {word_data['ru']}"
        },
        "questions": {
            "en": f"Interrogative word asking {word_data['en']}",
            "ru": f"Вопросительное слово {word_data['ru']}"
        },
        "desires": {
            "en": f"Expression of desire or need - {word_data['en']}",
            "ru": f"Выражение желания или потребности - {word_data['ru']}"
        },
        "actions": {
            "en": f"Basic life action - {word_data['en']}",
            "ru": f"Базовое жизненное действие - {word_data['ru']}"
        },
        "assistance": {
            "en": f"Request for assistance - {word_data['en']}",
            "ru": f"Просьба о помощи - {word_data['ru']}"
        },
        "politeness": {
            "en": f"Polite social expression - {word_data['en']}",
            "ru": f"Вежливое социальное выражение - {word_data['ru']}"
        },
        "people": {
            "en": f"Person or profession - {word_data['en']}",
            "ru": f"Человек или профессия - {word_data['ru']}"
        },
        "places": {
            "en": f"Location or place - {word_data['en']}",
            "ru": f"Место или локация - {word_data['ru']}"
        },
        "substances": {
            "en": f"Basic substance essential for life - {word_data['en']}",
            "ru": f"Базовое вещество, необходимое для жизни - {word_data['ru']}"
        },
        "verbs": {
            "en": f"Basic verb - {word_data['en']}",
            "ru": f"Базовый глагол - {word_data['ru']}"
        },
        "family": {
            "en": f"Family member - {word_data['en']}",
            "ru": f"Член семьи - {word_data['ru']}"
        },
        "time": {
            "en": f"Time concept - {word_data['en']}",
            "ru": f"Временное понятие - {word_data['ru']}"
        },
        "location": {
            "en": f"Location reference - {word_data['en']}",
            "ru": f"Указание на место - {word_data['ru']}"
        },
        "food": {
            "en": f"Food item - {word_data['en']}",
            "ru": f"Продукт питания - {word_data['ru']}"
        },
        "states": {
            "en": f"Physical or emotional state - {word_data['en']}",
            "ru": f"Физическое или эмоциональное состояние - {word_data['ru']}"
        },
        "objects": {
            "en": f"Important object - {word_data['en']}",
            "ru": f"Важный предмет - {word_data['ru']}"
        },
        "activities": {
            "en": f"Daily activity - {word_data['en']}",
            "ru": f"Повседневная деятельность - {word_data['ru']}"
        },
        "qualities": {
            "en": f"Quality or characteristic - {word_data['en']}",
            "ru": f"Качество или характеристика - {word_data['ru']}"
        },
        "numbers": {
            "en": f"Number - {word_data['en']}",
            "ru": f"Число - {word_data['ru']}"
        },
        "quantities": {
            "en": f"Quantity expression - {word_data['en']}",
            "ru": f"Выражение количества - {word_data['ru']}"
        },
        "greetings": {
            "en": f"Greeting expression - {word_data['en']}",
            "ru": f"Приветствие - {word_data['ru']}"
        },
        "colors": {
            "en": f"Color - {word_data['en']}",
            "ru": f"Цвет - {word_data['ru']}"
        },
        "directions": {
            "en": f"Direction - {word_data['en']}",
            "ru": f"Направление - {word_data['ru']}"
        },
        "body_parts": {
            "en": f"Body part - {word_data['en']}",
            "ru": f"Часть тела - {word_data['ru']}"
        },
        "emergency": {
            "en": f"Emergency concept - {word_data['en']}",
            "ru": f"Экстренная ситуация - {word_data['ru']}"
        },
        "communication": {
            "en": f"Communication concept - {word_data['en']}",
            "ru": f"Понятие общения - {word_data['ru']}"
        }
    }
    
    category = word_data["category"]
    description = category_descriptions.get(category, {
        "en": f"Basic A0 concept - {word_data['en']}",
        "ru": f"Базовое понятие A0 - {word_data['ru']}"
    })
    
    # Создаем concept_name из английского слова
    concept_name = word_data["en"].lower().replace(" ", "_").replace("-", "_")
    
    return {
        "concept_id": concept_id,
        "level": "A0",
        "priority": priority,
        "category": category,
        "concept_name": concept_name,
        "description": description,
        "semantic_field": word_data["semantic_field"],
        "usage_context": "basic_communication",
        "examples": {
            "en": f"Basic usage of '{word_data['en']}'",
            "ru": f"Базовое использование '{word_data['ru']}'"
        },
        "translation_notes": {
            "general": f"Use the most common and simple translation for '{word_data['en']}' in each language"
        },
        "created_at": "2025-06-23T12:00:00Z",
        "updated_at": "2025-06-23T12:00:00Z"
    }

def main():
    """Основная функция"""
    print("🧠 Создание недостающих концептов A0...")
    
    # Создаем директории
    concepts_dir = Path("data/concepts")
    concepts_dir.mkdir(exist_ok=True)
    
    all_concepts = []
    concept_mapping = {}
    
    # Обрабатываем все слова
    for index, word_data in A0_WORDS.items():
        # Используем существующий UUID или генерируем новый
        if index in EXISTING_CONCEPTS:
            concept_id = EXISTING_CONCEPTS[index]["uuid"]
            print(f"✅ Используем существующий UUID для #{index} ({word_data['ru']}): {concept_id}")
        else:
            concept_id = generate_uuid_with_pattern(index)
            print(f"🆕 Генерируем новый UUID для #{index} ({word_data['ru']}): {concept_id}")
        
        # Создаем концепт
        concept = create_concept_description(index, word_data, concept_id)
        all_concepts.append(concept)
        
        # Добавляем в маппинг
        if index <= 15:
            priority = "ultra_core"
        elif index <= 52:
            priority = "core"
        else:
            priority = "extended"
        concept_mapping[f"{index:02d}"] = {
            "ru": word_data["ru"],
            "en": word_data["en"],
            "concept_id": concept_id,
            "priority": priority,
            "category": word_data["category"],
            "status": "COMPLETED" if index in EXISTING_CONCEPTS else "TO_BE_CREATED"
        }
    
    # Сохраняем полный файл концептов
    concepts_file = concepts_dir / "A0_complete_concepts.json"
    with open(concepts_file, 'w', encoding='utf-8') as f:
        json.dump(all_concepts, f, ensure_ascii=False, indent=2)
    
    # Сохраняем только новые концепты (для импорта)
    new_concepts = [c for c in all_concepts if c["concept_id"] not in [ex["uuid"] for ex in EXISTING_CONCEPTS.values()]]
    new_concepts_file = concepts_dir / "A0_new_concepts.json"
    with open(new_concepts_file, 'w', encoding='utf-8') as f:
        json.dump(new_concepts, f, ensure_ascii=False, indent=2)
    
    # Сохраняем маппинг
    mapping_file = concepts_dir / "A0_concept_mapping.json"
    with open(mapping_file, 'w', encoding='utf-8') as f:
        json.dump(concept_mapping, f, ensure_ascii=False, indent=2)
    
    print(f"\n📊 Статистика:")
    print(f"  Всего концептов A0: {len(all_concepts)}")
    print(f"  Существующих: {len(EXISTING_CONCEPTS)}")
    print(f"  Новых: {len(new_concepts)}")
    print(f"  ULTRA-CORE: {len([c for c in all_concepts if c['priority'] == 'ultra_core'])}")
    print(f"  CORE: {len([c for c in all_concepts if c['priority'] == 'core'])}")
    print(f"  EXTENDED: {len([c for c in all_concepts if c['priority'] == 'extended'])}")
    
    print(f"\n📁 Созданные файлы:")
    print(f"  ✅ {concepts_file} - все концепты")
    print(f"  ✅ {new_concepts_file} - только новые концепты")
    print(f"  ✅ {mapping_file} - маппинг для обновления документации")
    
    print(f"\n🎯 Следующие шаги:")
    print(f"  1. Импортировать новые концепты: python -m scripts.concepts import {new_concepts_file}")
    print(f"  2. Обновить md/A0_word_list.md с правильными concept_id")
    print(f"  3. Создать JSON файлы для слов 7-15 и далее")

if __name__ == "__main__":
    main()
