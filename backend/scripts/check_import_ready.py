#!/usr/bin/env python3
"""
Скрипт для проверки готовности файлов к импорту.
Проверяет файлы в папке new/ и показывает статистику.
"""

import json
import sys
from pathlib import Path

def check_files():
    """Проверяет файлы в папке new/ и показывает статистику."""
    
    print("🔍 Проверка файлов для импорта")
    print("=" * 50)
    
    # Путь к папке new
    project_root = Path(__file__).parent.parent
    new_dir = project_root / "data" / "words" / "new"
    
    if not new_dir.exists():
        print(f"❌ Папка {new_dir} не найдена!")
        return False
    
    # Находим JSON файлы
    json_files = list(new_dir.glob("*.json"))
    
    if not json_files:
        print(f"❌ JSON файлы не найдены в {new_dir}")
        return False
    
    print(f"📁 Найдено {len(json_files)} JSON файлов:")
    
    total_words = 0
    cebuano_words = 0
    languages = set()
    
    for json_file in json_files:
        print(f"\n📄 Файл: {json_file.name}")
        
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                words = json.load(f)
            
            if not isinstance(words, list):
                print(f"  ❌ Неверный формат: ожидается массив")
                continue
            
            file_words = len(words)
            file_cebuano = 0
            file_languages = set()
            
            for word in words:
                if isinstance(word, dict):
                    lang = word.get('language', '').lower()
                    if lang:
                        file_languages.add(lang)
                        languages.add(lang)
                        if lang == 'cb':
                            file_cebuano += 1
                            cebuano_words += 1
            
            total_words += file_words
            
            print(f"  ✅ Слов: {file_words}")
            print(f"  🇵🇭 Себуано (cb): {file_cebuano}")
            print(f"  🌍 Языков: {len(file_languages)}")
            
        except Exception as e:
            print(f"  ❌ Ошибка при чтении файла: {e}")
    
    print(f"\n📊 Общая статистика:")
    print(f"Всего слов: {total_words}")
    print(f"Слов Себуано (cb): {cebuano_words}")
    print(f"Всего языков: {len(languages)}")
    print(f"Языки: {', '.join(sorted(languages))}")
    
    # Проверяем наличие Себуано
    if 'cb' in languages:
        print(f"\n✅ Код языка Себуано исправлен на 'cb'")
    else:
        print(f"\n❌ Код языка Себуано не найден или не исправлен")
    
    print(f"\n🚀 Файлы готовы к импорту!")
    print(f"Для импорта в MongoDB выполните:")
    print(f"  export PATH=$PATH:/home/<USER>/.local/bin")
    print(f"  python3 scripts/words/import_words.py")
    
    return True

if __name__ == "__main__":
    success = check_files()
    if not success:
        sys.exit(1)
