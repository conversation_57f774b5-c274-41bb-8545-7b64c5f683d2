#!/bin/bash

# Скрипт для быстрой проверки качества JSON файла после создания
# Использование: ./check_json_quality.sh файл.json

if [ $# -eq 0 ]; then
    echo "Использование: $0 <файл.json>"
    exit 1
fi

FILE="$1"

if [ ! -f "$FILE" ]; then
    echo "Файл $FILE не найден!"
    exit 1
fi

echo "🔍 ПРОВЕРКА КАЧЕСТВА: $FILE"
echo "=================================="

# Проверяем основные языки
echo "📋 ВЫБОРОЧНАЯ ПРОВЕРКА (RU, EN, DE):"
echo ""

echo "🇷🇺 РУССКИЙ:"
grep -A 5 '"language": "ru"' "$FILE" | head -6

echo ""
echo "🇺🇸 АНГЛИЙСКИЙ:"
grep -A 5 '"language": "en"' "$FILE" | head -6

echo ""
echo "🇩🇪 НЕМЕЦКИЙ:"
grep -A 5 '"language": "de"' "$FILE" | head -6

echo ""
echo "=================================="

# Проверяем на плейсхолдеры
PLACEHOLDERS=$(grep -c '\[.*\]' "$FILE")
if [ $PLACEHOLDERS -gt 0 ]; then
    echo "⚠️  НАЙДЕНО $PLACEHOLDERS ПЛЕЙСХОЛДЕРОВ:"
    grep -n '\[.*\]' "$FILE" | head -5
    echo ""
fi

# Проверяем на пропуски ___
GAPS=$(grep -c '___' "$FILE")
echo "✅ ПРОПУСКОВ ___: $GAPS"

# Проверяем количество языков
LANGUAGES=$(grep -c '"language":' "$FILE")
echo "🌍 ЯЗЫКОВ: $LANGUAGES/37"

# Специальная проверка CB языка
CB_LINES=$(grep -A 5 '"language": "cb"' "$FILE" | grep '"sentence"' | head -3)
if [ ! -z "$CB_LINES" ]; then
    echo ""
    echo "🇵🇭 ПРОВЕРКА CB (СЕБУАНО):"
    echo "$CB_LINES"
    echo ""
    echo "⚠️  ПРОВЕРЬТЕ CB ФРАЗЫ ПО СПРАВОЧНИКУ:"
    echo "   backend/data/languages/cb_cebuano_guide.md"
    echo "   - Нет лишних 'ng' в 'Gusto ko'"
    echo "   - Правильные формы глаголов"
fi

echo ""
echo "🎯 СЛЕДУЮЩИЙ ШАГ: Если всё выглядит правильно, запустите валидацию:"
echo "   python -m scripts.words.validate_word $FILE"
