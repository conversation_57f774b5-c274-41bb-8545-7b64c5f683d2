#!/usr/bin/env python3
"""
Скрипт для создания базовых языковых справочников для всех 37 языков.
"""

import os
from pathlib import Path

# Все 37 языков из frontend/constants/languages.ts
LANGUAGES = {
    'en': {'name': 'English', 'family': 'Germanic'},
    'zh': {'name': '中文', 'family': 'Sino-Tibetan'},
    'hi': {'name': 'हिन्दी', 'family': 'Indo-European'},
    'es': {'name': 'Español', 'family': 'Romance'},
    'pa': {'name': 'ਪੰਜਾਬੀ', 'family': 'Indo-European'},
    'mr': {'name': 'मराठी', 'family': 'Indo-European'},
    'ar': {'name': 'العربية', 'family': 'Semitic'},
    'bn': {'name': 'বাংলা', 'family': 'Indo-European'},
    'fr': {'name': 'Français', 'family': 'Romance'},
    'ru': {'name': 'Русский', 'family': 'Slavic'},
    'pt': {'name': 'Português', 'family': 'Romance'},
    'ur': {'name': 'اردو', 'family': 'Indo-European'},
    'id': {'name': 'Bahasa Indonesia', 'family': 'Austronesian'},
    'ja': {'name': '日本語', 'family': 'Japonic'},
    'fa': {'name': 'فارسی', 'family': 'Indo-European'},
    'de': {'name': 'Deutsch', 'family': 'Germanic'},
    'tr': {'name': 'Türkçe', 'family': 'Turkic'},
    'ko': {'name': '한국어', 'family': 'Koreanic'},
    'vi': {'name': 'Tiếng Việt', 'family': 'Austroasiatic'},
    'it': {'name': 'Italiano', 'family': 'Romance'},
    'th': {'name': 'ไทย', 'family': 'Tai-Kadai'},
    'pl': {'name': 'Polski', 'family': 'Slavic'},
    'uk': {'name': 'Українська', 'family': 'Slavic'},
    'my': {'name': 'မြန်မာ', 'family': 'Sino-Tibetan'},
    'ms': {'name': 'Bahasa Malaysia', 'family': 'Austronesian'},
    'uz': {'name': 'Oʻzbekcha', 'family': 'Turkic'},
    'ne': {'name': 'नेपाली', 'family': 'Indo-European'},
    'nl': {'name': 'Nederlands', 'family': 'Germanic'},
    'ro': {'name': 'Română', 'family': 'Romance'},
    'tl': {'name': 'Tagalog', 'family': 'Austronesian'},
    'cb': {'name': 'Cebuano', 'family': 'Austronesian'},
    'kk': {'name': 'Қазақша', 'family': 'Turkic'},
    'sv': {'name': 'Svenska', 'family': 'Germanic'},
    'da': {'name': 'Dansk', 'family': 'Germanic'},
    'fi': {'name': 'Suomi', 'family': 'Finno-Ugric'},
    'no': {'name': 'Norsk', 'family': 'Germanic'},
    'ka': {'name': 'ქართული', 'family': 'Kartvelian'}
}

def create_basic_guide(lang_code: str, lang_info: dict) -> str:
    """Создает базовый языковой справочник"""
    
    name = lang_info['name']
    family = lang_info['family']
    
    # Определяем особенности по языковой семье
    if family == 'Romance':
        features = "- Gender (masculine/feminine)\n- Number (singular/plural)\n- Articles\n- Verb conjugation"
        example = "noun, masculine, singular"
    elif family == 'Slavic':
        features = "- Gender (masculine/feminine/neuter)\n- Number (singular/plural)\n- Cases (6-7 cases)\n- Aspect (perfective/imperfective)"
        example = "существительное, мужской род, единственное число, именительный падеж"
    elif family == 'Germanic':
        features = "- Gender (der/die/das for German)\n- Number (singular/plural)\n- Cases (for German)\n- Strong/weak verbs"
        example = "noun, singular" if lang_code == 'en' else "Substantiv, maskulin, Singular"
    elif family == 'Sino-Tibetan':
        features = "- Tones (for tonal languages)\n- Classifiers\n- Aspect markers\n- Word order variations"
        example = "名词, 单数" if lang_code == 'zh' else "noun, singular"
    elif family == 'Indo-European' and lang_code in ['hi', 'pa', 'mr', 'ne']:
        features = "- Gender (masculine/feminine/neuter)\n- Number (singular/plural)\n- Cases\n- Postpositions"
        example = "संज्ञा, पुल्लिंग, एकवचन"
    elif family == 'Semitic':
        features = "- Root system (3-consonant roots)\n- Gender (masculine/feminine)\n- Number (singular/dual/plural)\n- Cases (nominative/accusative/genitive)"
        example = "اسم، مذكر، مفرد"
    elif family == 'Austronesian':
        features = "- Focus system (actor/object/locative)\n- Aspect (completed/incompleted/contemplated)\n- Reduplication\n- Affixation"
        example = "pangngalan, singular"
    elif family == 'Finno-Ugric':
        features = "- Cases (15 cases for Finnish)\n- No gender\n- Agglutination\n- Vowel harmony"
        example = "substantiivi, yksikkö, nominatiivi"
    elif family == 'Turkic':
        features = "- Agglutination\n- Vowel harmony\n- No gender\n- Extensive case system"
        example = "isim, tekil, yalın hal"
    else:
        features = "- Basic grammatical features\n- Part of speech marking\n- Number distinctions"
        example = "noun, singular"
    
    return f"""# {name} Language Guide

## General Guidelines
- Use {name} for all labels when possible
- Separate characteristics with commas
- Include only contextually relevant features
- Use full part of speech names (not abbreviations)

## Language Family: {family}

## Key Features:
{features}

## Basic Format
**Example:** `{example}`

## Parts of Speech

### Noun
**Format:** `noun, [additional features]`
**Examples:**
- Basic noun form
- Plural forms if applicable
- Gender marking if applicable

### Verb
**Format:** `verb, [tense/aspect], [person], [number]`
**Examples:**
- Present tense forms
- Past tense forms
- Future tense forms

### Adjective
**Format:** `adjective, [degree], [agreement]`
**Examples:**
- Positive degree
- Comparative degree
- Superlative degree

### Common Patterns
- Use the most natural form for A0 level
- Prefer simple, basic forms
- Avoid complex grammatical constructions
- Focus on survival vocabulary

## A0 Specific Guidelines
- Keep examples simple and practical
- Use basic vocabulary only
- Ensure natural pronunciation
- Consider cultural context

## Notes
This is a basic guide for A0 level words. For more complex grammatical features, consult specialized {name} grammar resources.
"""

def main():
    """Создает языковые справочники для всех языков"""
    
    guides_dir = Path("data/words/language_guides")
    guides_dir.mkdir(exist_ok=True)
    
    existing_guides = set()
    created_guides = []
    
    # Проверяем существующие справочники
    for guide_file in guides_dir.glob("*.md"):
        lang_code = guide_file.stem.lower()
        existing_guides.add(lang_code)
    
    print(f"📚 Создание языковых справочников...")
    print(f"📁 Директория: {guides_dir}")
    print(f"✅ Существующих: {len(existing_guides)}")
    
    # Создаем справочники для недостающих языков
    for lang_code, lang_info in LANGUAGES.items():
        guide_file = guides_dir / f"{lang_code.upper()}.md"
        
        if lang_code.lower() in existing_guides:
            print(f"⏭️  {lang_code.upper()}: уже существует")
            continue
        
        # Создаем справочник
        guide_content = create_basic_guide(lang_code, lang_info)
        
        with open(guide_file, 'w', encoding='utf-8') as f:
            f.write(guide_content)
        
        created_guides.append(lang_code.upper())
        print(f"🆕 {lang_code.upper()}: создан ({lang_info['name']})")
    
    print(f"\n📊 Статистика:")
    print(f"  Всего языков: {len(LANGUAGES)}")
    print(f"  Существовало: {len(existing_guides)}")
    print(f"  Создано новых: {len(created_guides)}")
    print(f"  Итого справочников: {len(existing_guides) + len(created_guides)}")
    
    if created_guides:
        print(f"\n🆕 Созданные справочники:")
        for guide in created_guides:
            print(f"  - {guide}.md")
    
    print(f"\n✅ Все языковые справочники готовы!")

if __name__ == "__main__":
    main()
