#!/usr/bin/env python3
"""
Скрипт для создания шаблонов JSON файлов для разных приоритетов A0 слов.
"""

import json
from pathlib import Path
from typing import Dict, List, Any

# Языки из frontend/constants/languages.ts
ALL_LANGUAGES = [
    'en', 'zh', 'hi', 'es', 'pa', 'mr', 'ar', 'bn', 'fr', 'ru', 'pt', 'ur', 'id', 
    'ja', 'fa', 'de', 'tr', 'ko', 'vi', 'it', 'th', 'pl', 'uk', 'my', 'ms', 'uz', 
    'ne', 'nl', 'ro', 'tl', 'cb', 'kk', 'sv', 'da', 'fi', 'no', 'ka'
]

def create_word_template(concept_id: str, priority: str, concept_name: str, example_words: Dict[str, str]) -> List[Dict[str, Any]]:
    """Создает шаблон JSON файла для одного концепта"""
    
    template = []
    
    for lang in ALL_LANGUAGES:
        # Базовое слово (заполнить вручную)
        word = example_words.get(lang, f"[{lang.upper()}_WORD]")
        
        # Базовый пример предложения
        if lang == 'ru':
            sentence = f"Я ___ есть"
            correct_answers = [word] if word != f"[{lang.upper()}_WORD]" else ["[WORD]"]
        elif lang == 'en':
            sentence = f"I ___ to eat"
            correct_answers = [word] if word != f"[{lang.upper()}_WORD]" else ["[WORD]"]
        else:
            sentence = f"[{lang.upper()}_SENTENCE_WITH___]"
            correct_answers = ["[WORD]"]
        
        # Определяем часть речи по концепту
        if "pronoun" in concept_name:
            part_of_speech = "pronoun"
        elif "want" in concept_name or "eat" in concept_name or "drink" in concept_name:
            part_of_speech = "verb"
        elif "help" in concept_name or "water" in concept_name or "home" in concept_name:
            part_of_speech = "noun"
        elif "yes" in concept_name or "no" in concept_name:
            part_of_speech = "interjection"
        elif "what" in concept_name or "where" in concept_name:
            part_of_speech = "pronoun"
        else:
            part_of_speech = "noun"  # По умолчанию
        
        word_entry = {
            "concept_id": concept_id,
            "word": word,
            "language": lang,
            "level": "A0",
            "priority": priority,
            "part_of_speech": part_of_speech,
            "examples": [
                {
                    "sentence": sentence,
                    "correct_answers": correct_answers
                }
            ],
            "word_info": {
                "frequency": "very_high" if priority == "ultra_core" else "high",
                "difficulty": "beginner",
                "context": "basic_needs" if priority == "ultra_core" else "daily_life"
            }
        }
        
        template.append(word_entry)
    
    return template

def create_templates():
    """Создает шаблоны для разных типов концептов"""
    
    templates_dir = Path("data/words/templates")
    templates_dir.mkdir(exist_ok=True)
    
    # Шаблоны для разных приоритетов
    templates = [
        {
            "filename": "ultra_core_template.json",
            "concept_id": "550e8400-e29b-41d4-a716-446655440007",
            "priority": "ultra_core",
            "concept_name": "want",
            "description": "Шаблон для ULTRA-CORE слов (концепты 7-15)",
            "example_words": {
                "ru": "хочу",
                "en": "want",
                "es": "quiero",
                "fr": "veux",
                "de": "will",
                "it": "voglio"
            }
        },
        {
            "filename": "core_template.json", 
            "concept_id": "550e8400-e29b-41d4-a716-446655440026",
            "priority": "core",
            "concept_name": "mother",
            "description": "Шаблон для CORE слов (концепты 16-52)",
            "example_words": {
                "ru": "мама",
                "en": "mother",
                "es": "madre",
                "fr": "mère",
                "de": "Mutter",
                "it": "madre"
            }
        },
        {
            "filename": "extended_template.json",
            "concept_id": "550e8400-e29b-41d4-a716-446655440053",
            "priority": "extended", 
            "concept_name": "we",
            "description": "Шаблон для EXTENDED слов (концепты 53-145)",
            "example_words": {
                "ru": "мы",
                "en": "we",
                "es": "nosotros",
                "fr": "nous",
                "de": "wir",
                "it": "noi"
            }
        }
    ]
    
    created_files = []
    
    for template_info in templates:
        # Создаем JSON файл
        template_data = create_word_template(
            template_info["concept_id"],
            template_info["priority"],
            template_info["concept_name"],
            template_info["example_words"]
        )
        
        # Добавляем комментарий в начало
        template_with_comment = [
            {
                "_comment": template_info["description"],
                "_concept_id": template_info["concept_id"],
                "_priority": template_info["priority"],
                "_instructions": [
                    "1. Замените [LANG_WORD] на правильные переводы",
                    "2. Замените [LANG_SENTENCE_WITH___] на предложения с пропуском",
                    "3. Проверьте по проблемным языкам",
                    "4. Валидируйте: python -m scripts.words validate [file]",
                    "5. Импортируйте: python -m scripts.words import"
                ]
            }
        ] + template_data
        
        # Сохраняем файл
        template_file = templates_dir / template_info["filename"]
        with open(template_file, 'w', encoding='utf-8') as f:
            json.dump(template_with_comment, f, ensure_ascii=False, indent=2)
        
        created_files.append(template_info["filename"])
        print(f"✅ Создан: {template_info['filename']} ({template_info['priority']})")
    
    # Создаем README для шаблонов
    readme_content = """# Шаблоны JSON файлов для A0 слов

Этот каталог содержит шаблоны для создания JSON файлов с A0 словами.

## Доступные шаблоны:

### 🔥 ultra_core_template.json
- **Приоритет**: ultra_core (концепты 1-15)
- **Пример**: "хочу" / "want"
- **Использование**: Самые критичные слова для выживания

### 🎯 core_template.json  
- **Приоритет**: core (концепты 16-52)
- **Пример**: "мама" / "mother"
- **Использование**: Базовые слова для ежедневного общения

### 📚 extended_template.json
- **Приоритет**: extended (концепты 53-145)
- **Пример**: "мы" / "we"
- **Использование**: Расширенный словарь

## Как использовать:

1. **Скопируйте нужный шаблон**:
   ```bash
   cp data/words/templates/ultra_core_template.json data/words/A0/concept_007.json
   ```

2. **Замените заглушки**:
   - `[LANG_WORD]` → правильный перевод
   - `[LANG_SENTENCE_WITH___]` → предложение с пропуском
   - Обновите `concept_id` на нужный

3. **Валидируйте**:
   ```bash
   python -m scripts.words validate data/words/A0/concept_007.json
   ```

4. **Скопируйте в new/ для импорта**:
   ```bash
   cp data/words/A0/concept_007.json data/words/new/
   ```

5. **Импортируйте**:
   ```bash
   python -m scripts.words import
   ```

## Проверка качества:

Обязательно протестируйте по всем 37 проблемным языкам:
```bash
python test_a0_word_creation.py
```
"""
    
    readme_file = templates_dir / "README.md"
    with open(readme_file, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"\n📊 Статистика:")
    print(f"  Создано шаблонов: {len(created_files)}")
    print(f"  Языков в каждом: {len(ALL_LANGUAGES)}")
    print(f"  Записей на шаблон: {len(ALL_LANGUAGES) + 1} (+ комментарий)")
    
    print(f"\n📁 Созданные файлы:")
    for filename in created_files:
        print(f"  - {filename}")
    print(f"  - README.md")
    
    print(f"\n✅ Все шаблоны готовы!")
    print(f"📍 Расположение: {templates_dir}")

if __name__ == "__main__":
    create_templates()
