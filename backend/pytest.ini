[pytest]
# Используем основное окружение
env =
    PYTHONPATH=.
    ENV_FILE=.
    TESTING=True

# Настройки для асинхронных тестов
asyncio_mode = auto

# Отключаем предупреждения
filterwarnings =
    ignore::DeprecationWarning
    ignore::UserWarning
    ignore::pytest.PytestUnraisableExceptionWarning

# Настройки вывода тестов
addopts = -v --tb=short --color=yes -p no:warnings

# Автоматическое обнаружение тестов
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Настройки для asyncio уже определены выше
