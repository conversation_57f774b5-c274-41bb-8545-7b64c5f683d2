#!/bin/zsh

OLD_PATH="/Users/<USER>/Library/CloudStorage/Dropbox/EVO/2.EnApp/backend/venv"
NEW_PATH="/Users/<USER>/EVO/MEMO/backend/venv"

# Исправляем пути в бинарных файлах
find "$NEW_PATH/bin" -type f -exec grep -l "$OLD_PATH" {} \; | while read -r file; do
    echo "Fixing $file"
    sed -i '' "s|$OLD_PATH|$NEW_PATH|g" "$file"
    chmod +x "$file"
    # Исправляем шебанг, если он есть
    if head -n 1 "$file" | grep -q "^#!"; then
        shebang="$(head -n 1 "$file" | sed "s|$OLD_PATH|$NEW_PATH|g")"
        tail -n +2 "$file" > "${file}.tmp"
        echo "$shebang" > "$file"
        cat "${file}.tmp" >> "$file"
        rm "${file}.tmp"
    fi
done

echo "Virtual environment has been fixed. Please restart your terminal."
