# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo
*~

# Logs
*.log

# Environment variables
.env

# Test coverage
.coverage
htmlcov/
coverage.xml
*.cover

# Jupyter Notebook
.ipynb_checkpoints

# Local development
.DS_Store

# Test database
*.sqlite3
*.db

# Pytest
.pytest_cache/

# Local development
server.log
