[pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Настройки для asyncio
asyncio_mode = auto

# Отчет о покрытии кода
addopts = --cov=app --cov-report=term-missing --cov-report=xml

# Игнорируемые директории
norecursedirs = .git .tox .mypy_cache .pytest_cache __pycache__ venv env

# Настройки для flake8
flake8-max-line-length = 120
flake8-ignore =
    # Игнорировать неиспользуемые импорты в __init__.py
    __init__.py: F401
    # Игнорировать слишком длинные строки в тестах
    tests/*.py: E501
