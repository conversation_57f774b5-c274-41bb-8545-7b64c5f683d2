# Тестовые данные

Эта директория содержит тестовые данные для интеграционных тестов.

## Структура

- `fixtures/` - Фикстуры для тестов
  - `words.json` - Примеры слов для тестирования
  - `users.json` - Примеры пользователей для тестирования
- `mock_responses/` - Моки ответов от внешних сервисов

## Использование

Тестовые данные загружаются автоматически при запуске тестов с использованием фикстур из `conftest.py`.
