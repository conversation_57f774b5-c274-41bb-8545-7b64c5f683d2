# 🏗️ BACKEND STRUCTURE - Организованная структура

Реорганизованная структура backend приложения uMemo для лучшей навигации и поддержки.

## 📁 СТРУКТУРА ПАПОК

```
backend/
├── app/                    # 🎯 ОСНОВНОЕ ПРИЛОЖЕНИЕ
│   ├── api/               # API endpoints
│   ├── models/            # Модели данных
│   ├── routers/           # Маршрутизация
│   ├── services/          # Бизнес-логика
│   └── utils/             # Утилиты приложения
├── scripts/                # 🔧 ВАЖНЫЕ СКРИПТЫ
│   ├── concepts.py        # Работа с концептами
│   ├── words/             # Скрипты для слов
│   └── migrate_*.py       # Миграции
├── tests/                  # ✅ UNIT ТЕСТЫ
│   ├── test_api.py        # Тесты API
│   ├── test_db.py         # Тесты БД
│   └── test_spaced_*.py   # Тесты spaced repetition
├── temp_scripts/           # 🧪 ВРЕМЕННЫЕ/ОТЛАДОЧНЫЕ СКРИПТЫ
│   ├── test/              # Временные тесты (25+ файлов)
│   ├── check/             # Проверки системы (15+ файлов)
│   ├── debug/             # Отладочные скрипты
│   ├── create/            # Одноразовые создания
│   ├── update/            # Одноразовые обновления
│   ├── fill/              # Заполнение данных
│   └── monitor/           # Мониторинг
├── tools/                  # 🛠️ УТИЛИТЫ РАЗРАБОТКИ
│   ├── analysis/          # Анализ производительности
│   ├── optimization/      # Оптимизация и очистка
│   └── database/          # Работа с базой данных
├── config/                 # ⚙️ КОНФИГУРАЦИОННЫЕ ФАЙЛЫ
│   ├── logging_config.py  # Настройки логирования
│   └── pytest.ini.bak     # Backup конфигурации
├── legacy/                 # 📦 СТАРЫЕ/НЕИСПОЛЬЗУЕМЫЕ ФАЙЛЫ
│   ├── A0/               # Старые концепты (перенесены в vocabulary/)
│   ├── frontend/         # Старый frontend код
│   ├── venv*/            # Старые виртуальные окружения
│   └── requirements_old.txt
├── docs/                   # 📚 ДОКУМЕНТАЦИЯ
├── logs/                   # 📝 ЛОГИ ПРИЛОЖЕНИЯ
├── venv_rona/             # 🐍 АКТИВНОЕ ВИРТУАЛЬНОЕ ОКРУЖЕНИЕ
├── run_dev.py             # 🚀 ЗАПУСК РАЗРАБОТКИ
├── requirements*.txt       # 📋 ЗАВИСИМОСТИ
├── pytest.ini            # ⚙️ КОНФИГУРАЦИЯ ТЕСТОВ
└── Dockerfile             # 🐳 КОНТЕЙНЕРИЗАЦИЯ
```

## 🎯 НАЗНАЧЕНИЕ ПАПОК

### **🎯 ОСНОВНЫЕ (production-ready):**
- **`app/`** - основное приложение FastAPI
- **`scripts/`** - важные скрипты для production
- **`tests/`** - unit тесты

### **🧪 РАЗРАБОТКА:**
- **`temp_scripts/`** - временные скрипты для отладки и тестирования
- **`tools/`** - утилиты для разработки и обслуживания
- **`config/`** - конфигурационные файлы

### **📦 АРХИВ:**
- **`legacy/`** - старые файлы, которые больше не используются

## 📊 СТАТИСТИКА

- **Временные тесты**: 25+ файлов в `temp_scripts/test/`
- **Проверки системы**: 15+ файлов в `temp_scripts/check/`
- **Утилиты БД**: 6 файлов в `tools/database/`
- **Анализ**: 2 файла в `tools/analysis/`
- **Оптимизация**: 4 файла в `tools/optimization/`

## 🚀 БЫСТРЫЙ СТАРТ

### **Запуск приложения:**
```bash
cd backend
source venv_rona/bin/activate
python run_dev.py
```

### **Запуск тестов:**
```bash
pytest tests/
```

### **Использование утилит:**
```bash
# Анализ производительности
python tools/analysis/analyze_query_performance.py

# Оптимизация БД
python tools/optimization/optimize_database.py

# Проверка системы
python temp_scripts/check/check_db.py
```

## ✅ ПРЕИМУЩЕСТВА НОВОЙ СТРУКТУРЫ

1. **🧹 ЧИСТОТА** - корень содержит только важные файлы
2. **🔍 НАВИГАЦИЯ** - легко найти нужный тип файла
3. **🎯 ФОКУС** - разделение production и development кода
4. **📈 МАСШТАБИРУЕМОСТЬ** - готово к росту проекта
5. **🛡️ БЕЗОПАСНОСТЬ** - временные файлы не мешают основному коду

**Структура готова к профессиональной разработке!** 🎯
