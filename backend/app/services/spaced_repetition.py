"""
Модуль для работы с интервальным повторением слов.

📖 ПОЛНАЯ ДОКУМЕНТАЦИЯ: backend/docs/SPACED_REPETITION_SYSTEM.md

Содержит всю бизнес-логику для работы с карточками слов,
интервальным повторением и очередями повторений.

⚠️ ВАЖНО: Часть логики обрабатывается на фронтенде:
- Предзагрузка карточек (frontend/services/cardPreloader.ts)
- Фильтрация дублирующихся карточек
- Локальная обработка неправильных ответов
- Мгновенные переходы между карточками

🔑 КЛЮЧЕВЫЕ ОСОБЕННОСТИ:
- Новые слова (interval_level = -1) изучаются немедленно при правильном ответе
- Предзагрузка НЕ создает записи в user_progress (используется флаг preload=true)
- Интервалы: 30сек → 5мин → 10мин → ... → 3 года (15 уровней)

Задачи на доработку:
1. При загрузке новых слов учитывать язык и уровень пользователя:
   - Выбирать слова только для изучаемого языка пользователя
   - !!! На основе уровня языка пользователя (A1, A2, B1, и т.д.)
"""
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Union
from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorClient
from motor.core import AgnosticDatabase
import os
from dotenv import load_dotenv
from pathlib import Path

# Импортируем кэширование
from .cache_service import cached_word, cached_user_progress, cache_service

# Импортируем логирование
from ..utils.logger import logger, LogCategory
import time

# Загружаем переменные окружения
env_path = Path(__file__).parent.parent.parent / '.env'
if env_path.exists():
    load_dotenv(dotenv_path=env_path)

# Интервалы повторения в минутах
REPETITION_INTERVALS = [
    0.5,        # 30 секунд
    2,          # 2 минут
    10,         # 10 минут
    60,         # 1 час
    1440,       # 1 день (24 * 60)
    4320,       # 3 дня
    10080,      # 7 дней
    20160,      # 14 дней
    43200,      # 30 дней
    86400,      # 60 дней (2 месяца)
    172800,     # 120 дней (~4 месяца)
    345600,     # 240 дней (~8 месяцев)
    525600,     # 1 год (365 дней)
    788400,     # 1.5 года (547.5 дней)
    1051200,    # 2 года (730 дней)
    1576800     # 3 года (1095 дней)
]

# Максимальный уровень интервала
MAX_INTERVAL_LEVEL = len(REPETITION_INTERVALS) - 1

# Инициализация подключения к MongoDB
async def get_database() -> AgnosticDatabase:
    """Возвращает подключение к базе данных."""
    mongo_uri = os.getenv("MONGODB_URL")
    if not mongo_uri:
        raise ValueError("MONGODB_URL не найден в переменных окружения")
        
    db_name = os.getenv("WORDS_DATABASE_NAME", "word_master")
    client = AsyncIOMotorClient(mongo_uri)
    return client[db_name]

class SpacedRepetitionService:
    """Сервис для работы с интервальным повторением."""
    
    def __init__(self, db: Optional[AgnosticDatabase] = None):
        """Инициализация сервиса.
        
        Args:
            db: Опциональное подключение к базе данных. Если не указано, 
                будет создано новое подключение.
        """
        self._db = db
        self._words_collection = None
        self._user_progress_collection = None
    
    @property
    async def db(self) -> AgnosticDatabase:
        if self._db is None:
            self._db = await get_database()
        return self._db
    
    @property
    async def words_collection(self):
        if self._words_collection is None:
            db = await self.db
            self._words_collection = db.words
        return self._words_collection

    @property
    async def user_progress_collection(self):
        if self._user_progress_collection is None:
            from ..database import Database
            db_instance = Database()
            words_db = db_instance.get_db('words')  # user_progress находится в word_master!
            self._user_progress_collection = words_db.user_progress
        return self._user_progress_collection
    

    
    # ===== Основные методы =====
    
    async def get_next_word(self, user_id: Union[str, ObjectId], target_lang: str = None, skip_forced: bool = False, preload: bool = False, exclude_card_id: Optional[ObjectId] = None) -> Optional[dict]:
        """Получить следующее слово для повторения.

        Args:
            user_id: ID пользователя
            target_lang: Целевой язык (например, 'en', 'ru')
            skip_forced: Пропустить форсированную очередь (для предзагрузки)
            preload: Режим предзагрузки (не добавляет новые слова в прогресс)
            exclude_card_id: ID карточки для исключения из выборки (для предзагрузки)

        Returns:
            Слово для повторения или None, если слова не найдены

        Приоритеты:
        1. Слова из форсированной очереди (неправильные ответы) - если skip_forced=False
        2. Слова из активной очереди (на очереди на повторение)
        3. Новые слова (если есть не изученные)
        """
        start_time = time.time()

        # 🔍 ДЕТАЛЬНЫЕ ЛОГИ: Начало поиска слова
        exclude_info = f", исключая {str(exclude_card_id)[-8:]}" if exclude_card_id else ""
        logger.spaced_rep_start(str(user_id), target_lang, preload)
        logger.debug(LogCategory.SPACED_REP, f"🔍 ПОИСК СЛОВА: Начинаем поиск для пользователя {str(user_id)[-8:]}")
        logger.debug(LogCategory.SPACED_REP, f"📋 ПАРАМЕТРЫ: target_lang={target_lang}, skip_forced={skip_forced}, preload={preload}")
        if exclude_card_id:
            logger.debug(LogCategory.SPACED_REP, f"🚫 ИСКЛЮЧЕНИЕ: Исключаем карточку {str(exclude_card_id)[-8:]}")

        # 1. Проверяем форсированную очередь (слова с неправильным ответом) - только если не пропускаем
        if not skip_forced:
            logger.debug(LogCategory.SPACED_REP, f"🔍 ЭТАП 1: Проверяем форсированную очередь")
            forced_word = await self._get_forced_word(user_id, target_lang, exclude_card_id)
            if forced_word:
                logger.debug(LogCategory.SPACED_REP, f"✅ ЭТАП 1: Найдено форсированное слово '{forced_word.get('word')}'")
                prepared_word = await self._prepare_word_data(forced_word, is_forced=True)
                if prepared_word:
                    duration_ms = (time.time() - start_time) * 1000
                    logger.spaced_rep_found(forced_word.get('word'), "форсированная очередь", duration_ms, str(user_id))
                    return prepared_word
            else:
                logger.debug(LogCategory.SPACED_REP, f"❌ ЭТАП 1: Форсированных слов не найдено")
        else:
            logger.debug(LogCategory.SPACED_REP, f"⏭️ ЭТАП 1: Пропускаем форсированную очередь (skip_forced=True)")

        # 2. Проверяем активную очередь (слова, которые нужно повторить)
        logger.debug(LogCategory.SPACED_REP, f"🔍 ЭТАП 2: Проверяем активную очередь")
        active_word = await self._get_next_active_word(user_id, target_lang, exclude_card_id, preload)
        if active_word:
            logger.debug(LogCategory.SPACED_REP, f"✅ ЭТАП 2: Найдено активное слово '{active_word.get('word')}'")
            prepared = await self._prepare_word_data(active_word, is_new=False, is_forced=False)
            if prepared:
                duration_ms = (time.time() - start_time) * 1000
                logger.spaced_rep_found(active_word.get('word'), "активная очередь", duration_ms, str(user_id))
                return prepared
        else:
            logger.debug(LogCategory.SPACED_REP, f"❌ ЭТАП 2: Активных слов не найдено")

        # 3. Если активная очередь пуста, берем новое слово
        logger.debug(LogCategory.SPACED_REP, f"🔍 ЭТАП 3: Ищем новое слово")
        new_word = await self._get_new_word(user_id, target_lang, exclude_card_id, preload)
        if new_word:
            logger.debug(LogCategory.SPACED_REP, f"✅ ЭТАП 3: Найдено новое слово '{new_word.get('word')}'")
            if preload:
                logger.debug(LogCategory.SPACED_REP, f"🔄 ПРЕДЗАГРУЗКА: Подготавливаем новое слово БЕЗ добавления в прогресс")
                # Для предзагрузки НЕ добавляем слово в прогресс
                prepared = await self._prepare_word_data(new_word, is_new=True, is_forced=False)
                if prepared:
                    duration_ms = (time.time() - start_time) * 1000
                    logger.spaced_rep_found(new_word.get('word'), "новые слова (предзагрузка)", duration_ms, str(user_id))
                    return prepared
            else:
                logger.debug(LogCategory.SPACED_REP, f"💾 ОСНОВНОЙ ЗАПРОС: Добавляем новое слово в прогресс")
                # Для обычной загрузки добавляем слово в прогресс пользователя
                added = await self._add_word_to_progress(user_id, new_word["_id"])
                if not added:
                    logger.warning(LogCategory.SPACED_REP, f"❌ НЕ ДОБАВЛЕНО: Слово {new_word.get('word')} уже в прогрессе, ищем следующее")
                    # Пробуем получить следующее слово, если не удалось добавить текущее
                    return await self.get_next_word(user_id, target_lang, skip_forced, preload, exclude_card_id)

                logger.debug(LogCategory.SPACED_REP, f"✅ ДОБАВЛЕНО: Слово {new_word.get('word')} добавлено в прогресс")
                prepared = await self._prepare_word_data(new_word, is_new=True, is_forced=False)
                if prepared:
                    duration_ms = (time.time() - start_time) * 1000
                    logger.spaced_rep_found(new_word.get('word'), "новые слова", duration_ms, str(user_id))
                    return prepared

                # Если что-то пошло не так, пробуем получить следующее слово
                logger.warning(LogCategory.SPACED_REP, "❌ ОШИБКА: Не удалось подготовить данные нового слова")
                return await self.get_next_word(user_id, target_lang, skip_forced, preload, exclude_card_id)
        else:
            logger.debug(LogCategory.SPACED_REP, f"❌ ЭТАП 3: Новых слов не найдено")

        # Ничего не найдено
        duration_ms = (time.time() - start_time) * 1000
        logger.debug(LogCategory.SPACED_REP, f"🚨 РЕЗУЛЬТАТ: Ничего не найдено за {duration_ms:.1f}ms")
        logger.spaced_rep_not_found(duration_ms)
        return None
    
    async def process_answer(
        self, 
        user_id: Union[str, ObjectId], 
        word_id: Union[str, ObjectId], 
        is_correct: bool
    ) -> dict:
        """Обработать ответ пользователя.
        
        Args:
            user_id: ID пользователя
            word_id: ID слова
            is_correct: Правильный ли ответ
            
        Returns:
            Обновленный прогресс по слову
        """
        if not isinstance(user_id, ObjectId):
            user_id = ObjectId(user_id)
        if not isinstance(word_id, ObjectId):
            word_id = ObjectId(word_id)

        # Получаем слово для логирования
        word_data = await self._get_word_by_id_cached(word_id)
        word_text = word_data.get('word', 'unknown') if word_data else 'unknown'

        # Получаем текущий прогресс (с кэшированием)
        progress = await self._get_user_progress_cached(user_id, word_id)
        
        now = datetime.utcnow()
        
        if not progress:
            # Если прогресса нет, создаем новую запись
            # Начинаем с interval_level = -1 (новое слово)
            progress = {
                "user_id": user_id,
                "word_id": word_id,
                "interval_level": -1,  # Новое слово начинается с -1
                "correct_answers": 0,
                "incorrect_answers": 0,
                "last_reviewed": now,
                "next_review": now,
                "is_learned": False,
                "force_review": False,
                "created_at": now,
                "updated_at": now
            }
        
        # Получаем текущий уровень и состояние предыдущего ответа
        current_level = progress.get("interval_level", -1)
        was_last_incorrect = progress.get("last_answer_was_incorrect", False)
        
        # Обновляем счетчики ответов
        if is_correct:
            # 🔧 ИСПРАВЛЕНИЕ: Проверяем, является ли это действительно новым словом ДО увеличения счетчиков
            # Новое слово = interval_level = -1 И никогда не было ответов (correct_answers = 0 И incorrect_answers = 0)
            is_truly_new_word = (
                current_level == -1 and
                progress.get("correct_answers", 0) == 0 and
                progress.get("incorrect_answers", 0) == 0
            )

            # Правильный ответ - увеличиваем счетчик ПОСЛЕ проверки
            progress["correct_answers"] = progress.get("correct_answers", 0) + 1

            if is_truly_new_word:
                # 🔧 ИСПРАВЛЕНИЕ: Новые слова с первого раза правильно → сразу выучены (уровень 15)
                new_level = 15  # Новое слово выучено с первого раза
                progress["interval_level"] = new_level
                progress["is_learned"] = True  # Выучено!
                progress["force_review"] = False
                progress["last_answer_was_incorrect"] = False

                # 🔧 ИСПРАВЛЕНИЕ: Устанавливаем далекую дату для выученного слова
                progress["next_review"] = now + timedelta(days=365)  # Через год

                # 🔍 ОТЛАДКА: Проверяем установленное время
                next_review_str = progress["next_review"].strftime("%Y-%m-%d %H:%M:%S")
                logger.info(LogCategory.SPACED_REP, f"🔧 НОВОЕ СЛОВО: Установлено next_review = {next_review_str}")

                logger.info(LogCategory.SPACED_REP, f"Новое слово выучено с первого раза! Уровень: {current_level} -> {new_level}")
            else:
                # Обычная логика для уже изучаемых слов
                new_level = current_level + 1
                progress["interval_level"] = new_level

                # Сбрасываем флаги
                progress["force_review"] = False
                progress["last_answer_was_incorrect"] = False

                # Проверяем, достигли ли максимального уровня (слово выучено)
                if new_level >= MAX_INTERVAL_LEVEL:
                    progress["is_learned"] = True
                    # Устанавливаем далекую дату следующего повторения
                    progress["next_review"] = now + timedelta(days=365)
                    logger.info(LogCategory.SPACED_REP, f"Слово достигло максимального уровня и считается выученным! Уровень: {current_level} -> {new_level}")

                    # Отладочная информация для выученных слов
                    logger.debug(LogCategory.SPACED_REP, f"🔧 ИНТЕРВАЛ: уровень {new_level} → выучено мин (индекс N/A)")
                else:
                    # Вычисляем следующий интервал для неполностью выученных слов
                    level = max(0, min(new_level, len(REPETITION_INTERVALS) - 1))
                    interval_minutes = REPETITION_INTERVALS[level]
                    progress["next_review"] = now + timedelta(minutes=interval_minutes)

                    # Отладочная информация для изучаемых слов
                    logger.debug(LogCategory.SPACED_REP, f"🔧 ИНТЕРВАЛ: уровень {new_level} → {interval_minutes} мин (индекс {level})")
        else:
            # Неправильный ответ
            progress["incorrect_answers"] = progress.get("incorrect_answers", 0) + 1
            
            # Определяем новый уровень в зависимости от предыдущего ответа
            if was_last_incorrect:
                # Два неправильных ответа подряд - сбрасываем до -1
                new_level = -1
            else:
                # Первый неправильный ответ - уменьшаем на 1 (но не меньше -1)
                new_level = max(-1, current_level - 1)
            
            progress["interval_level"] = new_level
            
            # Устанавливаем следующие параметры для неправильного ответа
            progress["next_review"] = now  # Следующее повторение сразу
            progress["force_review"] = True  # Добавляем в форсированную очередь
            progress["last_answer_was_incorrect"] = True  # Запоминаем, что ответ был неправильный
        
        # Обновляем даты для всех случаев
        progress["last_reviewed"] = now
        progress["updated_at"] = now

        # is_learned уже правильно установлено в логике выше, не перезаписываем
        
        # 🔍 ОТЛАДКА: Логируем время следующего повторения ДО сохранения
        next_review_str = progress["next_review"].strftime("%H:%M:%S") if progress.get("next_review") else "None"
        logger.info(LogCategory.SPACED_REP, f"🕒 ВРЕМЯ: '{word_text}' next_review = {next_review_str}")

        # Сохраняем прогресс
        collection = await self.user_progress_collection
        result = await collection.update_one(
            {"user_id": user_id, "word_id": word_id},
            {"$set": progress},
            upsert=True
        )

        # 🔍 ОТЛАДКА: Проверяем, что запись действительно обновилась
        if result.modified_count > 0 or result.upserted_id:
            logger.info(LogCategory.SPACED_REP, f"✅ СОХРАНЕНО: '{word_text}' прогресс обновлен в базе")
        else:
            logger.warning(LogCategory.SPACED_REP, f"⚠️ НЕ СОХРАНЕНО: '{word_text}' прогресс НЕ обновлен в базе!")

        # Инвалидируем кэш для этого прогресса
        await self._invalidate_user_progress_cache(user_id, word_id)

        # Логируем результат обработки ответа
        new_level = progress.get("interval_level", -1)

        # Специальное логирование для карточек, которые могли использовать подсказку
        was_forced = progress.get("force_review", False)
        if was_forced and is_correct:
            logger.info(LogCategory.SPACED_REP, f"🎯 ПОСЛЕ ПОДСКАЗКИ: '{word_text}' отвечена правильно, уровень {current_level} → {new_level}")

        logger.spaced_rep_answer(str(user_id), word_text, is_correct, new_level)

        return progress
    
    # ===== Вспомогательные методы =====

    @cached_word(ttl=600)  # Кэшируем слова на 10 минут
    async def _get_word_by_id_cached(self, word_id: ObjectId) -> Optional[dict]:
        """Получить слово по ID с кэшированием."""
        words_collection = await self.words_collection
        return await words_collection.find_one({"_id": word_id})

    @cached_user_progress(ttl=30)  # Кэшируем прогресс на 30 секунд
    async def _get_user_progress_cached(self, user_id: ObjectId, word_id: ObjectId) -> Optional[dict]:
        """Получить прогресс пользователя по слову с кэшированием."""
        collection = await self.user_progress_collection
        return await collection.find_one({
            "user_id": user_id,
            "word_id": word_id
        })

    @cached_user_progress(ttl=60)  # Кэшируем список изученных слов на 1 минуту
    async def _get_user_learned_words_cached(self, user_id: ObjectId) -> list:
        """Получить список ID всех слов в прогрессе пользователя с кэшированием.

        ВАЖНО: Возвращает ВСЕ слова в прогрессе (и изученные, и изучаемые),
        чтобы исключить их из поиска новых слов.
        """
        collection = await self.user_progress_collection

        learned_word_ids = []
        cursor = collection.find(
            {"user_id": user_id},
            {"word_id": 1, "_id": 0}
        )
        async for doc in cursor:
            learned_word_ids.append(doc["word_id"])

        # 🔍 ОТЛАДКА: Логируем количество слов в прогрессе
        logger.debug(LogCategory.SPACED_REP, f"🔍 ПРОГРЕСС: Найдено {len(learned_word_ids)} слов в прогрессе пользователя")

        # 🔧 КРИТИЧЕСКАЯ ОТЛАДКА: Проверяем выученные слова в кэше
        if learned_word_ids:
            # Проверяем, сколько из них действительно выучены
            learned_check = await collection.find({
                "user_id": user_id,
                "word_id": {"$in": learned_word_ids},
                "is_learned": True
            }, {"word_id": 1, "_id": 0}).to_list(None)

            actual_learned_ids = [str(doc["word_id"]) for doc in learned_check]
            logger.debug(LogCategory.SPACED_REP, f"🔧 КЭШ: Из {len(learned_word_ids)} слов в кэше, {len(actual_learned_ids)} действительно выучены")

            if len(actual_learned_ids) < len(learned_word_ids):
                logger.warning(LogCategory.SPACED_REP, f"⚠️ КЭШ: Кэш содержит невыученные слова! Всего: {len(learned_word_ids)}, выучено: {len(actual_learned_ids)}")

        return learned_word_ids

    async def _invalidate_user_progress_cache(self, user_id: ObjectId, word_id: ObjectId):
        """Инвалидировать кэш прогресса пользователя."""
        # Очищаем кэш конкретного прогресса слова
        cache_key = cache_service._generate_key("user_progress", user_id, word_id)
        await cache_service.delete(cache_key)

        # 🔧 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Правильная очистка кэша списка изученных слов
        # Используем тот же метод генерации ключа, что и в декораторе @cached_user_progress
        learned_words_cache_key = cache_service._generate_key("user_progress", user_id)
        await cache_service.delete(learned_words_cache_key)

        logger.debug(LogCategory.SPACED_REP, f"🔧 КЭШ: Очищен кэш прогресса и списка слов для {user_id}")
        logger.debug(LogCategory.SPACED_REP, f"🔧 КЭШ: Ключи - прогресс: {cache_key}, список: {learned_words_cache_key}")
    
    async def _get_forced_word(self, user_id: ObjectId, target_lang: str = None, exclude_card_id: Optional[ObjectId] = None) -> Optional[dict]:
        """Получить слово из форсированной очереди."""
        collection = await self.user_progress_collection

        # Оптимизированный запрос: сначала находим прогресс, потом получаем слово
        query = {
            "user_id": user_id,
            "force_review": True
        }

        # Исключаем указанную карточку
        if exclude_card_id:
            query["word_id"] = {"$ne": exclude_card_id}

        # Находим прогресс с сортировкой по next_review
        progress = await collection.find_one(
            query,
            sort=[("next_review", 1)]
        )

        if not progress:
            return None

        # Получаем данные слова отдельным запросом (с кэшированием)
        word_data = await self._get_word_by_id_cached(progress["word_id"])

        if not word_data:
            return None

        # Проверяем язык, если указан
        if target_lang and word_data.get("language", "").lower() != target_lang.lower():
            # Если язык не подходит, ищем следующее слово
            remaining_progress = collection.find(
                query,
                sort=[("next_review", 1)]
            ).skip(1)

            async for prog in remaining_progress:
                word = await self._get_word_by_id_cached(prog["word_id"])
                if word and word.get("language", "").lower() == target_lang.lower():
                    # Объединяем данные прогресса и слова
                    return {**prog, **word}
            return None

        # Объединяем данные прогресса и слова
        return {**progress, **word_data}
    
    async def _get_next_active_word(self, user_id: ObjectId, target_lang: str = None, exclude_card_id: Optional[ObjectId] = None, preload: bool = False) -> Optional[dict]:
        """Получить следующее слово из активной очереди.

        Возвращает слово, которое нужно повторить в данный момент.

        Args:
            user_id: ID пользователя
            target_lang: Целевой язык (например, 'en', 'ru')
        """
        try:
            now = datetime.utcnow()

            # 🔍 ДЕТАЛЬНЫЕ ЛОГИ: Начало поиска активных слов
            logger.debug(LogCategory.SPACED_REP, f"🔍 АКТИВНЫЕ: Ищем активные слова для пользователя {str(user_id)[-8:]}")

            query = {
                "user_id": user_id,
                "next_review": {"$lte": now},
                "force_review": False,
                "is_learned": False,
                "interval_level": {"$gte": 0},  # Исключаем новые слова (interval_level = -1)
            }

            # Исключаем указанную карточку
            if exclude_card_id:
                query["word_id"] = {"$ne": exclude_card_id}
                logger.debug(LogCategory.SPACED_REP, f"🚫 АКТИВНЫЕ: Исключаем карточку {str(exclude_card_id)[-8:]}")

            # 🚀 УМНАЯ ФИЛЬТРАЦИЯ: Вместо буфера времени используем исключение недавно отвеченных карточек
            if preload:
                # Для предзагрузки исключаем карточки, на которые недавно отвечали
                # Это предотвращает race conditions без блокировки быстрых интервалов
                recent_threshold = datetime.utcnow() - timedelta(seconds=10)
                query["last_reviewed"] = {"$lt": recent_threshold}
                logger.debug(LogCategory.SPACED_REP, f"🧠 АКТИВНЫЕ: Исключаем карточки, отвеченные менее 10 секунд назад (после {recent_threshold.strftime('%H:%M:%S')})")
            else:
                logger.debug(LogCategory.SPACED_REP, f"⏰ АКТИВНЫЕ: Без фильтрации (до {now.strftime('%H:%M:%S')})")

            collection = await self.user_progress_collection

            # 🔍 ОТЛАДКА: Логируем запрос для активных карточек
            logger.debug(LogCategory.SPACED_REP, f"🔍 АКТИВНЫЕ: Ищем карточки с запросом: {query}")

            progress = await collection.find_one(
                query,
                sort=[("next_review", 1)]
            )

            if not progress:
                # 🔍 ДЕТАЛЬНЫЕ ЛОГИ: Проверяем, есть ли вообще карточки в прогрессе
                total_progress = await collection.count_documents({"user_id": user_id})
                logger.debug(LogCategory.SPACED_REP, f"❌ АКТИВНЫЕ: Не найдено активных карточек. Всего в прогрессе: {total_progress}")

                # Дополнительная диагностика - проверяем карточки по категориям
                now = datetime.utcnow()
                ready_count = await collection.count_documents({
                    "user_id": user_id,
                    "next_review": {"$lte": now},
                    "is_learned": False
                })
                learned_count = await collection.count_documents({
                    "user_id": user_id,
                    "is_learned": True
                })
                future_count = await collection.count_documents({
                    "user_id": user_id,
                    "next_review": {"$gt": now},
                    "is_learned": False
                })

                logger.debug(LogCategory.SPACED_REP, f"📊 ДИАГНОСТИКА: Готовых к повторению: {ready_count}, Выученных: {learned_count}, Будущих: {future_count}")
                return None

            # 🔍 ДЕТАЛЬНЫЕ ЛОГИ: Найденная карточка
            next_review_str = progress["next_review"].strftime("%H:%M:%S") if progress.get("next_review") else "None"
            logger.debug(LogCategory.SPACED_REP, f"✅ АКТИВНЫЕ: Найдена карточка level={progress.get('interval_level')}, next_review={next_review_str}")

            # Получаем данные слова отдельным запросом (с кэшированием)
            word_data = await self._get_word_by_id_cached(progress["word_id"])

            if not word_data:
                logger.debug(LogCategory.SPACED_REP, f"❌ АКТИВНЫЕ: Не найдены данные слова для ID {str(progress['word_id'])[-8:]}")
                return None

            # Проверяем язык, если указан
            if target_lang and word_data.get("language", "").lower() != target_lang.lower():
                logger.debug(LogCategory.SPACED_REP, f"🌐 АКТИВНЫЕ: Язык не подходит ({word_data.get('language')} != {target_lang}), ищем следующее")
                # Если язык не подходит, ищем следующее слово
                remaining_progress = collection.find(
                    query,
                    sort=[("next_review", 1)]
                ).skip(1)

                async for prog in remaining_progress:
                    word = await self._get_word_by_id_cached(prog["word_id"])
                    if word and word.get("language", "").lower() == target_lang.lower():
                        logger.debug(LogCategory.SPACED_REP, f"✅ АКТИВНЫЕ: Найдено подходящее слово '{word.get('word')}'")
                        return {**prog, **word}
                logger.debug(LogCategory.SPACED_REP, f"❌ АКТИВНЫЕ: Не найдено слов подходящего языка")
                return None

            # Объединяем данные прогресса и слова
            logger.debug(LogCategory.SPACED_REP, f"✅ АКТИВНЫЕ: Возвращаем слово '{word_data.get('word')}'")
            return {**progress, **word_data}

        except Exception as e:
            logger.error(LogCategory.SPACED_REP, f"❌ ОШИБКА в _get_next_active_word: {str(e)}")
            return None
    
    async def _get_new_word(self, user_id: ObjectId, target_lang: str = None, exclude_card_id: Optional[ObjectId] = None, preload: bool = False) -> Optional[dict]:
        """Получить новое слово для изучения.

        ОПТИМИЗИРОВАННАЯ ВЕРСИЯ: Использует простые запросы вместо тяжелой агрегации.

        Args:
            user_id: ID пользователя
            target_lang: Целевой язык (например, 'en', 'ru')

        Returns:
            Случайное слово или None, если слова не найдены
        """
        try:
            # 🚀 ОПТИМИЗАЦИЯ: Используем быструю агрегацию вместо двух отдельных запросов
            # Результаты тестирования: ускорение в 2.5 раза (с 832ms до 330ms)
            if preload:  # preload=True указывает на предзагрузку
                logger.debug(LogCategory.SPACED_REP, f"🚀 ПРЕДЗАГРУЗКА: Используем оптимизированную агрегацию")
                collection = await self.user_progress_collection

                # 🚀 ОПТИМИЗИРОВАННАЯ АГРЕГАЦИЯ: Получаем слова в прогрессе одним запросом
                if target_lang:
                    pipeline = [
                        {
                            "$match": {
                                "user_id": user_id,
                                "word_id": {"$exists": True}
                            }
                        },
                        {
                            "$lookup": {
                                "from": "words",
                                "localField": "word_id",
                                "foreignField": "_id",
                                "as": "word_info"
                            }
                        },
                        {
                            "$match": {
                                "word_info.language": target_lang.lower()
                            }
                        },
                        {
                            "$project": {
                                "word_id": 1,
                                "_id": 0
                            }
                        }
                    ]
                else:
                    # Если язык не указан, берем весь прогресс (простой запрос)
                    pipeline = [
                        {
                            "$match": {
                                "user_id": user_id,
                                "word_id": {"$exists": True}
                            }
                        },
                        {
                            "$project": {
                                "word_id": 1,
                                "_id": 0
                            }
                        }
                    ]

                cursor = collection.aggregate(pipeline)
                learned_word_ids = [doc["word_id"] async for doc in cursor]

                logger.debug(LogCategory.SPACED_REP, f"🚀 ПРЕДЗАГРУЗКА: Найдено {len(learned_word_ids)} слов в прогрессе для языка {target_lang} (оптимизированная агрегация)")
            else:
                # Для обычных запросов используем кэш
                learned_word_ids = await self._get_user_learned_words_cached(user_id)

            # 🔍 ДЕТАЛЬНЫЕ ЛОГИ: Исключаемые слова
            logger.debug(LogCategory.SPACED_REP, f"🔍 НОВЫЕ: Исключаем {len(learned_word_ids)} слов из поиска новых")
            if exclude_card_id:
                logger.debug(LogCategory.SPACED_REP, f"🚫 НОВЫЕ: Дополнительно исключаем карточку {str(exclude_card_id)[-8:]}")

            # 🔧 ДОПОЛНИТЕЛЬНАЯ ОТЛАДКА: Проверяем, есть ли выученные слова в списке исключений
            if learned_word_ids:
                collection = await self.user_progress_collection
                learned_count = await collection.count_documents({
                    "user_id": user_id,
                    "is_learned": True
                })
                logger.debug(LogCategory.SPACED_REP, f"🔧 КЭШ: В базе {learned_count} выученных слов, в кэше {len(learned_word_ids)} всего слов")

                # 🔧 КРИТИЧЕСКАЯ ОТЛАДКА: Показываем первые несколько выученных слов
                learned_words_sample = await collection.find({
                    "user_id": user_id,
                    "is_learned": True
                }, {"word_id": 1, "_id": 0}).limit(5).to_list(None)
                learned_sample_ids = [str(doc["word_id"]) for doc in learned_words_sample]
                logger.debug(LogCategory.SPACED_REP, f"🔧 КЭШ: Примеры выученных слов в базе: {learned_sample_ids}")

                # Проверяем, есть ли они в кэше
                cache_sample_ids = [str(wid) for wid in learned_word_ids[:5]]
                logger.debug(LogCategory.SPACED_REP, f"🔧 КЭШ: Примеры слов в кэше: {cache_sample_ids}")

            # Формируем условия для поиска новых слов
            words_collection = await self.words_collection
            match_conditions = {
                "_id": {"$nin": learned_word_ids}  # Исключаем уже изученные
            }

            # Добавляем фильтр по языку
            if target_lang:
                match_conditions["language"] = target_lang.lower()

            # Исключаем указанную карточку (добавляем к существующему списку исключений)
            if exclude_card_id:
                # Объединяем исключение изученных слов и текущей карточки
                exclude_list = match_conditions["_id"]["$nin"] + [exclude_card_id]
                # Удаляем дубликаты для чистоты (MongoDB и так их игнорирует)
                match_conditions["_id"]["$nin"] = list(set(exclude_list))

            # 🔧 УНИВЕРСАЛЬНАЯ логика приоритетов для ВСЕХ уровней (A0, A1, A2, B1, B2, C1, C2)
            # 🚨 КРИТИЧНО: Для предзагрузки используем те же данные что и для основного запроса
            if preload:
                # 🚨 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Для предзагрузки учитываем исключенные карточки
                # Добавляем exclude_card_id к списку исключений для правильного определения приоритетов
                exclude_list = learned_word_ids.copy() if learned_word_ids else []
                if exclude_card_id:
                    exclude_list.append(exclude_card_id)
                    logger.debug(LogCategory.SPACED_REP, f"🔍 ПРЕДЗАГРУЗКА: Добавляем exclude_card_id {exclude_card_id} к исключениям")

                priority_filter = await self._get_universal_priority_filter_with_learned_words(user_id, target_lang, exclude_list)
            else:
                # Для основного запроса используем стандартную логику
                priority_filter = await self._get_universal_priority_filter(user_id, target_lang)

            if priority_filter:
                match_conditions.update(priority_filter)
                logger.debug(LogCategory.SPACED_REP, f"🔍 ПРИОРИТЕТ: Применяем фильтр {priority_filter}")

            # 🚀 ОПТИМИЗАЦИЯ: Простая агрегация только для случайного выбора
            pipeline = [
                {"$match": match_conditions},
                {"$sample": {"size": 1}}
            ]

            # 🔍 ДЕТАЛЬНЫЕ ЛОГИ: Запрос к базе данных
            logger.debug(LogCategory.SPACED_REP, f"🔍 НОВЫЕ: Выполняем запрос с условиями: {match_conditions}")

            cursor = words_collection.aggregate(pipeline)
            words = await cursor.to_list(length=1)

            if not words:
                logger.debug(LogCategory.SPACED_REP, f"❌ НОВЫЕ: Не найдено слов с условиями: {match_conditions}")
                return None

            word = words[0]
            logger.debug(LogCategory.SPACED_REP, f"✅ НОВЫЕ: Найдено слово '{word.get('word')}' (ID: {str(word.get('_id'))[-8:]})")
            return word

        except Exception as e:
            logger.error(LogCategory.SPACED_REP, f"❌ ОШИБКА в _get_new_word: {str(e)}")
            return None
    
    async def _add_word_to_progress(self, user_id: ObjectId, word_id: ObjectId) -> bool:
        """Добавить слово в прогресс пользователя.
        
        Args:
            user_id: ID пользователя
            word_id: ID слова
            
        Returns:
            bool: True, если слово успешно добавлено, иначе False
        """
        try:
            now = datetime.utcnow()
            
            # Получаем коллекции
            collection = await self.user_progress_collection
            words_collection = await self.words_collection

            # Проверяем существование слова в коллекции слов
            word = await words_collection.find_one({"_id": word_id})
            if not word:
                return False

            # Проверяем, нет ли уже такого слова у пользователя
            existing = await collection.find_one({
                "user_id": user_id,
                "word_id": word_id
            })

            if existing:
                return False
            
            # Создаем новую запись прогресса
            # Начинаем с interval_level = -1 (еще не было ответов)
            progress = {
                "user_id": user_id,
                "word_id": word_id,
                "interval_level": -1,  # Начинаем с -1 (еще не было ответов)
                "correct_answers": 0,
                "incorrect_answers": 0,
                "last_reviewed": now,
                "next_review": now,  # Показываем сразу
                "is_learned": False,
                "force_review": False,  # Новые слова НЕ идут в форсированную очередь сразу
                "created_at": now,
                "updated_at": now
            }
            
            # Вставляем новую запись
            result = await collection.insert_one(progress)
            if not result.inserted_id:
                return False

            # Проверяем, что запись действительно добавилась
            check = await collection.find_one({"user_id": user_id, "word_id": word_id})
            if not check:
                return False

            # 🔧 ИСПРАВЛЕНИЕ: Очищаем кэш через единую функцию
            await self._invalidate_user_progress_cache(user_id, word_id)

            return True
            
        except Exception as e:
            logger.error(LogCategory.SPACED_REP, f"Ошибка в _add_word_to_progress: {str(e)}")
            return False
    
    async def _prepare_word_data(
        self,
        word_data: dict,
        is_new: bool = False,
        is_forced: bool = False
    ) -> Optional[dict]:
        """Подготовить данные слова для отображения с переводом.

        Обрабатывает различные форматы входных данных и добавляет перевод:
        1. Данные прогресса (содержат word_id)
        2. Полные данные слова (содержат _id и word)
        3. Результат агрегации (содержит word_data)

        Args:
            word_data: Данные слова или прогресса
            is_new: Это новое слово
            is_forced: Это слово из форсированной очереди

        Returns:
            Словарь с данными слова и переводом или None, если слово не найдено
        """
        try:
            # Если это данные прогресса, получаем детали слова
            if "word_id" in word_data and ("word" not in word_data or not word_data["word"]):
                word_doc = await self._get_word_by_id_cached(word_data["word_id"])
                if not word_doc:
                    return None

                # Объединяем данные прогресса с данными слова
                word_data = {**word_data, **word_doc}

            # Если это результат агрегации с вложенными данными слова
            if "word_data" in word_data and isinstance(word_data["word_data"], dict):
                word_data = {**word_data, **word_data["word_data"]}

            # 🚀 ОПТИМИЗАЦИЯ: Получаем перевод сразу здесь
            translation_word = None
            concept_id = word_data.get("concept_id")
            current_language = word_data.get("language", "").lower()

            if concept_id:
                # Определяем язык перевода (противоположный текущему)
                target_translation_lang = "en" if current_language == "ru" else "ru"

                translation_word = await self._get_translation_by_concept_cached(concept_id, target_translation_lang)

            # Формируем результат с переводом
            result = {
                "word_id": str(word_data.get("word_id", word_data.get("_id", ""))),
                "concept_id": word_data.get("concept_id", ""),
                "word": word_data.get("word", ""),
                "examples": word_data.get("examples", []),
                "ipa_pronunciation": word_data.get("ipa_pronunciation", ""),
                "part_of_speech": word_data.get("part_of_speech", ""),
                "level": word_data.get("level", ""),
                "priority": word_data.get("priority", None),
                "language": word_data.get("language", ""),
                "is_new": is_new,
                "is_forced_review": is_forced or word_data.get("force_review", False),
                # 🚀 Добавляем данные перевода
                "translation": translation_word.get("word", "") if translation_word else "",
                "translation_examples": translation_word.get("examples", []) if translation_word else [],
                "translation_language": target_translation_lang if translation_word else ""
            }

            # Добавляем дополнительную информацию о прогрессе, если есть
            if "correct_answers" in word_data:
                result["correct_answers"] = word_data["correct_answers"]
            if "incorrect_answers" in word_data:
                result["incorrect_answers"] = word_data["incorrect_answers"]
            if "interval_level" in word_data:
                result["interval_level"] = word_data["interval_level"]

            # 🔍 ОТЛАДКА: Логируем данные карточки для проверки
            word_text = result.get("word", "unknown")
            correct_count = result.get("correct_answers", 0)
            incorrect_count = result.get("incorrect_answers", 0)
            interval_level = result.get("interval_level", -1)
            is_new_flag = result.get("is_new", False)

            logger.info(LogCategory.SPACED_REP, f"📊 КАРТОЧКА: '{word_text}' | Правильных: {correct_count} | Неправильных: {incorrect_count} | Уровень: {interval_level} | Новая: {is_new_flag}")

            return result

        except Exception as e:
            logger.error(LogCategory.SPACED_REP, f"Ошибка в _prepare_word_data: {str(e)}")
            return None

    @cached_word(ttl=600)  # Кэшируем переводы на 10 минут
    async def _get_translation_by_concept_cached(self, concept_id: str, language: str) -> Optional[dict]:
        """Получить перевод по concept_id с кэшированием."""
        words_collection = await self.words_collection
        return await words_collection.find_one({
            "concept_id": concept_id,
            "language": language.lower()
        })
    
    def _calculate_next_review(self, interval_level: int) -> datetime:
        """Рассчитать следующую дату повторения.
        
        Args:
            interval_level: Уровень интервала (индекс в REPETITION_INTERVALS)
            
        Returns:
            datetime: Дата следующего повторения
        """
        now = datetime.utcnow()
        # Если уровень отрицательный - используем первый интервал (30 секунд)
        level = max(0, min(interval_level, len(REPETITION_INTERVALS) - 1))
        minutes = REPETITION_INTERVALS[level]

        # Отладочная информация
        logger.debug(LogCategory.SPACED_REP, f"🔧 _calculate_next_review: уровень {interval_level} → индекс {level} → {minutes} мин")

        return now + timedelta(minutes=minutes)

    async def _get_a0_priority_filter(self, user_id: ObjectId, target_lang: str = None) -> Optional[dict]:
        """Получить фильтр приоритетов для A0 уровня с трехуровневой системой.

        Логика: переходим на следующий уровень когда слова кончаются, а не по проценту.

        Args:
            user_id: ID пользователя
            target_lang: Целевой язык

        Returns:
            Словарь с фильтрами или None, если не A0
        """
        try:
            # Оптимизированная проверка - получаем все доступные A0 слова одним запросом
            progress_collection = await self.user_progress_collection
            words_collection = await self.words_collection

            # 🚀 ОПТИМИЗАЦИЯ: Используем быструю агрегацию вместо двух отдельных запросов
            if target_lang:
                pipeline = [
                    {
                        "$match": {
                            "user_id": user_id,
                            "word_id": {"$exists": True}
                        }
                    },
                    {
                        "$lookup": {
                            "from": "words",
                            "localField": "word_id",
                            "foreignField": "_id",
                            "as": "word_info"
                        }
                    },
                    {
                        "$match": {
                            "word_info.language": target_lang.lower()
                        }
                    },
                    {
                        "$project": {
                            "word_id": 1,
                            "_id": 0
                        }
                    }
                ]
            else:
                # Если язык не указан, берем весь прогресс (простой запрос)
                pipeline = [
                    {
                        "$match": {
                            "user_id": user_id,
                            "word_id": {"$exists": True}
                        }
                    },
                    {
                        "$project": {
                            "word_id": 1,
                            "_id": 0
                        }
                    }
                ]

            cursor = progress_collection.aggregate(pipeline)
            learned_word_ids = [doc["word_id"] async for doc in cursor]

            # Получаем количество доступных слов каждого приоритета одним агрегационным запросом
            # words_collection уже определена выше
            query = {
                "_id": {"$nin": learned_word_ids},
                "level": "A0"
            }

            if target_lang:
                query["language"] = target_lang.lower()

            # Агрегация для подсчета по приоритетам
            pipeline = [
                {"$match": query},
                {
                    "$group": {
                        "_id": "$priority",
                        "count": {"$sum": 1}
                    }
                }
            ]

            cursor = words_collection.aggregate(pipeline)
            priority_counts = {doc["_id"]: doc["count"] async for doc in cursor}

            # Проверяем приоритеты в порядке важности
            if priority_counts.get("ultra_core", 0) > 0:
                return {
                    "level": "A0",
                    "priority": "ultra_core"
                }

            if priority_counts.get("core", 0) > 0:
                return {
                    "level": "A0",
                    "priority": "core"
                }

            if priority_counts.get("extended", 0) > 0:
                return {
                    "level": "A0",
                    "priority": "extended"
                }

            # Если все A0 слова изучены, показываем все A0 для повторения
            return {
                "level": "A0"
            }

        except Exception as e:
            logger.error(LogCategory.SPACED_REP, f"Ошибка при определении приоритета A0: {e}")
            return None

    async def _get_a0_priority_filter_with_learned_words(self, user_id: ObjectId, target_lang: str = None, learned_word_ids: list = None) -> Optional[dict]:
        """Получить фильтр приоритетов для A0 уровня с использованием переданного списка изученных слов.

        Эта функция используется для предзагрузки, чтобы обеспечить консистентность
        с основным запросом при определении доступных приоритетов.

        Args:
            user_id: ID пользователя
            target_lang: Целевой язык
            learned_word_ids: Список ID изученных слов (для предзагрузки)

        Returns:
            Словарь с фильтрами или None, если не A0
        """
        try:
            words_collection = await self.words_collection

            # Используем переданный список изученных слов (для предзагрузки)
            if learned_word_ids is None:
                learned_word_ids = []

            # Получаем количество доступных слов каждого приоритета одним агрегационным запросом
            query = {
                "_id": {"$nin": learned_word_ids},
                "level": "A0"
            }

            if target_lang:
                query["language"] = target_lang.lower()

            # Агрегация для подсчета по приоритетам
            pipeline = [
                {"$match": query},
                {
                    "$group": {
                        "_id": "$priority",
                        "count": {"$sum": 1}
                    }
                }
            ]

            cursor = words_collection.aggregate(pipeline)
            priority_counts = {doc["_id"]: doc["count"] async for doc in cursor}

            logger.debug(LogCategory.SPACED_REP, f"🔍 A0 ПРИОРИТЕТЫ: Доступно слов - ultra_core: {priority_counts.get('ultra_core', 0)}, core: {priority_counts.get('core', 0)}, extended: {priority_counts.get('extended', 0)}")
            logger.debug(LogCategory.SPACED_REP, f"🔍 A0 ИСКЛЮЧЕНО: {len(learned_word_ids)} слов из поиска")

            # Проверяем приоритеты в порядке важности
            if priority_counts.get("ultra_core", 0) > 0:
                logger.debug(LogCategory.SPACED_REP, f"✅ A0 ПРИОРИТЕТ: Выбран ultra_core ({priority_counts.get('ultra_core', 0)} слов)")
                return {
                    "level": "A0",
                    "priority": "ultra_core"
                }

            if priority_counts.get("core", 0) > 0:
                logger.debug(LogCategory.SPACED_REP, f"✅ A0 ПРИОРИТЕТ: Выбран core ({priority_counts.get('core', 0)} слов)")
                return {
                    "level": "A0",
                    "priority": "core"
                }

            if priority_counts.get("extended", 0) > 0:
                logger.debug(LogCategory.SPACED_REP, f"✅ A0 ПРИОРИТЕТ: Выбран extended ({priority_counts.get('extended', 0)} слов)")
                return {
                    "level": "A0",
                    "priority": "extended"
                }

            # Если все A0 слова изучены, показываем все A0 для повторения
            logger.debug(LogCategory.SPACED_REP, f"⚠️ A0 ПРИОРИТЕТ: Все приоритеты исчерпаны, показываем все A0")
            return {
                "level": "A0"
            }

        except Exception as e:
            logger.error(LogCategory.SPACED_REP, f"Ошибка при определении приоритета A0 с переданными словами: {e}")
            return None

    async def _get_universal_priority_filter(self, user_id: ObjectId, target_lang: str = None) -> Optional[dict]:
        """🔧 УНИВЕРСАЛЬНАЯ функция для определения приоритетов ВСЕХ уровней (A0, A1, A2, B1, B2, C1, C2).

        🚨 ВАЖНО: Эта функция заменяет старую _get_a0_priority_filter и работает для всех уровней.
        При добавлении новых уровней (A1, A2, etc.) логика автоматически подхватит их приоритеты.

        Args:
            user_id: ID пользователя
            target_lang: Целевой язык

        Returns:
            Словарь с фильтрами уровня и приоритета или None
        """
        try:
            # Пока используем старую логику для A0, но готовимся к расширению
            return await self._get_a0_priority_filter(user_id, target_lang)
        except Exception as e:
            logger.error(LogCategory.SPACED_REP, f"Ошибка при определении универсального приоритета: {e}")
            return None

    async def _get_universal_priority_filter_with_learned_words(self, user_id: ObjectId, target_lang: str = None, learned_word_ids: list = None) -> Optional[dict]:
        """🔧 УНИВЕРСАЛЬНАЯ функция для предзагрузки с поддержкой ВСЕХ уровней.

        🚨 КРИТИЧНО: Эта функция обеспечивает консистентность между предзагрузкой и основным запросом
        для ВСЕХ уровней (A0, A1, A2, B1, B2, C1, C2).

        🚀 ОПТИМИЗАЦИЯ: Кэшируем результаты для ускорения предзагрузки.

        📋 БУДУЩЕЕ РАСШИРЕНИЕ:
        - При добавлении A1: добавить приоритеты ultra_core, core, extended для A1
        - При добавлении A2: добавить приоритеты ultra_core, core, extended для A2
        - И так далее для всех уровней

        Args:
            user_id: ID пользователя
            target_lang: Целевой язык
            learned_word_ids: Список ID изученных слов (для предзагрузки)

        Returns:
            Словарь с фильтрами уровня и приоритета или None
        """
        try:
            # 🚨 ВРЕМЕННО ОТКЛЮЧАЕМ КЭШИРОВАНИЕ для отладки проблемы с приоритетами
            # Проблема: кэш сохраняет неправильное состояние и система пропускает ultra_core слова

            # Всегда вычисляем приоритет заново для точности
            priority_filter = await self._get_a0_priority_filter_with_learned_words(user_id, target_lang, learned_word_ids)

            logger.debug(LogCategory.SPACED_REP, f"🔍 ПРИОРИТЕТ БЕЗ КЭША: Вычислен приоритет {priority_filter}")

            return priority_filter

        except Exception as e:
            logger.error(LogCategory.SPACED_REP, f"Ошибка при определении универсального приоритета с переданными словами: {e}")
            return None



    async def _check_available_words(self, user_id: ObjectId, target_lang: str = None, priority: str = "ultra_core") -> int:
        """Проверить количество доступных слов определенного приоритета.

        Args:
            user_id: ID пользователя
            target_lang: Целевой язык
            priority: Приоритет ("ultra_core", "core", "extended")

        Returns:
            Количество доступных (неизученных) слов
        """
        try:
            # Получаем ID всех слов определенного языка, которые уже есть в прогрессе пользователя
            progress_collection = await self.user_progress_collection
            words_collection = await self.words_collection

            # Если указан целевой язык, фильтруем прогресс только по этому языку
            if target_lang:
                # Сначала находим все слова нужного языка
                target_lang_words = await words_collection.find(
                    {"language": target_lang.lower()},
                    {"_id": 1}
                ).to_list(None)
                target_lang_word_ids = [doc["_id"] for doc in target_lang_words]

                # Затем находим прогресс только по словам этого языка
                cursor = progress_collection.find(
                    {
                        "user_id": user_id,
                        "word_id": {"$in": target_lang_word_ids}
                    },
                    {"word_id": 1, "_id": 0}
                )
            else:
                # Если язык не указан, берем весь прогресс
                cursor = progress_collection.find(
                    {"user_id": user_id},
                    {"word_id": 1, "_id": 0}
                )

            learned_word_ids = [doc["word_id"] async for doc in cursor]

            # Ищем слова определенного приоритета, которых нет в прогрессе
            # words_collection уже определена выше
            query = {
                "_id": {"$nin": learned_word_ids},
                "level": "A0",
                "priority": priority
            }

            if target_lang:
                query["language"] = target_lang.lower()

            available_count = await words_collection.count_documents(query)
            return available_count

        except Exception as e:
            logger.error(LogCategory.SPACED_REP, f"Ошибка при проверке доступных {priority} слов: {e}")
            return 0

    async def _calculate_a0_priority_progress(self, user_id: ObjectId, target_lang: str = None, priority: str = "core") -> float:
        """Рассчитать прогресс изучения слов A0 по приоритету.

        Args:
            user_id: ID пользователя
            target_lang: Целевой язык
            priority: Приоритет ("ultra_core", "core", "extended")

        Returns:
            Процент изученных слов данного приоритета (0.0 - 1.0)
        """
        try:
            # Получаем общее количество слов A0 данного приоритета для языка
            words_collection = await self.words_collection

            if priority == "core":
                # Для CORE считаем ULTRA-CORE + CORE слова
                query = {"level": "A0", "priority": {"$in": ["ultra_core", "core"]}}
            else:
                # Для конкретного приоритета
                query = {"level": "A0", "priority": priority}

            if target_lang:
                query["language"] = target_lang.lower()

            total_words = await words_collection.count_documents(query)

            if total_words == 0:
                return 1.0  # Если нет слов, считаем что все изучено

            # Получаем ID всех слов данного приоритета
            cursor = words_collection.find(query, {"_id": 1})
            word_ids = [doc["_id"] async for doc in cursor]

            # Считаем сколько из них уже в прогрессе пользователя
            progress_collection = await self.user_progress_collection
            learned_count = await progress_collection.count_documents({
                "user_id": user_id,
                "word_id": {"$in": word_ids}
            })

            progress = learned_count / total_words
            return progress

        except Exception as e:
            logger.error(LogCategory.SPACED_REP, f"Ошибка при расчете прогресса A0 {priority}: {e}")
            return 0.0

    async def get_user_statistics(self, user_id: Union[str, ObjectId], target_lang: str = None) -> dict:
        """Получить статистику пользователя.

        Args:
            user_id: ID пользователя
            target_lang: Целевой язык (например, 'en', 'ru')

        Returns:
            Словарь со статистикой пользователя
        """
        try:
            if not isinstance(user_id, ObjectId):
                user_id = ObjectId(user_id)

            collection = await self.user_progress_collection
            words_collection = await self.words_collection

            # Если указан целевой язык, фильтруем только по этому языку
            if target_lang:
                # Сначала находим все слова нужного языка
                target_lang_words = await words_collection.find(
                    {"language": target_lang.lower()},
                    {"_id": 1}
                ).to_list(None)
                target_lang_word_ids = [doc["_id"] for doc in target_lang_words]

                # Фильтруем прогресс только по словам этого языка
                progress_filter = {
                    "user_id": user_id,
                    "word_id": {"$in": target_lang_word_ids}
                }

                # Получаем общее количество доступных слов в языке
                total_words_available = await words_collection.count_documents({
                    "language": target_lang.lower()
                })
            else:
                # Если язык не указан, берем весь прогресс
                progress_filter = {"user_id": user_id}
                total_words_available = await words_collection.count_documents({})

            # Получаем статистику выученных слов
            learned_count = await collection.count_documents({
                **progress_filter,
                "is_learned": True
            })

            # Получаем общее количество слов в изучении
            total_in_progress = await collection.count_documents(progress_filter)

            # Получаем количество слов в активной очереди (готовых к повторению)
            now = datetime.utcnow()
            active_count = await collection.count_documents({
                **progress_filter,
                "next_review": {"$lte": now},
                "force_review": False,
                "is_learned": False
            })

            # Получаем количество слов в форсированной очереди
            forced_count = await collection.count_documents({
                **progress_filter,
                "force_review": True
            })

            # Получаем количество слов в пассивной очереди (ожидающих интервала)
            passive_count = await collection.count_documents({
                **progress_filter,
                "next_review": {"$gt": now},
                "force_review": False,
                "is_learned": False
            })

            # Вычисляем процент прогресса
            progress_percentage = (learned_count / total_words_available * 100) if total_words_available > 0 else 0

            return {
                "learned_words": learned_count,
                "total_words_in_progress": total_in_progress,
                "total_words_available": total_words_available,
                "active_words": active_count,
                "passive_words": passive_count,
                "forced_words": forced_count,
                "progress_percentage": round(progress_percentage, 1),
                "target_language": target_lang
            }

        except Exception as e:
            logger.error(LogCategory.SPACED_REP, f"Ошибка при получении статистики пользователя: {str(e)}")
            return {
                "learned_words": 0,
                "total_words_in_progress": 0,
                "total_words_available": 0,
                "active_words": 0,
                "passive_words": 0,
                "forced_words": 0,
                "progress_percentage": 0,
                "target_language": target_lang
            }

# Создаем экземпляр сервиса для импорта
spaced_repetition_service = SpacedRepetitionService()
