"""
Сервис кэширования для оптимизации производительности приложения.
"""

import asyncio
import time
from typing import Dict, Any, Optional, Callable
from functools import wraps
import json
import hashlib

class CacheService:
    """Простой in-memory кэш с TTL (Time To Live)."""
    
    def __init__(self):
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._cleanup_task = None
    
    def _start_cleanup_task(self):
        """Запускает фоновую задачу очистки устаревших записей."""
        if self._cleanup_task is None:
            try:
                self._cleanup_task = asyncio.create_task(self._cleanup_expired())
            except RuntimeError:
                # Event loop не запущен, задача будет создана позже
                pass
    
    async def _cleanup_expired(self):
        """Периодически очищает устаревшие записи из кэша."""
        while True:
            try:
                current_time = time.time()
                expired_keys = []
                
                for key, data in self._cache.items():
                    if data['expires_at'] < current_time:
                        expired_keys.append(key)
                
                for key in expired_keys:
                    del self._cache[key]
                

                
                # Очистка каждые 60 секунд
                await asyncio.sleep(60)
                
            except Exception as e:
                await asyncio.sleep(60)
    
    def _generate_key(self, prefix: str, *args, **kwargs) -> str:
        """Генерирует ключ кэша на основе префикса и параметров."""
        # Создаем строку из всех параметров
        params_str = json.dumps({
            'args': args,
            'kwargs': sorted(kwargs.items())
        }, sort_keys=True, default=str)
        
        # Создаем хэш для уникальности
        params_hash = hashlib.md5(params_str.encode()).hexdigest()[:8]
        
        return f"{prefix}:{params_hash}"
    
    async def get(self, key: str) -> Optional[Any]:
        """Получает значение из кэша."""
        # Запускаем задачу очистки при первом обращении
        self._ensure_cleanup_task()

        if key not in self._cache:
            return None

        data = self._cache[key]

        # Проверяем, не истекло ли время жизни
        if data['expires_at'] < time.time():
            del self._cache[key]
            return None

        return data['value']

    def _ensure_cleanup_task(self):
        """Обеспечивает запуск задачи очистки."""
        if self._cleanup_task is None:
            try:
                self._cleanup_task = asyncio.create_task(self._cleanup_expired())
            except RuntimeError:
                # Event loop не запущен
                pass
    
    async def set(self, key: str, value: Any, ttl: int = 300) -> None:
        """Сохраняет значение в кэш с указанным TTL (в секундах)."""
        # Запускаем задачу очистки при первом обращении
        self._ensure_cleanup_task()

        expires_at = time.time() + ttl
        self._cache[key] = {
            'value': value,
            'expires_at': expires_at
        }
    
    async def delete(self, key: str) -> None:
        """Удаляет значение из кэша."""
        if key in self._cache:
            del self._cache[key]
    
    async def clear(self) -> None:
        """Очищает весь кэш."""
        self._cache.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """Возвращает статистику кэша."""
        current_time = time.time()
        active_entries = sum(1 for data in self._cache.values() 
                           if data['expires_at'] > current_time)
        
        return {
            'total_entries': len(self._cache),
            'active_entries': active_entries,
            'expired_entries': len(self._cache) - active_entries
        }

# Глобальный экземпляр кэша
cache_service = CacheService()

def cached(ttl: int = 300, prefix: str = "default"):
    """
    Декоратор для кэширования результатов функций.
    
    Args:
        ttl: Время жизни кэша в секундах (по умолчанию 5 минут)
        prefix: Префикс для ключей кэша
    """
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Генерируем ключ кэша
            cache_key = cache_service._generate_key(prefix, *args, **kwargs)
            
            # Пытаемся получить из кэша
            cached_result = await cache_service.get(cache_key)
            if cached_result is not None:
                return cached_result

            # Если в кэше нет, выполняем функцию
            result = await func(*args, **kwargs)
            
            # Сохраняем результат в кэш
            if result is not None:
                await cache_service.set(cache_key, result, ttl)
            
            return result
        
        return wrapper
    return decorator

# Специализированные декораторы для разных типов данных
def cached_word(ttl: int = 600):  # 10 минут для слов
    """Кэширование данных слов."""
    return cached(ttl=ttl, prefix="word")

def cached_user_progress(ttl: int = 60):  # 1 минута для прогресса пользователя
    """Кэширование прогресса пользователя."""
    return cached(ttl=ttl, prefix="user_progress")

def cached_card(ttl: int = 300):  # 5 минут для карточек
    """Кэширование карточек."""
    return cached(ttl=ttl, prefix="card")
