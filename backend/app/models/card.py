from pydantic import BaseModel, Field, ConfigDict, field_validator
from pydantic_core import core_schema
from typing import List, Optional, Dict, Any, Union, Literal
from datetime import datetime
from enum import Enum
from bson import ObjectId

class PyObjectId(str):
    @classmethod
    def __get_pydantic_core_schema__(
        cls, _source_type: Any, _handler: Any
    ) -> core_schema.CoreSchema:
        return core_schema.json_or_python_schema(
            json_schema=core_schema.str_schema(pattern='^[0-9a-fA-F]{24}$'),
            python_schema=core_schema.union_schema([
                core_schema.is_instance_schema(str),
                core_schema.no_info_plain_validator_function(cls.validate)
            ]),
            serialization=core_schema.plain_serializer_function_ser_schema(
                lambda x: str(x)
            )
        )

    @classmethod
    def validate(cls, v):
        if not ObjectId.is_valid(v):
            raise ValueError("Invalid ObjectId")
        if isinstance(v, ObjectId):
            return str(v)
        return v

class Language(str, Enum):
    EN = "en"
    RU = "ru"
    ES = "es"
    FR = "fr"
    DE = "de"
    IT = "it"
    PT = "pt"
    ZH = "zh"
    JA = "ja"
    KO = "ko"
    AR = "ar"
    HI = "hi"
    TH = "th"
    VI = "vi"
    TR = "tr"
    PL = "pl"
    NL = "nl"
    SV = "sv"
    NO = "no"
    DA = "da"
    FI = "fi"
    CB = "cb"
    CEB = "ceb"  # Альтернативный код для Себуано (для совместимости с существующими данными)
    TL = "tl"
    ID = "id"
    MS = "ms"
    UK = "uk"
    KA = "ka"

class Word(BaseModel):
    # Основные поля
    word: str = Field(default="", alias="text")  # Поле text в базе соответствует word в модели
    language: Language
    
    # Дополнительные поля
    concept_id: Optional[str] = None
    translations: List[Dict[str, Any]] = []
    examples: List[Dict[str, Any]] = []
    tags: List[str] = []
    transcription: Optional[str] = None
    audio_url: Optional[str] = None
    image_url: Optional[str] = None
    level: Optional[str] = None
    priority: Optional[str] = None  # Приоритет для A0 уровня
    
    # Конфигурация модели
    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
        json_encoders={
            ObjectId: str,
            datetime: lambda v: v.isoformat()
        }
    )
    
    # Валидатор для обработки данных из базы
    @field_validator('*', mode='before')
    def set_defaults(cls, v, info):
        # Если поле не заполнено и у него есть значение по умолчанию, используем его
        if v is None and info.field_name in ['word', 'translations', 'examples', 'tags']:
            return cls.model_fields[info.field_name].default
        return v

class CardBase(BaseModel):
    front: Word
    back: Word
    tags: List[str] = []
    difficulty: float = 0.0
    last_reviewed: Optional[datetime] = None
    next_review: Optional[datetime] = None
    review_count: int = 0
    correct_answers: int = 0
    wrong_answers: int = 0
    is_active: bool = True
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

class CardCreate(CardBase):
    pass

class CardUpdate(BaseModel):
    front: Optional[Word] = None
    back: Optional[Word] = None
    tags: Optional[List[str]] = None
    difficulty: Optional[float] = None
    last_reviewed: Optional[datetime] = None
    next_review: Optional[datetime] = None
    is_active: Optional[bool] = None
    updated_at: datetime = Field(default_factory=datetime.utcnow)

class CardInDB(CardBase):
    id: PyObjectId = Field(alias="_id")
    user_id: PyObjectId
    
    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
        json_encoders={
            ObjectId: str,
            datetime: lambda v: v.isoformat()
        }
    )
    
    @field_validator('id', 'user_id', mode='before')
    def validate_ids(cls, v):
        if isinstance(v, str):
            if not ObjectId.is_valid(v):
                raise ValueError("Invalid ObjectId")
            return v
        elif isinstance(v, ObjectId):
            return str(v)
        return v

class Card(BaseModel):
    """Модель карточки, объединяющая слово на родном и изучаемом языке"""
    native_word: Word
    target_word: Word
    
    # Добавляем метод для создания карточки из двух слов
    @classmethod
    def from_words(cls, native_word: dict, target_word: dict) -> 'Card':
        """Создает карточку из двух слов (словарей)"""
        # Преобразуем словари в модели Word
        native_data = {**native_word}
        target_data = {**target_word}
        
        # Обрабатываем случай, если в данных есть _id
        if '_id' in native_data:
            native_data['id'] = str(native_data.pop('_id'))
        if '_id' in target_data:
            target_data['id'] = str(target_data.pop('_id'))
            
        # Создаем экземпляры Word
        native = Word.model_validate(native_data)
        target = Word.model_validate(target_data)
        
        # Создаем карточку
        return cls(native_word=native, target_word=target)
    
    # Конфигурация модели
    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
        json_encoders={
            ObjectId: str,
            datetime: lambda v: v.isoformat()
        }
    )
    
    # Метод для преобразования в словарь
    def to_dict(self) -> dict:
        """Преобразует карточку в словарь"""
        result = {
            'native_word': self.native_word.model_dump(by_alias=True, exclude_none=True),
            'target_word': self.target_word.model_dump(by_alias=True, exclude_none=True)
        }

        # Извлекаем priority из target_word на верхний уровень для A0
        if hasattr(self.target_word, 'priority') and self.target_word.priority:
            result['priority'] = self.target_word.priority

        return result
