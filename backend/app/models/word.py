from pydantic import BaseModel, Field, ConfigDict, field_validator
from pydantic_core import core_schema
from typing import List, Optional, Any, Dict, Union, Annotated
from datetime import datetime
from enum import Enum
from bson import ObjectId
from pydantic import Field, ConfigDict, field_validator

class PyObjectId(str):
    @classmethod
    def __get_pydantic_core_schema__(
        cls, _source_type: Any, _handler: Any
    ) -> core_schema.CoreSchema:
        return core_schema.json_or_python_schema(
            json_schema=core_schema.str_schema(pattern='^[0-9a-fA-F]{24}$'),
            python_schema=core_schema.union_schema([
                core_schema.is_instance_schema(str),
                core_schema.no_info_plain_validator_function(cls.validate)
            ]),
            serialization=core_schema.plain_serializer_function_ser_schema(
                lambda x: str(x)
            )
        )

    @classmethod
    def validate(cls, v):
        if not ObjectId.is_valid(v):
            raise ValueError("Invalid ObjectId")
        if isinstance(v, ObjectId):
            return str(v)
        return v

class WordLevel(str, Enum):
    A0 = "A0"  # Топ-100 самых базовых слов
    A1 = "A1"
    A2 = "A2"
    B1 = "B1"
    B2 = "B2"
    C1 = "C1"
    C2 = "C2"

class WordPriority(str, Enum):
    ULTRA_CORE = "ultra_core"  # Самые базовые слова A0 (15 слов)
    CORE = "core"              # Базовые слова A0 (35 слов)
    EXTENDED = "extended"      # Расширенные слова A0 (101 слово)



class Example(BaseModel):
    sentence: str
    correct_answers: List[str]
    word_info: Optional[str] = None

class WordBase(BaseModel):
    word: str
    level: WordLevel
    priority: Optional[WordPriority] = None  # Только для A0 уровня
    frequency: int = 1
    translations: List[str] = Field(default_factory=list)  # Делаем поле необязательным
    examples: List[Example] = Field(default_factory=list)
    tags: List[str] = Field(default_factory=list)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    model_config = ConfigDict(
        populate_by_name=True,
        json_encoders={
            datetime: lambda v: v.isoformat(),
            ObjectId: str
        },
        json_schema_extra={
            "example": {
                "word": "example",
                "level": "A1",
                "frequency": 1,
                "translations": ["пример"],
                "examples": [{"en": "This is an example", "ru": "Это пример"}],
                "tags": ["common"]
            }
        }
    )

class WordCreate(WordBase):
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "word": "example",
                "level": "A1",
                "frequency": 1,
                "translations": ["пример"],
                "examples": [
                    {
                        "sentence": "This is an example",
                        "correct_answers": ["example"],
                        "word_info": "Noun; Sing."
                    }
                ],
                "tags": ["common"]
            }
        }
    )

class Word(WordBase):
    id: PyObjectId = Field(alias="_id")
    
    @field_validator('id', mode='before')
    @classmethod
    def validate_id(cls, v):
        if isinstance(v, ObjectId):
            return str(v)
        return v

class WordInDB(WordBase):
    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
        json_encoders={
            ObjectId: str,
            datetime: lambda v: v.isoformat()
        }
    )
