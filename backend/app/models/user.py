from pydantic import BaseModel, EmailStr, Field, field_validator, ConfigDict
from typing import Optional, List, Dict, Any
from datetime import datetime
from bson import ObjectId

class PyObjectId(ObjectId):
    @classmethod
    def __get_pydantic_core_schema__(cls, _source_type, _handler):
        from pydantic_core import core_schema
        
        def validate(value: str) -> ObjectId:
            if not ObjectId.is_valid(value):
                raise ValueError("Invalid ObjectId")
            return ObjectId(value)
            
        return core_schema.no_info_plain_validator_function(
            function=validate,
            serialization=core_schema.to_string_ser_schema(),
        )

class UserBase(BaseModel):
    email: EmailStr
    username: Optional[str] = None
    native_language: str = "ru"  # Родной язык пользователя
    learning_languages: List[str] = []  # Все изучаемые языки (пустой массив для онбординга)
    settings: Dict[str, Any] = {
        "daily_goal": 20,
        "notifications_enabled": True,
        "difficulty_level": "beginner",
        "target_language": "",    # Будет установлен в онбординге
        "language_levels": {}     # Пустой объект для онбординга
    }

class UserCreate(UserBase):
    password: str

class UserUpdate(BaseModel):
    email: Optional[EmailStr] = None
    username: Optional[str] = None
    native_language: Optional[str] = None
    learning_languages: Optional[List[str]] = None
    settings: Optional[Dict[str, Any]] = None

class UserInDB(UserBase):
    id: PyObjectId = Field(alias="_id")
    hashed_password: str
    created_at: datetime
    updated_at: Optional[datetime] = None
    is_active: bool = True
    is_superuser: bool = False
    
    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
        json_schema_extra={
            "example": {
                "email": "<EMAIL>",
                "username": "johndoe",
                "native_language": "ru",
                "learning_languages": [],
                "settings": {
                    "daily_goal": 20,
                    "notifications_enabled": True,
                    "difficulty_level": "beginner",
                    "target_language": "en",
                    "language_levels": {
                        "en": "A0",
                        "es": "A1"
                    }
                },
                "is_active": True,
                "is_superuser": False
            }
        }
    )
    
    @field_validator('id', mode='before')
    def validate_id(cls, v):
        if isinstance(v, str):
            if not ObjectId.is_valid(v):
                raise ValueError("Invalid ObjectId")
            return v
        elif isinstance(v, ObjectId):
            return str(v)
        return v

class Token(BaseModel):
    access_token: str
    token_type: str
    user: dict

class TokenData(BaseModel):
    email: Optional[str] = None
