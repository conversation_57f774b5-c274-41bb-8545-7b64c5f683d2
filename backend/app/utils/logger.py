"""
Централизованная система логирования для бэкенда
Обеспечивает единообразное форматирование и категоризацию логов
"""

import time
from datetime import datetime
from enum import Enum
from typing import Any, Optional
import os


class LogLevel(Enum):
    DEBUG = "DEBUG"
    INFO = "INFO"
    SUCCESS = "SUCCESS"
    WARNING = "WARNING"
    ERROR = "ERROR"
    TIMING = "TIMING"


class LogCategory(Enum):
    SPACED_REP = "SPACED_REP"
    DATABASE = "DATABASE"
    API = "API"
    CACHE = "CACHE"
    AUTH = "AUTH"
    PERFORMANCE = "PERFORMANCE"
    SYSTEM = "SYSTEM"


# Эмодзи для разных типов логов
LOG_ICONS = {
    LogLevel.DEBUG: "🔍",
    LogLevel.INFO: "ℹ️",
    LogLevel.SUCCESS: "✅",
    LogLevel.WARNING: "⚠️",
    LogLevel.ERROR: "❌",
    LogLevel.TIMING: "⏱️"
}

CATEGORY_ICONS = {
    LogCategory.SPACED_REP: "🧠",
    LogCategory.DATABASE: "🗄️",
    LogCategory.API: "🌐",
    LogCategory.CACHE: "💾",
    LogCategory.AUTH: "🔐",
    LogCategory.PERFORMANCE: "⚡",
    LogCategory.SYSTEM: "🚀"
}


class Logger:
    def __init__(self):
        # В продакшене можно отключить логи через переменную окружения
        self.is_enabled = os.getenv("ENABLE_LOGS", "true").lower() == "true"
        
    def _format_time(self) -> str:
        """Форматирует время в читаемый вид"""
        now = datetime.now()
        return now.strftime("%Y-%m-%d %H:%M:%S")  # Формат: 2025-06-17 14:30:45
    
    def _format_user_id(self, user_id: str) -> str:
        """Форматирует user_id для логов (показываем только последние 8 символов)"""
        return str(user_id)[-8:] if user_id else "unknown"
    
    def _log(
        self,
        level: LogLevel,
        category: LogCategory,
        message: str,
        data: Optional[Any] = None,
        add_separator: bool = False
    ) -> None:
        """Основной метод логирования"""
        if not self.is_enabled:
            return
            
        time_str = self._format_time()
        icon = LOG_ICONS[level]
        category_icon = CATEGORY_ICONS[category]
        
        # Формат: YYYY-MM-DD HH:MM:SS LEVEL 🔥 🧠 Сообщение (данные)
        level_str = level.value.upper()

        if data is not None:
            if isinstance(data, dict) and len(data) == 1:
                # Если один элемент в data, показываем его значение
                key, value = next(iter(data.items()))
                data_str = f" ({value})"
            else:
                data_str = f" {data}"
            formatted_message = f"{time_str} {level_str} {icon} {category_icon} {message}{data_str}"
        else:
            formatted_message = f"{time_str} {level_str} {icon} {category_icon} {message}"

        print(formatted_message)

    # === МЕТОДЫ ДЛЯ SPACED REPETITION ===
    
    def spaced_rep_start(self, user_id: str, target_lang: str, preload: bool = False) -> None:
        """Начало поиска следующего слова"""
        mode = "предзагрузка" if preload else "основной запрос"

        # Добавляем предупреждение о возможных одновременных запросах
        if hasattr(self, '_active_requests'):
            if user_id in self._active_requests:
                self._log(
                    LogLevel.WARNING,
                    LogCategory.SPACED_REP,
                    f"⚠️ ОДНОВРЕМЕННЫЙ ЗАПРОС для {self._format_user_id(user_id)}! Уже выполняется: {self._active_requests[user_id]}, новый: {mode}"
                )
            else:
                self._active_requests[user_id] = mode
        else:
            self._active_requests = {user_id: mode}

        self._log(
            LogLevel.INFO,
            LogCategory.SPACED_REP,
            f"Поиск слова для {self._format_user_id(user_id)} ({mode})",
            {"language": target_lang},
            add_separator=True
        )
    
    def spaced_rep_found(self, word: str, source: str, duration_ms: float, user_id: str = None) -> None:
        """Слово найдено"""
        # Убираем пользователя из активных запросов
        if hasattr(self, '_active_requests') and user_id:
            self._active_requests.pop(user_id, None)

        # Добавляем индикатор медленного запроса
        speed_indicator = ""
        if duration_ms > 2000:
            speed_indicator = " 🐌 ОЧЕНЬ МЕДЛЕННО!"
        elif duration_ms > 1000:
            speed_indicator = " 🐌 МЕДЛЕННО"
        elif duration_ms > 500:
            speed_indicator = " ⚠️ медленно"

        self._log(
            LogLevel.SUCCESS,
            LogCategory.SPACED_REP,
            f'Найдено слово "{word}" из {source}{speed_indicator}',
            {"duration": f"{duration_ms:.1f}ms"}
        )
    
    def spaced_rep_not_found(self, duration_ms: float) -> None:
        """Слово не найдено"""
        self._log(
            LogLevel.WARNING,
            LogCategory.SPACED_REP,
            "Слова не найдены",
            {"duration": f"{duration_ms:.1f}ms"}
        )
    
    def spaced_rep_answer(self, user_id: str, word: str, is_correct: bool, new_level: int) -> None:
        """Обработка ответа пользователя"""
        result = "правильный" if is_correct else "неправильный"
        self._log(
            LogLevel.INFO,
            LogCategory.SPACED_REP,
            f'{self._format_user_id(user_id)}: "{word}" - {result}',
            {"new_level": new_level},
            add_separator=True
        )

    # === МЕТОДЫ ДЛЯ БАЗЫ ДАННЫХ ===
    
    def db_query_start(self, collection: str, operation: str) -> None:
        """Начало запроса к БД"""
        self._log(
            LogLevel.DEBUG,
            LogCategory.DATABASE,
            f"{operation} в {collection}"
        )
    
    def db_query_complete(self, collection: str, operation: str, duration_ms: float, count: int = None) -> None:
        """Завершение запроса к БД"""
        data = {"duration": f"{duration_ms:.1f}ms"}
        if count is not None:
            data["count"] = count
            
        self._log(
            LogLevel.SUCCESS,
            LogCategory.DATABASE,
            f"{operation} в {collection} завершен",
            data
        )

    # === МЕТОДЫ ДЛЯ API ===
    
    def api_request(self, method: str, endpoint: str, user_id: str = None) -> None:
        """Входящий API запрос"""
        user_info = f" от {self._format_user_id(user_id)}" if user_id else ""
        self._log(
            LogLevel.INFO,
            LogCategory.API,
            f"{method} {endpoint}{user_info}",
            add_separator=True
        )
    
    def api_response(self, endpoint: str, status: int, duration_ms: float) -> None:
        """Ответ API"""
        level = LogLevel.SUCCESS if 200 <= status < 300 else LogLevel.ERROR
        self._log(
            level,
            LogCategory.API,
            f"Ответ {endpoint} ({status})",
            {"duration": f"{duration_ms:.1f}ms"}
        )

    # === МЕТОДЫ ДЛЯ ПРОИЗВОДИТЕЛЬНОСТИ ===
    
    def perf_step(self, step_name: str, duration_ms: float) -> None:
        """Время выполнения шага"""
        self._log(
            LogLevel.TIMING,
            LogCategory.PERFORMANCE,
            f"{step_name}",
            {"duration": f"{duration_ms:.1f}ms"}
        )
    
    def perf_total(self, operation: str, duration_ms: float) -> None:
        """Общее время операции"""
        self._log(
            LogLevel.TIMING,
            LogCategory.PERFORMANCE,
            f"ИТОГО {operation}",
            {"duration": f"{duration_ms:.1f}ms"}
        )

    # === ОБЩИЕ МЕТОДЫ ===
    
    def debug(self, category: LogCategory, message: str, data: Any = None) -> None:
        self._log(LogLevel.DEBUG, category, message, data)
    
    def info(self, category: LogCategory, message: str, data: Any = None) -> None:
        self._log(LogLevel.INFO, category, message, data)
    
    def success(self, category: LogCategory, message: str, data: Any = None) -> None:
        self._log(LogLevel.SUCCESS, category, message, data)
    
    def warning(self, category: LogCategory, message: str, data: Any = None) -> None:
        self._log(LogLevel.WARNING, category, message, data)
    
    def error(self, category: LogCategory, message: str, data: Any = None) -> None:
        self._log(LogLevel.ERROR, category, message, data)


# Создаем единственный экземпляр логгера
logger = Logger()


# === КОНТЕКСТНЫЙ МЕНЕДЖЕР ДЛЯ ИЗМЕРЕНИЯ ВРЕМЕНИ ===

class PerfTimer:
    """Контекстный менеджер для измерения времени выполнения"""
    
    def __init__(self, operation_name: str, log_steps: bool = True):
        self.operation_name = operation_name
        self.log_steps = log_steps
        self.start_time = None
        self.step_times = []
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            total_duration = (time.time() - self.start_time) * 1000
            logger.perf_total(self.operation_name, total_duration)

    def step(self, step_name: str):
        """Отметить промежуточный шаг"""
        if self.log_steps and self.start_time:
            current_time = time.time()
            if self.step_times:
                step_duration = (current_time - self.step_times[-1]) * 1000
            else:
                step_duration = (current_time - self.start_time) * 1000

            logger.perf_step(step_name, step_duration)
            self.step_times.append(current_time)

    @property
    def elapsed_ms(self):
        """Получить время с начала операции в миллисекундах"""
        if self.start_time:
            return (time.time() - self.start_time) * 1000
        return 0


# === УДОБНЫЕ АЛИАСЫ ===

log = logger
perf = PerfTimer
