"""
Word Master Backend Application

Модуль содержит основную логику бэкенд-приложения для Word Master.
"""

__version__ = "0.1.0"

# Импортируем основные объекты для доступа из других модулей
from .database import db, Database, init_collections
from .config import get_settings, Settings

# Экспортируем основные объекты
__all__ = [
    'db',
    'Database',
    'init_collections',
    'get_settings',
    'Settings'
]
