import logging
from typing import Optional, Dict, Any
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from pymongo.errors import ConnectionFailure, OperationFailure

from .config import get_settings

# Настройка логирования
logger = logging.getLogger(__name__)
settings = get_settings()

class Database:
    client: Optional[AsyncIOMotorClient] = None
    words_db: Optional[AsyncIOMotorDatabase] = None
    users_db: Optional[AsyncIOMotorDatabase] = None
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    async def connect_to_db(cls) -> bool:
        """Подключиться к MongoDB и инициализировать соединения с базами.
        
        Returns:
            bool: True, если подключение успешно, иначе False
        """
        try:
            if cls.client is None:
                cls.client = AsyncIOMotorClient(
                    settings.mongodb_url,
                    serverSelectionTimeoutMS=10000
                )
                # Проверяем соединение
                await cls.client.admin.command('ping')

                # Подключаемся к обеим базам данных
                cls.words_db = cls.client[settings.words_database_name]
                cls.users_db = cls.client[settings.users_database_name]

                # Создаем коллекции, если они не существуют
                await cls._ensure_collections_exist()

                return True
            return True  # Уже подключено
        except Exception as e:
            print(f"❌ Ошибка подключения к MongoDB: {e}")
            return False

    @classmethod
    async def _ensure_collections_exist(cls):
        """Проверяем существование коллекций и создаем их при необходимости"""
        try:
            # Создаем коллекции и индексы без логирования
            if 'words' not in await cls.words_db.list_collection_names():
                await cls.words_db.create_collection('words')

            if 'users' not in await cls.users_db.list_collection_names():
                await cls.users_db.create_collection('users')

            # Создаем индексы
            await cls.words_db.words.create_index([("word", 1), ("language", 1)], unique=True, background=True)
            await cls.words_db.words.create_index("concept_id", background=True)
            await cls.users_db.users.create_index("email", unique=True, background=True)

        except Exception as e:
            print(f"❌ Ошибка создания коллекций: {e}")
            raise
    
    @classmethod
    async def close_db_connection(cls):
        """Закрыть соединение с базой данных"""
        try:
            if cls.client:
                cls.client.close()
                cls.client = None
                cls.words_db = None
                cls.users_db = None
        except Exception as e:
            print(f"❌ Ошибка закрытия соединения: {e}")
            raise
    
    @classmethod
    def get_db(cls, db_name: str = None):
        """Получить экземпляр базы данных
        
        Args:
            db_name (str, optional): Имя базы данных ('words' или 'users'). 
                                 Если не указано, возвращает словарь с обеими базами.
        
        Returns:
            Union[AsyncIOMotorDatabase, Dict[str, AsyncIOMotorDatabase]]: 
                Экземпляр базы данных или словарь с базами данных
        """
        if cls.client is None:
            raise RuntimeError("Database not connected. Call connect_to_db() first.")
            
        if db_name == 'words':
            return cls.words_db
        elif db_name == 'users':
            return cls.users_db
        else:
            return {'words': cls.words_db, 'users': cls.users_db}

    @classmethod
    def get_collection(cls, collection_name: str, db_name: str = 'words'):
        """Получить коллекцию из указанной базы данных
        
        Args:
            collection_name (str): Имя коллекции
            db_name (str, optional): Имя базы данных ('words' или 'users'). По умолчанию 'words'.
            
        Returns:
            MotorCollection: Коллекция из указанной базы данных
        """
        if cls.client is None:
            raise RuntimeError("Database not connected. Call connect_to_db() first.")
            
        db = cls.words_db if db_name == 'words' else cls.users_db
        return db[collection_name]

# Создаем экземпляр базы данных для использования в других модулях
db = Database()

# Функция для получения экземпляра базы данных
def get_database():
    """Возвращает экземпляр базы данных.
    
    Returns:
        Database: Экземпляр класса Database
    """
    return db

# Инициализация коллекций и индексов
async def init_collections():
    """Инициализация индексов для коллекций"""
    try:
        # Получаем экземпляры баз данных
        db = Database()
        words_db = db.get_db('words')
        users_db = db.get_db('users')

        if words_db is None or users_db is None:
            raise RuntimeError("Database connections not initialized")

        # Создаем коллекции без логирования
        if 'words' not in await words_db.list_collection_names():
            await words_db.create_collection('words')

        if 'users' not in await users_db.list_collection_names():
            await users_db.create_collection('users')

        if 'user_progress' not in await words_db.list_collection_names():
            await words_db.create_collection('user_progress')

        # Создаем все индексы без логирования
        await words_db.words.create_index([("word", 1), ("language", 1)], unique=True, background=True)
        await words_db.words.create_index("concept_id", background=True)
        await users_db.users.create_index("email", unique=True, background=True)
        await words_db.user_progress.create_index([("user_id", 1), ("word_id", 1)], unique=True, background=True)
        await words_db.user_progress.create_index([("user_id", 1), ("next_review", 1), ("force_review", 1)], background=True)
        await words_db.user_progress.create_index([("user_id", 1), ("is_learned", 1)], background=True)
        await words_db.user_progress.create_index([("user_id", 1), ("force_review", 1), ("next_review", 1)], background=True)

        return True

    except Exception as e:
        print(f"❌ Ошибка инициализации БД: {e}")
        raise
