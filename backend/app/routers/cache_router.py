"""
Роутер для управления кэшем
"""

from fastapi import APIRouter, HTTPException, status
from typing import Dict, Any

from ..services.cache_service import cache_service
from ..utils.logger import logger, LogCategory

# Создаем роутер
router = APIRouter(prefix="/cache", tags=["cache"])

@router.post("/clear", response_model=Dict[str, Any])
async def clear_all_cache():
    """
    Очистить весь кэш приложения.
    
    Очищает:
    - Кэш слов (word)
    - Кэш прогресса пользователей (user_progress) 
    - Кэш карточек (card)
    - Все остальные кэшированные данные
    
    Полезно при:
    - Изменении интервалов spaced repetition
    - Обновлении словарей
    - Отладке проблем с данными
    """
    try:
        # Получаем статистику до очистки
        stats_before = cache_service.get_stats()
        
        # Очищаем весь кэш
        await cache_service.clear()
        
        # Получаем статистику после очистки
        stats_after = cache_service.get_stats()
        
        logger.info(LogCategory.API, f"Весь кэш очищен. До: {stats_before['total_entries']} записей, После: {stats_after['total_entries']} записей")
        
        return {
            "success": True,
            "message": "Весь кэш успешно очищен",
            "stats_before": stats_before,
            "stats_after": stats_after,
            "cleared_entries": stats_before['total_entries']
        }
        
    except Exception as e:
        logger.error(LogCategory.API, f"Ошибка при очистке кэша: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to clear cache: {str(e)}"
        )

@router.get("/stats", response_model=Dict[str, Any])
async def get_cache_stats():
    """
    Получить статистику кэша.
    
    Показывает:
    - Общее количество записей
    - Количество активных записей
    - Количество устаревших записей
    """
    try:
        stats = cache_service.get_stats()
        
        return {
            "success": True,
            "stats": stats
        }
        
    except Exception as e:
        logger.error(LogCategory.API, f"Ошибка при получении статистики кэша: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get cache stats: {str(e)}"
        )
