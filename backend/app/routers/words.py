from fastapi import APIRouter, HTTPException, Depends, Query, Path, status, Request
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
from typing import List, Optional, Any, Dict, Union
from datetime import datetime
from bson import ObjectId
from pymongo.errors import DuplicateKeyError, OperationFailure
from pydantic import ConfigDict, Field, field_validator
import logging

from ..database import Database
from ..models.word import Word, WordCreate, WordLevel, WordInDB, PyObjectId

# Настройка логирования
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/words", tags=["words"])

# Кастомные исключения
class WordAlreadyExistsError(HTTPException):
    def __init__(self, word: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Word '{word}' already exists",
            headers={"X-Error": "Word already exists"}
        )

class WordNotFoundError(HTTPException):
    def __init__(self, word_id: str):
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Word with id {word_id} not found",
            headers={"X-Error": "Word not found"}
        )

# Зависимости
async def get_db():
    try:
        # Указываем, что нам нужна база данных word_master
        db = Database.get_db('words')
        return db
    except Exception as e:
        logger.error(f"Database connection error: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Database connection error"
        )

# Модели ответов
class WordResponse(Word):
    model_config = ConfigDict(
        json_encoders={ObjectId: str},
        from_attributes=True,
        populate_by_name=True
    )
    
    @field_validator('*', mode='before')
    def parse_objectid(cls, v):
        if isinstance(v, ObjectId):
            return str(v)
        return v

@router.post("/", 
            response_model=WordResponse, 
            status_code=status.HTTP_201_CREATED,
            responses={
                400: {"description": "Word already exists"},
                500: {"description": "Internal server error"}
            })
async def create_word(word: WordCreate, request: Request, db = Depends(get_db)):
    """
    Create a new word in the database.
    
    - **word**: The word to create (e.g., "apple")
    - **level**: The CEFR level (A0, A1, A2, B1, B2, C1, C2)
    - **translations**: List of translations
    - **examples**: List of example sentences (optional)
    - **tags**: List of tags (optional)
    """
    try:
        logger.info(f"Creating new word: {word.word}")
        
        # Преобразуем Pydantic модель в словарь
        word_dict = word.model_dump()
        current_time = datetime.utcnow()
        
        # Добавляем метаданные
        word_dict.update({
            "created_at": current_time,
            "updated_at": current_time
        })
        
        try:
            # Пытаемся вставить новое слово
            result = await db.words.insert_one(word_dict)
            
            # Получаем созданный документ
            created_word = await db.words.find_one({"_id": result.inserted_id})
            
            if not created_word:
                logger.error(f"Failed to retrieve created word: {word.word}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to retrieve created word"
                )
                
            logger.info(f"Successfully created word: {word.word} (ID: {result.inserted_id})")
            return WordResponse(**created_word)
            
        except DuplicateKeyError:
            logger.warning(f"Attempt to create duplicate word: {word.word}")
            raise WordAlreadyExistsError(word.word)
            
    except HTTPException:
        # Пробрасываем HTTP-исключения как есть
        raise
        
    except Exception as e:
        logger.error(f"Error creating word '{word.word}': {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while creating the word"
        )

@router.get("/", 
           response_model=List[WordResponse],
           response_model_exclude_none=True,
           summary="Get list of words",
           description="Retrieve a paginated list of words with optional filtering by level")
async def get_words(
    request: Request,
    level: Optional[WordLevel] = Query(
        None, 
        description="Filter words by CEFR level (A0, A1, A2, B1, B2, C1, C2)"
    ),
    limit: int = Query(
        10, 
        ge=1, 
        le=100,
        description="Number of items per page (1-100)"
    ),
    skip: int = Query(
        0, 
        ge=0,
        description="Number of items to skip for pagination"
    ),
    db = Depends(get_db)
):
    """
    Get a list of words with optional filtering by level.
    
    - **level**: Filter by CEFR level (A0-C2)
    - **limit**: Number of items per page (1-100)
    - **skip**: Number of items to skip (for pagination)
    """
    try:
        logger.info(f"Fetching words (level: {level}, limit: {limit}, skip: {skip})")
        
        # Строим запрос
        query = {}
        if level:
            query["level"] = level
            
        # Получаем общее количество документов для пагинации
        total = await db.words.count_documents(query)
        
        # Получаем слова с пагинацией
        cursor = db.words.find(query).sort("word", 1).skip(skip).limit(limit)
        words = await cursor.to_list(length=limit)
        
        # Добавляем заголовки для пагинации
        response_headers = {
            "X-Total-Count": str(total),
            "X-Page-Size": str(limit),
            "X-Page-Offset": str(skip),
            "X-Has-Next-Page": str(skip + limit < total)
        }
        
        # Возвращаем ответ с заголовками
        response = [WordResponse(**word) for word in words]
        return JSONResponse(
            content=jsonable_encoder(response),
            headers=response_headers
        )
        
    except OperationFailure as e:
        logger.error(f"Database operation failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Database operation failed"
        )
    except Exception as e:
        logger.error(f"Error fetching words: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while fetching words"
        )


        
    except HTTPException:
        raise  # Пробрасываем HTTP-исключения как есть
        
    except OperationFailure as e:
        logger.error(f"Database operation failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Database operation failed"
        )
        
    except Exception as e:
        logger.error(f"Error fetching word with ID {word_id}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while fetching the word"
        )

@router.get(
    "/{word_id}", 
    response_model=WordResponse,
    response_model_exclude_none=True,
    responses={
        404: {"description": "Word not found"},
        400: {"description": "Invalid word ID format"},
        500: {"description": "Internal server error"}
    },
    summary="Get word by ID",
    description="Retrieve a single word by its unique identifier"
)
async def get_word(
    word_id: str = Path(..., description="The ID of the word to retrieve"),
    db = Depends(get_db)
):
    """
    Get a single word by its ID.
    
    - **word_id**: The unique identifier of the word to retrieve
    """
    try:
        logger.info(f"Fetching word with ID: {word_id}")
        
        # Валидация ID
        if not ObjectId.is_valid(word_id):
            logger.warning(f"Invalid word ID format: {word_id}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid word ID format. Must be a valid ObjectId"
            )
            
        # Поиск слова
        word = await db.words.find_one({"_id": ObjectId(word_id)})
        
        if not word:
            logger.warning(f"Word not found: {word_id}")
            raise WordNotFoundError(word_id)
            
        logger.info(f"Successfully retrieved word: {word.get('word')} (ID: {word_id})")
        return WordResponse(**word)
        
    except HTTPException:
        raise  # Пробрасываем HTTP-исключения как есть
        
    except OperationFailure as e:
        logger.error(f"Database operation failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Database operation failed"
        )
        
    except Exception as e:
        logger.error(f"Error fetching word {word_id}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while fetching the word"
        )

@router.get(
    "/concept/{word_id}",
    response_model=WordResponse,
    response_model_exclude_none=True,
    responses={
        404: {"description": "Word not found for the given ID and language"},
        400: {"description": "Invalid ID format"},
        500: {"description": "Internal server error"}
    },
    summary="Get word by ID and language",
    description="Retrieve a word by its ID and language, searching by both _id and concept_id"
)
async def get_word_translation(
    word_id: str = Path(..., description="The ID or concept_id of the word to retrieve"),
    lang: str = Query(..., description="Language code (e.g., 'en', 'ru')"),
    db = Depends(get_db)
):
    """
    Get a word by its ID or concept_id and language.
    
    - **word_id**: The ID or concept_id of the word
    - **lang**: The language code (e.g., 'en', 'ru')
    """
    try:
        logger.info(f"Fetching word with ID: {word_id}, lang: {lang}")
        
        # Функция для нормализации слова из базы
        def normalize_word(word_doc):
            if not word_doc:
                return None
                
            # Добавляем обязательные поля, если их нет
            word_doc.setdefault('translations', [])
            word_doc.setdefault('examples', [])
            word_doc.setdefault('tags', [])
            
            # Преобразуем ObjectId в строку, если нужно
            if '_id' in word_doc and isinstance(word_doc['_id'], ObjectId):
                word_doc['_id'] = str(word_doc['_id'])
                
            return word_doc
        
        # Пытаемся найти слово по _id и языку
        if ObjectId.is_valid(word_id):
            word = await db.words.find_one({
                "_id": ObjectId(word_id),
                "language": lang.lower()
            })
            
            if word:
                logger.info(f"Found word by _id: {word.get('word')} (id: {word_id}, lang: {lang})")
                word = normalize_word(word)
                return WordResponse(**word)
            
            # Если не нашли по _id, ищем по concept_id
            word_by_concept = await db.words.find_one({
                "concept_id": word_id,
                "language": lang.lower()
            })
            
            if word_by_concept:
                logger.info(f"Found word by concept_id: {word_by_concept.get('word')} (concept_id: {word_id}, lang: {lang})")
                word_by_concept = normalize_word(word_by_concept)
                return WordResponse(**word_by_concept)
        else:
            # Если невалидный ObjectId, ищем только по concept_id
            word = await db.words.find_one({
                "concept_id": word_id,
                "language": lang.lower()
            })
            
            if word:
                logger.info(f"Found word by concept_id: {word.get('word')} (concept_id: {word_id}, lang: {lang})")
                word = normalize_word(word)
                return WordResponse(**word)
        
        # Если ничего не нашли, возвращаем 404
        logger.warning(f"Word not found for ID: {word_id}, lang: {lang}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Word with ID {word_id} and language {lang} not found"
        )
        
    except HTTPException:
        raise  # Пробрасываем HTTP-исключения как есть
        
    except OperationFailure as e:
        logger.error(f"Database operation failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Database operation failed"
        )

    except Exception as e:
        logger.error(f"Error fetching word with ID {word_id}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while fetching the word"
        )

@router.get(
    "/concept/{concept_id}/full",
    response_model=Dict[str, Any],
    responses={
        404: {"description": "Concept not found"},
        500: {"description": "Internal server error"}
    },
    summary="Get concept with all its words",
    description="Retrieve a concept description with all its translations across languages"
)
async def get_concept_with_words(
    concept_id: str = Path(..., description="The concept_id to retrieve"),
    db = Depends(get_db)
):
    """
    Get a concept with all its words across languages.

    - **concept_id**: The unique identifier of the concept

    Returns concept description and all word translations.
    """
    try:
        logger.info(f"Fetching concept with ID: {concept_id}")

        # Получаем все слова концепта
        words_cursor = db.words.find({"concept_id": concept_id})
        words = await words_cursor.to_list(length=None)

        if not words:
            logger.warning(f"No words found for concept: {concept_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Concept {concept_id} not found"
            )

        # Пытаемся получить описание концепта из коллекции concepts
        concept_description = None
        try:
            concepts_collection = db.concepts
            concept_doc = await concepts_collection.find_one({"_id": concept_id})
            if concept_doc:
                concept_description = {
                    "concept_id": concept_doc["_id"],
                    "concept_name": concept_doc.get("concept_name"),
                    "category": concept_doc.get("category"),
                    "description": concept_doc.get("description", {}),
                    "semantic_field": concept_doc.get("semantic_field"),
                    "usage_context": concept_doc.get("usage_context"),
                    "translation_notes": concept_doc.get("translation_notes", {})
                }
        except Exception as e:
            logger.warning(f"Could not fetch concept description: {e}")

        # Группируем слова по языкам
        words_by_language = {}
        for word in words:
            lang = word["language"]
            words_by_language[lang] = {
                "word": word["word"],
                "examples": word.get("examples", []),
                "part_of_speech": word.get("part_of_speech"),
                "level": word.get("level"),
                "priority": word.get("priority")
            }

        result = {
            "concept_id": concept_id,
            "concept_description": concept_description,
            "words": words_by_language,
            "languages_count": len(words_by_language),
            "available_languages": list(words_by_language.keys())
        }

        logger.info(f"Successfully retrieved concept {concept_id} with {len(words)} words")
        return result

    except HTTPException:
        raise

    except Exception as e:
        logger.error(f"Error fetching concept {concept_id}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while fetching the concept"
        )
