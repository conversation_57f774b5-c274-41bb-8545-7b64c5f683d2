from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2Password<PERSON>earer, OAuth2PasswordRequestForm
from jose import JWTError, jwt
from passlib.context import Crypt<PERSON>ontext
from pydantic import BaseModel, EmailStr
from bson import ObjectId

from ..database import db
from ..config import Settings

settings = Settings()
from ..models.user import UserInDB, UserCreate, UserUpdate

router = APIRouter(prefix="", tags=["users"])

# Настройки для хеширования паролей
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

async def get_user(email: str) -> Optional[UserInDB]:
    user_data = await db.users_db.users.find_one({"email": email})
    if user_data:
        # Преобразуем ObjectId в строку и добавляем id
        user_data["id"] = str(user_data["_id"])
        # Удаляем _id, так как у нас есть алиас id
        user_data.pop("_id", None)
        # Убедимся, что все обязательные поля присутствуют
        if "created_at" not in user_data:
            user_data["created_at"] = datetime.utcnow()
        if "updated_at" not in user_data:
            user_data["updated_at"] = datetime.utcnow()
        return UserInDB(**user_data)
    return None

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.secret_key, algorithm=settings.algorithm)
    return encoded_jwt

async def get_current_user(token: str = Depends(OAuth2PasswordBearer(tokenUrl="api/token"))):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, settings.secret_key, algorithms=[settings.algorithm])
        email: str = payload.get("sub")
        if email is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    
    user = await get_user(email)
    if user is None:
        raise credentials_exception
    return user

async def get_current_active_user(current_user: UserInDB = Depends(get_current_user)):
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user

@router.post("/register", response_model=UserInDB, status_code=status.HTTP_201_CREATED)
async def register(user: UserCreate):
    # Получаем коллекцию пользователей из базы user_db
    users_collection = db.users_db.users
    
    # Проверяем, существует ли пользователь
    existing_user = await users_collection.find_one({"email": user.email})
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )
    
    # Хешируем пароль
    hashed_password = get_password_hash(user.password)
    user_data = user.model_dump(exclude={"password"})
    
    # Добавляем дополнительные поля
    user_data.update({
        "hashed_password": hashed_password,
        "is_active": True,
        "is_superuser": False,
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow()
    })
    
    # Сохраняем пользователя
    result = await users_collection.insert_one(user_data)
    created_user = await users_collection.find_one({"_id": result.inserted_id})
    
    # Преобразуем ObjectId в строку для ответа
    created_user["id"] = str(created_user["_id"])
    return UserInDB(**created_user)

@router.post("/token")
async def login(form_data: OAuth2PasswordRequestForm = Depends()):
    user = await get_user(form_data.username)
    if not user or not verify_password(form_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Обновляем время последнего входа
    await db.users_db.users.update_one(
        {"email": form_data.username},
        {"$set": {"last_login": datetime.utcnow()}}
    )
    
    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    access_token = create_access_token(
        data={"sub": user.email}, expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user_id": str(user.id),
        "email": user.email
    }
    return {"access_token": access_token, "token_type": "bearer"}

@router.get("/users/me", response_model=UserInDB)
async def read_users_me(current_user: UserInDB = Depends(get_current_active_user)):
    return current_user

@router.patch("/users/me", response_model=UserInDB)
async def update_user_me(
    update_data: UserUpdate,
    current_user: UserInDB = Depends(get_current_active_user)
):
    update_data = update_data.model_dump(exclude_unset=True)
    
    # Если обновляется пароль, хешируем его
    if "password" in update_data:
        update_data["hashed_password"] = get_password_hash(update_data.pop("password"))
    
    # Добавляем время обновления
    update_data["updated_at"] = datetime.utcnow()
    
    # Обновляем пользователя в базе данных
    result = await db.users_db.users.update_one(
        {"email": current_user.email},
        {"$set": update_data}
    )
    
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Возвращаем обновленные данные пользователя
    updated_user = await db.users_db.users.find_one({"email": current_user.email})
    updated_user["id"] = str(updated_user["_id"])
    return UserInDB(**updated_user)