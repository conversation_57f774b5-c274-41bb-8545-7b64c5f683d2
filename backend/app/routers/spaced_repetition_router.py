"""
Роутер для работы с интервальным повторением
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.responses import JSONResponse
from typing import Dict, Any, Optional
from bson import ObjectId
from datetime import datetime, timedelta, date
import time

from ..database import Database, db
from ..models.card import Card, Word, Language
from ..services.spaced_repetition import SpacedRepetitionService
from ..routers.users import get_current_user
from ..utils.logger import logger, LogCategory

# Создаем роутер
router = APIRouter(prefix="/spaced", tags=["spaced_repetition"])

# Инициализируем сервис интервального повторения
spaced_repetition_service = SpacedRepetitionService()

async def get_db():
    """Зависимость для получения экземпляра базы данных"""
    try:
        db_instance = Database()
        if not await db_instance.connect_to_db():
            raise Exception("Could not connect to database")
        return db_instance
    except Exception as e:
        logger.error(f"Database connection error: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Database connection error: {str(e)}"
        )

@router.get("/next", response_model=Dict[str, Any])
async def get_next_card(
    user_id: str = Query(..., description="ID пользователя"),
    native_lang: Language = Query(
        Language.RU,
        description="Родной язык пользователя (по умолчанию 'ru')"
    ),
    target_lang: Language = Query(
        Language.EN,
        description="Изучаемый язык (по умолчанию 'en')"
    ),
    preload: bool = Query(False, description="Предзагрузка следующей карточки"),
    exclude_card_id: Optional[str] = Query(None, description="ID карточки для исключения из выборки"),
    db = Depends(get_db)
):
    start_time = time.time()
    logger.api_request("GET", "/spaced/next", user_id)
    """
    Получить следующую карточку для повторения с учетом прогресса пользователя.
    
    - **user_id**: ID пользователя
    - **native_lang**: Родной язык пользователя (по умолчанию 'ru')
    - **target_lang**: Изучаемый язык (по умолчанию 'en')
    """
    try:
        # Преобразуем user_id в ObjectId
        try:
            user_id_obj = ObjectId(user_id)
        except Exception as e:
            logger.api_response("/spaced/next", 400, (time.time() - start_time) * 1000)
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid user_id format: {str(e)}"
            )

        # Преобразуем exclude_card_id в ObjectId если он указан
        exclude_card_id_obj = None
        if exclude_card_id:
            try:
                exclude_card_id_obj = ObjectId(exclude_card_id)
            except Exception as e:
                logger.warning(LogCategory.API, f"Неверный формат exclude_card_id: {exclude_card_id}")

        # Получаем следующую карточку с учетом прогресса и целевого языка
        card = await spaced_repetition_service.get_next_word(user_id_obj, target_lang=target_lang, skip_forced=preload, preload=preload, exclude_card_id=exclude_card_id_obj)
        
        if not card:
            duration_ms = (time.time() - start_time) * 1000
            logger.api_response("/spaced/next", 404, duration_ms)
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No cards available for review"
            )

        duration_ms = (time.time() - start_time) * 1000
        logger.api_response("/spaced/next", 200, duration_ms)
        return card

    except HTTPException:
        raise
    except Exception as e:
        duration_ms = (time.time() - start_time) * 1000
        logger.api_response("/spaced/next", 500, duration_ms)
        logger.error(LogCategory.API, f"Error getting next card: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get next card: {str(e)}"
        )

@router.get("/preload", response_model=Dict[str, Any])
async def preload_cards(
    user_id: str = Query(..., description="ID пользователя"),
    target_lang: Language = Query(
        Language.EN,
        description="Изучаемый язык (по умолчанию 'en')"
    ),
    count: int = Query(3, description="Количество карточек для предзагрузки", ge=1, le=10),
    db = Depends(get_db)
):
    """
    Предзагрузить несколько карточек для оптимизации производительности.

    - **user_id**: ID пользователя
    - **target_lang**: Изучаемый язык (по умолчанию 'en')
    - **count**: Количество карточек для предзагрузки (1-10)
    """
    try:
        # Преобразуем user_id в ObjectId
        try:
            user_id_obj = ObjectId(user_id)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid user_id format: {str(e)}"
            )

        # Получаем несколько карточек
        cards = []
        for i in range(count):
            card = await spaced_repetition_service.get_next_word(user_id_obj, target_lang=target_lang)
            if card:
                cards.append(card)
            else:
                break  # Больше карточек нет
        return {
            "cards": cards,
            "count": len(cards),
            "requested_count": count
        }

    except Exception as e:
        logger.error(f"Error preloading cards: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to preload cards: {str(e)}"
        )

@router.post("/{card_id}/response", response_model=Dict[str, Any])
async def submit_card_response(
    card_id: str,
    request: Dict[str, Any],
    db = Depends(get_db)
):
    """
    Отправить ответ пользователя по карточке.
    
    - **card_id**: ID карточки
    - **user_id**: ID пользователя
    - **is_correct**: Правильный ли ответ
    - **response_time**: Время ответа в секундах
    """
    try:
        # Извлекаем данные из запроса
        user_id = request.get("user_id")
        is_correct = request.get("is_correct", False)
        response_time = request.get("response_time", 0)
        
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="user_id is required"
            )
            
        # Преобразуем ID в ObjectId
        user_id_obj = ObjectId(user_id)
        card_id_obj = ObjectId(card_id)
        
        # Обрабатываем ответ пользователя
        result = await spaced_repetition_service.process_answer(
            user_id=user_id_obj,
            word_id=card_id_obj,
            is_correct=is_correct
        )
        
        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Card or user progress not found"
            )
        
        def convert_objectid(obj):
            """Рекурсивно преобразует ObjectId и даты в строки"""
            try:
                if obj is None:
                    return None
                    
                # Если это ObjectId
                if hasattr(obj, 'is_valid') and hasattr(obj, 'binary') and callable(obj.is_valid) and callable(obj.binary):
                    return str(obj)
                    
                # Если это словарь
                if isinstance(obj, dict):
                    return {k: convert_objectid(v) for k, v in obj.items()}
                
                # Если это список или кортеж
                if isinstance(obj, (list, tuple)):
                    return [convert_objectid(item) for item in obj]
                    
                # Если это дата
                if isinstance(obj, (datetime, date)):
                    return obj.isoformat()
                    
                return obj
                
            except Exception as e:
                logger.error(f"Ошибка при конвертации ObjectId: {str(e)}")
                return str(obj)  # В случае ошибки возвращаем строковое представление
                return str(obj)  # В крайнем случае преобразуем в строку
        
        try:
            # Преобразуем ObjectId в строки для сериализации
            result = convert_objectid(result)
        
            # Определяем is_new на основе логики: если слово выучено или имеет ответы, то оно не новое
            # ВАЖНО: Проверяем состояние ДО обработки текущего ответа
            is_new_value = (
                result.get("interval_level", -1) == -1 and
                result.get("correct_answers", 0) <= 1 and  # <= 1 потому что мы уже увеличили счетчик
                result.get("incorrect_answers", 0) == 0 and
                not result.get("is_learned", False)
            )

            # Формируем ответ
            response_data = {
                "success": True,
                "is_correct": is_correct,
                "card_id": str(card_id_obj),
                "user_id": str(user_id_obj),
                "response_time": response_time,
                "next_review": result.get("next_review"),
                "interval_level": result.get("interval_level"),
                "is_learned": result.get("is_learned", False),
                "is_new": is_new_value,
                "correct_answers": result.get("correct_answers", 0),
                "incorrect_answers": result.get("incorrect_answers", 0)
            }
        
            return response_data
            
        except Exception as e:
            logger.error(LogCategory.API, f"Ошибка при формировании ответа: {e}")
            # Возвращаем минимальный ответ в случае ошибки
            return {
                "success": False,
                "error": "Ошибка при обработке ответа",
                "details": str(e)
            }
        
    except HTTPException as he:
        raise he
    except Exception as e:
        logger.error(LogCategory.API, f"Error processing card response: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process card response: {str(e)}"
        )

@router.get("/api/user/statistics")
async def get_user_statistics(
    target_language: Optional[str] = Query(None, description="Target language (e.g., 'en', 'ru')"),
    current_user: dict = Depends(get_current_user)
):
    """Получить статистику пользователя по изучению слов.

    Args:
        target_language: Целевой язык для фильтрации статистики
        current_user: Текущий пользователь

    Returns:
        Статистика пользователя
    """
    try:
        user_id = str(current_user.id)
        # Получаем статистику от сервиса
        statistics = await spaced_repetition_service.get_user_statistics(
            user_id=user_id,
            target_lang=target_language
        )

        return statistics

    except Exception as e:
        logger.error(LogCategory.API, f"Ошибка при получении статистики: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting user statistics: {str(e)}"
        )

@router.delete("/debug/user/{user_id}/progress")
async def clear_user_progress(user_id: str):
    """
    🧹 ОЧИСТКА ПРОГРЕССА ПОЛЬЗОВАТЕЛЯ (только для тестирования)

    Удаляет весь прогресс указанного пользователя из базы данных.
    Используется для обеспечения чистого состояния при тестировании.

    Args:
        user_id: ID пользователя, чей прогресс нужно очистить

    Returns:
        Результат операции очистки

    ⚠️ ВНИМАНИЕ: Этот endpoint предназначен только для тестирования!
    """
    try:
        logger.info(LogCategory.API, f"🧹 Начинаем очистку прогресса для пользователя {user_id}")

        # Преобразуем user_id в ObjectId
        user_id_obj = ObjectId(user_id)

        # Получаем коллекцию user_progress
        collection = db.client.word_master.user_progress

        # Проверяем, есть ли записи до удаления
        count_before = await collection.count_documents({"user_id": user_id_obj})
        logger.debug(LogCategory.API, f"🔍 Найдено записей до удаления: {count_before}")

        if count_before > 0:
            # Удаляем записи
            result = await collection.delete_many({"user_id": user_id_obj})

            # Проверяем, что удаление прошло успешно
            count_after = await collection.count_documents({"user_id": user_id_obj})

            logger.info(LogCategory.API, f"🗑️ Удалено записей: {result.deleted_count}")
            logger.debug(LogCategory.API, f"🔍 Осталось записей после удаления: {count_after}")

            if count_after == 0:
                logger.success(LogCategory.API, f"✅ Прогресс пользователя {user_id} успешно очищен")
                return {
                    "success": True,
                    "message": f"Прогресс пользователя {user_id} успешно очищен",
                    "deleted_count": result.deleted_count,
                    "user_id": user_id
                }
            else:
                logger.warning(LogCategory.API, f"⚠️ Не удалось полностью очистить прогресс пользователя {user_id}")
                return {
                    "success": False,
                    "message": f"Не удалось полностью очистить прогресс пользователя {user_id}",
                    "deleted_count": result.deleted_count,
                    "remaining_count": count_after,
                    "user_id": user_id
                }
        else:
            logger.info(LogCategory.API, f"✅ Нет записей для удаления для пользователя {user_id}")
            return {
                "success": True,
                "message": f"Нет записей для удаления для пользователя {user_id}",
                "deleted_count": 0,
                "user_id": user_id
            }

    except Exception as e:
        logger.error(LogCategory.API, f"❌ Ошибка при очистке прогресса пользователя {user_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to clear user progress: {str(e)}"
        )
