from fastapi import APIRouter, HTTPException, Depends, Query, status, Request
from fastapi.responses import JSONResponse
from typing import List, Optional, Dict, Any
from bson import ObjectId
from pymongo import ReturnDocument
import logging

from ..database import Database
from ..models.card import Card, Word, Language

# Настройка логирования
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/cards", tags=["cards"])

# Зависимости
async def get_db():
    try:
        # Получаем экземпляр класса Database
        db_instance = Database()
        # Проверяем подключение к базе данных
        if not await db_instance.connect_to_db():
            raise Exception("Could not connect to database")
        # Возвращаем экземпляр класса Database для доступа к методам
        return db_instance
    except Exception as e:
        logger.error(f"Database connection error: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Database connection error: {str(e)}"
        )

@router.get("/random", response_model=dict)
async def get_random_card(
    native_lang: Language = Query(
        Language.RU, 
        description="Родной язык пользователя (по умолчанию 'ru')"
    ),
    target_lang: Language = Query(
        Language.EN, 
        description="Изучаемый язык (по умолчанию 'en')"
    ),
    level: Optional[str] = Query(
        None, 
        description="Уровень сложности (опционально)"
    ),
    db = Depends(get_db)
):
    """
    Получить случайную карточку для изучения.
    
    - **native_lang**: Родной язык пользователя (по умолчанию 'ru')
    - **target_lang**: Изучаемый язык (по умолчанию 'en')
    - **level**: Уровень сложности (опционально)
    """
    try:
        # Получаем коллекцию words.words
        words_collection = db.get_db("words").words
        
        # Логируем параметры запроса
        logger.info(f"Fetching random card with params: native_lang={native_lang}, target_lang={target_lang}, level={level}")
        
        # Получаем список доступных уровней сложности для отладки
        pipeline_levels = [
            {"$match": {"language": target_lang.value}},
            {"$group": {"_id": "$level"}},
            {"$project": {"level": "$_id", "_id": 0}}
        ]
        levels = await words_collection.aggregate(pipeline_levels).to_list(length=100)
        logger.info(f"Available levels in DB: {[l.get('level') for l in levels]}")
        
        # Создаем пайплайн для получения случайного слова
        pipeline = [
            # Фильтруем по языку и уровню (если указан)
            {"$match": {"language": target_lang.value}}
        ]
        
        if level:
            pipeline[0]["$match"]["level"] = level

            # Специальная логика для A0 уровня - учитываем приоритеты
            if level == "A0":
                # Для демонстрации пока показываем все A0 слова
                # В реальном приложении здесь будет логика CORE/EXTENDED
                pass

        # Добавляем этап выборки случайного документа
        pipeline.append({"$sample": {"size": 1}})
        
        # Выполняем агрегацию
        cursor = words_collection.aggregate(pipeline)
        target_word = await cursor.to_list(length=1)
        
        if not target_word:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No words found for language {target_lang.value}"
            )
            
        target_word = target_word[0]  # Извлекаем первый (и единственный) документ из списка
            
        # Получаем перевод на родной язык
        native_word = await words_collection.find_one({
            "concept_id": target_word["concept_id"],
            "language": native_lang.value
        })
        
        if not native_word:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Translation not found for concept_id: {target_word['concept_id']}"
            )
        
        # Создаем карточку из слов и преобразуем в словарь
        card = Card.from_words(native_word, target_word)
        return card.to_dict()
        
    except HTTPException as he:
        raise he
    except Exception as e:
        logger.error(f"Error getting random card: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.get("/{concept_id}", response_model=dict)
async def get_card_by_concept_id(
    concept_id: str,
    native_lang: Language = Query(
        Language.RU, 
        description="Родной язык пользователя (по умолчанию 'ru')"
    ),
    target_lang: Language = Query(
        Language.EN, 
        description="Изучаемый язык (по умолчанию 'en')"
    ),
    db = Depends(get_db)
):
    """
    Получить карточку по concept_id.
    
    - **concept_id**: Идентификатор концепта (слова)
    - **native_lang**: Родной язык пользователя (по умолчанию 'ru')
    - **target_lang**: Изучаемый язык (по умолчанию 'en')
    """
    try:
        # Получаем коллекцию words.words
        words_collection = db.get_db("words").words
        
        # Получаем слово на целевом языке
        target_word = await words_collection.find_one({
            "concept_id": concept_id,
            "language": target_lang.value
        })
        
        if not target_word:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Word not found for concept_id: {concept_id} and language: {target_lang}"
            )
        
        # Получаем слово на родном языке
        native_word = await words_collection.find_one({
            "concept_id": concept_id,
            "language": native_lang.value
        })
        
        if not native_word:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Translation not found for concept_id: {concept_id} and language: {native_lang}"
            )
        
        # Создаем карточку из слов и преобразуем в словарь
        card = Card.from_words(native_word, target_word)
        return card.to_dict()
        
    except HTTPException as he:
        raise he
    except Exception as e:
        logger.error(f"Error getting card by concept_id: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )
