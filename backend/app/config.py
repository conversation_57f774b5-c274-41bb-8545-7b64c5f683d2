import os
from functools import lru_cache
from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import Field, field_validator
from dotenv import load_dotenv
from typing import Optional

# Загружаем переменные окружения из файла .env
load_dotenv()

class Settings(BaseSettings):
    # Приложение
    app_name: str = "Word Master API"
    debug: bool = Field(default=False, env='DEBUG')
    environment: str = Field(default="development", env='ENVIRONMENT')
    api_prefix: str = Field(default="/api", env='API_PREFIX')
    
    # База данных
    mongodb_url: str = Field(default="mongodb://localhost:27017", env='MONGODB_URL')
    words_database_name: str = Field(default="words", env='WORDS_DATABASE_NAME')
    users_database_name: str = Field(default="users", env='USERS_DATABASE_NAME')
    
    # JWT
    secret_key: str = Field(default="your-secret-key-here", env='SECRET_KEY')
    algorithm: str = Field(default="HS256", env='ALGORITHM')
    access_token_expire_minutes: int = Field(default=1440, env='ACCESS_TOKEN_EXPIRE_MINUTES')
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding='utf-8',
        extra='ignore'
    )
    
    @field_validator('debug', mode='before')
    @classmethod
    def parse_debug(cls, v):
        if isinstance(v, str):
            return v.lower() in ("true", "1", "t")
        return bool(v)

@lru_cache()
def get_settings() -> Settings:
    return Settings()
