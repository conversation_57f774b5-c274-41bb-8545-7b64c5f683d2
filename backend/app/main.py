from fastapi import Fast<PERSON><PERSON>, Request, HTTPException, status, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
import logging
from datetime import datetime
from typing import Any, Dict

from .database import db, Database
import traceback

from .config import get_settings
from .database import Database, init_collections
from .routers import users_router, words_router, cards_router, spaced_repetition_router, cache_router
from .utils.logger import logger, LogCategory
import uvicorn

# Настройка логирования - убираем избыточные логи
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)

# Убираем избыточные логи uvicorn
logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
logging.getLogger("uvicorn.error").setLevel(logging.WARNING)

# Используем стандартный logger только для системных ошибок
std_logger = logging.getLogger(__name__)



settings = get_settings()

app = FastAPI(
    title=settings.app_name,
    debug=settings.debug,
    openapi_url=f"{settings.api_prefix}/openapi.json",
    docs_url=f"{settings.api_prefix}/docs",
    redoc_url=f"{settings.api_prefix}/redoc"
)

# Настройка CORS с более широкими разрешениями для отладки
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Разрешаем все источники
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allow_headers=["*"],
    expose_headers=["*"],
    max_age=600,  # Кешировать предварительные запросы на 10 минут
)

# Middleware для логирования CORS
@app.middleware("http")
async def log_cors(request: Request, call_next):
    # Получаем заголовок Origin
    origin = request.headers.get('origin')
    
    # Обрабатываем OPTIONS запрос (preflight)
    if request.method == "OPTIONS":
        response = JSONResponse(content={"status": "preflight"}, status_code=200)
    else:
        # Пропускаем обычный запрос дальше
        response = await call_next(request)

    # Добавляем CORS заголовки
    response.headers["Access-Control-Allow-Origin"] = origin or "*"
    response.headers["Access-Control-Allow-Methods"] = "*"
    response.headers["Access-Control-Allow-Headers"] = "*"
    response.headers["Access-Control-Allow-Credentials"] = "true"

    return response

# Глобальный обработчик исключений
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    # Логируем ошибку
    std_logger.error(f"Unhandled exception: {str(exc)}\n{traceback.format_exc()}")
    
    # Создаем ответ с ошибкой
    response = JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )
    
    # Добавляем CORS заголовки
    origin = request.headers.get('origin')
    if origin:
        response.headers["Access-Control-Allow-Origin"] = origin
        response.headers["Access-Control-Allow-Methods"] = "*"
        response.headers["Access-Control-Allow-Headers"] = "*"
        response.headers["Access-Control-Allow-Credentials"] = "true"
    
    return response

# Middleware для логирования запросов
@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = datetime.utcnow()
    try:
        response = await call_next(request)
        process_time = (datetime.utcnow() - start_time).total_seconds() * 1000
        
        # Логируем только очень медленные запросы (>2000ms)
        if process_time > 2000:
            logger.warning(LogCategory.API, f"🐌 Очень медленный запрос: {request.method} {request.url.path} ({process_time:.0f}ms)")
        
        return response
        
    except Exception as e:
        # Логируем ошибку и пробрасываем её дальше (обработается глобальным обработчиком)
        std_logger.error(f"Request error: {str(e)}\n{traceback.format_exc()}")
        raise

# Подключение к БД при старте приложения
@app.on_event("startup")
async def startup_db_client():
    try:
        logger.info(LogCategory.SYSTEM, "🚀 Запуск приложения...")
        connected = await Database.connect_to_db()
        if connected:
            await init_collections()

            # Проверяем соединение с базами данных
            db = Database()
            words_db = db.get_db('words')
            users_db = db.get_db('users')

            if words_db is None or users_db is None:
                raise RuntimeError("Failed to access databases")

            logger.info(LogCategory.SYSTEM, f"✅ MongoDB подключена: {words_db.name} (слова), {users_db.name} (пользователи)")
            logger.info(LogCategory.SYSTEM, "🎉 Приложение готово к работе!")
        else:
            raise RuntimeError("Failed to connect to MongoDB")
    except Exception as e:
        logger.error(LogCategory.SYSTEM, f"❌ Ошибка подключения к БД: {e}")
        raise

# Закрытие соединения с БД при остановке приложения
@app.on_event("shutdown")
async def shutdown_db_client():
    await Database.close_db_connection()
    logger.info(LogCategory.SYSTEM, "👋 MongoDB отключена")

# Временный эндпоинт для отладки
@app.get("/api/debug/routes")
async def debug_routes():
    routes = []
    for route in app.routes:
        routes.append({
            "path": route.path,
            "name": getattr(route, "name", ""),
            "methods": getattr(route, "methods", [])
        })
    return {"routes": routes}

# Регистрируем роутеры
app.include_router(users_router, prefix="/api")
app.include_router(words_router, prefix="/api")
app.include_router(cards_router, prefix="/api")
app.include_router(spaced_repetition_router, prefix="/api")
app.include_router(cache_router, prefix="/api")

# Тестовый эндпоинт
@app.get(f"{settings.api_prefix}/health")
async def health_check():
    try:
        # Проверяем соединение с базой данных
        if db.client:
            # Используем существующий клиент для проверки соединения
            await db.client.admin.command('ping')
            return {
                "status": "ok",
                "environment": settings.environment,
                "debug": settings.debug,
                "database": "connected"
            }
        else:
            return {
                "status": "error",
                "message": "Database client not initialized"
            }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database connection error: {str(e)}")

# Тестовый эндпоинт для проверки работы с MongoDB
@app.get(f"{settings.api_prefix}/test-db")
async def test_db():
    try:
        db = Database.get_db()
        # Пробуем вставить тестовый документ
        result = await db.test_collection.insert_one({"test": "connection", "timestamp": datetime.utcnow()})
        # Получаем количество документов в коллекции
        count = await db.test_collection.count_documents({})
        return {
            "status": "success",
            "inserted_id": str(result.inserted_id),
            "total_documents": count
        }
    except Exception as e:
        std_logger.error(f"Database error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    from logging_config import LOGGING_CONFIG
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="warning",
        log_config=LOGGING_CONFIG
    )
