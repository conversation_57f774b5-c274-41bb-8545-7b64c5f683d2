import pytest
from app.database import Database
from app.config import get_settings

class TestDatabase:
    """Тесты для работы с базой данных"""
    
    async def test_database_connection(self, test_db):
        """Тест подключения к базе данных"""
        # Проверяем, что база данных существует
        assert test_db.name == "test_word_master"
        
        # Проверяем, что коллекции созданы
        collections = await test_db.list_collection_names()
        assert "words" in collections
        assert "user_words" in collections
    
    async def test_database_indexes(self, test_db):
        """Тест создания индексов"""
        # Получаем информацию об индексах коллекции words
        words_indexes = await test_db.words.index_information()
        
        # Проверяем, что создан индекс для поля word
        assert any("word_1" in idx for idx in words_indexes)
        
        # Проверяем, что индекс уникальный
        word_index = next(idx for idx in words_indexes.values() if "key" in idx and "word_1" in idx["key"])
        assert word_index["unique"] is True
    
    async def test_insert_and_retrieve_word(self, test_db):
        """Тест вставки и извлечения слова из базы данных"""
        # Подготовка тестовых данных
        test_word = {
            "text": "test_word",
            "language": "en",
            "concept_id": "test_concept_999",
            "translations": ["тестовое_слово"],
            "examples": [],
            "tags": ["test"],
            "level": "A1"
        }
        
        # Вставка тестового слова
        result = await test_db.words.insert_one(test_word)
        assert result.inserted_id is not None
        
        # Получение вставленного слова
        inserted_word = await test_db.words.find_one({"concept_id": "test_concept_999"})
        
        # Проверка, что слово корректно вставлено
        assert inserted_word is not None
        assert inserted_word["text"] == "test_word"
        assert inserted_word["language"] == "en"
        
        # Проверка уникальности индекса
        with pytest.raises(Exception):
            # Пытаемся вставить слово с тем же текстом и языком
            await test_db.words.insert_one({
                "text": "test_word",
                "language": "en",
                "concept_id": "another_concept_id",
                "translations": [],
                "examples": [],
                "tags": [],
                "level": "A1"
            })
