"""
Финальная версия тестов для API интервального повторения.
Использует существующего тестового пользователя.
"""
import os
import pytest
from fastapi import status
from fastapi.testclient import TestClient
from bson import ObjectId
from datetime import datetime
from app.main import app
from app.database import db

# Константы
TEST_USER_ID = "6848235d12259195693cb594"  # ID тестового пользователя <EMAIL>
TEST_USER_EMAIL = "<EMAIL>"

# Фикстура для тестового клиента
@pytest.fixture
def test_client():
    with TestClient(app) as client:
        yield client

# Фикстура для тестового пользователя
@pytest.fixture
def test_user():
    """Возвращает данные тестового пользователя."""
    return {
        "_id": ObjectId(TEST_USER_ID),
        "email": TEST_USER_EMAIL,
        "is_active": True
    }

# Фикстура для тестовых слов
@pytest.fixture
def test_words():
    """Возвращает тестовые слова для использования в тестах."""
    return [
        {
            "_id": ObjectId("507f1f77bcf86cd799439012"),
            "word": "test_word_1",
            "translation": "тестовое_слово_1",
            "language": "en",
            "level": "A1",
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        },
        {
            "_id": ObjectId("507f1f77bcf86cd799439013"),
            "word": "test_word_2",
            "translation": "тестовое_слово_2",
            "language": "en",
            "level": "A1",
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
    ]

# Инициализация тестовых данных
@pytest.fixture(scope="module")
def event_loop():
    """Создаем новый цикл событий для каждого тестового модуля."""
    import asyncio
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()

@pytest.fixture(scope="module")
async def init_test_data(test_user, test_words):
    """Инициализация тестовых данных."""
    if not os.getenv("TESTING"):
        pytest.skip("Тесты должны запускаться с переменной окружения TESTING=True")
    
    # Подключаемся к базе данных
    await db.connect_to_db()
    
    # Очищаем прогресс пользователя
    await db.users_db["user_progress"].delete_many({"user_id": test_user["_id"]})
    
    # Добавляем тестовые слова, если их нет
    for word in test_words:
        existing_word = await db.words_db["words"].find_one({"word": word["word"]})
        if not existing_word:
            await db.words_db["words"].insert_one(word)
    
    # Возвращаем управление тестам
    yield
    
    # Очистка после тестов
    await db.users_db["user_progress"].delete_many({"user_id": test_user["_id"]})

@pytest.mark.asyncio
class TestSpacedRepetitionAPI:
    """Тесты для API интервального повторения"""
    
    async def test_get_next_card_new_user(self, test_client, test_user, init_test_data):
        """Тест получения первой карточки для нового пользователя"""
        # Запрашиваем следующую карточку
        response = test_client.get(
            "/api/cards/next",
            headers={"X-User-Id": str(test_user["_id"])}
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "word" in data
        assert "translation" in data
        assert data.get("word") in ["test_word_1", "test_word_2"]
    
    async def test_submit_correct_answer(self, test_client, test_user, init_test_data):
        """Тест отправки правильного ответа"""
        # Сначала получаем карточку
        response = test_client.get(
            "/api/cards/next",
            headers={"X-User-Id": str(test_user["_id"])}
        )
        assert response.status_code == status.HTTP_200_OK
        card = response.json()
        
        # Отправляем правильный ответ
        response = test_client.post(
            "/api/cards/submit",
            headers={"X-User-Id": str(test_user["_id"])},
            json={
                "word_id": card["id"],
                "user_translation": card["translation"],
                "is_correct": True
            }
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "success" in data
        assert data["success"] is True
    
    async def test_submit_incorrect_answer(self, test_client, test_user, init_test_data):
        """Тест отправки неправильного ответа"""
        # Сначала получаем карточку
        response = test_client.get(
            "/api/cards/next",
            headers={"X-User-Id": str(test_user["_id"])}
        )
        assert response.status_code == status.HTTP_200_OK
        card = response.json()
        
        # Отправляем неправильный ответ
        response = test_client.post(
            "/api/cards/submit",
            headers={"X-User-Id": str(test_user["_id"])},
            json={
                "word_id": card["id"],
                "user_translation": "неправильный перевод",
                "is_correct": False
            }
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "success" in data
        assert data["success"] is True
    
    async def test_forced_queue_priority(self, test_client, test_user, init_test_data):
        """Тест приоритета принудительной очереди"""
        # Сначала получаем карточку
        response = test_client.get(
            "/api/cards/next",
            headers={"X-User-Id": str(test_user["_id"])}
        )
        assert response.status_code == status.HTTP_200_OK
        card = response.json()
        
        # Отправляем неправильный ответ, чтобы слово попало в приоритетную очередь
        response = test_client.post(
            "/api/cards/submit",
            headers={"X-User-Id": str(test_user["_id"])},
            json={
                "word_id": card["id"],
                "user_translation": "неправильный перевод",
                "is_correct": False
            }
        )
        assert response.status_code == status.HTTP_200_OK
        
        # Проверяем, что следующая карточка будет та же самая (из-за приоритета)
        response = test_client.get(
            "/api/cards/next",
            headers={"X-User-Id": str(test_user["_id"])}
        )
        assert response.status_code == status.HTTP_200_OK
        next_card = response.json()
        
        assert next_card["id"] == card["id"]  # Должна вернуться та же карточка
