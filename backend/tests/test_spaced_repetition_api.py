"""
Тесты для API интервального повторения.
"""
import os
import pytest
import asyncio
import pytest_asyncio
from fastapi import status
from fastapi.testclient import TestClient
from bson import ObjectId
from datetime import datetime, timedelta
from app.main import app
from app.models.card import Language, Card, Word
from app.database import Database, db
from app.config import get_settings

# Настройки
settings = get_settings()

# Фикстура для тестового клиента
@pytest.fixture(scope="module")
def test_client():
    with TestClient(app) as client:
        yield client

# Получаем конкретного пользователя по email
@pytest_asyncio.fixture(scope="module")
async def test_user():
    await db.connect_to_db()
    user = await db.users_db["users"].find_one({"email": "<EMAIL>"})
    if not user:
        raise ValueError("Не найден пользователь с email <EMAIL>")
    return user

# Фикстура для тестовых слов
@pytest_asyncio.fixture(scope="module")
async def test_words():
    return [
        {
            "_id": ObjectId(),
            "word": "test_word_1",
            "translation": "тестовое_слово_1",
            "language": "en",
            "level": "A1",
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        },
        {
            "_id": ObjectId(),
            "word": "test_word_2",
            "translation": "тестовое_слово_2",
            "language": "en",
            "level": "A1",
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
    ]

# Инициализация тестовых данных
@pytest_asyncio.fixture(scope="module")
async def init_test_data(test_user, test_words):
    """Подготовка тестового окружения"""
    if not os.getenv("TESTING"):
        raise EnvironmentError("Тесты должны запускаться с переменной окружения TESTING=True")
    
    await db.connect_to_db()
    
    # Проверяем наличие слов
    word_count = await db.words_db["words"].count_documents({})
    if word_count == 0:
        # Если слов нет, добавляем тестовые
        await db.words_db["words"].insert_many(test_words)
    
    # Очищаем только прогресс текущего пользователя
    await db.users_db["user_progress"].delete_many({"user_id": test_user["_id"]})
    
    yield  # Выполняем тесты
    
    # После тестов очищаем прогресс пользователя
    await db.users_db["user_progress"].delete_many({"user_id": test_user["_id"]})

class TestSpacedRepetitionAPI:
    """Тесты для API интервального повторения"""
    
    @pytest.mark.asyncio
    async def test_get_next_card_new_user(self, test_client, test_user, test_words, init_test_data):
        """Тест получения первой карточки для нового пользователя"""
        # Запрашиваем следующую карточку
        response = test_client.get(
            "/api/cards/next",
            params={
                "user_id": str(test_user["_id"]),
                "native_lang": "ru",
                "target_lang": "en"
            }
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Проверяем структуру ответа
        assert "id" in data
        assert "word" in data
        assert "translation" in data
        assert "is_new" in data
        assert data["is_new"] is True
        
        # Проверяем, что слово из тестовых данных
        assert data["word"] in [w["word"] for w in test_words] or data["translation"] in [w["translation"] for w in test_words]
        
        # Возвращаем ID карточки для использования в следующих тестах
        return data["id"]
    
    @pytest.mark.asyncio
    async def test_submit_correct_answer(self, test_client, test_user, test_words, init_test_data):
        """Тест отправки правильного ответа"""
        # Получаем следующую карточку
        next_card_response = test_client.get(
            "/api/cards/next",
            params={
                "user_id": str(test_user["_id"]),
                "native_lang": "ru",
                "target_lang": "en"
            }
        )
        assert next_card_response.status_code == status.HTTP_200_OK
        card_data = next_card_response.json()
        
        # Отправляем правильный ответ
        response = test_client.post(
            f"/api/cards/{card_data['id']}/response",
            json={
                "user_id": str(test_user["_id"]),
                "is_correct": True,
                "response_time": 5.5
            }
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Проверяем, что интервал увеличился
        assert "interval_level" in data
        assert data["interval_level"] == 1  # После первого правильного ответа уровень должен быть 1
        assert "next_review" in data
        
        # Проверяем, что next_review - это валидная дата в будущем
        next_review = datetime.fromisoformat(data["next_review"])
        assert next_review > datetime.utcnow()
    
    @pytest.mark.asyncio
    async def test_submit_incorrect_answer(self, test_client, test_user, test_words, init_test_data):
        """Тест отправки неправильного ответа"""
        # Получаем следующую карточку
        next_card_response = test_client.get(
            "/api/cards/next",
            params={
                "user_id": str(test_user["_id"]),
                "native_lang": "ru",
                "target_lang": "en"
            }
        )
        assert next_card_response.status_code == status.HTTP_200_OK
        card_data = next_card_response.json()
        
        # Отправляем неправильный ответ
        response = test_client.post(
            f"/api/cards/{card_data['id']}/response",
            json={
                "user_id": str(test_user["_id"]),
                "is_correct": False,
                "response_time": 3.2
            }
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Проверяем, что слово добавлено в форсированную очередь
        assert "in_forced_queue" in data
        assert data["in_forced_queue"] is True
        
        # Проверяем, что интервал сброшен
        assert "interval_level" in data
        assert data["interval_level"] == 0
    
    @pytest.mark.asyncio
    async def test_forced_queue_priority(self, test_client, test_user, test_words, init_test_data):
        """Тест приоритета форсированной очереди"""
        # Сначала получаем карточку
        next_card_response = test_client.get(
            "/api/cards/next",
            params={
                "user_id": str(test_user["_id"]),
                "native_lang": "ru",
                "target_lang": "en"
            }
        )
        assert next_card_response.status_code == status.HTTP_200_OK
        card_data = next_card_response.json()
        
        # Отправляем неправильный ответ, чтобы карточка попала в форсированную очередь
        response = test_client.post(
            f"/api/cards/{card_data['id']}/response",
            json={
                "user_id": str(test_user["_id"]),
                "is_correct": False,
                "response_time": 10.0
            }
        )
        assert response.status_code == status.HTTP_200_OK
        
        # Снова запрашиваем следующую карточку - должна быть та же самая
        next_card_response = test_client.get(
            "/api/cards/next",
            params={
                "user_id": str(test_user["_id"]),
                "native_lang": "ru",
                "target_lang": "en"
            }
        )
        assert next_card_response.status_code == status.HTTP_200_OK
        same_card_data = next_card_response.json()
        
        # Проверяем, что это та же карточка
        assert same_card_data["id"] == card_data["id"]
        
    @pytest.mark.asyncio
    async def test_same_card_after_incorrect_answer(self, test_client, test_user, test_words, init_test_data):
        """Тест получения той же карточки после неправильного ответа"""
        # 1. Получаем карточку
        next_card_response = test_client.get(
            "/api/cards/next",
            params={
                "user_id": str(test_user["_id"]),
                "native_lang": "ru",
                "target_lang": "en"
            }
        )
        assert next_card_response.status_code == status.HTTP_200_OK
        first_card = next_card_response.json()
        
        # 2. Отправляем неправильный ответ
        response = test_client.post(
            f"/api/cards/{first_card['id']}/response",
            json={
                "user_id": str(test_user["_id"]),
                "is_correct": False,
                "response_time": 8.5
            }
        )
        assert response.status_code == status.HTTP_200_OK
        
        # 3. Получаем следующую карточку - должна быть та же самая
        next_card_response = test_client.get(
            "/api/cards/next",
            params={
                "user_id": str(test_user["_id"]),
                "native_lang": "ru",
                "target_lang": "en"
            }
        )
        assert next_card_response.status_code == status.HTTP_200_OK
        same_card = next_card_response.json()
        
        # 4. Проверяем, что это та же карточка
        assert same_card["id"] == first_card["id"]
        
        # 5. Проверяем, что карточка находится в форсированной очереди
        user_progress = await db.users_db["user_progress"].find_one({
            "user_id": test_user["_id"],
            "card_id": first_card["id"]
        })
        assert user_progress is not None
        assert "force_queue" in user_progress
        assert user_progress["force_queue"] is True
        
        # Отправляем правильный ответ, чтобы убрать из форсированной очереди
        test_client.post(
            f"/api/cards/{first_card['id']}/response",
            json={
                "user_id": str(test_user["_id"]),
                "is_correct": True,
                "response_time": 3.5
            }
        )
        
        # Получаем следующую карточку - должна быть новая
        next_card_response = test_client.get(
            "/api/cards/next",
            params={
                "user_id": str(test_user["_id"]),
                "native_lang": "ru",
                "target_lang": "en"
            }
        )
        assert next_card_response.status_code == status.HTTP_200_OK
        new_card = next_card_response.json()
        
        # Проверяем, что это новая карточка
        assert new_card["id"] != first_card["id"]
        assert new_card["is_new"] is True
