"""
Тесты для API интервального повторения (исправленная версия).
"""
import os
import pytest
import asyncio
from fastapi import status
from fastapi.testclient import TestClient
from bson import ObjectId
from datetime import datetime, timedelta
from app.main import app
from app.models.card import Language, Card, Word
from app.database import Database, db
from app.config import get_settings

# Настройки
settings = get_settings()

# Фикстура для тестового клиента
@pytest.fixture(scope="module")
def test_client():
    with TestClient(app) as client:
        yield client

# Фикстура для тестового пользователя
@pytest.fixture(scope="module")
def test_user():
    # Используем реального пользователя из базы
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    async def get_user():
        await db.connect_to_db()
        user = await db.users_db['users'].find_one({'email': '<EMAIL>'})
        if not user:
            # Создаем пользователя, если его нет
            user = {
                "email": "<EMAIL>",
                "is_active": True,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            }
            result = await db.users_db['users'].insert_one(user)
            user['_id'] = result.inserted_id
        return user
    
    try:
        user = loop.run_until_complete(get_user())
        return user
    finally:
        loop.close()

# Фикстура для тестовых слов
@pytest.fixture(scope="module")
def test_words():
    # Используем реальные слова из базы или создаем тестовые
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    async def get_or_create_words():
        await db.connect_to_db()
        
        # Проверяем, есть ли слова в базе
        words = []
        test_word_data = [
            {
                "word": "test_word_1",
                "translation": "тестовое_слово_1",
                "language": "en",
                "level": "A1"
            },
            {
                "word": "test_word_2",
                "translation": "тестовое_слово_2",
                "language": "en",
                "level": "A1"
            }
        ]
        
        for word_data in test_word_data:
            word = await db.words_db['words'].find_one({"word": word_data["word"]})
            if not word:
                # Добавляем слово, если его нет
                word_data.update({
                    "created_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow()
                })
                result = await db.words_db['words'].insert_one(word_data)
                word_data['_id'] = result.inserted_id
                words.append(word_data)
            else:
                words.append(word)
        
        return words
    
    try:
        words = loop.run_until_complete(get_or_create_words())
        return words
    finally:
        loop.close()

# Инициализация тестовых данных
@pytest.fixture(scope="module")
def init_test_data(test_user, test_words):
    """Подготовка тестового окружения"""
    if not os.getenv("TESTING"):
        raise EnvironmentError("Тесты должны запускаться с переменной окружения TESTING=True")
    
    # Создаем новый цикл событий
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    async def setup():
        # Подключаемся к базе
        await db.connect_to_db()
        
        # Очищаем прогресс пользователя
        await db.users_db["user_progress"].delete_many({"user_id": test_user["_id"]})
        
        # Проверяем, что у нас есть слова для тестирования
        word_count = await db.words_db["words"].count_documents({})
        if word_count == 0:
            # Если слов нет, добавляем тестовые
            await db.words_db["words"].insert_many(test_words)
    
    try:
        # Выполняем настройку
        loop.run_until_complete(setup())
        
        # Возвращаем управление тестам
        yield
        
    finally:
        # Очистка после тестов
        async def cleanup():
            # Очищаем только прогресс пользователя, оставляя слова и пользователя
            await db.users_db["user_progress"].delete_many({"user_id": test_user["_id"]})
            
        try:
            loop.run_until_complete(cleanup())
        except Exception as e:
            print(f"Ошибка при очистке: {e}")
        finally:
            # Всегда закрываем цикл событий
            loop.close()

class TestSpacedRepetitionAPI:
    """Тесты для API интервального повторения"""
    
    @pytest.mark.asyncio
    async def test_get_next_card_new_user(self, test_client, test_user, test_words, init_test_data):
        """Тест получения первой карточки для нового пользователя"""
        # Запрашиваем следующую карточку
        response = test_client.get(
            "/api/cards/next",
            headers={"X-User-Id": str(test_user["_id"])}
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "word" in data
        assert "translation" in data
        assert data["word"] in [w["word"] for w in test_words]
    
    @pytest.mark.asyncio
    async def test_submit_correct_answer(self, test_client, test_user, test_words, init_test_data):
        """Тест отправки правильного ответа"""
        # Сначала получаем карточку
        response = test_client.get(
            "/api/cards/next",
            headers={"X-User-Id": str(test_user["_id"])}
        )
        assert response.status_code == status.HTTP_200_OK
        card = response.json()
        
        # Отправляем правильный ответ
        response = test_client.post(
            "/api/cards/submit",
            headers={"X-User-Id": str(test_user["_id"])},
            json={
                "word_id": card["id"],
                "user_translation": card["translation"],
                "is_correct": True
            }
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "success" in data
        assert data["success"] is True
    
    @pytest.mark.asyncio
    async def test_submit_incorrect_answer(self, test_client, test_user, test_words, init_test_data):
        """Тест отправки неправильного ответа"""
        # Сначала получаем карточку
        response = test_client.get(
            "/api/cards/next",
            headers={"X-User-Id": str(test_user["_id"])}
        )
        assert response.status_code == status.HTTP_200_OK
        card = response.json()
        
        # Отправляем неправильный ответ
        response = test_client.post(
            "/api/cards/submit",
            headers={"X-User-Id": str(test_user["_id"])},
            json={
                "word_id": card["id"],
                "user_translation": "неправильный перевод",
                "is_correct": False
            }
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "success" in data
        assert data["success"] is True
    
    @pytest.mark.asyncio
    async def test_forced_queue_priority(self, test_client, test_user, test_words, init_test_data):
        """Тест приоритета принудительной очереди"""
        # Сначала получаем карточку
        response = test_client.get(
            "/api/cards/next",
            headers={"X-User-Id": str(test_user["_id"])}
        )
        assert response.status_code == status.HTTP_200_OK
        card = response.json()
        
        # Отправляем неправильный ответ, чтобы слово попало в приоритетную очередь
        response = test_client.post(
            "/api/cards/submit",
            headers={"X-User-Id": str(test_user["_id"])},
            json={
                "word_id": card["id"],
                "user_translation": "неправильный перевод",
                "is_correct": False
            }
        )
        assert response.status_code == status.HTTP_200_OK
        
        # Проверяем, что следующая карточка будет та же самая (из-за приоритета)
        response = test_client.get(
            "/api/cards/next",
            headers={"X-User-Id": str(test_user["_id"])}
        )
        assert response.status_code == status.HTTP_200_OK
        next_card = response.json()
        
        assert next_card["id"] == card["id"]  # Должна вернуться та же карточка
