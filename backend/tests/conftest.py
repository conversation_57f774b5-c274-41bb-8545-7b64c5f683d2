import asyncio
import pytest
from fastapi.testclient import TestClient
from motor.motor_asyncio import AsyncIOMotorClient

from app.main import app
from app.database import Database
from app.config import get_settings

# Настройки для тестовой базы данных
TEST_DATABASE_URL = "mongodb://localhost:27017/"
TEST_DATABASE_NAME = "test_word_master"

@pytest.fixture(scope="session")
def event_loop():
    """Создаем event loop для тестов"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture(scope="session")
async def test_db():
    """Фикстура для тестовой базы данных"""
    # Подключаемся к тестовой базе данных
    client = AsyncIOMotorClient(TEST_DATABASE_URL)
    db = client[TEST_DATABASE_NAME]
    
    # Сохраняем текущие настройки
    settings = get_settings()
    original_url = settings.mongodb_url
    original_db_name = settings.words_database_name
    
    # Устанавливаем тестовые настройки
    settings.mongodb_url = TEST_DATABASE_URL
    settings.words_database_name = TEST_DATABASE_NAME
    
    # Инициализируем базу данных
    db_instance = Database()
    await db_instance.connect_to_db()
    
    yield db
    
    # Очищаем тестовую базу данных
    await client.drop_database(TEST_DATABASE_NAME)
    
    # Восстанавливаем оригинальные настройки
    settings.mongodb_url = original_url
    settings.words_database_name = original_db_name

@pytest.fixture(scope="function")
async def test_client():
    """Фикстура для тестового клиента FastAPI"""
    async with TestClient(app) as client:
        yield client

@pytest.fixture(scope="function")
async def test_data(test_db):
    """Фикстура с тестовыми данными"""
    test_words = [
        {
            "text": "hello",
            "language": "en",
            "concept_id": "test_concept_1",
            "translations": ["привет"],
            "examples": [
                {
                    "sentence": "___, how are you?",
                    "correct_answers": ["Hello"],
                    "word_info": "Interjection"
                }
            ],
            "tags": ["greeting", "basic"],
            "level": "A1"
        },
        {
            "text": "привет",
            "language": "ru",
            "concept_id": "test_concept_1",
            "translations": ["hello"],
            "examples": [
                {
                    "sentence": "___, как дела?",
                    "correct_answers": ["Привет"],
                    "word_info": "Междометие"
                }
            ],
            "tags": ["приветствие", "базовый"],
            "level": "A1"
        },
        {
            "text": "goodbye",
            "language": "en",
            "concept_id": "test_concept_2",
            "translations": ["до свидания"],
            "examples": [
                {
                    "sentence": "___ for now!",
                    "correct_answers": ["Goodbye"],
                    "word_info": "Interjection"
                }
            ],
            "tags": ["farewell", "basic"],
            "level": "A1"
        },
        {
            "text": "до свидания",
            "language": "ru",
            "concept_id": "test_concept_2",
            "translations": ["goodbye"],
            "examples": [
                {
                    "sentence": "___ и всего хорошего!",
                    "correct_answers": ["До свидания"],
                    "word_info": "Междометие"
                }
            ],
            "tags": ["прощание", "базовый"],
            "level": "A1"
        }
    ]
    
    # Вставляем тестовые данные в базу
    await test_db.words.insert_many(test_words)
    
    return test_words
