import pytest
from fastapi import status
from app.models.card import Language

class TestCardsAPI:
    """Тесты для API карточек"""
    
    async def test_get_random_card_success(self, test_client, test_data):
        """Тест успешного получения случайной карточки"""
        response = test_client.get(
            "/api/cards/random",
            params={
                "native_lang": Language.RU,
                "target_lang": Language.EN
            }
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "native_word" in data
        assert "target_word" in data
        assert data["native_word"]["language"] == "ru"
        assert data["target_word"]["language"] == "en"
        
        # Проверяем, что concept_id совпадает у обоих слов
        assert data["native_word"]["concept_id"] == data["target_word"]["concept_id"]
    
    async def test_get_random_card_with_level(self, test_client, test_data):
        """Тест получения случайной карточки с указанием уровня"""
        response = test_client.get(
            "/api/cards/random",
            params={
                "native_lang": Language.RU,
                "target_lang": Language.EN,
                "level": "A1"
            }
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["target_word"]["level"] == "A1"
    
    async def test_get_random_card_not_found(self, test_client):
        """Тест случая, когда карточки не найдены"""
        response = test_client.get(
            "/api/cards/random",
            params={
                "native_lang": "es",  # Испанский, которого нет в тестовых данных
                "target_lang": "fr"   # Французский, которого нет в тестовых данных
            }
        )
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
    
    async def test_get_card_by_concept_id_success(self, test_client, test_data):
        """Тест успешного получения карточки по concept_id"""
        # Берем concept_id из первого тестового слова
        concept_id = test_data[0]["concept_id"]
        
        response = test_client.get(
            f"/api/cards/{concept_id}",
            params={
                "native_lang": Language.RU,
                "target_lang": Language.EN
            }
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["native_word"]["concept_id"] == concept_id
        assert data["target_word"]["concept_id"] == concept_id
    
    async def test_get_card_by_concept_id_not_found(self, test_client):
        """Тест случая, когда карточка с указанным concept_id не найдена"""
        response = test_client.get(
            "/api/cards/non_existent_concept_id",
            params={
                "native_lang": Language.RU,
                "target_lang": Language.EN
            }
        )
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
