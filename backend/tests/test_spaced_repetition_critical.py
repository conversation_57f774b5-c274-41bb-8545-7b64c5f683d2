"""
Критические тесты системы интервального повторения

Проверяют основные сценарии, которые вызывали регрессии через API:
1. Правильный ответ с первого раза → interval_level = 15, is_learned = True
2. Неправильный, затем правильный → interval_level < 15, is_learned = False
3. Использование подсказок → НЕ считается выученным
4. Переходы между карточками без наследования прогресса
"""

import os
import pytest
from fastapi import status
from fastapi.testclient import TestClient
from bson import ObjectId
from datetime import datetime

from app.main import app

# Константы
TEST_USER_ID = "6848235d12259195693cb594"  # ID тестового пользователя <EMAIL>


@pytest.fixture
def test_client():
    """Фикстура для тестового клиента FastAPI"""
    with TestClient(app) as client:
        yield client


@pytest.fixture
def test_user():
    """Возвращает данные тестового пользователя"""
    return {
        "_id": ObjectId(TEST_USER_ID),
        "email": "<EMAIL>",
        "is_active": True
    }

class TestCriticalScenarios:
    """Критические сценарии системы интервального повторения через API"""

    def test_new_word_correct_first_try_api(self, test_client, test_user):
        """
        Сценарий 1: Новое слово отвечено правильно с первого раза через API
        Ожидаемый результат: interval_level = 15, is_learned = True
        """
        if not os.getenv("TESTING"):
            pytest.skip("Тест требует переменную окружения TESTING=True")

        # 1. Получаем новую карточку
        response = test_client.get(
            "/api/spaced/next",
            params={
                "user_id": str(test_user["_id"]),
                "target_lang": "cb"
            }
        )

        assert response.status_code == status.HTTP_200_OK
        card_data = response.json()
        assert card_data["is_new"] is True

        # 2. Отправляем правильный ответ с первого раза
        response = test_client.post(
            "/api/spaced/submit",
            json={
                "user_id": str(test_user["_id"]),
                "word_id": card_data["word_id"],
                "is_correct": True,
                "response_time": 2.5,
                "used_hint": False
            }
        )

        assert response.status_code == status.HTTP_200_OK
        result = response.json()

        # 3. Проверяем результат - новое слово должно быть выучено
        assert result["is_correct"] is True
        assert result["interval_level"] == 15, f"Expected interval_level=15, got {result['interval_level']}"
        assert result["is_learned"] is True, f"Expected is_learned=True, got {result['is_learned']}"

    def test_new_word_incorrect_then_correct_api(self, test_client, test_user):
        """
        Сценарий 2: Новое слово - неправильный ответ, затем правильный через API
        Ожидаемый результат: interval_level < 15, is_learned = False
        """
        if not os.getenv("TESTING"):
            pytest.skip("Тест требует переменную окружения TESTING=True")

        # 1. Получаем новую карточку
        response = test_client.get(
            "/api/spaced/next",
            params={
                "user_id": str(test_user["_id"]),
                "target_lang": "cb"
            }
        )

        assert response.status_code == status.HTTP_200_OK
        card_data = response.json()
        assert card_data["is_new"] is True

        # 2. Отправляем неправильный ответ
        response = test_client.post(
            "/api/spaced/submit",
            json={
                "user_id": str(test_user["_id"]),
                "word_id": card_data["word_id"],
                "is_correct": False,
                "response_time": 3.0,
                "used_hint": False
            }
        )

        assert response.status_code == status.HTTP_200_OK
        result1 = response.json()
        assert result1["is_correct"] is False
        assert result1["interval_level"] < 15
        assert result1["is_learned"] is False

        # 3. Отправляем правильный ответ
        response = test_client.post(
            "/api/spaced/submit",
            json={
                "user_id": str(test_user["_id"]),
                "word_id": card_data["word_id"],
                "is_correct": True,
                "response_time": 2.0,
                "used_hint": False
            }
        )

        assert response.status_code == status.HTTP_200_OK
        result2 = response.json()

        # 4. Слово НЕ должно быть выучено из-за предыдущей ошибки
        assert result2["is_correct"] is True
        assert result2["interval_level"] < 15, f"Expected interval_level < 15, got {result2['interval_level']}"
        assert result2["is_learned"] is False, f"Expected is_learned=False, got {result2['is_learned']}"

    def test_new_word_with_hint_api(self, test_client, test_user):
        """
        Сценарий 3: Новое слово с использованием подсказки через API
        Ожидаемый результат: НЕ считается выученным
        """
        if not os.getenv("TESTING"):
            pytest.skip("Тест требует переменную окружения TESTING=True")

        # 1. Получаем новую карточку
        response = test_client.get(
            "/api/spaced/next",
            params={
                "user_id": str(test_user["_id"]),
                "target_lang": "cb"
            }
        )

        assert response.status_code == status.HTTP_200_OK
        card_data = response.json()
        assert card_data["is_new"] is True

        # 2. Отправляем правильный ответ, но с подсказкой
        response = test_client.post(
            "/api/spaced/submit",
            json={
                "user_id": str(test_user["_id"]),
                "word_id": card_data["word_id"],
                "is_correct": True,
                "response_time": 1.0,
                "used_hint": True  # Использовали подсказку
            }
        )

        assert response.status_code == status.HTTP_200_OK
        result = response.json()

        # 3. Слово НЕ должно быть выучено из-за использования подсказки
        assert result["is_correct"] is True
        assert result["interval_level"] < 15, f"Expected interval_level < 15, got {result['interval_level']}"
        assert result["is_learned"] is False, f"Expected is_learned=False, got {result['is_learned']}"

    def test_card_transitions_no_inheritance_api(self, test_client, test_user):
        """
        Сценарий 4: Переходы между карточками без наследования прогресса через API
        """
        if not os.getenv("TESTING"):
            pytest.skip("Тест требует переменную окружения TESTING=True")

        # 1. Получаем первую карточку
        response = test_client.get(
            "/api/spaced/next",
            params={
                "user_id": str(test_user["_id"]),
                "target_lang": "cb"
            }
        )

        assert response.status_code == status.HTTP_200_OK
        card1_data = response.json()
        assert card1_data["is_new"] is True

        # 2. Отвечаем правильно на первую карточку (выучиваем её)
        response = test_client.post(
            "/api/spaced/submit",
            json={
                "user_id": str(test_user["_id"]),
                "word_id": card1_data["word_id"],
                "is_correct": True,
                "response_time": 2.0,
                "used_hint": False
            }
        )

        assert response.status_code == status.HTTP_200_OK
        result1 = response.json()
        assert result1["interval_level"] == 15  # Выучена
        assert result1["is_learned"] is True

        # 3. Получаем вторую карточку
        response = test_client.get(
            "/api/spaced/next",
            params={
                "user_id": str(test_user["_id"]),
                "target_lang": "cb"
            }
        )

        assert response.status_code == status.HTTP_200_OK
        card2_data = response.json()

        # 4. Вторая карточка должна быть новой и НЕ наследовать прогресс первой
        assert card2_data["word_id"] != card1_data["word_id"]  # Разные карточки
        assert card2_data["is_new"] is True  # Новая карточка
        assert card2_data.get("interval_level", -1) == -1  # Начальный уровень
        assert card2_data.get("is_learned", False) is False  # Не выучена

    def test_new_word_with_hint_logic(self):
        """
        Тест логики: новое слово с использованием подсказки
        Должно: НЕ считается выученным
        """
        # Симулируем логику для нового слова с подсказкой
        is_new_word = True
        is_correct = True
        used_hint = True  # Использовали подсказку
        previous_incorrect_count = 0

        # Логика из SpacedRepetitionService
        if is_new_word and is_correct and not used_hint and previous_incorrect_count == 0:
            # Новое слово выучено с первого раза
            interval_level = 15
            is_learned = True
        else:
            # Подсказка не позволяет выучить слово
            interval_level = 0
            is_learned = False

        assert interval_level < 15, f"Expected interval_level < 15, got {interval_level}"
        assert is_learned is False, f"Expected is_learned=False, got {is_learned}"

    def test_interval_calculation_logic(self):
        """
        Тест логики расчета интервалов для разных уровней
        """
        # Тестируем разные уровни интервалов
        test_cases = [
            (-1, "новое слово"),
            (0, "1 минута"),
            (1, "10 минут"),
            (5, "1 день"),
            (10, "1 месяц"),
            (15, "1 год"),
        ]

        for level, description in test_cases:
            # Проверяем, что уровень в допустимом диапазоне
            assert -1 <= level <= 15, f"Уровень {level} ({description}) вне допустимого диапазона"

            # Проверяем логику выученности
            if level == 15:
                is_learned = True
            else:
                is_learned = False

            # Для уровня 15 слово должно быть выучено
            if level == 15:
                assert is_learned is True, f"Уровень {level} должен означать выученное слово"
            else:
                assert is_learned is False, f"Уровень {level} НЕ должен означать выученное слово"

    def test_progress_bars_logic(self):
        """
        Тест логики отображения полосок прогресса
        """
        # Функция расчета полосок (из frontend)
        def get_progress_bars_info(interval_level, is_new, is_learned):
            if is_learned:
                return {"count": 5, "color": "#4CAF50"}  # 5 зелёных полосок
            elif is_new:
                return {"count": 1, "color": "#FFA500"}  # 1 оранжевая полоска
            elif -1 <= interval_level <= 4:
                return {"count": 1, "color": "#7fb4ff"}  # 1 голубая полоска
            elif 5 <= interval_level <= 8:
                return {"count": 2, "color": "#7fb4ff"}  # 2 голубые полоски
            elif 9 <= interval_level <= 11:
                return {"count": 3, "color": "#4a7dff"}  # 3 синие полоски
            elif 12 <= interval_level <= 14:
                return {"count": 4, "color": "#4a7dff"}  # 4 синие полоски
            else:
                return {"count": 5, "color": "#4CAF50"}  # 5 зелёных полосок

        # Тестируем разные сценарии
        test_cases = [
            # (interval_level, is_new, is_learned, expected_count, expected_color)
            (-1, True, False, 1, "#FFA500"),   # Новое слово
            (15, False, True, 5, "#4CAF50"),   # Выученное слово
            (0, False, False, 1, "#7fb4ff"),   # Низкий уровень
            (5, False, False, 2, "#7fb4ff"),   # Средний уровень
            (12, False, False, 4, "#4a7dff"),  # Высокий уровень
        ]

        for interval_level, is_new, is_learned, expected_count, expected_color in test_cases:
            result = get_progress_bars_info(interval_level, is_new, is_learned)
            assert result["count"] == expected_count, f"Неправильное количество полосок для уровня {interval_level}"
            assert result["color"] == expected_color, f"Неправильный цвет полосок для уровня {interval_level}"

    def test_no_progress_inheritance_logic(self):
        """
        Тест логики: новые карточки НЕ должны наследовать прогресс
        """
        # Предыдущая карточка (выученная)
        prev_card = {
            "interval_level": 15,
            "is_learned": True,
            "is_new": False
        }

        # Новая карточка (должна быть чистой)
        new_card = {
            "interval_level": -1,
            "is_learned": False,
            "is_new": True
        }

        # Проверяем, что карточки независимы
        assert prev_card["interval_level"] != new_card["interval_level"]
        assert prev_card["is_learned"] != new_card["is_learned"]
        assert prev_card["is_new"] != new_card["is_new"]

        # Новая карточка должна иметь правильные начальные значения
        assert new_card["interval_level"] == -1
        assert new_card["is_learned"] is False
        assert new_card["is_new"] is True





