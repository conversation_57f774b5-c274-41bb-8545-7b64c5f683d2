"""
Простой тест для проверки подключения к базе данных и базовой функциональности.
"""
import os
import pytest
from pymongo import MongoClient
import pytest
import asyncio
import httpx
from fastapi.testclient import TestClient
from bson import ObjectId
from dotenv import load_dotenv
from app.main import app
from app.database import db

# Загружаем переменные окружения
load_dotenv()

# ID тестового пользователя (<EMAIL>)
TEST_USER_ID = "6848235d12259195693cb594"

def test_db_connection():
    """Проверяем подключение к базе данных с помощью синхронного клиента."""
    # Получаем URL из переменных окружения
    mongo_url = os.getenv('MONGODB_URL')
    assert mongo_url, "Не задан MONGODB_URL в переменных окружения"
    
    try:
        # Подключаемся к MongoDB
        client = MongoClient(mongo_url)
        
        # Проверяем подключение
        client.admin.command('ping')
        print("✓ Успешное подключение к MongoDB")
        
        # Подключаемся к базе данных пользователей
        db = client.users_db
        
        # Получаем пользователя
        user = db.users.find_one({"_id": ObjectId(TEST_USER_ID)})
        assert user is not None, f"Пользователь с ID {TEST_USER_ID} не найден"
        
        print(f"\nНайден пользователь:")
        print(f"ID: {user['_id']}")
        print(f"Email: {user.get('email')}")
        print(f"Активен: {user.get('is_active', False)}")
        
        # Проверяем коллекцию слов
        word_db = client.word_master
        word_count = word_db.words.count_documents({})
        print(f"\nВсего слов в базе: {word_count}")
        
        # Выводим информацию о пользователе
        print("\nДанные пользователя:")
        for key, value in user.items():
            if key != 'hashed_password':  # Не выводим хеш пароля
                print(f"{key}: {value}")
        
    except Exception as e:
        pytest.fail(f"Ошибка при подключении к базе данных: {e}")
    finally:
        if 'client' in locals():
            client.close()

def test_get_next_card():
    """Тестируем получение следующей карточки для изучения."""
    # Создаем тестового клиента
    client = TestClient(app)
    
    try:
        # Получаем следующую карточку для тестового пользователя
        response = client.get(
            "/api/cards/next",
            headers={"X-User-Id": TEST_USER_ID}
        )
        
        # Проверяем, что запрос выполнен успешно
        assert response.status_code == 200, f"Ошибка при получении карточки: {response.text}"
        
        # Получаем данные карточки
        card = response.json()
        print("\nПолучена карточка:")
        print(f"ID: {card.get('id')}")
        print(f"Слово: {card.get('word')}")
        print(f"Перевод: {card.get('translation')}")
        
        # Проверяем, что в ответе есть все необходимые поля
        required_fields = ['id', 'word', 'translation', 'language', 'level']
        for field in required_fields:
            assert field in card, f"В ответе отсутствует обязательное поле: {field}"
        
        print("\nВсе обязательные поля присутствуют")
        
    except Exception as e:
        pytest.fail(f"Ошибка при тестировании API: {e}")
    finally:
        # Закрываем клиент
        client.close()

@pytest.mark.asyncio
async def test_api_endpoints():
    """Проверяем доступные эндпоинты API асинхронно."""
    # Подключаемся к базе данных
    print("\nПодключение к базе данных...")
    await db.connect_to_db()
    
    # Создаем асинхронный клиент
    async with httpx.AsyncClient(app=app, base_url="http://test") as client:
        try:
            # Проверяем стандартные эндпоинты
            endpoints_to_check = [
                ("/api/words/", "GET"),
                ("/api/cards/next", "GET", {"X-User-Id": TEST_USER_ID})
            ]
            
            print("\nПроверка эндпоинтов:")
            results = {}
            
            for endpoint_info in endpoints_to_check:
                endpoint = endpoint_info[0]
                method = endpoint_info[1].lower()
                headers = endpoint_info[2] if len(endpoint_info) > 2 else {}
                
                try:
                    # Выполняем запрос
                    print(f"\nВыполняем запрос: {method.upper()} {endpoint}")
                    response = await client.request(
                        method=method,
                        url=endpoint,
                        headers=headers
                    )
                    
                    # Выводим информацию о запросе
                    print(f"Статус: {response.status_code}")
                    
                    # Пытаемся распарсить JSON, если это возможно
                    try:
                        json_response = response.json()
                        print(f"Ответ (JSON): {json_response}")
                    except:
                        print(f"Ответ (текст): {response.text[:200]}..." if len(response.text) > 200 else f"Ответ: {response.text}")
                    
                    # Сохраняем результат
                    results[endpoint] = {
                        "status": response.status_code,
                        "response": response
                    }
                    
                except Exception as e:
                    print(f"{method.upper()} {endpoint}: ОШИБКА - {str(e)}")
                    results[endpoint] = {"error": str(e)}
            
            # Проверяем, что хотя бы один эндпоинт доступен
            if not any(200 <= result.get("status", 0) < 300 for result in results.values()):
                print("\nПредупреждение: Ни один из эндпоинтов не вернул успешный статус")
                print("Это может быть связано с отсутствием тестовых данных в базе.")
                
                # Проверяем содержимое базы данных
                try:
                    print("\nПроверка содержимого коллекции words:")
                    words_cursor = db.words_db.words.find().limit(5)
                    words = await words_cursor.to_list(length=5)
                    print(f"Первые {len(words)} слов из базы:")
                    for word in words:
                        print(f"- {word.get('word', 'Без названия')} (ID: {word.get('_id')})")
                    
                    # Проверяем наличие пользователя
                    print(f"\nПроверка пользователя с ID: {TEST_USER_ID}")
                    user = await db.users_db.users.find_one({"_id": ObjectId(TEST_USER_ID)})
                    if user:
                        print(f"Пользователь найден: {user.get('email')}")
                    else:
                        print("Пользователь не найден!")
                        
                except Exception as e:
                    print(f"Ошибка при проверке базы данных: {e}")
            
            # Проверяем, что тест не возвращает None
            assert results is not None, "Тест не должен возвращать None"
            
        except Exception as e:
            print(f"Критическая ошибка: {str(e)}")
            raise

def test_get_next_card():
    """Тестируем получение следующей карточки."""
    client = TestClient(app)
    
    try:
        # Сначала получаем список доступных слов
        response = client.get(
            "/api/words/",
            headers={"X-User-Id": TEST_USER_ID}
        )
        
        if response.status_code != 200:
            pytest.fail(f"Не удалось получить список слов: {response.text}")
        
        words = response.json()
        if not words:
            pytest.skip("Нет доступных слов для тестирования")
        
        # Пробуем получить следующую карточку
        response = client.get(
            "/api/cards/next",
            headers={"X-User-Id": TEST_USER_ID}
        )
        
        # Проверяем, что запрос выполнен успешно
        assert response.status_code == 200, f"Ошибка при получении карточки: {response.text}"
        
        # Получаем данные карточки
        card = response.json()
        print("\nПолучена карточка:")
        print(f"ID: {card.get('id')}")
        print(f"Слово: {card.get('word')}")
        print(f"Перевод: {card.get('translation')}")
        
        # Проверяем, что в ответе есть все необходимые поля
        required_fields = ['id', 'word', 'translation', 'language', 'level']
        for field in required_fields:
            assert field in card, f"В ответе отсутствует обязательное поле: {field}"
        
        print("\nВсе обязательные поля присутствуют")
        
        return card
        
    except Exception as e:
        pytest.fail(f"Ошибка при тестировании API: {e}")
    finally:
        client.close()

def test_get_next_card():
    """Проверяем получение следующей карточки."""
    with TestClient(app) as client:
        response = client.get(
            "/api/cards/next",
            headers={"X-User-Id": TEST_USER_ID}
        )
        
        assert response.status_code == 200, f"Ошибка при получении карточки: {response.text}"
        data = response.json()
        assert "word" in data, "В ответе отсутствует поле 'word'"
        assert "translation" in data, "В ответе отсутствует поле 'translation'"
        
        print(f"\nПолучена карточка:")
        print(f"Слово: {data['word']}")
        print(f"Перевод: {data.get('translation', 'нет перевода')}")

if __name__ == "__main__":
    # Запуск теста вручную
    test_db_connection()
    test_get_next_card()
