#!/usr/bin/env python3
"""
Скрипт для заполнения переводов слова "хочу/want" во всех языках
"""

import json

# Переводы слова "хочу" и предложений "Я хочу воду" на все языки
TRANSLATIONS = {
    "pt": {"word": "quero", "sentence": "Eu ___ água"},
    "ur": {"word": "چاہتا", "sentence": "میں پانی ___ ہوں"},
    "id": {"word": "ingin", "sentence": "Saya ___ air"},
    "ja": {"word": "欲しい", "sentence": "私は水が___"},
    "fa": {"word": "می‌خواهم", "sentence": "من آب ___"},
    "de": {"word": "will", "sentence": "Ich ___ Was<PERSON>"},
    "tr": {"word": "istiyorum", "sentence": "Ben su ___"},
    "ko": {"word": "원해", "sentence": "나는 물을 ___요"},
    "vi": {"word": "muốn", "sentence": "Tôi ___ nước"},
    "it": {"word": "voglio", "sentence": "Io ___ acqua"},
    "th": {"word": "อยาก", "sentence": "ฉัน___น้ำ"},
    "pl": {"word": "chcę", "sentence": "Ja ___ wodę"},
    "uk": {"word": "хочу", "sentence": "Я ___ воду"},
    "my": {"word": "လိုချင်", "sentence": "ကျွန်တော် ရေ ___"},
    "ms": {"word": "mahu", "sentence": "Saya ___ air"},
    "uz": {"word": "xohlayman", "sentence": "Men suv ___"},
    "ne": {"word": "चाहन्छु", "sentence": "म पानी ___"},
    "nl": {"word": "wil", "sentence": "Ik ___ water"},
    "ro": {"word": "vreau", "sentence": "Eu ___ apă"},
    "tl": {"word": "gusto", "sentence": "___ ko ng tubig"},
    "cb": {"word": "gusto", "sentence": "___ ko og tubig"},
    "kk": {"word": "қалаймын", "sentence": "Мен су ___"},
    "sv": {"word": "vill", "sentence": "Jag ___ ha vatten"},
    "da": {"word": "vil", "sentence": "Jeg ___ have vand"},
    "fi": {"word": "haluan", "sentence": "Minä ___ vettä"},
    "no": {"word": "vil", "sentence": "Jeg ___ ha vann"},
    "ka": {"word": "მინდა", "sentence": "მე წყალი ___"}
}

def fill_translations():
    """Заполнить переводы в JSON файле"""
    
    # Читаем файл
    with open('data/words/A0/ultra_core_07.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # Обновляем переводы
    for item in data:
        if isinstance(item, dict) and 'language' in item:
            lang = item['language']
            if lang in TRANSLATIONS:
                translation = TRANSLATIONS[lang]
                item['word'] = translation['word']
                item['examples'][0]['sentence'] = translation['sentence']
                item['examples'][0]['correct_answers'] = [translation['word']]
    
    # Сохраняем файл
    with open('data/words/A0/ultra_core_07.json', 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    
    print("✅ Переводы успешно заполнены!")

if __name__ == "__main__":
    fill_translations()
