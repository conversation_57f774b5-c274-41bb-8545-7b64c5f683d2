#!/usr/bin/env python3
"""
Скрипт для заполнения переводов слова "есть/eat" во всех языках
"""

import json

# Переводы слова "есть" и предложений "Я ем хлеб" на все языки
TRANSLATIONS = {
    "zh": {"word": "吃", "sentence": "我___面包"},
    "hi": {"word": "खाता", "sentence": "मैं रोटी ___"},
    "es": {"word": "como", "sentence": "Yo ___ pan"},
    "pa": {"word": "ਖਾਂਦਾ", "sentence": "ਮੈਂ ਰੋਟੀ ___"},
    "mr": {"word": "खातो", "sentence": "मी भाकरी ___"},
    "ar": {"word": "آكل", "sentence": "أنا ___ الخبز"},
    "bn": {"word": "খাই", "sentence": "আমি রুটি ___"},
    "fr": {"word": "mange", "sentence": "Je ___ du pain"},
    "ru": {"word": "ем", "sentence": "Я ___ хлеб"},
    "pt": {"word": "como", "sentence": "Eu ___ pão"},
    "ur": {"word": "کھاتا", "sentence": "میں روٹی ___"},
    "id": {"word": "makan", "sentence": "Saya ___ roti"},
    "ja": {"word": "食べる", "sentence": "私はパンを___"},
    "fa": {"word": "می‌خورم", "sentence": "من نان ___"},
    "de": {"word": "esse", "sentence": "Ich ___ Brot"},
    "tr": {"word": "yerim", "sentence": "Ben ekmek ___"},
    "ko": {"word": "먹어", "sentence": "나는 빵을 ___"},
    "vi": {"word": "ăn", "sentence": "Tôi ___ bánh mì"},
    "it": {"word": "mangio", "sentence": "Io ___ pane"},
    "th": {"word": "กิน", "sentence": "ฉัน___ขนมปัง"},
    "pl": {"word": "jem", "sentence": "Ja ___ chleb"},
    "uk": {"word": "їм", "sentence": "Я ___ хліб"},
    "my": {"word": "စား", "sentence": "ကျွန်တော် မုန့် ___"},
    "ms": {"word": "makan", "sentence": "Saya ___ roti"},
    "uz": {"word": "yeyapman", "sentence": "Men non ___"},
    "ne": {"word": "खान्छु", "sentence": "म रोटी ___"},
    "nl": {"word": "eet", "sentence": "Ik ___ brood"},
    "ro": {"word": "mănânc", "sentence": "Eu ___ pâine"},
    "tl": {"word": "kumain", "sentence": "___ ako ng tinapay"},
    "cb": {"word": "mokaon", "sentence": "___ ko og tinapay"},
    "kk": {"word": "жеймін", "sentence": "Мен нан ___"},
    "sv": {"word": "äter", "sentence": "Jag ___ bröd"},
    "da": {"word": "spiser", "sentence": "Jeg ___ brød"},
    "fi": {"word": "syön", "sentence": "Minä ___ leipää"},
    "no": {"word": "spiser", "sentence": "Jeg ___ brød"},
    "ka": {"word": "ვჭამ", "sentence": "მე პურს ___"}
}

def fill_translations():
    """Заполнить переводы в JSON файле"""
    
    # Читаем файл
    with open('data/words/A0/ultra_core_08.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # Создаем новый список с английским + всеми переводами
    new_data = [data[0]]  # Оставляем английский
    
    # Добавляем все остальные языки
    for lang, translation in TRANSLATIONS.items():
        new_entry = {
            "concept_id": "550e8400-e29b-41d4-a716-446655440008",
            "word": translation["word"],
            "language": lang,
            "level": "A0",
            "priority": "ultra_core",
            "part_of_speech": "verb",
            "examples": [
                {
                    "sentence": translation["sentence"],
                    "correct_answers": [translation["word"]]
                }
            ],
            "word_info": {
                "frequency": "very_high",
                "difficulty": "beginner",
                "context": "basic_needs"
            }
        }
        new_data.append(new_entry)
    
    # Сохраняем файл
    with open('data/words/A0/ultra_core_08.json', 'w', encoding='utf-8') as f:
        json.dump(new_data, f, ensure_ascii=False, indent=2)
    
    print("✅ Переводы успешно заполнены!")
    print(f"📊 Всего языков: {len(new_data)}")

if __name__ == "__main__":
    fill_translations()
