#!/usr/bin/env python3
"""
Скрипт для обновления существующих записей с новыми интервалами.
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta
from bson import ObjectId

# Добавляем путь к корню проекта
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.spaced_repetition import SpacedRepetitionService, REPETITION_INTERVALS
from app.database import Database

async def update_existing_intervals():
    """Обновляет существующие записи с новыми интервалами."""
    print("🔄 ОБНОВЛЕНИЕ СУЩЕСТВУЮЩИХ ИНТЕРВАЛОВ")
    print("=" * 50)
    
    # Подключаемся к базе данных
    db_instance = Database()
    words_db = db_instance.get_db('words')
    collection = words_db.user_progress
    
    # Выводим новые интервалы
    print("\n📋 Новые интервалы:")
    for i, interval in enumerate(REPETITION_INTERVALS[:5]):
        if interval < 1:
            print(f"   Уровень {i}: {interval * 60:.0f} секунд")
        elif interval < 60:
            print(f"   Уровень {i}: {interval} минут")
        else:
            print(f"   Уровень {i}: {interval / 60:.1f} часов")
    
    # Находим записи с interval_level = 0 (первый интервал)
    print("\n🔍 Поиск записей с interval_level = 0:")
    
    cursor = collection.find({"interval_level": 0})
    records = await cursor.to_list(length=None)
    
    print(f"Найдено {len(records)} записей с interval_level = 0")
    
    if len(records) == 0:
        print("Нет записей для обновления")
        return
    
    # Показываем несколько примеров
    print("\n📝 Примеры записей (до обновления):")
    for i, record in enumerate(records[:3]):
        last_reviewed = record.get("last_reviewed")
        next_review = record.get("next_review")
        if last_reviewed and next_review:
            diff_minutes = (next_review - last_reviewed).total_seconds() / 60
            print(f"   {i+1}. Word ID: {str(record['word_id'])[-8:]} | Интервал: {diff_minutes:.2f} мин")
    
    # Спрашиваем подтверждение
    print(f"\n❓ Обновить {len(records)} записей с новыми интервалами?")
    print("   Это пересчитает next_review для всех записей с interval_level = 0")
    print("   на основе нового интервала 30 секунд (0.5 минуты)")
    
    response = input("Продолжить? (y/N): ").strip().lower()
    if response != 'y':
        print("Отменено пользователем")
        return
    
    # Обновляем записи
    print("\n🔄 Обновление записей...")
    updated_count = 0
    
    for record in records:
        try:
            last_reviewed = record.get("last_reviewed")
            if last_reviewed:
                # Вычисляем новый next_review на основе нового интервала
                new_next_review = last_reviewed + timedelta(minutes=REPETITION_INTERVALS[0])  # 0.5 минуты
                
                # Обновляем запись
                await collection.update_one(
                    {"_id": record["_id"]},
                    {"$set": {"next_review": new_next_review}}
                )
                updated_count += 1
        except Exception as e:
            print(f"❌ Ошибка при обновлении записи {record['_id']}: {e}")
    
    print(f"✅ Обновлено {updated_count} записей")
    
    # Показываем результат
    print("\n📝 Примеры записей (после обновления):")
    cursor = collection.find({"interval_level": 0}).limit(3)
    async for i, record in enumerate(cursor):
        last_reviewed = record.get("last_reviewed")
        next_review = record.get("next_review")
        if last_reviewed and next_review:
            diff_minutes = (next_review - last_reviewed).total_seconds() / 60
            print(f"   {i+1}. Word ID: {str(record['word_id'])[-8:]} | Интервал: {diff_minutes:.2f} мин")
    
    print("\n" + "=" * 50)
    print("🏁 Обновление завершено")

if __name__ == "__main__":
    asyncio.run(update_existing_intervals())
