import json

# Читаем шаблон
with open('data/words/templates/A0_template.json', 'r', encoding='utf-8') as f:
    template = json.load(f)

# Обновляем каждый элемент
for item in template:
    if 'examples' in item and len(item['examples']) > 0:
        example = item['examples'][0]
        if 'alternative_answers' not in example:
            # Добавляем поле alternative_answers после correct_answers
            example['alternative_answers'] = []

# Сохраняем обновленный шаблон
with open('data/words/templates/A0_template.json', 'w', encoding='utf-8') as f:
    json.dump(template, f, ensure_ascii=False, indent=2)

print("Шаблон обновлен: добавлено поле alternative_answers для всех языков")
