from passlib.context import CryptContext
from pymongo import MongoClient
from dotenv import load_dotenv
import os

def update_password():
    # Загружаем переменные окружения из .env файла
    load_dotenv()
    
    # Получаем URL подключения к MongoDB из переменных окружения
    mongo_url = os.getenv("MONGODB_URL")
    db_name = os.getenv("USERS_DATABASE_NAME", "users_db")
    
    # Инициализируем контекст хеширования как в приложении
    pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
    
    try:
        # Подключаемся к MongoDB
        client = MongoClient(mongo_url)
        print("Connected to MongoDB")
        
        db = client[db_name]
        users = db.users
        
        # Генерируем хеш для пароля '111111' используя passlib
        password = '111111'
        hashed_password = pwd_context.hash(password)
        
        print(f"New password hash (passlib): {hashed_password}")
        
        # Обновляем пароль для пользователя <EMAIL>
        result = users.update_one(
            {"email": "<EMAIL>"},
            {"$set": {"hashed_password": hashed_password}}
        )
        
        print(f"Updated {result.modified_count} user(s)")
        
        # Проверяем обновление
        user = users.find_one({"email": "<EMAIL>"}, {"email": 1, "hashed_password": 1, "_id": 0})
        if user and 'hashed_password' in user:
            print("Verification: Password was updated successfully")
            print(f"New hash: {user['hashed_password']}")
            
            # Проверяем, что пароль можно верифицировать
            if pwd_context.verify(password, user['hashed_password']):
                print("Password verification: SUCCESS")
            else:
                print("Password verification: FAILED")
        
    except Exception as e:
        print(f"Error updating password: {e}")
    finally:
        if 'client' in locals():
            client.close()
            print("Disconnected from MongoDB")

if __name__ == "__main__":
    update_password()
