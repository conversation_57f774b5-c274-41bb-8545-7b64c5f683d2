"""
Скрипт для обновления прогресса пользователя.
Устанавливает next_review в текущее время и force_review в True.
"""

import asyncio
from bson import ObjectId
from datetime import datetime, timezone
from dotenv import load_dotenv
import os

from motor.motor_asyncio import AsyncIOMotorClient

# Загружаем переменные окружения из .env файла
load_dotenv()

# Настройки
USER_EMAIL = "<EMAIL>"

async def update_user_progress():
    """Обновляет прогресс пользователя, чтобы слова были доступны для повторения"""
    try:
        # Подключаемся к MongoDB
        client = AsyncIOMotorClient(os.getenv("MONGODB_URL"))
        db = client["word_master"]
        users_db = client["users_db"]
        
        # Находим пользователя
        user = await users_db.users.find_one({"email": USER_EMAIL})
        if not user:
            print(f"❌ Пользователь с email {USER_EMAIL} не найден")
            return
        
        user_id = user["_id"]
        print(f"Найден пользователь: {user['email']} (ID: {user_id})")
        
        # Устанавливаем next_review в текущее время и force_review в True
        now = datetime.now(timezone.utc)
        print(f"Текущее время (UTC): {now}")
        
        # Обновляем записи в user_progress
        result = await db.user_progress.update_many(
            {"user_id": user_id},
            {"$set": {
                "next_review": now,
                "force_review": True
            }}
        )
        
        print(f"\n✅ Обновлено {result.modified_count} записей в user_progress")
        
        # Проверяем обновленные записи
        count = await db.user_progress.count_documents({"user_id": user_id, "next_review": {"$lte": now}})
        print(f"Теперь у пользователя {count} слов доступно для повторения")
        
    except Exception as e:
        print(f"❌ Ошибка: {str(e)}")
    finally:
        if 'client' in locals():
            client.close()

if __name__ == "__main__":
    asyncio.run(update_user_progress())
