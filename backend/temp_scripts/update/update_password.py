import bcrypt
from pymongo import MongoClient
from dotenv import load_dotenv
import os

# Загружаем переменные окружения из .env файла
load_dotenv()

def update_password():
    # Получаем URL подключения к MongoDB из переменных окружения
    mongo_url = os.getenv("MONGODB_URL")
    db_name = os.getenv("USERS_DATABASE_NAME", "users_db")
    
    try:
        # Подключаемся к MongoDB
        client = MongoClient(mongo_url)
        print("Connected to MongoDB")
        
        db = client[db_name]
        users = db.users
        
        # Генерируем хеш для пароля 'test'
        password = 'test'.encode('utf-8')
        salt = bcrypt.gensalt(rounds=12)
        hashed_password = bcrypt.hashpw(password, salt).decode('utf-8')
        
        print(f"New password hash: {hashed_password}")
        
        # Обновляем пароль для пользователя <EMAIL>
        result = users.update_one(
            {"email": "<EMAIL>"},
            {"$set": {"hashed_password": hashed_password}}
        )
        
        print(f"Updated {result.modified_count} user(s)")
        
    except Exception as e:
        print(f"Error updating password: {e}")
    finally:
        if 'client' in locals():
            client.close()
            print("Disconnected from MongoDB")

if __name__ == "__main__":
    update_password()
