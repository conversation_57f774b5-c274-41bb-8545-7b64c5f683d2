#!/usr/bin/env python3
"""
Скрипт для обновления MD файлов - добавление ссылок на файлы концептов
"""

import os
import re
from pathlib import Path

def update_md_file(filepath):
    """Обновляет один MD файл, добавляя ссылку на концепт"""
    
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Извлекаем номер файла из имени
    filename = filepath.name

    # Пробуем разные паттерны
    # Для ultra_core: 0008_A0_ultra_core_08.md
    match = re.match(r'(\d{4})_A0_(ultra_core)_(\d{2})\.md', filename)
    if not match:
        # Для core: 0016_A0_core_01.md
        match = re.match(r'(\d{4})_A0_(core)_(\d{2})\.md', filename)
    if not match:
        # Для extended: 0051_A0_extended_01.md
        match = re.match(r'(\d{4})_A0_(extended)_(\d{2})\.md', filename)

    if not match:
        print(f"❌ Неправильный формат файла: {filename}")
        return False

    word_num, priority, priority_num = match.groups()
    concept_filename = f"{word_num}_A0_{priority}_{priority_num}_concept.json"
    
    # Ищем строку с файлами
    pattern = r'- файлы: ([^\n]+)'
    match = re.search(pattern, content)

    if match:
        current_files = match.group(1)
        # Проверяем, есть ли уже ссылка на концепт
        if '_concept.json' not in current_files:
            # Исправляем названия файлов на новую схему и добавляем концепт
            md_filename = f"{word_num}_A0_{priority}_{priority_num}.md"
            json_filename = f"{word_num}_A0_{priority}_{priority_num}.json"
            new_files = f"{md_filename}, {json_filename}, {concept_filename}"
            content = content.replace(f"- файлы: {current_files}", f"- файлы: {new_files}")
            
            # Ищем строку с концептом и добавляем ссылку на файл
            concept_pattern = r'(\*\*КОНЦЕПТ\*\*: [^\n]+)'
            concept_match = re.search(concept_pattern, content)
            
            if concept_match:
                concept_line = concept_match.group(1)
                new_concept_section = f"{concept_line}\n**Файл концепта**: `backend/data/concepts/A0/{concept_filename}`"
                content = content.replace(concept_line, new_concept_section)
            
            # Сохраняем обновленный файл
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ Обновлен: {filename}")
            return True
        else:
            print(f"⏭️ Уже обновлен: {filename}")
            return False
    else:
        print(f"❌ Не найдена строка с файлами в: {filename}")
        return False

def main():
    """Основная функция обновления всех MD файлов"""
    
    md_dir = Path("data/words/temp_md")
    
    if not md_dir.exists():
        print(f"❌ Папка не найдена: {md_dir}")
        return
    
    # Находим все MD файлы с новой схемой именования
    md_files = list(md_dir.glob("*_A0_*_*.md"))
    
    if not md_files:
        print("❌ Не найдены MD файлы с новой схемой именования")
        return
    
    print(f"🔍 Найдено {len(md_files)} MD файлов")
    
    updated_count = 0
    
    for md_file in sorted(md_files):
        if update_md_file(md_file):
            updated_count += 1
    
    print(f"\n🎉 Обновлено {updated_count} из {len(md_files)} файлов!")

if __name__ == "__main__":
    main()
