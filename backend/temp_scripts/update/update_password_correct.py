import bcrypt
from pymongo import MongoClient
from dotenv import load_dotenv
import os

def update_password():
    # Загружаем переменные окружения из .env файла
    load_dotenv()
    
    # Получаем URL подключения к MongoDB из переменных окружения
    mongo_url = os.getenv("MONGODB_URL")
    db_name = os.getenv("USERS_DATABASE_NAME", "users_db")
    
    try:
        # Подключаемся к MongoDB
        client = MongoClient(mongo_url)
        print("Connected to MongoDB")
        
        db = client[db_name]
        users = db.users
        
        # Генерируем хеш для пароля '111111'
        password = '111111'.encode('utf-8')
        salt = bcrypt.gensalt(rounds=12)
        hashed_password = bcrypt.hashpw(password, salt).decode('utf-8')
        
        print(f"New password hash: {hashed_password}")
        
        # Обновляем пароль для пользователя <EMAIL>
        result = users.update_one(
            {"email": "<EMAIL>"},
            {"$set": {"hashed_password": hashed_password}}
        )
        
        print(f"Updated {result.modified_count} user(s)")
        
        # Проверяем обновление
        user = users.find_one({"email": "<EMAIL>"}, {"email": 1, "hashed_password": 1, "_id": 0})
        if user and 'hashed_password' in user:
            print("Verification: Password was updated successfully")
            print(f"New hash: {user['hashed_password']}")
        
    except Exception as e:
        print(f"Error updating password: {e}")
    finally:
        if 'client' in locals():
            client.close()
            print("Disconnected from MongoDB")

if __name__ == "__main__":
    update_password()
