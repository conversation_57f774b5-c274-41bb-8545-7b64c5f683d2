const bcrypt = require('bcrypt');
const { MongoClient } = require('mongodb');

async function updatePassword() {
  const uri = 'mongodb+srv://emir:<EMAIL>/users_db';
  const client = new MongoClient(uri);

  try {
    await client.connect();
    console.log('Connected to MongoDB');

    const database = client.db('users_db');
    const users = database.collection('users');

    // Генерируем хеш для пароля '111111'
    const salt = await bcrypt.genSalt(12);
    const hashedPassword = await bcrypt.hash('111111', salt);
    
    console.log('New password hash:', hashedPassword);

    // Обновляем пароль для пользователя <EMAIL>
    const result = await users.updateOne(
      { email: '<EMAIL>' },
      { $set: { password: hashedPassword } }
    );

    console.log(`Updated ${result.modifiedCount} user(s)`);
  } catch (error) {
    console.error('Error updating password:', error);
  } finally {
    await client.close();
    console.log('Disconnected from MongoDB');
  }
}

updatePassword();
