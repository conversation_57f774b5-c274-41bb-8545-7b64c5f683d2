import os
from pymongo import MongoClient
from dotenv import load_dotenv
from pathlib import Path

def check_words_collection():
    # Загружаем переменные окружения
    env_path = Path(__file__).parent / '.env'
    if env_path.exists():
        load_dotenv(dotenv_path=env_path, override=True)
    
    # Выводим все переменные окружения (для отладки)
    print("\nТекущие переменные окружения:")
    for key, value in os.environ.items():
        if 'MONGODB' in key or 'DATABASE' in key or 'DB' in key:
            print(f"{key} = {value}")
    
    # Получаем настройки подключения
    mongo_uri = os.getenv("MONGODB_URL")
    
    # Принудительно используем test_word_master
    words_db_name = "test_word_master"
    
    if not mongo_uri:
        print("Ошибка: Не задана переменная MONGODB_URL в .env файле")
        return False
    
    print(f"Подключение к MongoDB: {mongo_uri}")
    print(f"База данных слов: {words_db_name}")
    
    try:
        # Подключаемся к MongoDB
        client = MongoClient(mongo_uri, serverSelectionTimeoutMS=5000)
        
        # Проверяем соединение
        client.admin.command('ping')
        print("✅ Успешное подключение к MongoDB")
        
        # Проверяем базу данных слов
        if words_db_name in client.list_database_names():
            db = client[words_db_name]
            print(f"\nКоллекции в {words_db_name}:")
            for col in db.list_collection_names():
                count = db[col].count_documents({})
                print(f"- {col} ({count} документов)")
                
                # Показываем первые 3 документа в коллекции
                if count > 0:
                    print("  Примеры документов:")
                    for doc in db[col].find().limit(3):
                        print(f"  - {doc.get('word', 'No word field')} ({doc.get('_id')})")
        else:
            print(f"\nБаза данных {words_db_name} не найдена")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при подключении к MongoDB: {e}")
        return False
    finally:
        if 'client' in locals():
            client.close()

if __name__ == "__main__":
    check_words_collection()
