const bcrypt = require('bcrypt');

const hash = '$2b$12$IbGOseNGbi6nCSmV0xgMSuJH3ljQWm6MDimjgCR8/C05N4HgSZVda';
const testPasswords = [
  'password', '123456', 'test', 'test123', 'qwerty', 'admin',
  '12345678', '123456789', '12345', '1234567', '1234567890',
  'password1', '123123', '111111', 'abc123', 'qwerty123',
  '1q2w3e4r', 'admin123', 'qwertyuiop', '654321', '555555',
  'lovely', '7777777', 'welcome', '888888', 'princess',
  'dragon', '1234', 'login', 'solo', 'passw0rd', 'master'
];

async function checkPasswords() {
  for (const password of testPasswords) {
    const match = await bcrypt.compare(password, hash);
    if (match) {
      console.log(`Найден пароль: ${password}`);
      return;
    }
  }
  console.log('Пароль не найден среди тестовых вариантов');
}

checkPasswords();
