"""
Скрипт для проверки переменных окружения в тестах.
"""
import os
import sys
from dotenv import load_dotenv
from pathlib import Path

def main():
    # Показываем текущий рабочий каталог
    print(f"Текущий рабочий каталог: {os.getcwd()}")
    
    # Пробуем загрузить .env файлы
    env_paths = [
        Path.cwd() / '.env',
        Path.cwd() / '.env.test',
        Path.cwd().parent / '.env',
        Path.cwd().parent / '.env.test',
    ]
    
    loaded_envs = []
    for path in env_paths:
        if path.exists():
            load_dotenv(path, override=True)
            loaded_envs.append(str(path))
    
    print("\nЗагруженные .env файлы:", loaded_envs)
    
    # Выводим все переменные окружения, связанные с базой данных
    print("\nПеременные окружения:")
    for key, value in os.environ.items():
        if 'MONGO' in key or 'DATABASE' in key or 'DB' in key or 'ENV' in key:
            print(f"{key} = {value}")

if __name__ == "__main__":
    main()
