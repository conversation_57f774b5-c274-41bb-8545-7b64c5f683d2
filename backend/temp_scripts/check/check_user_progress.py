import os
from pymongo import MongoClient
from dotenv import load_dotenv
from bson import ObjectId

def check_user_progress():
    # Загружаем переменные окружения
    load_dotenv()

    # Подключаемся к MongoDB
    client = MongoClient(os.getenv("MONGODB_URL"))
    db = client['word_master']  # Используем базу данных word_master

    try:
        # Получаем коллекцию user_progress
        user_progress = db.user_progress

        # Получаем общую статистику
        total_count = user_progress.count_documents({})
        print(f"Всего документов в коллекции user_progress: {total_count}")

        # Проверяем документы без interval_level
        missing_interval = user_progress.count_documents({"interval_level": {"$exists": False}})
        print(f"Документов без поля interval_level: {missing_interval}")

        # Проверяем документы с interval_level = null
        null_interval = user_progress.count_documents({"interval_level": None})
        print(f"Документов с interval_level = null: {null_interval}")

        # Проверяем документы с is_learned = true но без interval_level
        learned_no_interval = user_progress.count_documents({
            "is_learned": True,
            "interval_level": {"$exists": False}
        })
        print(f"Документов с is_learned=true но без interval_level: {learned_no_interval}")

        # Показываем примеры проблемных документов
        print("\n=== ПРОБЛЕМНЫЕ ДОКУМЕНТЫ ===")

        # Документы без interval_level
        if missing_interval > 0:
            print(f"\nПримеры документов БЕЗ interval_level ({missing_interval} шт.):")
            for doc in user_progress.find({"interval_level": {"$exists": False}}).limit(5):
                print(f"  - user_id: {doc.get('user_id')}")
                print(f"    word_id: {doc.get('word_id')}")
                print(f"    is_learned: {doc.get('is_learned')}")
                print(f"    correct_answers: {doc.get('correct_answers')}")
                print(f"    incorrect_answers: {doc.get('incorrect_answers')}")
                print(f"    created_at: {doc.get('created_at')}")
                print()

        # Документы с is_learned=true но без interval_level
        if learned_no_interval > 0:
            print(f"\nПримеры ВЫУЧЕННЫХ слов БЕЗ interval_level ({learned_no_interval} шт.):")
            for doc in user_progress.find({
                "is_learned": True,
                "interval_level": {"$exists": False}
            }).limit(5):
                print(f"  - user_id: {doc.get('user_id')}")
                print(f"    word_id: {doc.get('word_id')}")
                print(f"    is_learned: {doc.get('is_learned')}")
                print(f"    correct_answers: {doc.get('correct_answers')}")
                print(f"    incorrect_answers: {doc.get('incorrect_answers')}")
                print()

        # Показываем нормальные документы для сравнения
        print("\n=== НОРМАЛЬНЫЕ ДОКУМЕНТЫ (для сравнения) ===")
        normal_docs = user_progress.find({"interval_level": {"$exists": True}}).limit(3)
        for doc in normal_docs:
            print(f"  - user_id: {doc.get('user_id')}")
            print(f"    word_id: {doc.get('word_id')}")
            print(f"    interval_level: {doc.get('interval_level')}")
            print(f"    is_learned: {doc.get('is_learned')}")
            print(f"    correct_answers: {doc.get('correct_answers')}")
            print(f"    incorrect_answers: {doc.get('incorrect_answers')}")
            print()

        # Статистика по interval_level
        print("\n=== СТАТИСТИКА ПО INTERVAL_LEVEL ===")
        pipeline = [
            {"$match": {"interval_level": {"$exists": True}}},
            {"$group": {"_id": "$interval_level", "count": {"$sum": 1}}},
            {"$sort": {"_id": 1}}
        ]

        for result in user_progress.aggregate(pipeline):
            level = result["_id"]
            count = result["count"]
            print(f"  interval_level {level}: {count} документов")

        # Получаем первую запись для анализа структуры
        first_record = user_progress.find_one()

        if first_record:
            print("\n=== СТРУКТУРА ДОКУМЕНТА ===")
            for key, value in first_record.items():
                print(f"{key}: {value} ({type(value).__name__})")
        else:
            print("Коллекция user_progress пуста")
            
    except Exception as e:
        print(f"Ошибка при проверке коллекции user_progress: {e}")
    finally:
        client.close()

if __name__ == "__main__":
    check_user_progress()
