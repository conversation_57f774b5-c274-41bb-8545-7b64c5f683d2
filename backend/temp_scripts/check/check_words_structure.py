import os
from pymongo import MongoClient
from dotenv import load_dotenv
from pathlib import Path
from pprint import pprint

def get_field_types(doc, parent_key=''):
    """Рекурсивно собирает типы полей в документе"""
    field_types = {}
    
    if not isinstance(doc, dict):
        return {parent_key: type(doc).__name__}
    
    for key, value in doc.items():
        full_key = f"{parent_key}.{key}" if parent_key else key
        
        if isinstance(value, dict):
            nested_types = get_field_types(value, full_key)
            field_types.update(nested_types)
        elif isinstance(value, list) and value and isinstance(value[0], dict):
            # Обрабатываем список словарей
            list_item_types = {}
            for i, item in enumerate(value[:3]):  # Проверяем первые 3 элемента
                item_types = get_field_types(item, f"{full_key}[{i}]")
                # Объединяем типы для всех элементов списка
                for k, v in item_types.items():
                    if k in list_item_types:
                        if v != list_item_types[k]:
                            list_item_types[k] = f"{list_item_types[k]}|{v}"
                    else:
                        list_item_types[k] = v
            field_types.update(list_item_types)
        else:
            field_types[full_key] = type(value).__name__
    
    return field_types

def check_words_structure():
    # Загружаем переменные окружения
    env_path = Path(__file__).parent / '.env'
    if env_path.exists():
        load_dotenv(dotenv_path=env_path)
    
    mongo_uri = os.getenv("MONGODB_URL")
    words_db_name = os.getenv("WORDS_DATABASE_NAME", "word_master")
    
    if not mongo_uri:
        print("Ошибка: Не задана переменная MONGODB_URL в .env файле")
        return
    
    try:
        # Подключаемся к MongoDB
        client = MongoClient(mongo_uri, serverSelectionTimeoutMS=5000)
        db = client[words_db_name]
        
        # Получаем количество документов в коллекции words
        words_count = db.words.count_documents({})
        print(f"Всего слов в коллекции: {words_count}")
        
        if words_count == 0:
            print("Коллекция words пуста")
            return
        
        # Получаем первый документ для анализа структуры
        first_word = db.words.find_one()
        
        print("\nСтруктура документа:")
        pprint(first_word)
        
        # Анализируем типы полей
        print("\nТипы полей:")
        field_types = get_field_types(first_word)
        for field, type_name in field_types.items():
            print(f"{field}: {type_name}")
        
        # Проверяем наличие обязательных полей
        required_fields = ['word', 'translation', 'language']
        missing_fields = [field for field in required_fields if field not in first_word]
        
        if missing_fields:
            print(f"\n⚠️ Отсутствуют обязательные поля: {', '.join(missing_fields)}")
        else:
            print("\n✅ Все обязательные поля присутствуют")
        
        # Проверяем индексы
        indexes = db.words.index_information()
        print("\nИндексы коллекции words:")
        for name, index in indexes.items():
            print(f"- {name}: {index['key']}")
        
    except Exception as e:
        print(f"❌ Ошибка при проверке структуры коллекции words: {e}")
    finally:
        if 'client' in locals():
            client.close()

if __name__ == "__main__":
    check_words_structure()
