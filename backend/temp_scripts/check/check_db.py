import asyncio
import os
from motor.motor_asyncio import AsyncIOMotorClient
from dotenv import load_dotenv
from bson import ObjectId

# Загрузка переменных окружения
load_dotenv()

async def check_db():
    # Подключение к MongoDB
    mongo_url = os.getenv('MONGODB_URL')
    print(f"Подключение к MongoDB по URL: {mongo_url}")
    
    try:
        client = AsyncIOMotorClient(mongo_url)
        # Проверяем соединение
        await client.admin.command('ping')
        print("✓ Успешное подключение к MongoDB")
        
        # Получаем список баз данных
        dbs = await client.list_database_names()
        print(f"\nДоступные базы данных: {dbs}")
        
        # Проверяем базу данных word_master
        db_name = os.getenv('DATABASE_NAME', 'word_master')
        db = client[db_name]
        print(f"\nПроверяем базу данных: {db_name}")
        
        # Получаем количество документов в коллекциях
        collections = await db.list_collection_names()
        print(f"Коллекции в базе {db_name}: {collections}")
        
        for collection_name in collections:
            count = await db[collection_name].count_documents({})
            print(f"  - {collection_name}: {count} документов")
        
        # Проверяем наличие тестового пользователя
        test_user = await db.users.find_one({"email": "<EMAIL>"})
        if test_user:
            print(f"\nНайден тестовый пользователь:")
            print(f"ID: {test_user['_id']}")
            print(f"Email: {test_user['email']}")
            print(f"Active: {test_user.get('is_active', False)}")
        else:
            print("\nТестовый пользователь не найден")
        
        # Получаем первые 5 слов
        words = await db.words.find().to_list(length=5)
        if words:
            print("\nПервые 5 слов в базе:")
            print("=" * 50)
            for word in words:
                print(f"Слово: {word.get('word', 'N/A')}")
                print(f"Перевод: {word.get('translation', 'N/A')}")
                print("-" * 30)
        else:
            print("\nВ базе нет слов")
            
    except Exception as e:
        print(f"❌ Ошибка при подключении к MongoDB: {e}")
    finally:
        if 'client' in locals():
            client.close()
            print("\nСоединение с базой данных закрыто")
    
    print("\n" + "=" * 50)
    print("Проверка завершена")

if __name__ == "__main__":
    asyncio.run(check_db())
