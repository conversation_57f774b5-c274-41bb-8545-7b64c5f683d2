from pymongo import MongoClient
from dotenv import load_dotenv
import os

def check_user_structure():
    # Загружаем переменные окружения из .env файла
    load_dotenv()
    
    # Получаем URL подключения к MongoDB из переменных окружения
    mongo_url = os.getenv("MONGODB_URL")
    db_name = os.getenv("USERS_DATABASE_NAME", "users_db")
    
    try:
        # Подключаемся к MongoDB
        client = MongoClient(mongo_url)
        print("Connected to MongoDB")
        
        db = client[db_name]
        users = db.users
        
        # Находим первого пользователя
        first_user = users.find_one({}, {"_id": 0})
        
        if first_user:
            print("First user structure:")
            for key, value in first_user.items():
                print(f"{key}: {value}")
        else:
            print("No users found in the database")
            
    except Exception as e:
        print(f"Error checking user structure: {e}")
    finally:
        if 'client' in locals():
            client.close()
            print("Disconnected from MongoDB")

if __name__ == "__main__":
    check_user_structure()
