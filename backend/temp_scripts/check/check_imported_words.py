"""
Скрипт для проверки импортированных слов в базе данных.
"""
import os
import asyncio
from motor.motor_asyncio import AsyncIOMotorClient
from dotenv import load_dotenv
from pathlib import Path

# Загружаем переменные окружения
load_dotenv()

# Настройки подключения к MongoDB
MONGODB_URL = os.getenv('MONGODB_URL')
WORDS_DATABASE_NAME = os.getenv('WORDS_DATABASE_NAME', 'word_master')

async def check_imported_words():
    """Проверяет импортированные слова в базе данных."""
    try:
        # Подключаемся к MongoDB
        client = AsyncIOMotorClient(MONGODB_URL)
        db = client[WORDS_DATABASE_NAME]
        
        # Получаем количество документов в коллекции words
        count = await db.words.count_documents({})
        print(f"Всего слов в базе данных: {count}")
        
        # Получаем несколько примеров слов
        print("\nПримеры слов:")
        cursor = db.words.find().limit(5)
        async for word in cursor:
            print(f"- {word.get('word')} ({word.get('language')}, {word.get('level')})")
        
    except Exception as e:
        print(f"Ошибка при подключении к базе данных: {e}")
    finally:
        if 'client' in locals():
            client.close()

if __name__ == "__main__":
    asyncio.run(check_imported_words())
