#!/usr/bin/env python3
"""
Проверяем состояние конкретного слова в базе данных.
"""

import asyncio
import sys
import os
from bson import ObjectId

# Добавляем путь к корню проекта
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import Database

async def check_word_state():
    """Проверяем состояние слова 'asa' в базе данных."""
    
    print("🔍 Проверка состояния слова 'asa'")
    print("=" * 50)
    
    # Подключаемся к базе данных
    db_instance = Database()
    await db_instance.connect_to_db()
    
    # Получаем коллекции
    words_db = db_instance.get_db('words')
    words_collection = words_db.words
    progress_collection = words_db.user_progress
    
    try:
        # Ищем слово "asa"
        word = await words_collection.find_one({"word": "asa", "language": "cb"})
        
        if word:
            print(f"📝 Слово найдено:")
            print(f"   _id: {word.get('_id')}")
            print(f"   word: {word.get('word')}")
            print(f"   language: {word.get('language')}")
            print(f"   concept_id: {word.get('concept_id')}")
            
            word_id = word.get('_id')
            
            # Ищем прогресс для этого слова у всех пользователей
            progress_cursor = progress_collection.find({"word_id": word_id})
            progress_list = await progress_cursor.to_list(None)
            
            print(f"\n📊 Найдено {len(progress_list)} записей прогресса:")
            
            for i, progress in enumerate(progress_list, 1):
                print(f"\n   Запись {i}:")
                print(f"     user_id: {progress.get('user_id')}")
                print(f"     interval_level: {progress.get('interval_level')}")
                print(f"     force_review: {progress.get('force_review')}")
                print(f"     is_learned: {progress.get('is_learned')}")
                print(f"     correct_answers: {progress.get('correct_answers')}")
                print(f"     incorrect_answers: {progress.get('incorrect_answers')}")
                print(f"     next_review: {progress.get('next_review')}")
                print(f"     created_at: {progress.get('created_at')}")
                print(f"     updated_at: {progress.get('updated_at')}")
                
                # Проверяем, есть ли проблемные записи
                if progress.get('force_review') == True:
                    print(f"     ⚠️  ПРОБЛЕМА: force_review = True!")
                    
                if progress.get('interval_level') == -1 and progress.get('correct_answers', 0) == 0:
                    print(f"     ℹ️  Это новое слово (не было ответов)")
                    
        else:
            print("❌ Слово 'asa' не найдено в базе данных")
            
        # Также проверим пользователя из логов
        user_id_str = "684c7900e9e41d7a3b5c0d62"  # Из логов
        try:
            user_id = ObjectId(user_id_str)
            print(f"\n👤 Проверяем прогресс пользователя {user_id_str}:")
            
            user_progress = await progress_collection.find({"user_id": user_id}).to_list(None)
            print(f"   Всего слов в прогрессе: {len(user_progress)}")
            
            # Ищем конкретно слово "asa"
            if word:
                asa_progress = await progress_collection.find_one({
                    "user_id": user_id,
                    "word_id": word.get('_id')
                })
                
                if asa_progress:
                    print(f"\n   📝 Прогресс по слову 'asa':")
                    print(f"     interval_level: {asa_progress.get('interval_level')}")
                    print(f"     force_review: {asa_progress.get('force_review')}")
                    print(f"     is_learned: {asa_progress.get('is_learned')}")
                    print(f"     correct_answers: {asa_progress.get('correct_answers')}")
                    print(f"     incorrect_answers: {asa_progress.get('incorrect_answers')}")
                else:
                    print(f"   ℹ️  У пользователя нет прогресса по слову 'asa'")
                    
        except Exception as e:
            print(f"❌ Ошибка при проверке пользователя: {e}")
            
    except Exception as e:
        print(f"❌ ОШИБКА: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 50)
    print("🏁 Проверка завершена")

if __name__ == "__main__":
    asyncio.run(check_word_state())
