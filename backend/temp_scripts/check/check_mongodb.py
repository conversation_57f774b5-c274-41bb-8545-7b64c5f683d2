"""
Скрипт для проверки структуры базы данных MongoDB
"""
import asyncio
import os
from dotenv import load_dotenv
from motor.motor_asyncio import AsyncIOMotorClient

# Загружаем переменные окружения из .env файла
load_dotenv()

async def check_database_structure():
    """Проверяем структуру базы данных"""
    try:
        # Подключаемся к MongoDB Atlas
        client = AsyncIOMotorClient(os.getenv("MONGODB_URL"))
        
        # Получаем список всех баз данных
        db_names = await client.list_database_names()
        print("Доступные базы данных:", db_names)
        
        # Для каждой базы данных выводим коллекции
        for db_name in db_names:
            if db_name not in ['admin', 'local', 'config']:  # Пропускаем служебные БД
                db = client[db_name]
                collections = await db.list_collection_names()
                print(f"\nБаза данных: {db_name}")
                print("Коллекции:", collections)
                
                # Для каждой коллекции выводим количество документов
                for collection_name in collections:
                    collection = db[collection_name]
                    count = await collection.count_documents({})
                    print(f"  - {collection_name}: {count} документов")
                    
                    # Если коллекция не пустая, выводим первый документ
                    if count > 0:
                        doc = await collection.find_one()
                        print(f"    Пример документа: {doc}")
    
    except Exception as e:
        print(f"Ошибка: {e}")
    finally:
        client.close()

if __name__ == "__main__":
    asyncio.run(check_database_structure())
