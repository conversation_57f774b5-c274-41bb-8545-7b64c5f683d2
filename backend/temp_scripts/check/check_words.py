import asyncio
from motor.motor_asyncio import AsyncIOMotorClient
from pymongo import MongoClient
from bson import ObjectId
import os
from dotenv import load_dotenv

# Загружаем переменные окружения
load_dotenv()

# Параметры подключения к MongoDB
MONGODB_URL = os.getenv("MONGODB_URL")
WORDS_DB_NAME = os.getenv("WORDS_DATABASE_NAME", "word_master")

async def check_words():
    # Подключаемся к MongoDB
    client = AsyncIOMotorClient(MONGODB_URL)
    db = client[WORDS_DB_NAME]
    
    # Получаем первое слово из коллекции
    word = await db.words.find_one()
    
    if word:
        print("Найдено слово:")
        print(f"ID: {word['_id']}")
        print(f"Текст: {word.get('text', 'N/A')}")
        print(f"Язык: {word.get('language', 'N/A')}")
        print(f"Переводы: {word.get('translations', [])}")
        print(f"Примеры: {word.get('examples', [])}")
        print(f"Теги: {word.get('tags', [])}")
    else:
        print("В коллекции words нет документов")
    
    # Закрываем соединение
    client.close()

if __name__ == "__main__":
    asyncio.run(check_words())
