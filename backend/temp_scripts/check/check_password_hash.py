from pymongo import MongoClient
from dotenv import load_dotenv
import os

def check_password_hash():
    # Загружаем переменные окружения из .env файла
    load_dotenv()
    
    # Получаем URL подключения к MongoDB из переменных окружения
    mongo_url = os.getenv("MONGODB_URL")
    db_name = os.getenv("USERS_DATABASE_NAME", "users_db")
    
    try:
        # Подключаемся к MongoDB
        client = MongoClient(mongo_url)
        print("Connected to MongoDB")
        
        db = client[db_name]
        users = db.users
        
        # Находим пользователя <EMAIL>
        user = users.find_one({"email": "<EMAIL>"}, {"email": 1, "password": 1, "_id": 0})
        
        if user:
            print("User found:")
            print(f"Email: {user['email']}")
            print(f"Password hash: {user['password']}")
            
            # Проверяем, соответствует ли хеш паролю '111111'
            import bcrypt
            password_matches = bcrypt.checkpw(
                '111111'.encode('utf-8'),
                user['password'].encode('utf-8')
            )
            print(f"Password '111111' matches hash: {password_matches}")
        else:
            print("User not found")
            
    except Exception as e:
        print(f"Error checking password: {e}")
    finally:
        if 'client' in locals():
            client.close()
            print("Disconnected from MongoDB")

if __name__ == "__main__":
    check_password_hash()
