from passlib.context import CryptContext
from pymongo import MongoClient
import os
from dotenv import load_dotenv

# Загружаем переменные окружения
load_dotenv()

# Параметры подключения к MongoDB
MONGODB_URL = os.getenv("MONGODB_URL")
USERS_DB_NAME = os.getenv("USERS_DATABASE_NAME", "users_db")

# Инициализация клиента MongoDB
client = MongoClient(MONGODB_URL)
db = client[USERS_DB_NAME]

# Инициализация контекста для хеширования паролей
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def check_user(email: str, password: str):
    """Проверяет пользователя по email и паролю"""
    user = db.users.find_one({"email": email})
    if not user:
        print(f"Пользователь с email {email} не найден")
        return False
    
    print(f"Найден пользователь: {user['email']}")
    print(f"Хеш пароля: {user['hashed_password']}")
    
    # Проверяем пароль
    is_valid = pwd_context.verify(password, user['hashed_password'])
    print(f"Пароль {'верный' if is_valid else 'неверный'}")
    
    return is_valid

def reset_password(email: str, new_password: str):
    """Сбрасывает пароль пользователя"""
    hashed_password = pwd_context.hash(new_password)
    result = db.users.update_one(
        {"email": email},
        {"$set": {"hashed_password": hashed_password}}
    )
    
    if result.matched_count > 0:
        print(f"Пароль для пользователя {email} успешно обновлен")
        return True
    else:
        print(f"Пользователь с email {email} не найден")
        return False

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 2:
        print("Использование:")
        print("  python check_user.py check <email> <password> - проверить пользователя")
        print("  python check_user.py reset <email> <new_password> - сбросить пароль")
        sys.exit(1)
    
    command = sys.argv[1]
    
    if command == "check" and len(sys.argv) == 4:
        email = sys.argv[2]
        password = sys.argv[3]
        check_user(email, password)
    elif command == "reset" and len(sys.argv) == 4:
        email = sys.argv[2]
        new_password = sys.argv[3]
        reset_password(email, new_password)
    else:
        print("Неверные аргументы")
        print("Использование:")
        print("  python check_user.py check <email> <password> - проверить пользователя")
        print("  python check_user.py reset <email> <new_password> - сбросить пароль")
