import os
from pymongo import MongoClient
from dotenv import load_dotenv
from pathlib import Path

def check_connection():
    # Загружаем переменные окружения
    env_path = Path(__file__).parent / '.env'
    if env_path.exists():
        load_dotenv(dotenv_path=env_path)
    
    # Получаем настройки подключения
    mongo_uri = os.getenv("MONGODB_URL")
    users_db = os.getenv("USERS_DATABASE_NAME", "users_db")
    words_db = os.getenv("WORDS_DATABASE_NAME", "words_db")
    
    if not mongo_uri:
        print("Ошибка: Не задана переменная MONGODB_URL в .env файле")
        return False
    
    print(f"Подключение к MongoDB: {mongo_uri}")
    print(f"База данных пользователей: {users_db}")
    print(f"База данных слов: {words_db}")
    
    try:
        # Подключаемся к MongoDB
        client = MongoClient(mongo_uri, serverSelectionTimeoutMS=5000)
        
        # Проверяем соединение
        client.admin.command('ping')
        print("✅ Успешное подключение к MongoDB")
        
        # Получаем список баз данных
        print("\nДоступные базы данных:")
        for db_name in client.list_database_names():
            print(f"- {db_name}")
        
        # Проверяем базу данных пользователей
        if users_db in client.list_database_names():
            db = client[users_db]
            print(f"\nКоллекции в {users_db}:")
            for col in db.list_collection_names():
                print(f"- {col} ({db[col].count_documents({})} документов)")
        
        # Проверяем базу данных слов
        if words_db in client.list_database_names():
            db = client[words_db]
            print(f"\nКоллекции в {words_db}:")
            for col in db.list_collection_names():
                print(f"- {col} ({db[col].count_documents({})} документов)")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка подключения к MongoDB: {e}")
        return False
    finally:
        if 'client' in locals():
            client.close()

if __name__ == "__main__":
    check_connection()
