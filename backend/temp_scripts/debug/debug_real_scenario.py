#!/usr/bin/env python3
"""
Детальное исследование реального сценария пользователя.
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta
from bson import ObjectId

# Добавляем путь к корню проекта
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import Database

async def debug_real_scenario():
    """Детальное исследование реального сценария."""
    
    print("🔍 ДЕТАЛЬНОЕ ИССЛЕДОВАНИЕ РЕАЛЬНОГО СЦЕНАРИЯ")
    print("=" * 70)
    
    # Подключаемся к базе данных
    db_instance = Database()
    await db_instance.connect_to_db()
    
    # Импортируем сервис
    from app.services.spaced_repetition import spaced_repetition_service
    
    # Получаем коллекции
    words_db = db_instance.get_db('words')
    progress_collection = words_db.user_progress
    
    # Создаем тестового пользователя
    user_id = ObjectId()
    print(f"👤 Тестовый пользователь: {user_id}")
    
    try:
        print("\n" + "="*50)
        print("ЭТАП 1: Получаем новое слово")
        print("="*50)
        
        # 1. Получаем новое слово
        word1 = await spaced_repetition_service.get_next_word(user_id, target_lang="cb")
        if not word1:
            print("❌ Не удалось получить новое слово")
            return
            
        word_text = word1.get('word')
        word_id = ObjectId(word1.get('word_id'))
        
        print(f"✅ Получено слово: {word_text}")
        print(f"📋 word_id: {word_id}")
        print(f"📋 is_new: {word1.get('is_new')}")
        print(f"📋 interval_level: {word1.get('interval_level')}")
        
        # Проверяем состояние в БД
        progress1 = await progress_collection.find_one({"user_id": user_id, "word_id": word_id})
        print(f"\n📊 Состояние в БД после получения:")
        print(f"   interval_level: {progress1.get('interval_level')}")
        print(f"   force_review: {progress1.get('force_review')}")
        print(f"   last_reviewed: {progress1.get('last_reviewed')}")
        print(f"   next_review: {progress1.get('next_review')}")
        
        print("\n" + "="*50)
        print("ЭТАП 2: Неправильный ответ")
        print("="*50)
        
        # 2. Неправильный ответ
        result1 = await spaced_repetition_service.process_answer(user_id, word_id, False)
        print(f"✅ Неправильный ответ обработан")
        print(f"📋 interval_level: {result1.get('interval_level')}")
        print(f"📋 force_review: {result1.get('force_review')}")
        print(f"📋 last_reviewed: {result1.get('last_reviewed')}")
        print(f"📋 next_review: {result1.get('next_review')}")
        
        print("\n" + "="*50)
        print("ЭТАП 3: Правильный ответ")
        print("="*50)
        
        # 3. Правильный ответ
        result2 = await spaced_repetition_service.process_answer(user_id, word_id, True)
        print(f"✅ Правильный ответ обработан")
        print(f"📋 interval_level: {result2.get('interval_level')}")
        print(f"📋 force_review: {result2.get('force_review')}")
        print(f"📋 last_reviewed: {result2.get('last_reviewed')}")
        print(f"📋 next_review: {result2.get('next_review')}")
        print(f"📋 is_learned: {result2.get('is_learned')}")
        
        # Проверяем состояние в БД
        progress2 = await progress_collection.find_one({"user_id": user_id, "word_id": word_id})
        print(f"\n📊 Состояние в БД после правильного ответа:")
        print(f"   interval_level: {progress2.get('interval_level')}")
        print(f"   force_review: {progress2.get('force_review')}")
        print(f"   last_reviewed: {progress2.get('last_reviewed')}")
        print(f"   next_review: {progress2.get('next_review')}")
        print(f"   is_learned: {progress2.get('is_learned')}")
        
        print("\n" + "="*50)
        print("ЭТАП 4: Проверяем через 1 минуту (имитация)")
        print("="*50)
        
        # 4. Имитируем прошедшее время (1 минута)
        now_plus_1min = datetime.utcnow() + timedelta(minutes=1, seconds=10)
        print(f"🕐 Имитируем время: {now_plus_1min}")
        
        # Проверяем, что вернет активная очередь
        print("\n🔍 Проверяем активную очередь через 1+ минуту:")
        
        # Временно изменяем время в БД для имитации
        await progress_collection.update_one(
            {"user_id": user_id, "word_id": word_id},
            {"$set": {"next_review": now_plus_1min - timedelta(seconds=30)}}  # Делаем доступным
        )
        
        active_word = await spaced_repetition_service._get_next_active_word(user_id, target_lang="cb")
        
        if active_word:
            print(f"❌ ПРОБЛЕМА: Активная очередь вернула слово: {active_word.get('word')}")
            print(f"📋 last_reviewed: {active_word.get('last_reviewed')}")
            print(f"📋 next_review: {active_word.get('next_review')}")
        else:
            print(f"✅ Активная очередь пуста (слово исключено)")
            
        print("\n" + "="*50)
        print("ЭТАП 5: Проверяем через 3 минуты (имитация)")
        print("="*50)
        
        # 5. Имитируем прошедшее время (3 минуты)
        now_plus_3min = datetime.utcnow() + timedelta(minutes=3)
        print(f"🕐 Имитируем время: {now_plus_3min}")
        
        # Обновляем last_reviewed на 3+ минуты назад
        await progress_collection.update_one(
            {"user_id": user_id, "word_id": word_id},
            {"$set": {
                "last_reviewed": now_plus_3min - timedelta(minutes=4),  # 4 минуты назад
                "next_review": now_plus_3min - timedelta(seconds=30)    # Доступно для повторения
            }}
        )
        
        print("\n🔍 Проверяем активную очередь через 3+ минуты:")
        active_word_3min = await spaced_repetition_service._get_next_active_word(user_id, target_lang="cb")
        
        if active_word_3min:
            print(f"✅ Активная очередь вернула слово: {active_word_3min.get('word')}")
            print(f"📋 last_reviewed: {active_word_3min.get('last_reviewed')}")
            print(f"📋 next_review: {active_word_3min.get('next_review')}")
        else:
            print(f"❌ ПРОБЛЕМА: Активная очередь пуста (слово должно быть доступно)")
            
        print("\n" + "="*50)
        print("ЭТАП 6: Получаем следующую карточку через get_next_word")
        print("="*50)
        
        # 6. Проверяем полный цикл get_next_word
        next_word = await spaced_repetition_service.get_next_word(user_id, target_lang="cb")
        
        if next_word:
            next_word_text = next_word.get('word')
            print(f"📋 Получена карточка: {next_word_text}")
            print(f"📋 is_forced_review: {next_word.get('is_forced_review')}")
            print(f"📋 interval_level: {next_word.get('interval_level')}")
            
            if next_word_text == word_text:
                print(f"🔄 Это то же слово: {word_text}")
                print(f"📋 Причина: интервал прошел, слово доступно для повторения")
            else:
                print(f"🆕 Это новое слово: {next_word_text}")
        else:
            print(f"❌ Нет доступных карточек")
            
        print("\n" + "="*50)
        print("ЭТАП 7: Отвечаем правильно еще раз")
        print("="*50)
        
        # 7. Отвечаем правильно еще раз
        if next_word and next_word.get('word') == word_text:
            result3 = await spaced_repetition_service.process_answer(user_id, word_id, True)
            print(f"✅ Второй правильный ответ обработан")
            print(f"📋 interval_level: {result3.get('interval_level')}")
            print(f"📋 force_review: {result3.get('force_review')}")
            print(f"📋 next_review: {result3.get('next_review')}")
            print(f"📋 is_learned: {result3.get('is_learned')}")
            
            # Проверяем, что будет дальше
            print("\n🔍 Проверяем, что вернет get_next_word сразу после второго правильного ответа:")
            immediate_next = await spaced_repetition_service.get_next_word(user_id, target_lang="cb")
            
            if immediate_next:
                immediate_word = immediate_next.get('word')
                if immediate_word == word_text:
                    print(f"❌ КРИТИЧЕСКАЯ ПРОБЛЕМА: Опять то же слово {word_text}!")
                    print(f"📋 Это означает, что исключение недавно отвеченных слов НЕ РАБОТАЕТ")
                else:
                    print(f"✅ Получено другое слово: {immediate_word}")
            else:
                print(f"✅ Нет доступных карточек (исключение работает)")
                
        print("\n" + "="*50)
        print("АНАЛИЗ ВСЕХ ЗАПРОСОВ К БД")
        print("="*50)
        
        # Показываем все записи прогресса для этого пользователя
        all_progress = await progress_collection.find({"user_id": user_id}).to_list(None)
        print(f"\n📊 Всего записей прогресса: {len(all_progress)}")
        
        for i, prog in enumerate(all_progress):
            print(f"\n📋 Запись {i+1}:")
            print(f"   word_id: {prog.get('word_id')}")
            print(f"   interval_level: {prog.get('interval_level')}")
            print(f"   force_review: {prog.get('force_review')}")
            print(f"   is_learned: {prog.get('is_learned')}")
            print(f"   last_reviewed: {prog.get('last_reviewed')}")
            print(f"   next_review: {prog.get('next_review')}")
            print(f"   correct_answers: {prog.get('correct_answers')}")
            print(f"   incorrect_answers: {prog.get('incorrect_answers')}")
            
    finally:
        # Очищаем тестовые данные
        await progress_collection.delete_many({"user_id": user_id})
        print(f"\n🧹 Очищены тестовые данные для пользователя {user_id}")
        
    print("\n🏁 Исследование завершено")

if __name__ == "__main__":
    asyncio.run(debug_real_scenario())
