#!/usr/bin/env python3
"""
Скрипт для отладки интервалов spaced repetition.
Проверяет, какие интервалы используются в системе.
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta
from bson import ObjectId

# Добавляем путь к корню проекта
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.spaced_repetition import SpacedRepetitionService, REPETITION_INTERVALS

async def debug_intervals():
    """Отладка интервалов."""
    print("🔧 ОТЛАДКА ИНТЕРВАЛОВ SPACED REPETITION")
    print("=" * 50)
    
    # Выводим текущие интервалы
    print("\n📋 Текущие интервалы из REPETITION_INTERVALS:")
    for i, interval in enumerate(REPETITION_INTERVALS):
        if interval < 1:
            print(f"   Уровень {i}: {interval * 60:.0f} секунд")
        elif interval < 60:
            print(f"   Уровень {i}: {interval} минут")
        elif interval < 1440:
            print(f"   Уровень {i}: {interval / 60:.1f} часов")
        else:
            print(f"   Уровень {i}: {interval / 1440:.1f} дней")
    
    # Создаем сервис
    service = SpacedRepetitionService()
    
    # Тестируем функцию _calculate_next_review
    print("\n🧪 Тестируем _calculate_next_review:")
    for level in range(5):  # Первые 5 уровней
        next_review = service._calculate_next_review(level)
        now = datetime.utcnow()
        diff_minutes = (next_review - now).total_seconds() / 60
        print(f"   Уровень {level}: {diff_minutes:.2f} минут")
    
    # Проверяем, есть ли записи в базе с interval_level = 0
    try:
        collection = await service.user_progress_collection
        
        # Ищем записи с interval_level = 0
        count_level_0 = await collection.count_documents({"interval_level": 0})
        print(f"\n📊 Записей с interval_level = 0: {count_level_0}")
        
        if count_level_0 > 0:
            # Показываем несколько примеров
            cursor = collection.find({"interval_level": 0}).limit(3)
            print("\n📝 Примеры записей с interval_level = 0:")
            async for doc in cursor:
                next_review = doc.get("next_review")
                last_reviewed = doc.get("last_reviewed")
                if next_review and last_reviewed:
                    diff_minutes = (next_review - last_reviewed).total_seconds() / 60
                    print(f"   Word ID: {str(doc['word_id'])[-8:]} | Интервал: {diff_minutes:.2f} мин | next_review: {next_review}")
    
    except Exception as e:
        print(f"❌ Ошибка при проверке базы данных: {e}")
    
    print("\n" + "=" * 50)
    print("🏁 Отладка завершена")

if __name__ == "__main__":
    asyncio.run(debug_intervals())
