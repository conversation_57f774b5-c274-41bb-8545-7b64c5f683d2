#!/usr/bin/env python3
"""
Комплексные тесты для проверки всех функций системы.
Эти тесты гарантируют, что мы не ломаем существующий функционал.
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta
from bson import ObjectId

# Добавляем путь к корню проекта
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import Database

class ComprehensiveTestSuite:
    """Комплексный набор тестов для системы интервального повторения."""
    
    def __init__(self):
        self.db_instance = None
        self.spaced_repetition_service = None
        self.progress_collection = None
        self.test_users = []
        
    async def setup(self):
        """Настройка тестовой среды."""
        print("🔧 Настройка тестовой среды...")
        
        # Подключаемся к базе данных
        self.db_instance = Database()
        await self.db_instance.connect_to_db()
        
        # Импортируем сервис только после подключения к БД
        from app.services.spaced_repetition import spaced_repetition_service
        self.spaced_repetition_service = spaced_repetition_service
        
        # Получаем коллекции
        words_db = self.db_instance.get_db('words')
        self.progress_collection = words_db.user_progress
        
        print("✅ Тестовая среда настроена")
        
    async def cleanup(self):
        """Очистка после тестов."""
        print("🧹 Очистка тестовых данных...")
        
        # Удаляем всех тестовых пользователей
        for user_id in self.test_users:
            await self.progress_collection.delete_many({"user_id": user_id})
            
        print(f"✅ Очищены данные для {len(self.test_users)} тестовых пользователей")
        
    def create_test_user(self) -> ObjectId:
        """Создать тестового пользователя."""
        user_id = ObjectId()
        self.test_users.append(user_id)
        return user_id
        
    def log_test(self, test_name: str, passed: bool, message: str = ""):
        """Логирование результата теста."""
        status = "✅" if passed else "❌"
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {status} {test_name}: {message}")
        return passed
        
    async def test_basic_word_flow(self) -> bool:
        """Тест базового потока работы со словами."""
        print("\n🧪 Тест базового потока работы со словами")
        print("-" * 50)
        
        user_id = self.create_test_user()
        tests_passed = 0
        total_tests = 0
        
        try:
            # 1. Получение нового слова
            new_word = await self.spaced_repetition_service.get_next_word(user_id, target_lang="cb")
            total_tests += 1
            if self.log_test("Получение нового слова", new_word is not None, 
                           f"Получено: {new_word.get('word') if new_word else 'None'}"):
                tests_passed += 1
                
            if not new_word:
                return False
                
            word_text = new_word.get('word')
            word_id = ObjectId(new_word.get('word_id'))
            
            # 2. Неправильный ответ
            result1 = await self.spaced_repetition_service.process_answer(user_id, word_id, False)
            total_tests += 1
            if self.log_test("Неправильный ответ", result1.get('force_review') == True,
                           f"force_review: {result1.get('force_review')}"):
                tests_passed += 1
                
            # 3. Форсированная очередь
            forced_word = await self.spaced_repetition_service.get_next_word(user_id, target_lang="cb")
            total_tests += 1
            if self.log_test("Форсированная очередь", 
                           forced_word and forced_word.get('word') == word_text,
                           f"Получено: {forced_word.get('word') if forced_word else 'None'}"):
                tests_passed += 1
                
            # 4. Правильный ответ
            result2 = await self.spaced_repetition_service.process_answer(user_id, word_id, True)
            total_tests += 1
            if self.log_test("Правильный ответ", 
                           result2.get('interval_level') >= 0 and result2.get('force_review') == False,
                           f"interval_level: {result2.get('interval_level')}, force_review: {result2.get('force_review')}"):
                tests_passed += 1
                
            return tests_passed == total_tests
            
        except Exception as e:
            self.log_test("Базовый поток", False, f"Ошибка: {e}")
            return False
            
    async def test_preload_duplication_fix(self) -> bool:
        """Тест исправления дублирования в предзагрузке."""
        print("\n🧪 Тест исправления дублирования в предзагрузке")
        print("-" * 50)
        
        user_id = self.create_test_user()
        tests_passed = 0
        total_tests = 0
        
        try:
            # 1. Получаем первое слово (обычная загрузка)
            word1 = await self.spaced_repetition_service.get_next_word(user_id, target_lang="cb", preload=False)
            total_tests += 1
            if self.log_test("Обычная загрузка", word1 is not None,
                           f"Получено: {word1.get('word') if word1 else 'None'}"):
                tests_passed += 1
                
            if not word1:
                return False
                
            word1_text = word1.get('word')
            
            # 2. Предзагрузка (должна вернуть другое слово)
            preload_word = await self.spaced_repetition_service.get_next_word(
                user_id, target_lang="cb", skip_forced=True, preload=True
            )
            
            total_tests += 1
            if preload_word:
                preload_text = preload_word.get('word')
                if self.log_test("Предзагрузка возвращает другое слово", 
                               preload_text != word1_text,
                               f"Обычная: {word1_text}, Предзагрузка: {preload_text}"):
                    tests_passed += 1
            else:
                if self.log_test("Предзагрузка", True, "Нет доступных слов (нормально)"):
                    tests_passed += 1
                    
            # 3. Проверяем, что предзагрузка НЕ добавила слово в прогресс
            if preload_word:
                preload_word_id = ObjectId(preload_word.get('word_id'))
                progress_check = await self.progress_collection.find_one({
                    "user_id": user_id,
                    "word_id": preload_word_id
                })
                
                total_tests += 1
                if self.log_test("Предзагрузка НЕ добавляет в прогресс", 
                               progress_check is None,
                               f"Прогресс найден: {progress_check is not None}"):
                    tests_passed += 1
                    
            return tests_passed == total_tests
            
        except Exception as e:
            self.log_test("Предзагрузка", False, f"Ошибка: {e}")
            return False
            
    async def test_recent_word_exclusion(self) -> bool:
        """Тест исключения недавно отвеченных слов."""
        print("\n🧪 Тест исключения недавно отвеченных слов")
        print("-" * 50)
        
        user_id = self.create_test_user()
        tests_passed = 0
        total_tests = 0
        
        try:
            # 1. Создаем слово с прогрессом в прошлом (готово к повторению)
            words_collection = self.db_instance.get_db('words').words
            word_doc = await words_collection.find_one({"language": "cb"})
            
            if not word_doc:
                self.log_test("Исключение недавних слов", False, "Нет слов в базе")
                return False
                
            word_id = word_doc["_id"]
            word_text = word_doc["word"]
            
            # Создаем прогресс с интервалом в прошлом
            past_time = datetime.utcnow() - timedelta(minutes=10)
            progress = {
                "user_id": user_id,
                "word_id": word_id,
                "interval_level": 0,
                "correct_answers": 1,
                "incorrect_answers": 1,
                "last_reviewed": past_time,
                "next_review": past_time + timedelta(minutes=1),  # 9 минут назад
                "is_learned": False,
                "force_review": False,
                "created_at": past_time,
                "updated_at": past_time
            }
            
            await self.progress_collection.insert_one(progress)
            
            # 2. Слово должно быть доступно (время прошло)
            active_word = await self.spaced_repetition_service.get_next_word(user_id, target_lang="cb")
            total_tests += 1
            if self.log_test("Слово доступно после интервала", 
                           active_word and active_word.get('word') == word_text,
                           f"Получено: {active_word.get('word') if active_word else 'None'}"):
                tests_passed += 1
                
            # 3. Отвечаем правильно (обновляем last_reviewed)
            await self.spaced_repetition_service.process_answer(user_id, word_id, True)
            
            # 4. Сразу же проверяем активную очередь (должно быть исключено)
            immediate_word = await self.spaced_repetition_service._get_next_active_word(user_id, target_lang="cb")
            total_tests += 1
            if self.log_test("Недавно отвеченное слово исключено", 
                           not immediate_word or immediate_word.get('word') != word_text,
                           f"Получено: {immediate_word.get('word') if immediate_word else 'None'}"):
                tests_passed += 1
                
            return tests_passed == total_tests
            
        except Exception as e:
            self.log_test("Исключение недавних слов", False, f"Ошибка: {e}")
            return False
            
    async def test_interval_progression(self) -> bool:
        """Тест прогрессии интервалов."""
        print("\n🧪 Тест прогрессии интервалов")
        print("-" * 50)
        
        user_id = self.create_test_user()
        tests_passed = 0
        total_tests = 0
        
        try:
            # Получаем новое слово
            new_word = await self.spaced_repetition_service.get_next_word(user_id, target_lang="cb")
            if not new_word:
                return False
                
            word_id = ObjectId(new_word.get('word_id'))
            
            # Тестируем прогрессию: -1 → 0 → 1 → 2
            expected_levels = [0, 1, 2]
            
            for i, expected_level in enumerate(expected_levels):
                # Отвечаем правильно
                result = await self.spaced_repetition_service.process_answer(user_id, word_id, True)
                
                total_tests += 1
                if self.log_test(f"Интервал {i}: уровень {expected_level}", 
                               result.get('interval_level') == expected_level,
                               f"Ожидался: {expected_level}, получен: {result.get('interval_level')}"):
                    tests_passed += 1
                    
            return tests_passed == total_tests
            
        except Exception as e:
            self.log_test("Прогрессия интервалов", False, f"Ошибка: {e}")
            return False
            
    async def test_new_word_immediate_learning(self) -> bool:
        """Тест немедленного изучения новых слов."""
        print("\n🧪 Тест немедленного изучения новых слов")
        print("-" * 50)
        
        user_id = self.create_test_user()
        tests_passed = 0
        total_tests = 0
        
        try:
            # Получаем новое слово
            new_word = await self.spaced_repetition_service.get_next_word(user_id, target_lang="cb")
            if not new_word:
                return False
                
            word_id = ObjectId(new_word.get('word_id'))
            
            # Отвечаем правильно с первого раза
            result = await self.spaced_repetition_service.process_answer(user_id, word_id, True)
            
            total_tests += 1
            if self.log_test("Новое слово выучено с первого раза", 
                           result.get('is_learned') == True,
                           f"is_learned: {result.get('is_learned')}"):
                tests_passed += 1
                
            return tests_passed == total_tests
            
        except Exception as e:
            self.log_test("Немедленное изучение", False, f"Ошибка: {e}")
            return False
            
    async def run_all_tests(self) -> bool:
        """Запуск всех тестов."""
        print("🚀 Запуск комплексных тестов системы")
        print("=" * 70)
        
        await self.setup()
        
        test_results = []
        
        try:
            # Запускаем все тесты
            test_results.append(await self.test_basic_word_flow())
            test_results.append(await self.test_preload_duplication_fix())
            test_results.append(await self.test_recent_word_exclusion())
            test_results.append(await self.test_interval_progression())
            test_results.append(await self.test_new_word_immediate_learning())
            
        finally:
            await self.cleanup()
            
        # Подводим итоги
        passed = sum(test_results)
        total = len(test_results)
        
        print(f"\n🏁 ИТОГИ ТЕСТИРОВАНИЯ: {passed}/{total} тестов пройдено")
        
        if passed == total:
            print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ! Система работает корректно!")
            print("✅ Существующий функционал не нарушен")
            print("✅ Исправление дублирования предзагрузки работает")
            print("✅ Исключение недавно отвеченных слов работает")
            return True
        else:
            print("⚠️ Некоторые тесты не пройдены. Требуется доработка.")
            return False

async def main():
    """Главная функция."""
    test_suite = ComprehensiveTestSuite()
    success = await test_suite.run_all_tests()
    
    if not success:
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
