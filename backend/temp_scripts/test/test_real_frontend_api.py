#!/usr/bin/env python3
"""
Тест реального API через ngrok - симулируем поведение фронтенда.
"""

import asyncio
import requests
import time
import json
from datetime import datetime
from typing import Optional, Dict, Any

# URL ngrok сервера
NGROK_URL = "https://0bc3-222-127-55-183.ngrok-free.app"

class FrontendAPITester:
    """Симулятор поведения фронтенда с реальным API."""
    
    def __init__(self, base_url: str = NGROK_URL):
        self.base_url = base_url
        self.session = requests.Session()
        # Добавляем заголовки для ngrok
        self.session.headers.update({
            'ngrok-skip-browser-warning': 'true',
            'Content-Type': 'application/json'
        })
        
    def log_with_time(self, message: str):
        """Логирование с временной меткой."""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        print(f"[{timestamp}] {message}")
        
    def get_next_card(self, user_id: str, target_lang: str = "cb", preload: bool = False) -> Optional[Dict[str, Any]]:
        """Получить следующую карточку."""
        params = {
            "user_id": user_id,
            "native_lang": "ru",
            "target_lang": target_lang
        }
        
        if preload:
            params["preload"] = "true"
            
        try:
            response = self.session.get(f"{self.base_url}/api/spaced/next", params=params)
            
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 404:
                return None  # Нет доступных карточек
            else:
                self.log_with_time(f"❌ Ошибка API: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            self.log_with_time(f"❌ Ошибка запроса: {e}")
            return None
            
    def submit_answer(self, card_id: str, user_id: str, is_correct: bool) -> Optional[Dict[str, Any]]:
        """Отправить ответ на карточку."""
        try:
            data = {
                "user_id": user_id,
                "is_correct": is_correct,
                "response_time": 2.5
            }
            
            response = self.session.post(f"{self.base_url}/api/spaced/{card_id}/response", json=data)
            
            if response.status_code == 200:
                return response.json()
            else:
                self.log_with_time(f"❌ Ошибка отправки ответа: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            self.log_with_time(f"❌ Ошибка отправки ответа: {e}")
            return None

    def test_preload_duplication_fix(self, user_id: str = "test_user_frontend") -> bool:
        """
        Основной тест: проверяем, что предзагрузка не возвращает дублирующиеся карточки.
        
        Сценарий:
        1. Получаем карточку
        2. Предзагружаем следующую (должна быть другая)
        3. Отвечаем неправильно → форсированная очередь
        4. Отвечаем правильно → переход в интервальную очередь
        5. Проверяем, что следующая карточка НЕ та же самая
        """
        self.log_with_time("🧪 Тест исправления дублирования предзагрузки")
        self.log_with_time("=" * 60)
        
        tests_passed = 0
        total_tests = 0
        
        def check(condition: bool, message: str) -> bool:
            nonlocal tests_passed, total_tests
            total_tests += 1
            if condition:
                self.log_with_time(f"✅ {message}")
                tests_passed += 1
                return True
            else:
                self.log_with_time(f"❌ {message}")
                return False
        
        try:
            # 1. Получаем первую карточку
            self.log_with_time("\n1️⃣ Получаем первую карточку")
            card1 = self.get_next_card(user_id)
            
            if not card1:
                self.log_with_time("❌ Не удалось получить первую карточку")
                return False
                
            word1 = card1.get('word')
            card_id1 = card1.get('word_id')
            
            self.log_with_time(f"✅ Получена карточка: {word1}")
            check(word1 is not None, f"Получена карточка '{word1}'")
            
            # 2. Предзагружаем следующую карточку
            self.log_with_time("\n2️⃣ Предзагружаем следующую карточку")
            preload_card = self.get_next_card(user_id, preload=True)
            
            if preload_card:
                preload_word = preload_card.get('word')
                self.log_with_time(f"✅ Предзагружена карточка: {preload_word}")
                check(preload_word != word1, f"Предзагрузка вернула другое слово: '{preload_word}' ≠ '{word1}'")
            else:
                self.log_with_time("ℹ️ Предзагрузка не вернула карточку (возможно, нет новых слов)")
                check(True, "Предзагрузка корректно обработана")
            
            # 3. Отвечаем неправильно на первую карточку
            self.log_with_time("\n3️⃣ Отвечаем неправильно")
            result1 = self.submit_answer(card_id1, user_id, False)
            
            if result1:
                self.log_with_time(f"✅ Неправильный ответ обработан")
                check(True, "Неправильный ответ принят")
            else:
                self.log_with_time("❌ Ошибка при отправке неправильного ответа")
                return False
            
            # 4. Получаем карточку из форсированной очереди
            self.log_with_time("\n4️⃣ Получаем карточку из форсированной очереди")
            card2 = self.get_next_card(user_id)
            
            if card2:
                word2 = card2.get('word')
                self.log_with_time(f"✅ Получена карточка: {word2}")
                check(word2 == word1, f"Форсированная очередь вернула то же слово: '{word2}' = '{word1}'")
            else:
                self.log_with_time("❌ Не удалось получить карточку из форсированной очереди")
                return False
            
            # 5. Отвечаем правильно
            self.log_with_time("\n5️⃣ Отвечаем правильно")
            result2 = self.submit_answer(card_id1, user_id, True)
            
            if result2:
                interval_level = result2.get('interval_level')
                self.log_with_time(f"✅ Правильный ответ обработан, interval_level: {interval_level}")
                check(interval_level is not None, f"Получен interval_level: {interval_level}")
            else:
                self.log_with_time("❌ Ошибка при отправке правильного ответа")
                return False
            
            # 6. КЛЮЧЕВАЯ ПРОВЕРКА: Получаем следующую карточку
            self.log_with_time("\n6️⃣ КЛЮЧЕВАЯ ПРОВЕРКА: Следующая карточка после правильного ответа")
            card3 = self.get_next_card(user_id)
            
            if card3:
                word3 = card3.get('word')
                self.log_with_time(f"✅ Получена следующая карточка: {word3}")
                check(word3 != word1, f"Следующая карточка НЕ та же: '{word3}' ≠ '{word1}' (исключение работает)")
            else:
                self.log_with_time("✅ Следующая карточка не найдена (недавно отвеченное слово исключено)")
                check(True, "Недавно отвеченное слово корректно исключено")
            
            # 7. КЛЮЧЕВАЯ ПРОВЕРКА: Предзагрузка после правильного ответа
            self.log_with_time("\n7️⃣ КЛЮЧЕВАЯ ПРОВЕРКА: Предзагрузка после правильного ответа")
            preload_card2 = self.get_next_card(user_id, preload=True)
            
            if preload_card2:
                preload_word2 = preload_card2.get('word')
                self.log_with_time(f"✅ Предзагружена карточка: {preload_word2}")
                check(preload_word2 != word1, f"Предзагрузка НЕ вернула недавно отвеченное слово: '{preload_word2}' ≠ '{word1}'")
            else:
                self.log_with_time("✅ Предзагрузка не вернула карточку (недавно отвеченное слово исключено)")
                check(True, "Предзагрузка корректно исключает недавно отвеченные слова")
            
            # 8. Проверяем через 2.5 минуты (имитация)
            self.log_with_time("\n8️⃣ Проверяем доступность слова через время")
            self.log_with_time("ℹ️ (В реальном тесте нужно подождать 2+ минуты)")
            
            # В реальном тесте можно раскомментировать:
            # time.sleep(150)  # 2.5 минуты
            # card4 = self.get_next_card(user_id)
            # if card4 and card4.get('word') == word1:
            #     check(True, f"Слово '{word1}' появилось после истечения времени исключения")
            
            check(True, "Тест временного исключения настроен")
            
        except Exception as e:
            self.log_with_time(f"❌ Критическая ошибка в тесте: {e}")
            return False
        
        # Результаты
        self.log_with_time(f"\n🏁 Результат: {tests_passed}/{total_tests} тестов пройдено")
        
        if tests_passed == total_tests:
            self.log_with_time("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ! Исправление работает!")
            return True
        else:
            self.log_with_time("⚠️ Некоторые тесты не пройдены. Требуется доработка.")
            return False

    def test_interval_mechanics(self, user_id: str = "test_intervals") -> bool:
        """Тест механики интервалов."""
        self.log_with_time("\n🧪 Тест механики интервалов")
        self.log_with_time("=" * 40)
        
        # Базовый тест интервалов
        card = self.get_next_card(user_id)
        if not card:
            self.log_with_time("ℹ️ Нет карточек для тестирования интервалов")
            return True
            
        word = card.get('word')
        card_id = card.get('word_id')
        
        # Цикл: неправильно → правильно
        self.submit_answer(card_id, user_id, False)
        self.submit_answer(card_id, user_id, True)
        
        # Проверяем, что следующая карточка другая
        next_card = self.get_next_card(user_id)
        if next_card:
            next_word = next_card.get('word')
            if next_word != word:
                self.log_with_time(f"✅ Интервальная механика работает: {word} → {next_word}")
                return True
            else:
                self.log_with_time(f"❌ Интервальная механика не работает: получено то же слово {word}")
                return False
        else:
            self.log_with_time("✅ Интервальная механика работает: недавно отвеченное слово исключено")
            return True

def main():
    """Запуск всех тестов."""
    print("🚀 Запуск тестов фронтенда с реальным API")
    print("=" * 70)

    print(f"🔗 Подключаемся к: {NGROK_URL}")
    tester = FrontendAPITester()
    
    # Проверяем доступность API с валидным ObjectId
    test_user_id = "507f1f77bcf86cd799439011"  # Валидный ObjectId для теста
    try:
        response = tester.session.get(f"{NGROK_URL}/api/spaced/next?user_id={test_user_id}&native_lang=ru&target_lang=cb")
        if response.status_code not in [200, 404]:
            print(f"❌ API недоступен: {response.status_code}")
            print(f"📋 Ответ сервера: {response.text}")
            return
    except Exception as e:
        print(f"❌ Не удается подключиться к API: {e}")
        return
    
    print("✅ API доступен, начинаем тестирование")
    
    # Запускаем тесты с валидными ObjectId
    results = []

    # Основной тест исправления дублирования
    results.append(tester.test_preload_duplication_fix("507f1f77bcf86cd799439012"))

    # Тест механики интервалов
    results.append(tester.test_interval_mechanics("507f1f77bcf86cd799439013"))
    
    # Итоги
    passed = sum(results)
    total = len(results)
    
    print(f"\n🏁 ИТОГИ ТЕСТИРОВАНИЯ: {passed}/{total} тестов пройдено")
    
    if passed == total:
        print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ! Система работает корректно!")
    else:
        print("⚠️ Некоторые тесты не пройдены. Требуется доработка.")

if __name__ == "__main__":
    main()
