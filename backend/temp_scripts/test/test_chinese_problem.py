#!/usr/bin/env python3
"""
Тест для проверки решения "китайской проблемы".
Воспроизводит сценарий: изучение слова на одном языке, переключение на другой язык.
"""

import asyncio
import os
from dotenv import load_dotenv
from bson import ObjectId
from app.services.spaced_repetition import spaced_repetition_service

# Загружаем переменные окружения
load_dotenv()

async def test_chinese_problem():
    """Тестируем решение китайской проблемы"""
    
    # Подключаемся к базе данных
    from app.database import Database
    db_instance = Database()
    await db_instance.connect_to_db()
    
    # Используем тестового пользователя
    test_user_id = ObjectId("684c7900e9e41d7a3b5c0d62")
    
    print("=== Тест решения 'китайской проблемы' ===")
    print(f"Тестовый пользователь: {test_user_id}")
    
    # Шаг 1: Получаем слово на вьетнамском языке
    print("\n1. Получаем слово на вьетнамском языке:")
    vi_word = await spaced_repetition_service.get_next_word(test_user_id, target_lang="vi")
    if vi_word:
        print(f"   Вьетнамское слово: {vi_word.get('word')} (concept_id: {vi_word.get('concept_id')})")
        vi_concept_id = vi_word.get('concept_id')
        vi_word_id = vi_word.get('word_id')
    else:
        print("   ❌ Вьетнамских слов не найдено")
        return
    
    # Шаг 2: Отвечаем неправильно на вьетнамское слово (попадает в форсированную очередь)
    print("\n2. Отвечаем неправильно на вьетнамское слово:")
    vi_progress = await spaced_repetition_service.process_answer(
        user_id=test_user_id,
        word_id=ObjectId(vi_word_id),
        is_correct=False
    )
    print(f"   Прогресс: interval_level={vi_progress.get('interval_level')}, force_review={vi_progress.get('force_review')}")
    
    # Шаг 3: Переключаемся на английский язык и получаем слово
    print("\n3. Переключаемся на английский язык:")
    en_word = await spaced_repetition_service.get_next_word(test_user_id, target_lang="en")
    if en_word:
        print(f"   Английское слово: {en_word.get('word')} (concept_id: {en_word.get('concept_id')})")
        en_concept_id = en_word.get('concept_id')
        en_word_id = en_word.get('word_id')
    else:
        print("   ❌ Английских слов не найдено")
        return
    
    # Шаг 4: Проверяем, что это разные слова (разные word_id)
    print("\n4. Проверка изоляции языков:")
    print(f"   Вьетнамский word_id: {vi_word_id}")
    print(f"   Английский word_id: {en_word_id}")
    
    if vi_word_id != en_word_id:
        print("   ✅ word_id разные - изоляция работает")
    else:
        print("   ❌ word_id одинаковые - проблема не решена!")
    
    # Шаг 5: Проверяем concept_id (могут быть одинаковыми или разными)
    print(f"   Вьетнамский concept_id: {vi_concept_id}")
    print(f"   Английский concept_id: {en_concept_id}")
    
    if vi_concept_id == en_concept_id:
        print("   ℹ️  concept_id одинаковые - это нормально (один концепт, разные языки)")
    else:
        print("   ℹ️  concept_id разные - разные концепты")
    
    # Шаг 6: Отвечаем правильно на английское слово
    print("\n5. Отвечаем правильно на английское слово:")
    en_progress = await spaced_repetition_service.process_answer(
        user_id=test_user_id,
        word_id=ObjectId(en_word_id),
        is_correct=True
    )
    print(f"   Прогресс: interval_level={en_progress.get('interval_level')}, force_review={en_progress.get('force_review')}")
    
    # Шаг 7: Переключаемся обратно на вьетнамский и проверяем, что вьетнамское слово все еще в форсированной очереди
    print("\n6. Переключаемся обратно на вьетнамский:")
    vi_word_again = await spaced_repetition_service.get_next_word(test_user_id, target_lang="vi")
    if vi_word_again:
        print(f"   Вьетнамское слово: {vi_word_again.get('word')} (word_id: {vi_word_again.get('word_id')})")
        print(f"   Это форсированное повторение: {vi_word_again.get('is_forced_review')}")
        
        # Проверяем, что это то же самое слово
        if vi_word_again.get('word_id') == vi_word_id:
            print("   ✅ Вернулось то же вьетнамское слово - прогресс сохранен отдельно")
        else:
            print("   ❌ Вернулось другое слово - возможная проблема")
    else:
        print("   ❌ Вьетнамских слов не найдено")
    
    print("\n=== Тест завершен ===")
    
    # Итоговая проверка
    print("\n=== Итоговая проверка ===")
    if (vi_word_id != en_word_id and 
        vi_word_again and 
        vi_word_again.get('word_id') == vi_word_id and
        vi_word_again.get('is_forced_review')):
        print("🎉 УСПЕХ: 'Китайская проблема' решена!")
        print("   - Прогресс отслеживается отдельно для каждого языка")
        print("   - Форсированная очередь работает корректно")
        print("   - Переключение языков не влияет на прогресс других языков")
    else:
        print("❌ ПРОБЛЕМА: 'Китайская проблема' не полностью решена")

if __name__ == "__main__":
    asyncio.run(test_chinese_problem())
