import httpx
import asyncio
import json

async def test_register():
    url = "http://localhost:8001/api/register"
    data = {
        "email": "<EMAIL>",
        "password": "securepassword123",
        "username": "testuser"
    }
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(url, json=data)
            response.raise_for_status()
            print(f"Success! Response: {response.json()}")
        except httpx.HTTPStatusError as e:
            print(f"Error status: {e.response.status_code}")
            print(f"Error response: {e.response.text}")
        except Exception as e:
            print(f"Unexpected error: {e}")

if __name__ == "__main__":
    asyncio.run(test_register())
