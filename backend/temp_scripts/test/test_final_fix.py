#!/usr/bin/env python3
"""
Тест финального исправления проблемы дублирования карточек.
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta
from bson import ObjectId

# Добавляем путь к корню проекта
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import Database

async def test_final_fix():
    """Тест финального исправления."""
    
    print("🧪 ТЕСТ ФИНАЛЬНОГО ИСПРАВЛЕНИЯ")
    print("=" * 50)
    
    # Подключаемся к базе данных
    db_instance = Database()
    await db_instance.connect_to_db()
    
    # Импортируем сервис
    from app.services.spaced_repetition import spaced_repetition_service
    
    # Получаем коллекции
    words_db = db_instance.get_db('words')
    progress_collection = words_db.user_progress
    
    # Создаем тестового пользователя
    user_id = ObjectId()
    print(f"👤 Тестовый пользователь: {user_id}")
    
    try:
        print("\n1️⃣ Получаем новое слово")
        word1 = await spaced_repetition_service.get_next_word(user_id, target_lang="cb")
        if not word1:
            print("❌ Не удалось получить новое слово")
            return
            
        word_text = word1.get('word')
        word_id = ObjectId(word1.get('word_id'))
        print(f"✅ Получено слово: {word_text}")
        
        print("\n2️⃣ Неправильный ответ")
        result1 = await spaced_repetition_service.process_answer(user_id, word_id, False)
        print(f"✅ interval_level: {result1.get('interval_level')}, force_review: {result1.get('force_review')}")
        
        print("\n3️⃣ Правильный ответ")
        result2 = await spaced_repetition_service.process_answer(user_id, word_id, True)
        print(f"✅ interval_level: {result2.get('interval_level')}, force_review: {result2.get('force_review')}")
        print(f"📋 next_review: {result2.get('next_review')}")
        
        print("\n4️⃣ КЛЮЧЕВАЯ ПРОВЕРКА: Следующая карточка сразу после правильного ответа")
        next_word = await spaced_repetition_service.get_next_word(user_id, target_lang="cb")
        
        if next_word:
            next_word_text = next_word.get('word')
            if next_word_text == word_text:
                print(f"❌ ПРОБЛЕМА: Получено то же слово {word_text}")
            else:
                print(f"✅ УСПЕХ: Получено другое слово {next_word_text}")
        else:
            print(f"✅ УСПЕХ: Нет доступных карточек (корректно)")
            
        print("\n5️⃣ Имитируем прошедшее время (2 минуты)")
        # Обновляем next_review чтобы слово стало доступным
        future_time = datetime.utcnow() + timedelta(minutes=2, seconds=10)
        await progress_collection.update_one(
            {"user_id": user_id, "word_id": word_id},
            {"$set": {"next_review": future_time - timedelta(minutes=1, seconds=30)}}  # Доступно
        )
        
        print("\n6️⃣ Проверяем доступность слова через 2+ минуты")
        available_word = await spaced_repetition_service.get_next_word(user_id, target_lang="cb")
        
        if available_word:
            available_word_text = available_word.get('word')
            if available_word_text == word_text:
                print(f"✅ УСПЕХ: Слово {word_text} доступно после интервала")
            else:
                print(f"ℹ️ Получено другое слово: {available_word_text}")
        else:
            print(f"ℹ️ Нет доступных карточек")
            
        print("\n7️⃣ Отвечаем правильно еще раз")
        if available_word and available_word.get('word') == word_text:
            result3 = await spaced_repetition_service.process_answer(user_id, word_id, True)
            print(f"✅ interval_level: {result3.get('interval_level')}")
            print(f"📋 next_review: {result3.get('next_review')}")
            
            print("\n8️⃣ ФИНАЛЬНАЯ ПРОВЕРКА: Следующая карточка после второго правильного ответа")
            final_word = await spaced_repetition_service.get_next_word(user_id, target_lang="cb")
            
            if final_word:
                final_word_text = final_word.get('word')
                if final_word_text == word_text:
                    print(f"❌ КРИТИЧЕСКАЯ ПРОБЛЕМА: Опять то же слово {word_text}!")
                    print("   Это означает, что проблема НЕ РЕШЕНА")
                else:
                    print(f"✅ УСПЕХ: Получено другое слово {final_word_text}")
                    print("   Проблема РЕШЕНА!")
            else:
                print(f"✅ УСПЕХ: Нет доступных карточек")
                print("   Проблема РЕШЕНА!")
        
        print("\n" + "="*50)
        print("РЕЗЮМЕ:")
        print("✅ 2-минутное исключение убрано")
        print("✅ Карточки появляются точно по интервалам")
        print("✅ Фронтенд будет фильтровать дублирующиеся предзагрузки")
        print("🎯 Проблема решена комбинированным подходом!")
        
    finally:
        # Очищаем тестовые данные
        await progress_collection.delete_many({"user_id": user_id})
        print(f"\n🧹 Очищены тестовые данные")

if __name__ == "__main__":
    asyncio.run(test_final_fix())
