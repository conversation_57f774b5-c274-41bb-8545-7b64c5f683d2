#!/usr/bin/env python3
"""
Тест для проверки фильтрации по языкам в системе интервального повторения.
Проверяет, что прогресс отслеживается отдельно для каждого языка.
"""

import asyncio
import os
from dotenv import load_dotenv
from bson import ObjectId
from app.services.spaced_repetition import spaced_repetition_service

# Загружаем переменные окружения
load_dotenv()

async def test_language_filtering():
    """Тестируем фильтрацию по языкам"""

    # Подключаемся к базе данных
    from app.database import Database
    db_instance = Database()
    await db_instance.connect_to_db()

    # Используем тестового пользователя
    test_user_id = ObjectId("684c7900e9e41d7a3b5c0d62")  # Замените на реальный ID

    print("=== Тест фильтрации по языкам ===")
    print(f"Тестовый пользователь: {test_user_id}")
    
    # Тест 1: Получаем слово на русском языке
    print("\n1. Получаем слово на русском языке:")
    ru_word = await spaced_repetition_service.get_next_word(test_user_id, target_lang="ru")
    if ru_word:
        print(f"   Русское слово: {ru_word.get('word')} (ID: {ru_word.get('word_id')})")
    else:
        print("   Русских слов не найдено")
    
    # Тест 2: Получаем слово на английском языке
    print("\n2. Получаем слово на английском языке:")
    en_word = await spaced_repetition_service.get_next_word(test_user_id, target_lang="en")
    if en_word:
        print(f"   Английское слово: {en_word.get('word')} (ID: {en_word.get('word_id')})")
    else:
        print("   Английских слов не найдено")
    
    # Тест 3: Получаем слово на вьетнамском языке
    print("\n3. Получаем слово на вьетнамском языке:")
    vi_word = await spaced_repetition_service.get_next_word(test_user_id, target_lang="vi")
    if vi_word:
        print(f"   Вьетнамское слово: {vi_word.get('word')} (ID: {vi_word.get('word_id')})")
    else:
        print("   Вьетнамских слов не найдено")
    
    # Тест 4: Проверяем, что слова разных языков имеют разные word_id
    print("\n4. Проверка уникальности word_id для разных языков:")
    word_ids = []
    if ru_word:
        word_ids.append(("ru", ru_word.get('word_id')))
    if en_word:
        word_ids.append(("en", en_word.get('word_id')))
    if vi_word:
        word_ids.append(("vi", vi_word.get('word_id')))
    
    for lang, word_id in word_ids:
        print(f"   {lang}: {word_id}")
    
    # Проверяем, что все word_id разные
    unique_ids = set([word_id for _, word_id in word_ids])
    if len(unique_ids) == len(word_ids):
        print("   ✅ Все word_id уникальны")
    else:
        print("   ❌ Найдены дублирующиеся word_id")
    
    print("\n=== Тест завершен ===")

if __name__ == "__main__":
    asyncio.run(test_language_filtering())
