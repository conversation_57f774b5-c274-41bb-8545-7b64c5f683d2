#!/usr/bin/env python3
"""
Тестирование создания A0 слов с проверкой по проблемным языкам.

Этот скрипт:
1. Берет 20 рандомных слов из A0 списка
2. Создает для них примеры предложений
3. Проверяет по всем проблемным языкам
4. Выявляет проблемы и предлагает исправления
"""

import random
import asyncio
from typing import List, Dict, Any

# Список всех 22 поддерживаемых языков
ALL_LANGUAGES = [
    'en', 'ru', 'es', 'fr', 'de', 'it', 'pt', 'nl', 'pl', 'tr', 
    'ar', 'hi', 'ja', 'ko', 'zh', 'th', 'vi', 'id', 'ms', 'tl', 'ceb', 'fi'
]

# Проблемные языки для особой проверки (НАУЧНЫЙ АНАЛИЗ)
#
# КРИТЕРИЙ ПРОБЛЕМНОСТИ: язык МЕНЯЕТ ФОРМУ ЦЕЛЕВОГО СЛОВА в предложениях
# ❌ "Я вижу собакУ" (не "собака") - ПРОБЛЕМА
# ✅ "Собака лает" - НЕ ПРОБЛЕМА
#
PROBLEMATIC_LANGUAGES = [
    # 🔴 КРИТИЧЕСКИ ПРОБЛЕМНЫЕ - МЕНЯЮТ ФОРМУ СЛОВА
    'fi',  # Финский - 15 падежей: talo→talossa, äiti→äitini (агглютинация)
    'ru',  # Русский - 6 падежей: собака→собаку, дом→дома
    'pl',  # Польский - 7 падежей: dom→domu, mama→mamę
    'de',  # Немецкий - 4 падежа + артикли: der Hund→den Hund
    'tr',  # Турецкий - агглютинация: ev→evimiz, anne→annem
    'uk',  # Украинский - 7 падежей: мама→маму, дім→дому

    # 🟡 СРЕДНЕ ПРОБЛЕМНЫЕ - СЛОЖНЫЕ КОНСТРУКЦИИ
    'ja',  # Японский - частицы は/を/に меняют смысл
    'ko',  # Корейский - агглютинация + вежливость
    'ar',  # Арабский - корневая система + падежи
    'hi',  # Хинди - падежи + постпозиции: लड़का→लड़के को

    # 🟠 РОМАНСКИЕ - АРТИКЛИ + РОД (но слово не меняется)
    'it',  # Итальянский - il/la + род, но "casa" остается "casa"
    'es',  # Испанский - el/la + род, но "casa" остается "casa"
    'fr',  # Французский - le/la + род, но "maison" остается "maison"
    'pt',  # Португальский - o/a + род, но "casa" остается "casa"
    'nl',  # Нидерландский - de/het + род, но слово не меняется
]

# ✅ НЕ ПРОБЛЕМНЫЕ ЯЗЫКИ (слово НЕ меняет форму):
#
# 🟢 ПРОСТЫЕ (минимальная морфология):
# 'en' - английский: "house" всегда "house"
# 'id' - индонезийский: "rumah" всегда "rumah"
# 'ms' - малайский: "rumah" всегда "rumah"
#
# 🟡 СРЕДНИЕ (особенности есть, но слово не меняется):
# 'zh' - китайский: тоны, но 房子 всегда 房子
# 'th' - тайский: тоны, но บ้าน всегда บ้าน
# 'vi' - вьетнамский: тоны, но nhà всегда nhà
# 'my' - бирманский: тоны, но အိမ် всегда အိမ်
# 'sv' - шведский: en/ett артикли, но "hus" остается "hus"
# 'da' - датский: en/et артикли, но "hus" остается "hus"
# 'no' - норвежский: en/ei/et артикли, но "hus" остается "hus"
# 'kk' - казахский: агглютинация слабее турецкого
# 'uz' - узбекский: агглютинация слабее турецкого
# 'pa' - пенджаби: падежи слабее хинди
# 'mr' - маратхи: падежи слабее хинди
# 'bn' - бенгальский: падежи слабее хинди
# 'ur' - урду: падежи слабее хинди
# 'ne' - непальский: падежи слабее хинди
# 'fa' - персидский: изафет, но слово не меняется
# 'tl' - тагалог: фокус, но слово не меняется
# 'cb' - себуано: фокус, но слово не меняется
# 'ka' - грузинский: эргативность, но для A0 не критично
# 'ro' - румынский: падежи есть, но слабее славянских
#
# Итого: 15 проблемных языков из 37 (41%)

# Полный список A0 слов (151 слово) с приоритетами
A0_WORDS = {
    # ULTRA-CORE (15 слов)
    "я": "ultra_core", "ты": "ultra_core", "это": "ultra_core", 
    "да": "ultra_core", "нет": "ultra_core", "хочу": "ultra_core",
    "есть": "ultra_core", "пить": "ultra_core", "помощь": "ultra_core",
    "спасибо": "ultra_core", "извините": "ultra_core", "врач": "ultra_core",
    "вода": "ultra_core", "как": "ultra_core", "где": "ultra_core",
    
    # CORE (35 слов) - исправлены проблемные формы
    "он": "core", "она": "core", "мы": "core", "вы": "core", "они": "core",
    "что": "core", "кто": "core", "когда": "core", "почему": "core",
    "мама": "core", "папа": "core", "друг": "core", "дом": "core",
    "время": "core", "день": "core", "ночь": "core", "утро": "core",  # Исправлено: утром → утро
    "хорошо": "core", "плохо": "core", "большой": "core", "маленький": "core",
    "новый": "core", "старый": "core", "горячий": "core", "холодный": "core",
    "быстро": "core", "медленно": "core", "много": "core", "мало": "core",
    "один": "core", "два": "core", "три": "core", "первый": "core",
    "последний": "core", "здесь": "core",
    
    # EXTENDED (остальные слова для примера)
    "работа": "extended", "школа": "extended", "машина": "extended",
    "еда": "extended", "деньги": "extended", "книга": "extended",
    "музыка": "extended", "фильм": "extended", "игра": "extended",
    "спорт": "extended", "путешествие": "extended", "семья": "extended",
    # ... (остальные 89 слов)
}

def get_random_a0_words(count: int = 20) -> List[Dict[str, Any]]:
    """Получить случайные A0 слова для тестирования."""
    words = list(A0_WORDS.keys())
    selected = random.sample(words, min(count, len(words)))
    
    result = []
    for i, word in enumerate(selected):
        result.append({
            "concept_id": f"a0-test-{i+1:03d}",
            "word_ru": word,
            "priority": A0_WORDS[word],
            "part_of_speech": "noun"  # Упрощаем для теста
        })
    
    return result

def create_simple_sentence(word: str, lang: str) -> str:
    """
    Создать простое предложение для A0 уровня.
    Используем самые безопасные конструкции БЕЗ артиклей.
    """

    # Безопасные шаблоны БЕЗ артиклей и сложных конструкций
    templates = {
        'ru': f"Я вижу {word}",      # Избегаем "это"
        'en': f"I see {word}",       # Избегаем "this is"
        'es': f"Veo {word}",         # Избегаем "esto es"
        'fr': f"Je vois {word}",     # Избегаем "c'est"
        'de': f"Ich sehe {word}",    # Избегаем "das ist"
        'it': f"Vedo {word}",        # Избегаем "questo è"
        'pt': f"Vejo {word}",        # Избегаем "isto é"
        'nl': f"Ik zie {word}",      # Избегаем "dit is"
        'pl': f"Widzę {word}",       # Избегаем "to jest"
        'tr': f"{word} görüyorum",   # Турецкий порядок слов
        'ar': f"أرى {word}",         # "Я вижу"
        'hi': f"मैं {word} देखता हूं", # "Я вижу"
        'ja': f"{word}を見ます",       # "Вижу [слово]"
        'ko': f"{word}을 봅니다",      # "Вижу [слово]"
        'zh': f"我看到{word}",        # "Я вижу"
        'th': f"ฉันเห็น{word}",       # "Я вижу"
        'vi': f"Tôi thấy {word}",    # "Я вижу"
        'id': f"Saya melihat {word}", # "Я вижу"
        'ms': f"Saya nampak {word}",  # "Я вижу"
        'tl': f"Nakikita ko ang {word}", # "Я вижу"
        'ceb': f"Nakita nako ang {word}", # "Я вижу"
        'fi': f"Näen {word}",        # "Я вижу" (без падежей)
    }

    return templates.get(lang, f"I see {word}")

def check_sentence_safety(sentence: str, target_word: str, lang: str) -> Dict[str, Any]:
    """
    Проверить безопасность предложения для конкретного языка.
    Возвращает результат проверки.
    """

    issues = []
    sentence_lower = sentence.lower()

    # Проверки для языков с падежами
    if lang in ['ru', 'pl', 'de', 'fi']:
        # Исключения - слова которые правильные несмотря на окончания
        exceptions = {
            'ru': ['дом', 'том', 'ком', 'много', 'мало', 'хорошо', 'плохо'],
            'pl': ['dom', 'tom', 'kom'],
            'de': ['den', 'dem', 'des'],
            'fi': ['koti', 'talo']
        }

        # Проверяем окончания, указывающие на неправильный падеж
        problematic_endings = {
            'ru': ['ами', 'ых', 'ую', 'ему', 'ими', 'ей', 'ах'],  # Убрали 'ом', 'ого'
            'pl': ['ach', 'ami', 'ów', 'ę', 'ą'],  # Убрали 'em', 'ie'
            'de': ['en', 'er', 'es', 'em'],
            'fi': ['ssa', 'sta', 'aan', 'lla', 'lta', 'lle', 'na', 'ksi']
        }

        if lang in problematic_endings and target_word not in exceptions.get(lang, []):
            for ending in problematic_endings[lang]:
                if target_word.endswith(ending) and len(target_word) > len(ending):
                    issues.append(f"Слово '{target_word}' может быть в неправильном падеже (окончание '{ending}')")

    # Проверки для языков с артиклями
    if lang == 'de':
        # Немецкие артикли
        german_articles = ['der', 'die', 'das', 'den', 'dem', 'des']
        for article in german_articles:
            if article in sentence_lower:
                issues.append(f"Найден немецкий артикль '{article}' в предложении")

    if lang in ['it', 'es', 'fr', 'pt']:
        # Романские артикли
        romance_articles = {
            'it': ['il', 'la', 'lo', 'gli', 'le'],
            'es': ['el', 'la', 'los', 'las'],
            'fr': ['le', 'la', 'les'],
            'pt': ['o', 'a', 'os', 'as']
        }

        if lang in romance_articles:
            for article in romance_articles[lang]:
                if f' {article} ' in sentence_lower or sentence_lower.startswith(f'{article} '):
                    issues.append(f"Найден артикль '{article}' в {lang}")

    # Проверки для азиатских языков
    if lang in ['ja', 'ko']:
        if len(sentence) > 25:
            issues.append(f"Предложение слишком длинное для {lang}: {len(sentence)} символов")

    # Проверки для арабского
    if lang == 'ar':
        if len(target_word) < 2:
            issues.append(f"Арабское слово слишком короткое: '{target_word}'")

    # Общие проверки
    if len(sentence) > 50:
        issues.append(f"Предложение слишком длинное: {len(sentence)} символов")

    if target_word not in sentence:
        issues.append(f"Целевое слово '{target_word}' не найдено в предложении")

    return {
        "lang": lang,
        "sentence": sentence,
        "target_word": target_word,
        "issues": issues,
        "is_safe": len(issues) == 0
    }

async def test_word_across_languages(word_data: Dict[str, Any]) -> Dict[str, Any]:
    """Протестировать одно слово по всем проблемным языкам."""
    
    concept_id = word_data["concept_id"]
    word_ru = word_data["word_ru"]
    priority = word_data["priority"]
    
    print(f"\n🧪 Тестируем слово: '{word_ru}' ({priority})")
    print(f"   Concept ID: {concept_id}")
    
    results = {
        "concept_id": concept_id,
        "word_ru": word_ru,
        "priority": priority,
        "language_results": {},
        "has_issues": False,
        "total_issues": 0
    }
    
    # Тестируем по всем проблемным языкам
    for lang in PROBLEMATIC_LANGUAGES:
        # Создаем простое предложение
        sentence = create_simple_sentence(word_ru, lang)
        
        # Проверяем безопасность
        check_result = check_sentence_safety(sentence, word_ru, lang)
        results["language_results"][lang] = check_result
        
        if not check_result["is_safe"]:
            results["has_issues"] = True
            results["total_issues"] += len(check_result["issues"])
            print(f"   ❌ {lang}: {check_result['issues']}")
        else:
            print(f"   ✅ {lang}: OK")
    
    return results

async def run_a0_word_test(word_count: int = 20):
    """Запустить полный тест A0 слов."""
    
    print("🚀 Запуск теста создания A0 слов")
    print(f"📊 Тестируем {word_count} случайных слов")
    print(f"🌍 Проверяем по {len(PROBLEMATIC_LANGUAGES)} проблемным языкам")
    print(f"🔍 Проблемные языки: {', '.join(PROBLEMATIC_LANGUAGES)}")
    
    # Получаем случайные слова
    test_words = get_random_a0_words(word_count)
    
    # Тестируем каждое слово
    all_results = []
    total_issues = 0
    
    for word_data in test_words:
        result = await test_word_across_languages(word_data)
        all_results.append(result)
        total_issues += result["total_issues"]
    
    # Итоговая статистика
    print(f"\n📈 ИТОГИ ТЕСТИРОВАНИЯ:")
    print(f"   Протестировано слов: {len(all_results)}")
    print(f"   Слов с проблемами: {sum(1 for r in all_results if r['has_issues'])}")
    print(f"   Всего проблем найдено: {total_issues}")
    
    # Показываем проблемные слова
    problematic_words = [r for r in all_results if r["has_issues"]]
    if problematic_words:
        print(f"\n⚠️  СЛОВА С ПРОБЛЕМАМИ:")
        for word_result in problematic_words:
            print(f"   • {word_result['word_ru']} ({word_result['priority']})")
            for lang, lang_result in word_result["language_results"].items():
                if not lang_result["is_safe"]:
                    for issue in lang_result["issues"]:
                        print(f"     - {lang}: {issue}")
    else:
        print(f"\n✅ ВСЕ СЛОВА ПРОШЛИ ПРОВЕРКУ!")
    
    return all_results

if __name__ == "__main__":
    # Запускаем тест
    asyncio.run(run_a0_word_test(20))
