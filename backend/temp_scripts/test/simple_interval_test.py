#!/usr/bin/env python3
"""
Простой тест интервалов без зависимостей.
"""

import sys
import os

# Добавляем путь к корню проекта
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_intervals():
    """Простой тест интервалов."""
    print("🧪 ПРОСТОЙ ТЕСТ ИНТЕРВАЛОВ")
    print("=" * 40)
    
    try:
        # Импортируем только константы
        from app.services.spaced_repetition import REPETITION_INTERVALS
        
        print("\n📋 Текущие интервалы из REPETITION_INTERVALS:")
        for i, interval in enumerate(REPETITION_INTERVALS[:5]):
            if interval < 1:
                print(f"   Уровень {i}: {interval * 60:.0f} секунд")
            elif interval < 60:
                print(f"   Уровень {i}: {interval} минут")
            else:
                print(f"   Уровень {i}: {interval / 60:.1f} часов")
        
        # Проверяем первый интервал
        first_interval = REPETITION_INTERVALS[0]
        if first_interval == 0.5:
            print(f"\n✅ ПРАВИЛЬНО: Первый интервал = {first_interval} минут (30 секунд)")
        else:
            print(f"\n❌ НЕПРАВИЛЬНО: Первый интервал = {first_interval} минут (ожидалось 0.5)")
        
        # Проверяем второй интервал
        second_interval = REPETITION_INTERVALS[1]
        if second_interval == 2:
            print(f"✅ ПРАВИЛЬНО: Второй интервал = {second_interval} минут")
        else:
            print(f"❌ НЕПРАВИЛЬНО: Второй интервал = {second_interval} минут (ожидалось 2)")
            
    except Exception as e:
        print(f"❌ Ошибка при импорте: {e}")
        return
    
    print("\n" + "=" * 40)
    print("🏁 Тест завершен")
    
    print("\n💡 Рекомендации:")
    print("1. Убедитесь, что сервер перезапущен после изменения интервалов")
    print("2. Проверьте, нет ли старых записей в базе данных")
    print("3. Очистите кэш приложения")

if __name__ == "__main__":
    test_intervals()
