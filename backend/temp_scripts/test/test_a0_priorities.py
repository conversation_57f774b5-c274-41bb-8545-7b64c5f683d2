#!/usr/bin/env python3
"""
Тест для проверки работы приоритетов A0 (CORE/EXTENDED)
"""

import asyncio
import os
from motor.motor_asyncio import AsyncIOMotorClient
from bson import ObjectId
from datetime import datetime
from dotenv import load_dotenv

# Загружаем переменные окружения
load_dotenv()

async def test_a0_priorities():
    """Тестирует логику приоритетов A0"""
    
    # Подключаемся к MongoDB
    mongo_uri = os.getenv("MONGODB_URL")
    if not mongo_uri:
        print("❌ MONGODB_URL не найден в .env файле")
        return
    
    client = AsyncIOMotorClient(mongo_uri)
    words_db = client.word_master
    users_db = client.word_master  # Используем ту же БД
    
    try:
        print("🧪 Тестирование приоритетов A0...")
        
        # 1. Проверяем наличие A0 слов с приоритетами
        words_collection = words_db.words

        ultra_core_count = await words_collection.count_documents({
            "level": "A0",
            "priority": "ultra_core"
        })

        core_count = await words_collection.count_documents({
            "level": "A0",
            "priority": "core"
        })

        extended_count = await words_collection.count_documents({
            "level": "A0",
            "priority": "extended"
        })

        total_a0 = await words_collection.count_documents({
            "level": "A0"
        })

        print(f"📊 A0 слова в БД:")
        print(f"   ULTRA-CORE: {ultra_core_count} слов")
        print(f"   CORE: {core_count} слов")
        print(f"   EXTENDED: {extended_count} слов")
        print(f"   Всего A0: {total_a0} слов")

        if ultra_core_count == 0:
            print("⚠️  Нет ULTRA-CORE слов A0 в базе данных")
            print("   Создайте A0 слова с полем priority: 'ultra_core'")
            return
        
        # 2. Создаем тестового пользователя
        users_collection = users_db.users
        test_user = {
            "email": "<EMAIL>",
            "username": "test_a0_user",
            "native_language": "ru",
            "learning_languages": ["en"],
            "created_at": datetime.utcnow()
        }
        
        # Удаляем если существует
        await users_collection.delete_one({"email": test_user["email"]})
        result = await users_collection.insert_one(test_user)
        user_id = result.inserted_id
        print(f"👤 Создан тестовый пользователь: {user_id}")
        
        # 3. Тестируем логику приоритетов
        from app.services.spaced_repetition import SpacedRepetitionService

        service = SpacedRepetitionService()

        # Получаем фильтр приоритетов (должен показывать ULTRA-CORE)
        priority_filter = await service._get_a0_priority_filter(user_id, "en")
        print(f"🎯 Начальный фильтр приоритетов: {priority_filter}")

        if priority_filter and priority_filter.get("priority") == "ultra_core":
            print("✅ Логика работает: показываем только ULTRA-CORE слова")
        else:
            print("❌ Ошибка: должны показываться только ULTRA-CORE слова")
        
        # 4. Симулируем изучение ULTRA-CORE слов
        print("\n🎓 Симулируем изучение ULTRA-CORE слов...")

        # Получаем все ULTRA-CORE слова
        ultra_core_words = await words_collection.find({
            "level": "A0",
            "priority": "ultra_core",
            "language": "en"
        }).to_list(length=100)

        if not ultra_core_words:
            print("❌ Нет ULTRA-CORE слов для английского языка")
            return

        # Добавляем ВСЕ ULTRA-CORE слова в прогресс
        progress_collection = users_db.user_progress

        for word in ultra_core_words:
            await progress_collection.insert_one({
                "user_id": user_id,
                "word_id": word["_id"],
                "interval_level": 5,  # Изученное слово
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            })

        print(f"📚 Добавлено {len(ultra_core_words)} ULTRA-CORE слов в прогресс")

        # 5. Проверяем новый фильтр (должен переключиться на CORE)
        new_priority_filter = await service._get_a0_priority_filter(user_id, "en")
        print(f"🎯 Новый фильтр после ULTRA-CORE: {new_priority_filter}")

        if new_priority_filter and new_priority_filter.get("priority") == "core":
            print("✅ Логика работает: переключились на CORE слова")
        else:
            print("❌ Ошибка: должны переключиться на CORE слова")

        # 6. Симулируем изучение CORE слов
        print("\n🎓 Симулируем изучение CORE слов...")

        core_words = await words_collection.find({
            "level": "A0",
            "priority": "core",
            "language": "en"
        }).to_list(length=100)

        if core_words:
            for word in core_words:
                await progress_collection.insert_one({
                    "user_id": user_id,
                    "word_id": word["_id"],
                    "interval_level": 5,
                    "created_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow()
                })

            print(f"📚 Добавлено {len(core_words)} CORE слов в прогресс")

            # 7. Проверяем финальный фильтр (должен переключиться на EXTENDED)
            final_priority_filter = await service._get_a0_priority_filter(user_id, "en")
            print(f"🎯 Финальный фильтр после CORE: {final_priority_filter}")

            if final_priority_filter and final_priority_filter.get("priority") == "extended":
                print("✅ Логика работает: переключились на EXTENDED слова")
            else:
                print("❌ Ошибка: должны переключиться на EXTENDED слова")
        
        # 6. Очистка
        await users_collection.delete_one({"_id": user_id})
        await progress_collection.delete_many({"user_id": user_id})
        print("🧹 Тестовые данные очищены")
        
        print("\n✅ Тест приоритетов A0 завершен успешно!")
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        client.close()

if __name__ == "__main__":
    asyncio.run(test_a0_priorities())
