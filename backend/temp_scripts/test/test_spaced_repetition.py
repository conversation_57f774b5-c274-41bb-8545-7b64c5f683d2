"""
Тестовый скрипт для проверки работы модуля spaced_repetition.py
"""
import asyncio
import os
import sys
from pathlib import Path
from bson import ObjectId
from datetime import datetime, timedelta
from app.services.spaced_repetition import SpacedRepetitionService

# Добавляем корень проекта в PYTHONPATH
project_root = str(Path(__file__).parent.absolute())
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# ID тестового пользователя
TEST_USER_ID = "666f6d6f6f6f6f6f6f6f6f6f"  # Замените на реальный ID пользователя

class TestSpacedRepetition:
    def __init__(self):
        self.service = SpacedRepetitionService()
        self.user_id = ObjectId(TEST_USER_ID)
        self.current_word = None
    
    async def get_word_progress(self, word_id: ObjectId) -> dict:
        """Получить прогресс по слову."""
        try:
            collection = await self.service.user_progress_collection
            progress = await collection.find_one({
                "user_id": self.user_id,
                "word_id": word_id
            })
            return progress
        except Exception as e:
            print(f"Ошибка при получении прогресса: {str(e)}")
            return None
    
    async def print_word_info(self, word, prefix=""):
        """Выводит информацию о слове"""
        if not word:
            print(f"{prefix}Слово не найдено")
            return
            
        print(f"{prefix}Слово: {word.get('word', 'N/A')}")
        print(f"{prefix}ID: {word.get('word_id', 'N/A')}")
        print(f"{prefix}is_new: {word.get('is_new', 'N/A')}")
        print(f"{prefix}is_forced_review: {word.get('is_forced_review', 'N/A')}")
        if 'examples' in word and word['examples']:
            print(f"{prefix}Пример: {word['examples'][0].get('sentence', 'Нет примера')}")
    
    async def print_progress(self, word_id, word_text=None):
        """Выводит прогресс по слову"""
        progress = await self.get_word_progress(word_id)
        if not progress:
            print(f"❌ Прогресс по слову {word_text or word_id} не найден")
            return None
            
        print(f"\nПрогресс по слову '{word_text or progress['word_id']}':")
        print(f"- Правильных ответов: {progress.get('correct_answers', 0)}")
        print(f"- Неправильных ответов: {progress.get('incorrect_answers', 0)}")
        print(f"- Уровень интервала: {progress.get('interval_level', 0)}")
        print(f"- force_review: {progress.get('force_review', False)}")
        print(f"- is_learned: {progress.get('is_learned', False)}")
        print(f"- Следующее повторение: {progress.get('next_review', 'Не указано')}")
        return progress
    
    async def test_get_new_word(self):
        """Тест получения нового слова"""
        print("\n=== ТЕСТ 1: Получение нового слова ===")
        
        # Получаем новое слово
        word = await self.service.get_next_word(self.user_id)
        if not word:
            print("❌ Не удалось получить новое слово")
            return False
        
        self.current_word = word
        print("\nПолучено новое слово:")
        await self.print_word_info(word, "  ")
        
        # Проверяем, что слово добавлено в прогресс
        progress = await self.get_word_progress(ObjectId(word['word_id']))
        if not progress:
            print("❌ Слово не добавлено в прогресс пользователя")
            return False
            
        print("\n✅ Слово успешно добавлено в прогресс:")
        await self.print_progress(ObjectId(word['word_id']), word.get('word'))
        return True
    
    async def test_incorrect_answer(self):
        """Тест обработки неправильного ответа"""
        if not self.current_word:
            print("❌ Нет текущего слова для тестирования")
            return False
            
        print("\n=== ТЕСТ 2: Обработка неправильного ответа ===")
        word_id = ObjectId(self.current_word['word_id'])
        
        # Симулируем неправильный ответ
        print(f"\nОтправляем неправильный ответ для слова {self.current_word.get('word')}")
        await self.service.process_answer(self.user_id, word_id, False)
        
        # Проверяем, что слово попало в форсированную очередь
        progress = await self.get_word_progress(word_id)
        if not progress:
            print("❌ Не удалось получить прогресс по слову")
            return False
            
        if not progress.get('force_review', False):
            print("❌ Слово не добавлено в форсированную очередь после неправильного ответа")
            return False
            
        print("\n✅ Слово успешно добавлено в форсированную очередь:")
        await self.print_progress(word_id, self.current_word.get('word'))
        
        # Проверяем, что следующее слово будет это же слово
        next_word = await self.service.get_next_word(self.user_id)
        if not next_word or next_word['word_id'] != self.current_word['word_id']:
            print("❌ Следующее слово не совпадает с ожидаемым для повторения")
            return False
            
        print("\n✅ Получено то же слово для повторения")
        return True
    
    async def test_correct_answer(self):
        """Тест обработки правильного ответа"""
        if not self.current_word:
            print("❌ Нет текущего слова для тестирования")
            return False
            
        print("\n=== ТЕСТ 3: Обработка правильного ответа ===")
        word_id = ObjectId(self.current_word['word_id'])
        
        # Симулируем правильный ответ
        print(f"\nОтправляем правильный ответ для слова {self.current_word.get('word')}")
        await self.service.process_answer(self.user_id, word_id, True)
        
        # Проверяем, что слово ушло из форсированной очереди
        progress = await self.get_word_progress(word_id)
        if not progress:
            print("❌ Не удалось получить прогресс по слову")
            return False
            
        if progress.get('force_review', True):
            print("❌ Слово осталось в форсированной очереди после правильного ответа")
            return False
            
        print("\n✅ Слово успешно убрано из форсированной очереди:")
        await self.print_progress(word_id, self.current_word.get('word'))
        
        # Проверяем, что следующее слово будет новым
        next_word = await self.service.get_next_word(self.user_id)
        if not next_word:
            print("❌ Не удалось получить следующее слово")
            return False
            
        if next_word['word_id'] == self.current_word['word_id']:
            print("❌ Получено то же слово, хотя ожидалось новое")
            return False
            
        print("\n✅ Получено новое слово для изучения:")
        await self.print_word_info(next_word, "  ")
        return True

    async def test_is_learned(self) -> bool:
        """Тестирование логики определения изученных слов."""
        print("\n=== ТЕСТ: Проверка флага is_learned ===")
        
        # Шаг 1: Создаем новое слово
        db = await self.service.db
        words_collection = db["words"]
        test_word_id = ObjectId()
        test_word = f"test_word_{test_word_id}"
        
        word_data = {
            "_id": test_word_id,
            "word": test_word,
            "language": "en",
            "level": "A1",
            "part_of_speech": "noun",
            "translations": ["тестовое слово"],
            "examples": [{"sentence": f"This is a test {test_word} ___.", "correct_answers": [test_word], "word_info": "Noun; Sing."}],
            "ipa_pronunciation": "/wɜːrd/",
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "frequency_rank": 1000,
            "tags": ["test"]
        }
        await words_collection.insert_one(word_data)
        
        # Добавляем слово в прогресс
        await self.service._add_word_to_progress(ObjectId(self.user_id), test_word_id)
        
        # Шаг 2: Проверяем начальное состояние
        progress = await self.get_word_progress(test_word_id)
        if progress.get("is_learned") != False:
            print("❌ Начальное состояние: is_learned должно быть False")
            return False
            
        # Шаг 3: Достигаем максимального уровня интервала
        max_level = len(REPETITION_INTERVALS) - 1
        for i in range(max_level + 1):
            await self.service.process_answer(test_word_id, True)
            
        # Шаг 4: Проверяем, что слово считается изученным
        progress = await self.get_word_progress(test_word_id)
        if progress.get("is_learned") != True:
            print("❌ После достижения максимального уровня: is_learned должно быть True")
            return False
            
        print("✅ Тест на флаг is_learned успешно пройден")
        return True

    async def test_queues(self) -> bool:
        """Тестирование работы с очередями карточек."""
        print("\n=== ТЕСТ: Проверка работы с очередями ===")
        
        # Шаг 1: Создаем тестовые слова
        db = await self.service.db
        words_collection = db["words"]
        
        # Создаем слово для активной очереди
        active_word_id = ObjectId()
        active_word = f"active_word_{active_word_id}"
        active_word_data = {
            "_id": active_word_id,
            "word": active_word,
            "language": "en",
            "level": "A1",
            "part_of_speech": "noun",
            "translations": ["активное слово"],
            "examples": [{"sentence": f"This is an active {active_word} ___.", "correct_answers": [active_word], "word_info": "Noun; Sing."}],
            "ipa_pronunciation": "/wɜːrd/",
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "frequency_rank": 1000,
            "tags": ["test"]
        }
        await words_collection.insert_one(active_word_data)
        
        # Создаем слово для форсированной очереди
        force_word_id = ObjectId()
        force_word = f"force_word_{force_word_id}"
        force_word_data = {
            "_id": force_word_id,
            "word": force_word,
            "language": "en",
            "level": "A1",
            "part_of_speech": "noun",
            "translations": ["форсированное слово"],
            "examples": [{"sentence": f"This is a force {force_word} ___.", "correct_answers": [force_word], "word_info": "Noun; Sing."}],
            "ipa_pronunciation": "/wɜːrd/",
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "frequency_rank": 1000,
            "tags": ["test"]
        }
        await words_collection.insert_one(force_word_data)
        
        # Добавляем слова в прогресс
        await self.service._add_word_to_progress(ObjectId(self.user_id), active_word_id)
        await self.service._add_word_to_progress(ObjectId(self.user_id), force_word_id)
        
        # Шаг 2: Проверяем активную очередь
        print("\n=== Проверка активной очереди ===")
        
        # Сначала активное слово должно быть в очереди
        active_progress = await self.get_word_progress(active_word_id)
        if not active_progress.get("force_review", False):
            print(f"❌ Активное слово должно быть в форсированной очереди")
            return False
            
        # После правильного ответа оно должно перейти в активную очередь
        await self.service.process_answer(active_word_id, True)
        active_progress = await self.get_word_progress(active_word_id)
        if active_progress.get("force_review", True):
            print(f"❌ Активное слово должно быть в активной очереди")
            return False
            
        # Шаг 3: Проверяем форсированную очередь
        print("\n=== Проверка форсированной очереди ===")
        
        # Сначала форсированное слово должно быть в форсированной очереди
        force_progress = await self.get_word_progress(force_word_id)
        if not force_progress.get("force_review", False):
            print(f"❌ Форсированное слово должно быть в форсированной очереди")
            return False
            
        # После правильного ответа оно должно перейти в активную очередь
        await self.service.process_answer(force_word_id, True)
        force_progress = await self.get_word_progress(force_word_id)
        if force_progress.get("force_review", True):
            print(f"❌ Форсированное слово должно быть в активной очереди")
            return False
            
        # Шаг 4: Проверяем, что после неправильного ответа слово возвращается в форсированную очередь
        print("\n=== Проверка возвращения в форсированную очередь ===")
        
        # Для активного слова
        await self.service.process_answer(active_word_id, False)
        active_progress = await self.get_word_progress(active_word_id)
        if not active_progress.get("force_review", False):
            print(f"❌ Активное слово должно вернуться в форсированную очередь")
            return False
            
        # Для форсированного слова
        await self.service.process_answer(force_word_id, False)
        force_progress = await self.get_word_progress(force_word_id)
        if not force_progress.get("force_review", False):
            print(f"❌ Форсированное слово должно вернуться в форсированную очередь")
            return False
            
        print("✅ Тест на работу с очередями успешно пройден")
        return True

    async def test_new_words_scenario(self) -> bool:
        """Тестирование сценария с новыми словами."""
        print("\n=== ТЕСТ: Сценарий с новыми словами ===")
        
        # Сценарий 3: Работа с новыми словами
        print("\n=== Сценарий 3: Работа с новыми словами ===")
        db = await self.service.db
        words_collection = db["words"]
        
        # Создаем несколько новых слов
        words = []
        for i in range(5):
            word_id = ObjectId()
            word = f"new_word_{word_id}"
            word_data = {
                "_id": word_id,
                "word": word,
                "language": "en",
                "level": "A1",
                "part_of_speech": "noun",
                "translations": [f"новое слово {i+1}"],
                "examples": [{"sentence": f"This is a new {word} ___.", "correct_answers": [word], "word_info": "Noun; Sing."}],
                "ipa_pronunciation": "/wɜːrd/",
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow(),
                "frequency_rank": 1000,
                "tags": ["test"]
            }
            await words_collection.insert_one(word_data)
            words.append(word_id)
        
        # Добавляем слова в прогресс
        for word_id in words:
            await self.service._add_word_to_progress(ObjectId(self.user_id), word_id)
        
        # Проверяем, что все слова добавлены в форсированную очередь
        for word_id in words:
            progress = await self.get_word_progress(word_id)
            if not progress.get('force_review', False):
                print(f"❌ Новое слово {word_id} должно быть в форсированной очереди")
                return False
        
        # Проверяем, что слова появляются по очереди
        current_word_index = 0
        for _ in range(len(words)):
            next_word = await self.service.get_next_word(self.user_id)
            if not next_word:
                print("❌ Не удалось получить следующее слово")
                return False
                
            word_id = ObjectId(next_word["word_id"])
            if word_id != words[current_word_index]:
                print(f"❌ Ожидалось слово {words[current_word_index]}, получено {word_id}")
                return False
                
            # Отвечаем правильно и проверяем переход в активную очередь
            await self.service.process_answer(word_id, True)
            progress = await self.get_word_progress(word_id)
            if progress.get('force_review', True):
                print(f"❌ Слово {word_id} должно перейти в активную очередь")
                return False
                
            current_word_index += 1
        
        print("\n✅ Сценарий с новыми словами успешно пройден")
        return True

    async def test_real_life_scenarios(self) -> bool:
        """Тестирование реальных сценариев использования."""
        print("\n=== ТЕСТ: Реальные сценарии использования ===")
        
        # Сценарий 1: Чередование правильных и неправильных ответов
        print("\n=== Сценарий 1: Чередование ответов ===")
        db = await self.service.db
        words_collection = db["words"]
        test_word_id = ObjectId()
        test_word = f"scenario_word_{test_word_id}"
        
        word_data = {
            "_id": test_word_id,
            "word": test_word,
            "language": "en",
            "level": "A1",
            "part_of_speech": "noun",
            "translations": ["тестовое слово"],
            "examples": [{"sentence": f"This is a scenario {test_word} ___.", "correct_answers": [test_word], "word_info": "Noun; Sing."}],
            "ipa_pronunciation": "/wɜːrd/",
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "frequency_rank": 1000,
            "tags": ["test"]
        }
        await words_collection.insert_one(word_data)
        
        # Добавляем слово в прогресс
        await self.service._add_word_to_progress(ObjectId(self.user_id), test_word_id)
        
        # Симулируем чередование ответов
        answers = [True, False, True, False, True, True]
        expected_levels = [-1, 0, 0, 0, 0, 1, 2]
        
        for i, is_correct in enumerate(answers):
            await self.service.process_answer(test_word_id, is_correct)
            progress = await self.get_word_progress(test_word_id)
            
            print(f"\n=== Ответ {i+1}: {'Правильный' if is_correct else 'Неправильный'} ===")
            print(f"- Уровень интервала: {progress.get('interval_level', -1)}")
            print(f"- Форсированная очередь: {progress.get('force_review', False)}")
            print(f"- Следующее повторение: {progress.get('next_review', datetime.utcnow())}")
            
            if progress.get('interval_level', -1) != expected_levels[i+1]:
                print(f"❌ Ожидался уровень {expected_levels[i+1]}, получен {progress.get('interval_level', -1)}")
                return False
                
        # Сценарий 2: Достижение максимального уровня и пометка как изученное
        print("\n=== Сценарий 2: Достижение максимального уровня ===")
        
        # Создаем новое слово
        max_level_word_id = ObjectId()
        max_level_word = f"max_level_word_{max_level_word_id}"
        
        word_data = {
            "_id": max_level_word_id,
            "word": max_level_word,
            "language": "en",
            "level": "A1",
            "part_of_speech": "noun",
            "translations": ["слово максимального уровня"],
            "examples": [{"sentence": f"This is a max level {max_level_word} ___.", "correct_answers": [max_level_word], "word_info": "Noun; Sing."}],
            "ipa_pronunciation": "/wɜːrd/",
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "frequency_rank": 1000,
            "tags": ["test"]
        }
        await words_collection.insert_one(word_data)
        
        # Добавляем слово в прогресс
        await self.service._add_word_to_progress(ObjectId(self.user_id), max_level_word_id)
        
        # Достигаем максимального уровня
        max_level = len(REPETITION_INTERVALS) - 1
        for i in range(max_level + 1):
            await self.service.process_answer(max_level_word_id, True)
            
        # Проверяем состояние
        progress = await self.get_word_progress(max_level_word_id)
        if not progress.get('is_learned', False):
            print(f"❌ После достижения максимального уровня слово должно быть помечено как изученное")
            return False
            
        print("\n✅ Все сценарии успешно пройдены")
        return True

    async def test_last_reviewed(self) -> bool:
        """Тестирование обновления времени последнего повторения."""
        print("\n=== ТЕСТ: Проверка обновления last_reviewed ===")
        
        # Шаг 1: Создаем новое слово
        db = await self.service.db
        words_collection = db["words"]
        test_word_id = ObjectId()
        test_word = f"test_word_last_{test_word_id}"
        
        word_data = {
            "_id": test_word_id,
            "word": test_word,
            "language": "en",
            "level": "A1",
            "part_of_speech": "noun",
            "translations": ["тестовое слово"],
            "examples": [{"sentence": f"This is a test {test_word} ___.", "correct_answers": [test_word], "word_info": "Noun; Sing."}],
            "ipa_pronunciation": "/wɜːrd/",
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "frequency_rank": 1000,
            "tags": ["test"]
        }
        await words_collection.insert_one(word_data)
        
        # Добавляем слово в прогресс
        await self.service._add_word_to_progress(ObjectId(self.user_id), test_word_id)
        
        # Шаг 2: Получаем начальное время
        progress = await self.get_word_progress(test_word_id)
        initial_last_reviewed = progress.get("last_reviewed")
        
        # Ждем немного, чтобы убедиться, что время изменилось
        await asyncio.sleep(1)
        
        # Шаг 3: Отправляем правильный ответ
        await self.service.process_answer(test_word_id, True)
        
        # Шаг 4: Проверяем, что время обновилось
        progress = await self.get_word_progress(test_word_id)
        new_last_reviewed = progress.get("last_reviewed")
        
        if new_last_reviewed <= initial_last_reviewed:
            print(f"❌ Время last_reviewed не обновилось: было {initial_last_reviewed}, стало {new_last_reviewed}")
            return False
            
        print("✅ Тест на обновление last_reviewed успешно пройден")
        return True

    async def test_interval_calculation(self):
        """Тестирование расчета интервалов после правильных ответов."""
        print("\n=== ТЕСТ 4: Проверка расчета интервалов ===")
        
        # Шаг 1: Создаем новое слово в базе
        db = await self.service.db
        words_collection = db["words"]
        
        # Генерируем уникальное имя для тестового слова
        test_word_id = ObjectId()
        test_word = f"test_word_{test_word_id}"
        
        word_data = {
            "_id": test_word_id,
            "word": test_word,
            "language": "en",
            "level": "A1",
            "part_of_speech": "noun",
            "translations": ["тестовое слово"],
            "examples": [{"sentence": f"This is a test {test_word} ___.", "correct_answers": [test_word], "word_info": "Noun; Sing."}],
            "ipa_pronunciation": "/wɜːrd/",
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "frequency_rank": 1000,
            "tags": ["test"]
        }
        await words_collection.insert_one(word_data)
        
        word_id = word_data["_id"]
        print(f"\nСоздано тестовое слово: test_word (ID: {word_id})")
        
        # Добавляем слово в прогресс
        await self.service._add_word_to_progress(ObjectId(self.user_id), word_id)
        
        # Шаг 2: Проверяем начальный уровень интервала (должен быть -1 для нового слова)
        progress = await self.get_word_progress(word_id)
        if not progress:
            print("❌ Не удалось получить прогресс по слову")
            return False
            
        interval_level = progress.get("interval_level", None)
        if interval_level != -1:
            print(f"❌ Начальный интервал должен быть -1, получено: {interval_level}")
            return False
        
        # Шаг 3: Отправляем несколько правильных ответов и проверяем интервалы
        # Ожидаемые интервалы в минутах для каждого уровня
        # Для уровня 0 (первый правильный ответ) ожидаем 30 секунд
        # Для уровня 1 (второй правильный ответ) ожидаем 5 минут и т.д.
        expected_intervals_minutes = [0.5, 5, 10, 60, 1440, 4320, 10080]  # 30 сек, 5 мин, 10 мин, 1 час, 1 день, 3 дня, 1 неделя
        
        # Запоминаем время начала теста
        now = datetime.utcnow()
        
        # Начинаем с уровня -1 (новое слово)
        expected_level = -1
        
        for i in range(1, len(expected_intervals_minutes) + 1):
            # Отправляем правильный ответ
            await self.service.process_answer(str(self.user_id), str(word_id), True)
            
            # Получаем обновленный прогресс
            progress = await self.get_word_progress(word_id)
            if not progress:
                print(f"❌ Не удалось получить прогресс после {i}-го правильного ответа")
                return False
                
            current_interval = progress.get("interval_level", -1)
            next_review = progress.get("next_review")
            
            print(f"\nПосле {i} правильного ответа:")
            print(f"- Текущий уровень интервала: {current_interval}")
            print(f"- Следующее повторение: {next_review}")
            
            # Ожидаемый уровень после i+1 правильных ответов
            expected_level_after = min(expected_level + 1, len(expected_intervals_minutes) - 1)
            expected_minutes = expected_intervals_minutes[expected_level_after]
            
            # Проверяем, что уровень интервала увеличился на 1
            if current_interval != expected_level + 1:
                print(f"❌ Ожидался уровень {expected_level + 1}, получен {current_interval}")
                return False
                
            # Обновляем ожидаемый уровень для следующей итерации
            expected_level = expected_level_after
            
            # Проверяем, что дата следующего повторения увеличилась на правильное количество минут
            actual_minutes = (next_review - now).total_seconds() / 60
            
            # Допустимая погрешность в 1 минуту
            if not (abs(actual_minutes - expected_minutes) <= 1):
                print(f"❌ Неправильный интервал до следующего повторения.")
                print(f"   Ожидалось: ~{expected_minutes} минут (уровень {expected_level})")
                print(f"   Получено:  {actual_minutes:.1f} минут (уровень {current_interval})")
                print(f"   Разница:   {abs(actual_minutes - expected_minutes):.1f} минут")
                return False
                
            print(f"✅ Интервал корректен: {actual_minutes:.1f} минут (ожидалось ~{expected_minutes} минут)")
        
        print("\n✅ Проверка интервалов успешно завершена")
        return True
    
    async def run_tests(self) -> None:
        """Запускает все тесты."""
        print("=== Начало тестирования SpacedRepetitionService ===")
        
        # Тесты на получение слов
        if await self.test_get_next_word():
            print("✅ Тест на получение нового слова пройден")
        
        # Тесты на обработку ответов
        if await self.test_process_answer():
            print("✅ Тест на обработку ответов пройден")
        
        # Тесты на флаг is_learned
        if await self.test_is_learned():
            print("✅ Тест на флаг is_learned пройден")
        
        # Тесты на обновление last_reviewed
        if await self.test_last_reviewed():
            print("✅ Тест на обновление last_reviewed пройден")
        
        # Тесты на работу с очередями
        if await self.test_queues():
            print("✅ Тест на работу с очередями пройден")
        
        # Тесты на реальные сценарии использования
        if await self.test_new_words_scenario():
            print("✅ Тест на сценарий с новыми словами пройден")
        
        # Тесты на расчет интервалов
        if await self.test_interval_calculation():
            print("✅ Тест на расчет интервалов пройден")
        
        print("=== Тестирование завершено ===")

async def run_tests():
    print("=== Начало тестирования SpacedRepetitionService ===\n")
    tester = TestSpacedRepetition()
    
    try:
        # Запускаем тесты по порядку
        if await tester.test_get_new_word():
            if await tester.test_incorrect_answer():
                if await tester.test_correct_answer():
                    if await tester.test_interval_calculation():
                        print("\n✅ Все тесты успешно пройдены!")
    except Exception as e:
        print(f"\n❌ Произошла ошибка: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        print("\n=== Тестирование завершено ===")

if __name__ == "__main__":
    asyncio.run(run_tests())
