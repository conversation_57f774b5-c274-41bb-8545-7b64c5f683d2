"""
Простой скрипт для проверки работы интервального повторения.
Проверяет, что при неправильном ответе пользователь получает ту же карточку снова.
"""

import asyncio
import os
import sys
from bson import ObjectId
from datetime import datetime
from dotenv import load_dotenv
from motor.motor_asyncio import AsyncIOMotorClient

# Загружаем переменные окружения из .env файла
load_dotenv()

# Параметры подключения к MongoDB Atlas
MONGODB_URL = os.getenv("MONGODB_URL")
USERS_DB = "users_db"
WORDS_DB = "word_master"

class TestDB:
    def __init__(self):
        self.client = None
        self.users_db = None
        self.words_db = None
        self.users = None
        self.words = None
        self.user_progress = None
    
    async def connect(self):
        """Подключение к MongoDB Atlas"""
        self.client = AsyncIOMotorClient(MONGODB_URL)
        self.users_db = self.client[USERS_DB]
        self.words_db = self.client[WORDS_DB]
        self.users = self.users_db["users"]
        self.words = self.words_db["words"]
        self.user_progress = self.words_db["user_progress"]
        print(f"Подключено к базам данных: {USERS_DB} и {WORDS_DB}")
    
    async def close(self):
        """Закрытие соединения с БД"""
        if self.client:
            self.client.close()
            print("Соединение с БД закрыто")

async def simulate_incorrect_answer(db, user_id, word_id, attempt_number):
    """Симулирует неправильный ответ на слово"""
    print(f"\nПопытка {attempt_number}:")
    
    # Получаем текущий прогресс
    progress = await db.user_progress.find_one({"user_id": user_id, "word_id": word_id})
    
    # Обновляем прогресс с неправильным ответом
    update_data = {
        "$set": {
            "last_reviewed": datetime.utcnow(),
            "next_review": datetime.utcnow(),
            "interval_level": -1,  # Отрицательное значение для форсированного повторения
            "incorrect_answers": (progress["incorrect_answers"] if progress else 0) + 1,
            "last_answer_was_incorrect": True,
            "is_learned": False,
            "updated_at": datetime.utcnow()
        },
        "$setOnInsert": {
            "user_id": user_id,
            "word_id": word_id,
            "created_at": datetime.utcnow(),
            "correct_answers": 0
        }
    }
    
    await db.user_progress.update_one(
        {"user_id": user_id, "word_id": word_id},
        update_data,
        upsert=True
    )
    
    # Получаем обновленный прогресс
    updated_progress = await db.user_progress.find_one({"user_id": user_id, "word_id": word_id})
    
    print(f"   Неправильный ответ записан")
    print(f"   Всего неправильных ответов: {updated_progress['incorrect_answers']}")
    
    return updated_progress

async def test_incorrect_answer_flow():
    db = TestDB()
    try:
        # Подключаемся к БД
        await db.connect()
        
        # Находим тестового пользователя (возьмем первого попавшегося)
        user = await db.users.find_one()
        if not user:
            print("Ошибка: Нет пользователей в базе данных")
            return
            
        user_id = user["_id"]
        print(f"Найден пользователь: {user.get('email')} (ID: {user_id})")
        
        # Получаем первое слово
        word = await db.words.find_one()
        if not word:
            print("Ошибка: Нет слов в базе данных")
            return
            
        word_id = word["_id"]
        print(f"\n1. Получено слово: {word.get('word')} ({word_id})")
        print(f"   Пример: {word.get('examples', [{}])[0].get('sentence', 'Нет примера')}")
        
        # Сбрасываем прогресс по этому слову
        await db.user_progress.delete_many({
            "user_id": user_id,
            "word_id": word_id
        })
        print("2. Сброшен прогресс по слову")
        
        # Симулируем три неправильных ответа подряд
        for attempt in range(1, 4):
            progress = await simulate_incorrect_answer(db, user_id, word_id, attempt)
            
            # Проверяем, что слово находится в очереди на повторение
            check_progress = await db.user_progress.find_one({
                "user_id": user_id,
                "word_id": word_id,
                "last_answer_was_incorrect": True
            })
            
            if check_progress:
                print(f"   Слово остается в очереди на повторение")
                print(f"   Всего неправильных ответов: {check_progress['incorrect_answers']}")
            else:
                print("❌ Ошибка: Слово исчезло из очереди повторений")
        
        # Получаем слово для повторения (должно быть наше тестовое)
        next_word = await db.words.find_one({"_id": word_id})
        if next_word:
            print(f"\n4. Итоговое слово для повторения: {next_word.get('word')} ({next_word.get('_id')})")
            print(f"   Пример: {next_word.get('examples', [{}])[0].get('sentence', 'Нет примера')}")
            
            # Проверяем, что это то же слово
            if str(next_word["_id"]) == str(word_id):
                print("✅ Тест пройден: После трех неправильных ответов получено то же слово")
                
                # Проверяем, что счетчик неправильных ответов увеличился
                final_progress = await db.user_progress.find_one({
                    "user_id": user_id,
                    "word_id": word_id
                })
                
                if final_progress and final_progress.get("incorrect_answers") == 3:
                    print(f"✅ Счетчик неправильных ответов: {final_progress['incorrect_answers']} (корректно)")
                else:
                    print(f"❌ Ошибка: Неправильный счетчик ответов: {final_progress.get('incorrect_answers')}")
            else:
                print("❌ Тест не пройден: Получено другое слово")
        else:
            print("❌ Ошибка: Не удалось получить слово для повторения")
        
    except Exception as e:
        print(f"Ошибка: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await db.close()

if __name__ == "__main__":
    asyncio.run(test_incorrect_answer_flow())
