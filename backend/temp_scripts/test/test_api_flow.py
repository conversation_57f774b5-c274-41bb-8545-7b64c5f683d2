"""
Тестирование API интервального повторения.

Использование:
`backend/venv/bin/activate && python backend/test_api_flow.py`

Перед каждым тестом очищаем прогресс пользователя.

ВНИМАНИЕ: Если тесты не проходят, проверьте логику в файле spaced_repetition.py,
так как именно там реализована основная логика работы с интервальными повторениями.

Список тестов (в порядке их выполнения):

1. test_incorrect_answer_flow - проверяет базовый сценарий работы с неправильными ответами:
   - Создает новую карточку
   - Отправляет правильный ответ, затем неправильный
   - Проверяет, что карточка попала в форсированную очередь
   - Убеждается, что карточка возвращается для повторного изучения

2. test_incorrect_after_correct_answer - проверяет сценарий, когда правильному ответу предшествует неправильный:
   - Создает новую карточку
   - Отправляет неправильный ответ
   - Проверяет добавление в форсированную очередь
   - Проверяет корректность обновления прогресса

3. test_correct_answer_flow - проверяет корректную обработку правильного ответа:
   - Создает новую карточку
   - Отправляет правильный ответ
   - Проверяет увеличение уровня интервала
   - Убеждается, что карточка не попала в форсированную очередь

4. test_multiple_correct_answers - проверяет серию правильных ответов:
   - Создает новую карточку
   - Отправляет несколько правильных ответов подряд
   - Проверяет постепенное увеличение уровня интервала
   - Убеждается, что карточка выходит из форсированной очереди

5. test_new_card_multiple_incorrect_answers - проверяет обработку неправильных ответов для новой карточки (уровень -1):
   - Создает новую карточку (уровень -1)
   - Отправляет 5 неправильных ответов подряд
   - Проверяет, что уровень остается -1
   - Убеждается, что карточка остается в форсированной очереди
   - Проверяет корректность обновления счетчиков правильных/неправильных ответов
   - Проверяет, что время следующего повторения устанавливается на текущее время
   - Убеждается, что карточка доступна для повторного ответа сразу после ошибки

6. test_multiple_incorrect_answers - проверяет обработку неправильных ответов для карточки с уровнем 2:
   - Создает новую карточку
   - Поднимает уровень карточки до 2 с помощью правильных ответов
   - Отправляет серию неправильных ответов
   - Проверяет понижение уровня карточки
   - Убеждается, что карточка попадает в форсированную очередь
   - Проверяет корректность обновления счетчиков

7. test_progress_through_all_levels - проверяет прохождение карточки через все уровни интервалов:
   - Создает новую карточку
   - Последовательно отвечает правильно, повышая уровень
   - Проверяет корректность обновления интервалов
   - Проверяет установку флага is_learned на максимальном уровне
   - Проверяет корректность времени следующего повторения на каждом уровне
   - Убеждается, что карточка выходит из активной очереди после достижения максимального уровня

8. test_new_word_loading - проверяет загрузку нового слова при пустой очереди:
   - Очищает прогресс пользователя
   - Добавляет одно тестовое слово
   - Отправляет правильный ответ, чтобы вывести слово из активной очереди
   - Проверяет, что система предлагает новое слово из коллекции words
   - Проверяет, что новое слово корректно добавлено в прогресс

Каждый тест включает проверку:
- Корректности обновления уровня интервала
- Состояния форсированной очереди
- Времени следующего повторения
- Счетчиков правильных и неправильных ответов

Для отладки и внесения изменений в логику работы с интервальными повторениями
обратитесь к файлу spaced_repetition.py, где реализована основная логика.
"""

import asyncio
import json
import os
import sys
from datetime import datetime, timezone, timedelta
import httpx
import logging
from dotenv import load_dotenv
from bson import ObjectId
from app.database import db as database, get_database, Database  # Добавляем импорт get_database

# Добавляем корневую директорию проекта в PYTHONPATH
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Загружаем переменные окружения из .env файла
load_dotenv()

# Настройки API
BASE_URL = "http://localhost:8001"  # URL локального сервера
TOKEN = None  # Токен аутентификации

async def get_card_progress(card_id: str) -> dict:
    """Получает прогресс карточки по её ID.
    
    Args:
        card_id: ID карточки
        
    Returns:
        dict: Прогресс карточки или None, если не найден
    """
    try:
        db = get_database()
        if not db.client:
            await db.connect_to_db()
            
        progress_collection = db.client.word_master.user_progress
        progress = await progress_collection.find_one({"word_id": ObjectId(card_id)})
        return progress
    except Exception as e:
        print(f"[ERROR] Ошибка при получении прогресса карточки: {e}")
        import traceback
        traceback.print_exc()
        return None

async def cleanup_user_progress(user_id: str):
    """Очищаем прогресс пользователя.
    
    Args:
        user_id: ID пользователя, чей прогресс нужно очистить
    """
    try:
        db = get_database()
        if not db.client:
            await db.connect_to_db()
            
        print(f"[DEBUG] Начинаем очистку прогресса для пользователя {user_id}")
        
        # Получаем коллекцию user_progress из базы word_master
        user_progress = db.client.word_master.user_progress
        
        # Проверяем, есть ли записи до удаления
        count_before = await user_progress.count_documents({"user_id": ObjectId(user_id)})
        print(f"[DEBUG] Найдено записей до удаления: {count_before}")
        
        if count_before > 0:
            # Удаляем записи
            result = await user_progress.delete_many({"user_id": ObjectId(user_id)})
            
            # Проверяем, что удаление прошло успешно
            count_after = await user_progress.count_documents({"user_id": ObjectId(user_id)})
            print(f"[DEBUG] Удалено записей: {result.deleted_count}")
            print(f"[DEBUG] Осталось записей после удаления: {count_after}")
            
            if count_after == 0:
                print(f"✅ Прогресс пользователя {user_id} успешно очищен")
            else:
                print(f"⚠️ Не удалось полностью очистить прогресс пользователя {user_id}")
        else:
            print("✅ Нет записей для удаления")
    except Exception as e:
        print(f"[ERROR] Ошибка при очистке прогресса пользователя: {e}")
        import traceback
        traceback.print_exc()
        raise

async def add_test_word(word: str, translations: list) -> dict:
    """Добавляет тестовое слово в базу данных.
    
    Args:
        word: Слово на английском
        translations: Список переводов
        
    Returns:
        dict: Созданное слово или None в случае ошибки
    """
    try:
        db = get_database()
        if not db.client:
            await db.connect_to_db()
            
        words_collection = db.client.word_master.words
        
        # Создаем тестовое слово
        test_word = {
            "word": word,
            "translations": translations,
            "language": "en",
            "level": "A1",
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        # Вставляем слово в базу
        result = await words_collection.insert_one(test_word)
        
        # Получаем созданное слово
        created_word = await words_collection.find_one({"_id": result.inserted_id})
        
        print(f"[DEBUG] Добавлено тестовое слово: {word} (ID: {result.inserted_id})")
        return created_word
        
    except Exception as e:
        print(f"[ERROR] Ошибка при добавлении тестового слова: {e}")
        import traceback
        traceback.print_exc()
        return None

async def get_auth_token():
    """Получаем токен аутентификации
    
    Returns:
        str: Токен доступа или None в случае ошибки
    """
    global TOKEN
    if TOKEN:
        return TOKEN
    
    auth_data = {
        "username": "<EMAIL>",
        "password": "Test123!"
    }
    
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.post(
                f"{BASE_URL}/api/token",
                data=auth_data,
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )
            
        if response.status_code == 200:
            TOKEN = response.json().get("access_token")
            if not TOKEN:
                print("❌ Ошибка: В ответе отсутствует access_token")
                return None
                
            print("✅ Успешная аутентификация")
            return TOKEN
        else:
            print(f"❌ Ошибка аутентификации {response.status_code}: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Исключение при аутентификации: {str(e)}")
        return None

async def get_next_card():
    """Получаем следующую карточку для изучения
    
    Returns:
        dict: Данные карточки или None в случае ошибки
    """
    token = await get_auth_token()
    if not token:
        print("❌ Ошибка: Не удалось получить токен аутентификации")
        return None
        
    headers = {
        "Authorization": f"Bearer {token}",
        "Accept": "application/json"
    }
    
    params = {
        "user_id": "6848235d12259195693cb594",  # ID тестового пользователя
        "native_lang": "ru",
        "target_lang": "en"
    }
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{BASE_URL}/api/spaced/next",
                headers=headers,
                params=params,
                timeout=10.0
            )
        
        if response.status_code == 200:
            result = response.json()
            print(f"[DEBUG] Получена карточка: {result.get('word')} (ID: {result.get('word_id')})")
            return result
        else:
            print(f"❌ Ошибка {response.status_code} при получении карточки: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Исключение при получении карточки: {str(e)}")
        return None

async def submit_card_response(card_id, is_correct, response_time=5.0):
    """Отправляем ответ на карточку
    
    Args:
        card_id: ID карточки
        is_correct: Правильный ли ответ
        response_time: Время ответа в секундах
        
    Returns:
        dict: Ответ сервера или None в случае ошибки
    """
    token = await get_auth_token()
    if not token:
        print("❌ Ошибка: Не удалось получить токен аутентификации")
        return None
        
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    data = {
        "user_id": "6848235d12259195693cb594",  # ID тестового пользователя
        "is_correct": is_correct,
        "response_time": response_time
    }
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{BASE_URL}/api/spaced/{card_id}/response",
                headers=headers,
                json=data,
                timeout=10.0
            )
        
        if response.status_code == 200:
            result = response.json()
            print(f"[DEBUG] Ответ сервера: {result}")
            return result
        else:
            print(f"❌ Ошибка {response.status_code} при отправке ответа: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Исключение при отправке ответа: {str(e)}")
        return None
        return None

async def test_incorrect_answer_flow():
    """Тестируем поток с неправильными ответами через API"""
    print("🔍 Начинаем тестирование API...")
    
    # Очищаем прогресс тестового пользователя
    test_user_id = "6848235d12259195693cb594"
    await cleanup_user_progress(test_user_id)
    
    # 1. Получаем первую карточку
    print("\n1. Получаем первую карточку...")
    first_card = await get_next_card()
    if not first_card:
        print("❌ Не удалось получить первую карточку")
        return
        
    card_id = first_card.get("word_id")
    print(f"   Получена карточка: {first_card.get('word')} (ID: {card_id})")
    
    # 2. Отправляем правильный ответ
    print(f"\n2. Отправляем правильный ответ для карточки {card_id}...")
    response = await submit_card_response(card_id, is_correct=True)
    if not response:
        print("❌ Не удалось отправить правильный ответ")
        return
        
    print(f"   Ответ сервера: интервал={response.get('interval_level')}, след. повторение={response.get('next_review')}")
    
    # 3. Отправляем неправильный ответ
    print(f"\n3. Отправляем неправильный ответ для карточки {card_id}...")
    response = await submit_card_response(card_id, is_correct=False)
    if not response:
        print("❌ Не удалось отправить неправильный ответ")
        return
        
    print(f"   Ответ сервера: интервал={response.get('interval_level')}, след. повторение={response.get('next_review')}")
    
    # 4. Получаем следующую карточку (должна быть та же самая)
    print("\n4. Получаем следующую карточку...")
    next_card = await get_next_card()
    if not next_card:
        print("❌ Не удалось получить следующую карточку")
        return
        
    next_card_id = next_card.get("word_id")
    print(f"   Получена карточка: {next_card.get('word')} (ID: {next_card_id})")
    
    # 5. Проверяем, что это та же карточка
    if str(next_card_id) == str(card_id):
        print("✅ Тест пройден: После неправильного ответа получена та же карточка")
    else:
        print(f"❌ Тест не пройден: Ожидалась карточка {card_id}, получена {next_card_id}")
        print(f"   Детали next_card: {next_card}")
        return
    
    # 6. Проверяем, что карточка в форсированной очереди
    print("6. Проверяем, что карточка в форсированной очереди...")
    next_card = await get_next_card()
    print(f"   Детали полученной карточки: {next_card}")
    
    if not next_card:
        print("❌ Ошибка: Не удалось получить карточку из форсированной очереди")
        return
        
    next_card_id = next_card.get("word_id")
    if next_card_id != card_id:
        print(f"❌ Тест не пройден: Карточка не в форсированной очереди. Ожидалось {card_id}, получено {next_card_id}")
        return
    print("✅ Тест пройден: Карточка в форсированной очереди")
    
    # 7. Проверяем, что карточка имеет флаг is_forced_review
    is_forced = next_card.get("is_forced_review", False)
    print(f"   Флаг is_forced_review: {is_forced}")
    
async def test_incorrect_after_correct_answer():
    """
    Тестируем поток с правильным, а затем неправильным ответом.
    Аналог первого теста, но с предварительным правильным ответом.
    """
    print("\n" + "="*50)
    print("=== Тест: правильный ответ, затем неправильный ===\n")
    
    # 1. Получаем первую карточку
    print("1. Получаем первую карточку...")
    first_card = await get_next_card()
    if not first_card:
        print("❌ Не удалось получить первую карточку")
        return False
        
    card_id = first_card.get("word_id")
    print(f"   Получена карточка: {first_card.get('word')} (ID: {card_id})")
    
    # 2. Отправляем правильный ответ
    print(f"\n2. Отправляем правильный ответ для карточки {card_id}...")
    response = await submit_card_response(card_id, is_correct=True)
    if not response:
        print("❌ Не удалось отправить правильный ответ")
        return False
        
    print(f"   Ответ сервера: интервал={response.get('interval_level')}, след. повторение={response.get('next_review')}")
    
    # 3. Отправляем неправильный ответ
    print(f"\n3. Отправляем неправильный ответ для карточки {card_id}...")
    response = await submit_card_response(card_id, is_correct=False)
    if not response:
        print("❌ Не удалось отправить неправильный ответ")
        return False
        
    print(f"   Ответ сервера: интервал={response.get('interval_level')}, след. повторение={response.get('next_review')}")
    
    # 4. Получаем следующую карточку (должна быть та же самая)
    print("\n4. Получаем следующую карточку...")
    next_card = await get_next_card()
    if not next_card:
        print("❌ Не удалось получить следующую карточку")
        return False
        
    next_card_id = next_card.get("word_id")
    print(f"   Получена карточка: {next_card.get('word')} (ID: {next_card_id})")
    
    # 5. Проверяем, что это та же карточка
    if str(next_card_id) == str(card_id):
        print("✅ Тест пройден: После неправильного ответа получена та же карточка")
    else:
        print(f"❌ Тест не пройден: Ожидалась карточка {card_id}, получена {next_card_id}")
        print(f"   Детали next_card: {next_card}")
        return False
    
    # 6. Проверяем, что карточка в форсированной очереди
    print("6. Проверяем, что карточка в форсированной очереди...")
    next_card = await get_next_card()
    print(f"   Детали полученной карточки: {next_card}")
    
    if not next_card:
        print("❌ Ошибка: Не удалось получить карточку из форсированной очереди")
        return False
        
    next_card_id = next_card.get("word_id")
    if next_card_id != card_id:
        print(f"❌ Тест не пройден: Карточка не в форсированной очереди. Ожидалось {card_id}, получено {next_card_id}")
        return False
    print("✅ Тест пройден: Карточка в форсированной очереди")
    
    # 7. Проверяем, что карточка имеет флаг is_forced_review
    is_forced = next_card.get("is_forced_review", False)
    print(f"   Флаг is_forced_review: {is_forced}")
    
    if not is_forced:
        print("❌ Тест не пройден: У карточки не установлен флаг is_forced_review")
        print(f"   Полная информация о карточке: {next_card}")
        return False
        
    print("✅ Тест пройден: У карточки установлен флаг is_forced_review")
    
    return True

async def test_correct_answer_flow():
    """Тестируем поток с правильным ответом через API"""
    print("\n🔍 Тестируем сценарий с правильным ответом...")
    
    # Очищаем прогресс тестового пользователя
    test_user_id = "6848235d12259195693cb594"
    await cleanup_user_progress(test_user_id)
    
    # 1. Получаем первую карточку
    print("\n1. Получаем первую карточку...")
    try:
        card = await get_next_card()
        if not card or not card.get("word_id"):
            print("❌ Ошибка: не удалось получить карточку")
            return
            
        card_id = card.get("word_id")
        if not card_id:
            print(f"❌ Ошибка: У карточки отсутствует word_id. Полученные данные: {card}")
            return
            
        print(f"   Получена карточка: {card.get('word', 'Без названия')} (ID: {card_id})")
    except Exception as e:
        print(f"❌ Непредвиденная ошибка при получении карточки: {str(e)}")
        import traceback
        traceback.print_exc()
        return
    
    # 2. Отправляем правильный ответ
    print(f"\n2. Отправляем правильный ответ для карточки {card_id}...")
    try:
        response = await submit_card_response(card_id, is_correct=True)
        
        # Проверяем, что ответ успешный
        if not response:
            print("❌ Ошибка: Пустой ответ от сервера")
            return
            
        if not response.get("success"):
            error_msg = response.get("error", "Неизвестная ошибка")
            print(f"❌ Ошибка при отправке ответа: {error_msg}")
            print(f"   Полный ответ: {response}")
            return
        
        print(f"✅ Ответ успешно обработан")
        print(f"   Детали: is_correct={response.get('is_correct')}, interval_level={response.get('interval_level')}")
        
    except Exception as e:
        print(f"❌ Непредвиденная ошибка при отправке ответа: {str(e)}")
        import traceback
        traceback.print_exc()
        return
    
    # 3. Проверяем, что следующее повторение установлено в будущем
    print("\n3. Проверяем, что следующее повторение установлено в будущем...")
    next_review = response.get("next_review")
    if not next_review:
        print("❌ Ошибка: next_review не установлен")
        return
    
    # Выводим отладочную информацию
    print(f"   Строка next_review: {next_review}")
    
    # Преобразуем строку с датой в datetime объект
    try:
        # Пробуем разобрать дату с часовым поясом
        if 'Z' in next_review or '+' in next_review or '-' in next_review[-6:]:
            # Удаляем 'Z' и добавляем +00:00 для совместимости
            next_review_clean = next_review.replace('Z', '+00:00')
            next_review_dt = datetime.fromisoformat(next_review_clean)
            # Если дата без информации о часовом поясе, добавляем UTC
            if next_review_dt.tzinfo is None:
                next_review_dt = next_review_dt.replace(tzinfo=timezone.utc)
        else:
            # Если дата без информации о времени, добавляем время и часовой пояс
            next_review_dt = datetime.fromisoformat(next_review).replace(tzinfo=timezone.utc)
            
        # Получаем текущее время с часовым поясом UTC
        now = datetime.now(timezone.utc)
        
        # Нормализуем оба времени к одному часовому поясу (UTC)
        next_review_dt = next_review_dt.astimezone(timezone.utc)
        now = now.astimezone(timezone.utc)
        
        print(f"   Следующее повторение (UTC): {next_review_dt}")
        print(f"   Текущее время (UTC): {now}")
        print(f"   Разница: {next_review_dt - now}")
        
        if next_review_dt > now:
            print(f"✅ Тест пройден: Следующее повторение установлено на {next_review_dt} (через {next_review_dt - now})")
        else:
            print(f"❌ Ошибка: Следующее повторение установлено в прошлом: {next_review_dt}")
            
    except ValueError as e:
        print(f"❌ Ошибка при разборе даты: {e}")
        import traceback
        traceback.print_exc()
        return
    except Exception as e:
        print(f"❌ Непредвиденная ошибка: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 4. Проверяем, что интервал установлен корректно (0 - это нормально после ошибки)
    print("\n4. Проверяем, что интервал установлен корректно...")
    interval = response.get("interval_level")
    if interval is None:
        print("❌ Ошибка: interval_level не установлен")
        return
    
    # Преобразуем в int, если пришло строкой
    try:
        interval = int(interval) if isinstance(interval, str) else interval
    except (ValueError, TypeError):
        print(f"❌ Ошибка: Невозможно преобразовать interval_level в число: {interval}")
        return
    
    # Проверяем, что интервал не отрицательный (кроме случаев форсированного повторения)
    if interval >= 0:
        print(f"✅ Тест пройден: Интервал установлен в {interval}")
    else:
        print(f"❌ Ошибка: Интервал не может быть отрицательным: {interval}")
    
    # 5. Проверяем, что карточка не в форсированной очереди
    print("\n5. Проверяем, что карточка не в форсированной очереди...")
    next_card = await get_next_card()
    next_card_id = next_card.get("word_id")
    
    if next_card_id != card_id:
        print(f"✅ Тест пройден: Получена новая карточка: {next_card.get('word')} (ID: {next_card_id})")
    else:
        print(f"❌ Ошибка: Получена та же карточка, что и до этого (ID: {next_card_id})")

async def test_multiple_correct_answers():
    """Тестируем несколько правильных ответов подряд"""
    print("\n" + "="*50)
    print("=== Тест нескольких правильных ответов подряд ===\n")
    
    # Очищаем прогресс тестового пользователя
    test_user_id = "6848235d12259195693cb594"
    await cleanup_user_progress(test_user_id)
    
    try:
        # 1. Получаем первую карточку
        print("1. Получаем первую карточку...")
        card = await get_next_card()
        if not card or not card.get("word_id"):
            print("❌ Ошибка: не удалось получить карточку")
            return
            
        card_id = card.get("word_id")
        print(f"   Получена карточка: {card.get('word', 'Без названия')} (ID: {card_id})")
        
        # 2. Отправляем неправильный ответ, чтобы карточка попала в форсированную очередь
        print(f"\n2. Отправляем неправильный ответ для карточки {card_id}...")
        response = await submit_card_response(card_id, is_correct=False)
        
        if not response or not response.get("success"):
            print(f"❌ Ошибка при отправке ответа: {response}")
            return
        
        print(f"✅ Ответ обработан. Интервал: {response.get('interval_level')}")
        
        # 3. Проверяем, что карточка в форсированной очереди
        print("\n3. Проверяем, что карточка в форсированной очереди...")
        card_again = await get_next_card()
        if not card_again or str(card_again.get("word_id")) != card_id:
            print(f"❌ Ошибка: не получена та же карточка. Ожидался ID: {card_id}, получено: {card_again}")
            return
            
        print(f"✅ Получена та же карточка: {card_again.get('word')} (ID: {card_again.get('word_id')})")
        print(f"   is_forced_review: {card_again.get('is_forced_review', False)}")
        
        # 4. Отправляем правильный ответ
        print(f"\n4. Отправляем правильный ответ...")
        correct_response = await submit_card_response(card_id, is_correct=True)
        
        if not correct_response or not correct_response.get("success"):
            print(f"❌ Ошибка при отправке правильного ответа: {correct_response}")
            return
            
        print(f"✅ Правильный ответ обработан. Новый интервал: {correct_response.get('interval_level')}")
        
        # 5. Проверяем, что карточка больше не в форсированной очереди
        print("\n5. Проверяем, что карточка больше не в форсированной очереди...")
        next_card = await get_next_card()
        if not next_card:
            print("❌ Ошибка: не удалось получить следующую карточку")
            return
            
        if str(next_card.get("word_id")) == card_id:
            print(f"❌ Ошибка: карточка все еще в очереди (ID: {card_id})")
        else:
            print(f"✅ Получена новая карточка: {next_card.get('word')} (ID: {next_card.get('word_id')})")
            print("   Карточка успешно вышла из форсированной очереди")
        
    except Exception as e:
        print(f"❌ Непредвиденная ошибка: {str(e)}")
        import traceback
        traceback.print_exc()

async def create_test_card():
    """Создаем тестовую карточку для проверки интервалов"""
    try:
        # Получаем базу данных
        db = get_database()
        
        # Подключаемся к базе данных, если еще не подключены
        if not db.client:
            await db.connect_to_db()
        
        # Получаем коллекции
        words_collection = db.words_db.get_collection("words")
        # Получаем коллекцию user_progress из базы word_master
        progress_collection = db.client.word_master.user_progress
        
        # Создаем тестовое слово
        test_word = {
            "word": f"test_word_{datetime.utcnow().timestamp()}",
            "translation": "тестовое_слово",
            "examples": ["This is a test word", "Это тестовое слово"],
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        # Вставляем слово в базу
        result = await words_collection.insert_one(test_word)
        word_id = str(result.inserted_id)
        
        # Создаем прогресс для пользователя
        user_id = "6848235d12259195693cb594"  # ID тестового пользователя
        
        progress = {
            "user_id": ObjectId(user_id),
            "word_id": ObjectId(word_id),
            "interval_level": -1,  # Начинаем с -1, так как первый правильный ответ установит уровень в 0
            "next_review": datetime.utcnow(),
            "correct_answers": 0,
            "incorrect_answers": 0,
            "is_learned": False,
            "force_review": False,
            "last_reviewed": None,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        result = await progress_collection.insert_one(progress)
        
        return {
            "word_id": word_id,
            "word": test_word["word"],
            "progress_id": str(result.inserted_id)
        }
        
    except Exception as e:
        print(f"❌ Ошибка при создании тестовой карточки: {e}")
        import traceback
        traceback.print_exc()
        return None

async def test_progress_through_all_levels():
    """
    Проверяем прохождение карточки через все уровни интервалов.
    
    Этот тест проверяет, как карточка проходит через все уровни интервалов,
    начиная с -1 (новая карточка) до максимального уровня.
    
    Основные проверки:
    1. Корректное увеличение уровня интервала при правильных ответах
    2. Корректное обновление времени следующего повторения
    3. Установка флага is_learned при достижении максимального уровня
    4. Корректность обновления счетчиков правильных ответов
    
    Ожидаемое поведение:
    1. При каждом правильном ответе уровень интервала увеличивается на 1
    2. Время следующего повторения устанавливается в соответствии с уровнем
    3. При достижении максимального уровня карточка помечается как выученная
    4. Счетчик правильных ответов увеличивается после каждого ответа
    
    Шаги теста:
    1. Создаем новую карточку (уровень -1)
    2. Последовательно даем правильные ответы, пока не достигнем максимального уровня
    3. После каждого ответа проверяем:
       - Текущий уровень интервала
       - Состояние is_learned
       - Время следующего повторения
       - Счетчик правильных ответов
    """
    print("\n" + "="*50)
    print("=== Тест прохождения через все уровни интервалов ===\n")
    
    # Очищаем прогресс тестового пользователя
    test_user_id = "6848235d12259195693cb594"
    await cleanup_user_progress(test_user_id)
    
    try:
        # 1. Создаем тестовую карточку
        print("1. Создаем тестовую карточку...")
        test_card = await create_test_card()
        if not test_card:
            print("❌ Не удалось создать тестовую карточку")
            return False
            
        card_id = test_card["word_id"]
        print(f"   Создана тестовая карточка: {test_card['word']} (ID: {card_id})")
        
        # 2. Получаем прогресс карточки, чтобы отслеживать изменения
        db = get_database()
        if not db.client:
            await db.connect_to_db()
            
        progress_collection = db.client.word_master.user_progress
        
        # 3. Функция для получения текущего прогресса
        async def get_card_progress():
            progress = await progress_collection.find_one({"word_id": ObjectId(card_id)})
            return progress
            
        # 4. Получаем начальный прогресс
        progress = await get_card_progress()
        if not progress:
            print("❌ Не удалось получить начальный прогресс карточки")
            return False
            
        print(f"   Начальный интервал: {progress.get('interval_level', -1)}")
        
        # 5. Импортируем MAX_INTERVAL_LEVEL из spaced_repetition
        from app.services.spaced_repetition import MAX_INTERVAL_LEVEL, REPETITION_INTERVALS
        
        print(f"\nМаксимальный уровень интервала: {MAX_INTERVAL_LEVEL}")
        print(f"Всего уровней: {len(REPETITION_INTERVALS)}")
        print("Интервалы:", [f"{i}: {REPETITION_INTERVALS[i]} мин" for i in range(len(REPETITION_INTERVALS))])
        
        # 6. Проходим все уровни интервалов
        for target_level in range(0, MAX_INTERVAL_LEVEL + 1):
            print(f"\n=== Переход на уровень {target_level} ===")
            
            # Отправляем правильный ответ
            print(f"{target_level + 1}. Отправляем правильный ответ...")
            response = await submit_card_response(card_id, is_correct=True)
            if not response or not response.get("success"):
                print(f"❌ Ошибка при отправке ответа: {response}")
                return False
                
            # Получаем обновленный прогресс
            progress = await get_card_progress()
            if not progress:
                print("❌ Не удалось получить обновленный прогресс")
                return False
                
            # Проверяем уровень интервала
            current_level = progress.get("interval_level", -1)
            next_review = progress.get("next_review")
            is_learned = progress.get("is_learned", False)
            
            print(f"   Текущий уровень: {current_level}")
            print(f"   Правильных ответов: {progress.get('correct_answers', 0)}")
            print(f"   Следующее повторение: {next_review}")
            print(f"   Выучено: {is_learned}")
            
            # Проверяем, что уровень увеличился на 1 (кроме перехода с -1 на 0)
            expected_level = target_level if target_level > 0 else 0
            if current_level != expected_level:
                print(f"❌ Неправильный уровень после ответа. Ожидался {expected_level}, получен {current_level}")
                return False
                
            # Проверяем, что карточка не в форсированной очереди
            if progress.get("force_review", False):
                print("❌ Карточка в форсированной очереди после правильного ответа")
                return False
                
            # Проверяем, что next_review установлен корректно
            if next_review is None:
                print("❌ Не установлено время следующего повторения")
                return False
                
            print("   ✅ Уровень интервала корректен")
            
            # Если это последний уровень, проверяем, что карточка помечена как выученная
            if target_level == MAX_INTERVAL_LEVEL:
                if not is_learned:
                    print("❌ Карточка не помечена как выученная на максимальном уровне")
                    return False
                print("   ✅ Карточка помечена как выученная")
            else:
                # Для всех уровней, кроме максимального, проверяем, что карточка не помечена как выученная
                if is_learned:
                    print(f"❌ Карточка помечена как выученная на уровне {current_level}, но еще не достигнут максимальный уровень")
                    return False
        
        print("\n✅ Тест пройден успешно!")
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при выполнении теста: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_new_card_multiple_incorrect_answers():
    """
    Проверяем обработку неправильных ответов для новой карточки (уровень -1).
    
    Этот тест проверяет, как система обрабатывает серию неправильных ответов для новой карточки,
    которая изначально имеет уровень -1 (только что добавленная карточка).
    
    Критически важные аспекты теста:
    1. Проверка сохранения уровня -1 при любом количестве неправильных ответов
    2. Контроль состояния форсированной очереди (должно оставаться True)
    3. Валидация времени следующего повторения (должно быть текущее время)
    4. Мониторинг счетчиков ответов (incorrect_answers должен увеличиваться)
    5. Проверка доступности карточки для немедленного повторного ответа
    
    Детальное описание проверок:
    
    1. Проверка уровня интервала:
       - После каждого неправильного ответа уровень должен оставаться -1
       - Это гарантирует, что карточка не будет переведена в обычную очередь
       
    2. Проверка форсированной очереди:
       - Флаг force_review должен быть True после каждого ответа
       - Это обеспечивает приоритетный показ карточки
       
    3. Проверка времени следующего повторения:
       - next_review должно быть установлено на текущее время
       - Это позволяет пользователю сразу же повторить карточку
       
    4. Проверка счетчиков ответов:
       - incorrect_answers должен увеличиваться на 1 после каждого неправильного ответа
       - correct_answers должен оставаться 0
       
    5. Проверка доступности карточки:
       - Карточка должна быть доступна для повторного ответа сразу же
       - Это проверяется запросом следующей карточки сразу после ошибки
    
    Ожидаемое поведение системы:
    1. При первом неправильном ответе:
       - Уровень остается -1
       - force_review = True
       - incorrect_answers = 1
       - next_review = текущее время
       
    2. При последующих неправильных ответах (до 5):
       - Уровень остается -1
       - force_review остается True
       - incorrect_answers увеличивается на 1
       - next_review обновляется на текущее время
    
    Этот тест критически важен для проверки базовой логики работы с ошибками
    и гарантирует, что пользователь сможет повторять сложные карточки до тех пор,
    пока не запомнит их правильно.
    
    Для отладки или изменения логики работы с неправильными ответами
    обратитесь к методу process_answer в файле spaced_repetition.py
    """
    print("\n" + "="*50)
    print("=== Тест 5 неправильных ответов для новой карточки (уровень -1) ===\n")
    
    # Очищаем прогресс тестового пользователя
    test_user_id = "6848235d12259195693cb594"
    await cleanup_user_progress(test_user_id)
    
    try:
        # 1. Создаем тестовую карточку
        print("1. Создаем тестовую карточку...")
        test_card = await create_test_card()
        if not test_card:
            print("❌ Не удалось создать тестовую карточку")
            return False
            
        card_id = test_card["word_id"]
        print(f"   Создана тестовая карточка: {test_card['word']} (ID: {card_id})")
        
        # 2. Получаем прогресс карточки, чтобы отслеживать изменения
        db = get_database()
        if not db.client:
            await db.connect_to_db()
            
        progress_collection = db.client.word_master.user_progress
        
        # 3. Функция для получения текущего прогресса
        async def get_card_progress():
            progress = await progress_collection.find_one({"word_id": ObjectId(card_id)})
            return progress
            
        # 4. Получаем начальный прогресс
        initial_progress = await get_card_progress()
        if not initial_progress:
            print("❌ Не удалось получить начальный прогресс карточки")
            return False
            
        print(f"   Начальный интервал: {initial_progress.get('interval_level', -1)}")
        
        # 5. Отправляем 5 неправильных ответов подряд
        expected_levels = [-1, -1, -1, -1, -1]  # Уровень всегда остается -1 для новой карточки
        
        for i in range(1, 6):
            print(f"\n{i}. Отправляем неправильный ответ...")
            
            # Отправляем ответ
            response = await submit_card_response(card_id, is_correct=False)
            if not response or not response.get("success"):
                print(f"❌ Ошибка при отправке ответа: {response}")
                return False
                
            # Получаем обновленный прогресс
            progress = await get_card_progress()
            if not progress:
                print("❌ Не удалось получить обновленный прогресс")
                return False
                
            # Проверяем интервал
            current_level = progress.get("interval_level", 0)
            expected_level = expected_levels[i-1]
            
            print(f"   Интервал: {current_level} (ожидалось: {expected_level})")
            print(f"   Правильных ответов: {progress.get('correct_answers', 0)}")
            print(f"   Неправильных ответов: {progress.get('incorrect_answers', 0)}")
            print(f"   Следующее повторение: {progress.get('next_review', 'не установлено')}")
            
            if current_level != expected_level:
                print(f"❌ Неправильный уровень после {i} неправильного ответа. Ожидался {expected_level}, получен {current_level}")
                return False
                
            # Проверяем, что карточка в форсированной очереди
            if not progress.get("force_review", False):
                print("❌ Карточка не в форсированной очереди")
                return False
                
            print("   ✅ Карточка в форсированной очереди")
            
            # Проверяем, что next_review установлен на текущее время
            next_review = progress.get("next_review")
            if not next_review:
                print("❌ Не установлено время следующего повторения")
                return False
                
            print(f"   ✅ Время следующего повторения: {next_review}")
            
            # Получаем карточку снова (должна быть та же самая)
            next_card = await get_next_card()
            if not next_card or str(next_card.get("word_id")) != card_id:
                print(f"❌ Не удалось получить ту же карточку. Получена: {next_card}")
                return False
                
            print("   ✅ Получена та же карточка для повторного ответа")
        
        print("\n✅ Тест пройден успешно!")
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при выполнении теста: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_multiple_incorrect_answers():
    """
    Проверяем обработку неправильных ответов для карточки с уровнем 2.
    
    Этот тест проверяет, как система обрабатывает серию неправильных ответов для карточки,
    которая уже была поднята до уровня 2 с помощью правильных ответов.
    
    Критически важные аспекты теста:
    1. Постепенное понижение уровня при неправильных ответах: 2 -> 1 -> -1
    2. Сохранение карточки в форсированной очереди после ошибок
    3. Корректное обновление времени следующего повторения
    4. Увеличение счетчика неправильных ответов
    
    Ожидаемое поведение:
    1. После первого неправильного ответа уровень понижается с 2 до 1
    2. После второго неправильного ответа уровень понижается с 1 до -1
    3. Последующие неправильные ответы оставляют уровень на -1
    4. После каждого ответа карточка остается доступной для немедленного повторения
    5. Время следующего повторения устанавливается на текущее время
    
    Для отладки или изменения логики работы с неправильными ответами
    обратитесь к методу process_answer в файле spaced_repetition.py
    """
    print("\n" + "="*50)
    print("=== Тест 5 неправильных ответов для карточки (уровень 2) ===\n")
    
    try:
        # 1. Создаем тестовую карточку
        print("1. Создаем тестовую карточку...")
        test_card = await create_test_card()
        if not test_card:
            print("❌ Не удалось создать тестовую карточку")
            return False
            
        card_id = test_card["word_id"]
        print(f"   Создана тестовая карточка: {test_card['word']} (ID: {card_id})")
        
        # 2. Получаем прогресс карточки, чтобы отслеживать изменения
        db = get_database()
        if not db.client:
            await db.connect_to_db()
            
        progress_collection = db.client.word_master.user_progress
        
        # 3. Функция для получения текущего прогресса
        async def get_card_progress():
            progress = await progress_collection.find_one({"word_id": ObjectId(card_id)})
            return progress
            
        # 4. Получаем начальный прогресс
        initial_progress = await get_card_progress()
        if not initial_progress:
            print("❌ Не удалось получить начальный прогресс карточки")
            return False
            
        print(f"   Начальный интервал: {initial_progress.get('interval_level', -1)}")
        
        # 5. Даем три правильных ответа, чтобы поднять уровень до 2
        print("\nПовышаем уровень карточки...")
        for i in range(1, 4):
            print(f"{i}. Отправляем правильный ответ...")
            response = await submit_card_response(card_id, is_correct=True)
            if not response or not response.get("success"):
                print(f"❌ Ошибка при отправке ответа: {response}")
                return False
            
            progress = await get_card_progress()
            print(f"   Новый уровень: {progress.get('interval_level')}")
        
        # 6. Проверяем, что уровень действительно 2 (было: -1 -> 0 -> 1 -> 2)
        progress = await get_card_progress()
        current_level = progress.get("interval_level", 0)
        if current_level != 2:
            print(f"❌ Неправильный уровень после 3 правильных ответов. Ожидался 2, получен {current_level}")
            return False
            
        print("✅ Уровень успешно поднят до 2")
        
        # 7. Отправляем 5 неправильных ответов подряд
        # Ожидаемые уровни: 2 -> 1 -> -1 -> -1 -> -1
        expected_levels = [1, -1, -1, -1, -1]  # Ожидаемые уровни после каждого ответа
        
        for i in range(1, 6):
            print(f"\n{i}. Отправляем неправильный ответ...")
            
            # Отправляем ответ
            response = await submit_card_response(card_id, is_correct=False)
            if not response or not response.get("success"):
                print(f"❌ Ошибка при отправке ответа: {response}")
                return False
                
            # Получаем обновленный прогресс
            progress = await get_card_progress()
            if not progress:
                print("❌ Не удалось получить обновленный прогресс")
                return False
                
            # Проверяем интервал
            current_level = progress.get("interval_level", 0)
            expected_level = expected_levels[i-1] if i-1 < len(expected_levels) else -1
            
            print(f"   Интервал: {current_level} (ожидалось: {expected_level})")
            print(f"   Правильных ответов: {progress.get('correct_answers', 0)}")
            print(f"   Неправильных ответов: {progress.get('incorrect_answers', 0)}")
            print(f"   Следующее повторение: {progress.get('next_review', 'не установлено')}")
            
            # Проверяем, что карточка в форсированной очереди
            if not progress.get('force_review', False):
                print("❌ Карточка не в форсированной очереди!")
                return False
            print("   ✅ Карточка в форсированной очереди")
            
            # Проверяем, что следующее повторение в ближайшем будущем (в течение 5 минут)
            next_review = progress.get('next_review')
            if next_review:
                # Преобразуем строку с датой в объект datetime с временной зоной
                if isinstance(next_review, str):
                    if next_review.endswith('Z'):
                        next_review = next_review[:-1] + '+00:00'
                    next_review_time = datetime.fromisoformat(next_review)
                else:
                    # Если это уже datetime, добавляем временную зону, если её нет
                    next_review_time = next_review
                    if next_review_time.tzinfo is None:
                        next_review_time = next_review_time.replace(tzinfo=timezone.utc)
                
                now = datetime.now(timezone.utc)
                time_diff = (next_review_time - now).total_seconds()
                print(f"   ✅ Время следующего повторения: {next_review_time}")
            
            # Получаем следующую карточку и проверяем, что это та же карточка
            next_card = await get_next_card()
            next_card_id = str(next_card.get('_id') or next_card.get('word_id', ''))
            if not next_card or next_card_id != card_id:
                print(f"❌ Не удалось получить ту же карточку. Ожидался ID: {card_id}, получен ID: {next_card_id}. Полная карточка: {next_card}")
                return False
            print(f"   ✅ Получена та же карточка для повторного ответа (ID: {next_card_id})")
            
            if current_level != expected_level:
                print(f"❌ Неправильный уровень после {i} неправильного ответа. Ожидался {expected_level}, получен {current_level}")
                return False
                
            # Проверяем, что карточка в форсированной очереди
            if not progress.get("force_review", False):
                print("❌ Карточка не в форсированной очереди")
                return False
                
            print("   ✅ Карточка в форсированной очереди")
            
            # Проверяем, что next_review установлен на текущее время
            next_review = progress.get("next_review")
            if not next_review:
                print("❌ Не установлено время следующего повторения")
                return False
                
            print(f"   ✅ Время следующего повторения: {next_review}")
            
            # Получаем карточку снова (должна быть та же самая)
            next_card = await get_next_card()
            if not next_card or str(next_card.get("word_id")) != card_id:
                print(f"❌ Не удалось получить ту же карточку. Получена: {next_card}")
                return False
                
            print("   ✅ Получена та же карточка для повторного ответа")
        
        print("\n✅ Тест пройден успешно!")
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при выполнении теста: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_new_word_loading() -> bool:
    """Тестирует загрузку нового слова при пустой очереди."""
    print("\n=== Тест 8: Загрузка нового слова при пустой очереди ===")
    
    try:
        # 1. Очищаем прогресс пользователя
        print("1. Очищаем прогресс пользователя...")
        test_user_id = "6848235d12259195693cb594"
        await cleanup_user_progress(test_user_id)
        
        # 2. Добавляем одно тестовое слово с уникальным именем
        import time
        test_word = f"test_word_{int(time.time())}"
        print(f"2. Добавляем тестовое слово: {test_word}...")
        word = await add_test_word(test_word, ["тестовый перевод"])
        if not word or "_id" not in word:
            print("❌ Ошибка при добавлении тестового слова")
            return False
        
        card_id = str(word["_id"])
        print(f"   Добавлено слово с ID: {card_id}")
        
        # 3. Получаем следующую карточку (может быть любая карточка из базы)
        print("3. Получаем следующую карточку...")
        card = await get_next_card()
        if not card or "word_id" not in card:
            print("❌ Ошибка: не удалось получить карточку")
            print(f"   Полученная карточка: {card}")
            return False
        
        first_card_id = str(card["word_id"])
        print(f"   Получена карточка с ID: {first_card_id}")
        
        # 4. Отправляем правильный ответ, чтобы вывести карточку из активной очереди
        print("4. Отправляем правильный ответ...")
        response = await submit_card_response(first_card_id, is_correct=True)
        if not response or not response.get("success"):
            print(f"❌ Ошибка при отправке ответа: {response}")
            return False
        
        # 5. Проверяем, что карточка ушла из активной очереди
        print("5. Проверяем, что карточка ушла из активной очереди...")
        next_card = await get_next_card()
        if not next_card or "word_id" not in next_card:
            print("❌ Ошибка: не получена новая карточка")
            return False
        
        second_card_id = str(next_card.get("word_id"))
        print(f"   Получена новая карточка с ID: {second_card_id}")
        
        # 6. Проверяем, что это действительно другая карточка
        if second_card_id == first_card_id:
            print("❌ Ошибка: получена та же самая карточка")
            return False
        
        # 7. Проверяем, что вторая карточка добавлена в прогресс
        progress = await get_card_progress(second_card_id)
        if not progress:
            print("❌ Ошибка: вторая карточка не добавлена в прогресс")
            return False
            
        print("   Вторая карточка успешно добавлена в прогресс")
        
        print("✅ Тест пройден успешно!")
        return True
        
    except Exception as e:
        print(f"❌ Непредвиденная ошибка: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Основная функция для запуска тестов"""
    print("=== Запуск тестов ===\n")
    
    # Запускаем тесты последовательно
    test_functions = [
        ("1. Тест с неправильным ответом", test_incorrect_answer_flow),
        ("2. Тест: правильный, затем неправильный ответ", test_incorrect_after_correct_answer),
        ("3. Тест с правильным ответом", test_correct_answer_flow),
        ("4. Тест нескольких правильных ответов", test_multiple_correct_answers),
        ("5. Тест неправильных ответов для новой карточки", test_new_card_multiple_incorrect_answers),
        ("6. Тест неправильных ответов для карточки уровня 2", test_multiple_incorrect_answers),
        ("7. Тест прохождения через все уровни интервалов", test_progress_through_all_levels),
        ("8. Тест загрузки нового слова при пустой очереди", test_new_word_loading)
    ]
    
    results = {}
    for name, test_func in test_functions:
        print(f"\n{'='*50}")
        print(f"=== {name} ===")
        try:
            # Очищаем прогресс тестового пользователя
            test_user_id = "6848235d12259195693cb594"
            await cleanup_user_progress(test_user_id)
            
            result = await test_func()
            results[name] = result is not False
            status = "ПРОЙДЕН" if result is not False else "НЕ ПРОЙДЕН"
            print(f"\n=== {status} ===\n")
        except Exception as e:
            results[name] = False
            print(f"\n❌ Ошибка при выполнении теста: {e}")
            import traceback
            traceback.print_exc()
            print(f"\n=== НЕ ПРОЙДЕН ===\n")
    
    # Выводим сводку
    print("\n" + "="*50)
    print("=== РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ ===")
    for name, passed in results.items():
        status = "✅ ПРОЙДЕН" if passed else "❌ НЕ ПРОЙДЕН"
        print(f"{name}: {status}")
    
    # Возвращаем общий результат (True, если все тесты прошли успешно)
    return all(results.values())

# Точка входа для запуска тестов
if __name__ == "__main__":
    asyncio.run(main())
