#!/usr/bin/env python3
"""
Тест для проверки исправления логики форсированной очереди.

Проверяем, что:
1. Новые слова НЕ попадают в форсированную очередь при загрузке
2. Слова попадают в форсированную очередь только после неправильного ответа
3. Предзагрузка не загружает слова из форсированной очереди
"""

import asyncio
import sys
import os
from datetime import datetime
from bson import ObjectId

# Добавляем путь к корню проекта
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Импортируем только то, что нужно, без инициализации сервисов
from app.database import Database

async def test_forced_queue_logic():
    """Тестируем логику форсированной очереди."""

    print("🧪 Тест логики форсированной очереди")
    print("=" * 50)

    # Подключаемся к базе данных
    db_instance = Database()
    await db_instance.connect_to_db()

    # Импортируем сервис только после подключения к БД
    from app.services.spaced_repetition import spaced_repetition_service

    # Получаем коллекции
    words_db = db_instance.get_db('words')
    words_collection = words_db.words
    progress_collection = words_db.user_progress

    # Создаем тестового пользователя
    test_user_id = ObjectId()
    print(f"👤 Тестовый пользователь: {test_user_id}")

    # Очищаем прогресс тестового пользователя
    await progress_collection.delete_many({"user_id": test_user_id})
    
    try:
        # 1. Тест: Загрузка нового слова
        print("\n1️⃣ Тест: Загрузка нового слова")
        print("-" * 30)
        
        # Получаем новое слово
        new_word = await spaced_repetition_service.get_next_word(test_user_id, target_lang="cb")
        
        if new_word:
            print(f"✅ Получено новое слово: {new_word.get('word')}")
            print(f"📊 is_forced_review: {new_word.get('is_forced_review')}")
            
            # Проверяем, что новое слово НЕ в форсированной очереди
            if new_word.get('is_forced_review') == False:
                print("✅ ПРАВИЛЬНО: Новое слово НЕ в форсированной очереди")
            else:
                print("❌ ОШИБКА: Новое слово попало в форсированную очередь!")
                
            # Проверяем в базе данных
            word_id = ObjectId(new_word.get('word_id'))
            progress = await progress_collection.find_one({
                "user_id": test_user_id,
                "word_id": word_id
            })
            
            if progress:
                print(f"📋 В БД force_review: {progress.get('force_review')}")
                if progress.get('force_review') == False:
                    print("✅ ПРАВИЛЬНО: В БД новое слово НЕ в форсированной очереди")
                else:
                    print("❌ ОШИБКА: В БД новое слово в форсированной очереди!")
            
            # 2. Тест: Неправильный ответ
            print("\n2️⃣ Тест: Неправильный ответ")
            print("-" * 30)
            
            # Отправляем неправильный ответ
            result = await spaced_repetition_service.process_answer(
                user_id=test_user_id,
                word_id=word_id,
                is_correct=False
            )
            
            print(f"📊 После неправильного ответа force_review: {result.get('force_review')}")
            
            if result.get('force_review') == True:
                print("✅ ПРАВИЛЬНО: После неправильного ответа слово попало в форсированную очередь")
            else:
                print("❌ ОШИБКА: После неправильного ответа слово НЕ попало в форсированную очередь!")
            
            # 3. Тест: Предзагрузка пропускает форсированную очередь
            print("\n3️⃣ Тест: Предзагрузка пропускает форсированную очередь")
            print("-" * 30)
            
            # Получаем слово с предзагрузкой (skip_forced=True)
            preload_word = await spaced_repetition_service.get_next_word(
                test_user_id, 
                target_lang="cb", 
                skip_forced=True
            )
            
            if preload_word:
                print(f"✅ Предзагрузка получила слово: {preload_word.get('word')}")
                print(f"📊 is_forced_review: {preload_word.get('is_forced_review')}")
                
                # Проверяем, что это НЕ слово из форсированной очереди
                preload_word_id = preload_word.get('word_id')
                if preload_word_id != new_word.get('word_id'):
                    print("✅ ПРАВИЛЬНО: Предзагрузка пропустила слово из форсированной очереди")
                else:
                    print("❌ ОШИБКА: Предзагрузка вернула слово из форсированной очереди!")
            else:
                print("ℹ️ Предзагрузка не нашла новых слов (это нормально)")
            
            # 4. Тест: Обычная загрузка возвращает слово из форсированной очереди
            print("\n4️⃣ Тест: Обычная загрузка возвращает форсированное слово")
            print("-" * 30)
            
            # Получаем слово без пропуска форсированной очереди
            forced_word = await spaced_repetition_service.get_next_word(
                test_user_id, 
                target_lang="cb", 
                skip_forced=False
            )
            
            if forced_word:
                print(f"✅ Обычная загрузка получила слово: {forced_word.get('word')}")
                print(f"📊 is_forced_review: {forced_word.get('is_forced_review')}")
                
                # Проверяем, что это слово из форсированной очереди
                forced_word_id = forced_word.get('word_id')
                if forced_word_id == new_word.get('word_id'):
                    print("✅ ПРАВИЛЬНО: Обычная загрузка вернула слово из форсированной очереди")
                else:
                    print("❌ ОШИБКА: Обычная загрузка НЕ вернула слово из форсированной очереди!")
            else:
                print("❌ ОШИБКА: Обычная загрузка не нашла слов!")
                
        else:
            print("❌ ОШИБКА: Не удалось получить новое слово!")
            
    except Exception as e:
        print(f"❌ ОШИБКА в тесте: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Очищаем тестовые данные
        await progress_collection.delete_many({"user_id": test_user_id})
        print(f"\n🧹 Очищены тестовые данные для пользователя {test_user_id}")
    
    print("\n" + "=" * 50)
    print("🏁 Тест завершен")

if __name__ == "__main__":
    asyncio.run(test_forced_queue_logic())
