#!/usr/bin/env python3
"""
Тест исправления предзагрузки.

Проверяем, что предзагрузка не возвращает то же новое слово.
"""

import asyncio
import sys
import os
from datetime import datetime
from bson import ObjectId

# Добавляем путь к корню проекта
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import Database

async def test_preload_fix():
    """Тестируем исправление предзагрузки."""
    
    print("🧪 Тест исправления предзагрузки")
    print("=" * 50)
    
    # Подключаемся к базе данных
    db_instance = Database()
    await db_instance.connect_to_db()
    
    # Импортируем сервис только после подключения к БД
    from app.services.spaced_repetition import spaced_repetition_service
    
    # Получаем коллекции
    words_db = db_instance.get_db('words')
    progress_collection = words_db.user_progress
    
    # Создаем тестового пользователя
    test_user_id = ObjectId()
    print(f"👤 Тестовый пользователь: {test_user_id}")
    
    # Очищаем прогресс тестового пользователя
    await progress_collection.delete_many({"user_id": test_user_id})
    
    try:
        # 1. Получаем первое новое слово
        print("\n1️⃣ Получаем первое новое слово")
        print("-" * 30)
        
        first_word = await spaced_repetition_service.get_next_word(test_user_id, target_lang="cb")
        
        if not first_word:
            print("❌ ОШИБКА: Не удалось получить первое слово!")
            return
            
        first_word_text = first_word.get('word')
        first_word_id = first_word.get('word_id')
        
        print(f"✅ Получено первое слово: {first_word_text}")
        print(f"📊 is_new: {first_word.get('is_new')}")
        print(f"📊 is_forced_review: {first_word.get('is_forced_review')}")
        
        # 2. Сразу же пытаемся получить слово для предзагрузки
        print("\n2️⃣ Пытаемся получить слово для предзагрузки")
        print("-" * 30)
        
        preload_word = await spaced_repetition_service.get_next_word(
            test_user_id,
            target_lang="cb",
            skip_forced=True,  # Пропускаем форсированную очередь
            preload=True       # Режим предзагрузки
        )
        
        if preload_word:
            preload_word_text = preload_word.get('word')
            preload_word_id = preload_word.get('word_id')
            
            print(f"✅ Получено слово для предзагрузки: {preload_word_text}")
            print(f"📊 is_new: {preload_word.get('is_new')}")
            print(f"📊 is_forced_review: {preload_word.get('is_forced_review')}")
            
            # Проверяем, что это НЕ то же самое слово
            if preload_word_id != first_word_id:
                print("✅ ПРАВИЛЬНО: Предзагрузка вернула другое слово")
            else:
                print("❌ ОШИБКА: Предзагрузка вернула то же самое слово!")
                print(f"   Первое слово: {first_word_text} (ID: {first_word_id})")
                print(f"   Предзагрузка: {preload_word_text} (ID: {preload_word_id})")
        else:
            print("✅ ПРАВИЛЬНО: Предзагрузка не нашла слов (новые слова исключены из активной очереди)")
        
        # 3. Проверяем состояние в БД
        print("\n3️⃣ Проверяем состояние в БД")
        print("-" * 30)
        
        # Проверяем первое слово
        first_progress = await progress_collection.find_one({
            "user_id": test_user_id,
            "word_id": ObjectId(first_word_id)
        })
        
        if first_progress:
            print(f"📋 Первое слово '{first_word_text}' в БД:")
            print(f"   interval_level: {first_progress.get('interval_level')}")
            print(f"   force_review: {first_progress.get('force_review')}")
            print(f"   is_learned: {first_progress.get('is_learned')}")
            
            if first_progress.get('interval_level') == -1:
                print("✅ ПРАВИЛЬНО: Первое слово имеет interval_level = -1 (новое)")
            else:
                print("❌ ОШИБКА: Первое слово не имеет interval_level = -1!")
        
        # Проверяем предзагруженное слово (если есть)
        if preload_word:
            preload_progress = await progress_collection.find_one({
                "user_id": test_user_id,
                "word_id": ObjectId(preload_word_id)
            })
            
            if preload_progress:
                print(f"📋 Предзагруженное слово '{preload_word_text}' в БД:")
                print(f"   interval_level: {preload_progress.get('interval_level')}")
                print(f"   force_review: {preload_progress.get('force_review')}")
                print(f"   is_learned: {preload_progress.get('is_learned')}")
        
        # 4. Тестируем логику активной очереди
        print("\n4️⃣ Тестируем логику активной очереди")
        print("-" * 30)
        
        # Проверяем, что новые слова НЕ попадают в активную очередь
        from app.services.spaced_repetition import spaced_repetition_service
        
        # Вызываем напрямую метод поиска активных слов
        active_word = await spaced_repetition_service._get_next_active_word(test_user_id, target_lang="cb")
        
        if active_word:
            print(f"❌ ОШИБКА: Найдено активное слово: {active_word.get('word')}")
            print(f"   interval_level: {active_word.get('interval_level')}")
        else:
            print("✅ ПРАВИЛЬНО: Активных слов не найдено (новые слова исключены)")
            
    except Exception as e:
        print(f"❌ ОШИБКА в тесте: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Очищаем тестовые данные
        await progress_collection.delete_many({"user_id": test_user_id})
        print(f"\n🧹 Очищены тестовые данные для пользователя {test_user_id}")
    
    print("\n" + "=" * 50)
    print("🏁 Тест завершен")

if __name__ == "__main__":
    asyncio.run(test_preload_fix())
