"""
Тестирование API с аутентификацией.
Проверяем получение токена и доступ к защищенным эндпоинтам.
"""

import asyncio
import httpx
from dotenv import load_dotenv
import os

# Загружаем переменные окружения из .env файла
load_dotenv()

# Настройки API
BASE_URL = "http://localhost:8001"
USER_EMAIL = "<EMAIL>"
USER_PASSWORD = "Test123!"

# Глобальные переменные для хранения токена и ID пользователя
TOKEN = None
USER_ID = None

async def get_auth_token():
    """Получаем токен аутентификации"""
    global TOKEN, USER_ID
    
    if TOKEN:
        return TOKEN, USER_ID
    
    auth_data = {
        "username": USER_EMAIL,
        "password": USER_PASSWORD
    }
    
    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"{BASE_URL}/api/token",
            data=auth_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
    
    if response.status_code == 200:
        data = response.json()
        TOKEN = data.get("access_token")
        USER_ID = data.get("user_id")
        print(f"✅ Успешная аутентификация. User ID: {USER_ID}")
        return TOKEN, USER_ID
    else:
        print(f"❌ Ошибка аутентификации: {response.status_code} - {response.text}")
        return None, None

async def get_next_card():
    """Получаем следующую карточку для изучения"""
    token, user_id = await get_auth_token()
    if not token or not user_id:
        return None
    
    headers = {
        "Authorization": f"Bearer {token}",
        "accept": "application/json"
    }
    
    # Добавляем user_id в параметры запроса
    params = {
        "user_id": user_id,
        "native_lang": "ru",
        "target_lang": "en"
    }
    
    async with httpx.AsyncClient() as client:
        response = await client.get(
            f"{BASE_URL}/api/cards/next",
            headers=headers,
            params=params
        )
    
    if response.status_code == 200:
        return response.json()
    else:
        print(f"❌ Ошибка при получении карточки: {response.status_code} - {response.text}")
        return None

async def submit_card_response(card_id: str, is_correct: bool, response_time: float = 5.0):
    """Отправляем ответ на карточку"""
    token, user_id = await get_auth_token()
    if not token or not user_id:
        return None
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json",
        "accept": "application/json"
    }
    
    # Добавляем user_id в тело запроса
    data = {
        "user_id": user_id,
        "is_correct": is_correct,
        "response_time": response_time
    }
    
    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"{BASE_URL}/api/cards/{card_id}/response",
            headers=headers,
            json=data
        )
    
    if response.status_code == 200:
        return response.json()
    else:
        print(f"❌ Ошибка при отправке ответа: {response.status_code} - {response.text}")
        return None

async def test_incorrect_answer_flow():
    """Тестируем поток с неправильными ответами через API"""
    print("🔍 Начинаем тестирование API с аутентификацией...")
    
    # 1. Получаем токен аутентификации
    token, user_id = await get_auth_token()
    if not token or not user_id:
        print("❌ Не удалось получить токен аутентификации")
        return
    
    print(f"\n1. Токен аутентификации получен. User ID: {user_id}")
    
    # 2. Получаем первую карточку
    print("\n2. Получаем первую карточку...")
    first_card = await get_next_card()
    if not first_card:
        print("❌ Не удалось получить карточку")
        return
    
    card_id = first_card.get("id")
    print(f"   Получена карточка: {first_card.get('word')} (ID: {card_id})")
    
    # 3. Отправляем неправильный ответ
    print(f"\n3. Отправляем неправильный ответ для карточки {card_id}...")
    response = await submit_card_response(card_id, is_correct=False)
    if not response:
        print("❌ Не удалось отправить ответ")
        return
    
    print(f"   Ответ сервера: интервал={response.get('interval_level')}, след. повторение={response.get('next_review')}")
    
    # 4. Получаем следующую карточку (должна быть та же самая)
    print("\n4. Получаем следующую карточку...")
    next_card = await get_next_card()
    if not next_card:
        print("❌ Не удалось получить следующую карточку")
        return
    
    next_card_id = next_card.get("id")
    print(f"   Получена карточка: {next_card.get('word')} (ID: {next_card_id})")
    
    # 5. Проверяем, что это та же карточка
    if str(next_card_id) == str(card_id):
        print("✅ Тест пройден: После неправильного ответа получена та же карточка")
    else:
        print(f"❌ Тест не пройден: Ожидалась карточка {card_id}, получена {next_card_id}")
    
    # 6. Проверяем, что карточка в очереди на повторение
    print("\n5. Проверяем, что карточка в очереди на повторение...")
    another_card = await get_next_card()
    if another_card and str(another_card.get("id")) == str(card_id):
        print("✅ Тест пройден: Карточка остается в очереди на повторение")
    else:
        print("❌ Тест не пройден: Карточка не в очереди на повторение")

if __name__ == "__main__":
    # Запускаем тест
    asyncio.run(test_incorrect_answer_flow())
