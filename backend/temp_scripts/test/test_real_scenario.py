#!/usr/bin/env python3
"""
Тест реального сценария: слово из интервальной очереди не должно повторяться сразу.
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta
from bson import ObjectId

# Добавляем путь к корню проекта
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import Database

async def test_real_scenario():
    """Тестируем реальный сценарий с интервальной очередью."""
    
    print("🧪 Тест реального сценария")
    print("=" * 60)
    
    # Подключаемся к базе данных
    db_instance = Database()
    await db_instance.connect_to_db()
    
    # Импортируем сервис только после подключения к БД
    from app.services.spaced_repetition import spaced_repetition_service
    
    # Получаем коллекции
    words_db = db_instance.get_db('words')
    progress_collection = words_db.user_progress
    
    # Создаем тестового пользователя
    test_user_id = ObjectId()
    print(f"👤 Тестовый пользователь: {test_user_id}")
    
    # Очищаем прогресс тестового пользователя
    await progress_collection.delete_many({"user_id": test_user_id})
    
    try:
        # 1. Создаем слово с interval_level=0 (готово к повторению через 1 минуту)
        print("\n1️⃣ Создаем слово готовое к повторению")
        print("-" * 30)
        
        # Находим любое слово
        words_collection = words_db.words
        word_doc = await words_collection.find_one({"language": "cb"})
        
        if not word_doc:
            print("❌ ОШИБКА: Не найдено слов в базе!")
            return
            
        word_id = word_doc["_id"]
        word_text = word_doc["word"]
        
        # Создаем запись прогресса с interval_level=0 и next_review в прошлом
        past_time = datetime.utcnow() - timedelta(minutes=5)
        progress = {
            "user_id": test_user_id,
            "word_id": word_id,
            "interval_level": 0,  # Уровень 0 = интервал 1 минута
            "correct_answers": 1,
            "incorrect_answers": 1,
            "last_reviewed": past_time,  # 5 минут назад
            "next_review": past_time + timedelta(minutes=1),  # 4 минуты назад (готово к повторению)
            "is_learned": False,
            "force_review": False,
            "created_at": past_time,
            "updated_at": past_time
        }
        
        await progress_collection.insert_one(progress)
        print(f"✅ Создано слово '{word_text}' готовое к повторению")
        print(f"   interval_level: 0")
        print(f"   next_review: {progress['next_review']} (в прошлом)")
        
        # 2. Получаем слово из активной очереди
        print("\n2️⃣ Получаем слово из активной очереди")
        print("-" * 30)
        
        active_word = await spaced_repetition_service.get_next_word(test_user_id, target_lang="cb")
        
        if active_word:
            active_word_text = active_word.get('word')
            print(f"✅ Получено слово из активной очереди: {active_word_text}")
            
            if active_word_text == word_text:
                print("✅ ПРАВИЛЬНО: Получено ожидаемое слово")
            else:
                print(f"❌ ОШИБКА: Ожидалось '{word_text}', получено '{active_word_text}'")
        else:
            print("❌ ОШИБКА: Не получено слово из активной очереди!")
            return
        
        # 3. Отвечаем правильно
        print("\n3️⃣ Отвечаем правильно")
        print("-" * 30)
        
        result = await spaced_repetition_service.process_answer(
            user_id=test_user_id,
            word_id=word_id,
            is_correct=True
        )
        
        print(f"📊 После правильного ответа:")
        print(f"   interval_level: {result.get('interval_level')}")
        print(f"   next_review: {result.get('next_review')}")
        print(f"   force_review: {result.get('force_review')}")
        
        # 4. Сразу же пытаемся получить следующее слово (имитация предзагрузки)
        print("\n4️⃣ Пытаемся получить следующее слово (предзагрузка)")
        print("-" * 30)
        
        next_word = await spaced_repetition_service.get_next_word(
            test_user_id, 
            target_lang="cb", 
            skip_forced=True  # Предзагрузка
        )
        
        if next_word:
            next_word_text = next_word.get('word')
            print(f"✅ Предзагрузка получила слово: {next_word_text}")
            
            if next_word_text == word_text:
                print("❌ ОШИБКА: Предзагрузка вернула то же слово!")
                print("   Это и есть проблема, которую мы решаем")
            else:
                print("✅ ПРАВИЛЬНО: Предзагрузка вернула другое слово")
        else:
            print("✅ ПРАВИЛЬНО: Предзагрузка не нашла слов (недавно отвеченное исключено)")
        
        # 5. Проверяем обычную загрузку (без skip_forced)
        print("\n5️⃣ Проверяем обычную загрузку")
        print("-" * 30)
        
        normal_word = await spaced_repetition_service.get_next_word(test_user_id, target_lang="cb")
        
        if normal_word:
            normal_word_text = normal_word.get('word')
            print(f"✅ Обычная загрузка получила слово: {normal_word_text}")
            
            if normal_word_text == word_text:
                print("❌ ОШИБКА: Обычная загрузка тоже вернула то же слово!")
            else:
                print("✅ ПРАВИЛЬНО: Обычная загрузка вернула другое слово")
        else:
            print("✅ ПРАВИЛЬНО: Обычная загрузка не нашла слов")
        
        # 6. Проверяем состояние в БД
        print("\n6️⃣ Проверяем состояние в БД")
        print("-" * 30)
        
        updated_progress = await progress_collection.find_one({
            "user_id": test_user_id,
            "word_id": word_id
        })
        
        if updated_progress:
            print(f"📋 Состояние слова '{word_text}' в БД:")
            print(f"   interval_level: {updated_progress.get('interval_level')}")
            print(f"   last_reviewed: {updated_progress.get('last_reviewed')}")
            print(f"   next_review: {updated_progress.get('next_review')}")
            print(f"   force_review: {updated_progress.get('force_review')}")
            
            # Проверяем, прошло ли достаточно времени для исключения
            now = datetime.utcnow()
            last_reviewed = updated_progress.get('last_reviewed')
            time_diff = now - last_reviewed
            
            print(f"   Время с последнего ответа: {time_diff.total_seconds():.1f} секунд")
            
            if time_diff.total_seconds() < 120:  # 2 минуты
                print("✅ ПРАВИЛЬНО: Слово должно быть исключено (< 2 минут)")
            else:
                print("ℹ️ Слово не должно быть исключено (> 2 минут)")
            
    except Exception as e:
        print(f"❌ ОШИБКА в тесте: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Очищаем тестовые данные
        await progress_collection.delete_many({"user_id": test_user_id})
        print(f"\n🧹 Очищены тестовые данные для пользователя {test_user_id}")
    
    print("\n" + "=" * 60)
    print("🏁 Тест завершен")

if __name__ == "__main__":
    asyncio.run(test_real_scenario())
