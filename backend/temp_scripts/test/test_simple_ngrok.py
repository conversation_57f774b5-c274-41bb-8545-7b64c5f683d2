#!/usr/bin/env python3
"""
Простой тест API через ngrok.
"""

import requests
import json
from datetime import datetime

NGROK_URL = "https://0bc3-222-127-55-183.ngrok-free.app"

def log_with_time(message):
    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] {message}")

def test_api_connection():
    """Простой тест подключения к API."""
    
    log_with_time("🧪 Простой тест API через ngrok")
    log_with_time("=" * 50)
    
    # Настраиваем сессию
    session = requests.Session()
    session.headers.update({
        'ngrok-skip-browser-warning': 'true',
        'Content-Type': 'application/json'
    })
    
    # Валидный ObjectId для тестирования
    test_user_id = "507f1f77bcf86cd799439011"
    
    try:
        # 1. Тест обычной загрузки карточки
        log_with_time("1️⃣ Тест обычной загрузки карточки")
        
        params = {
            "user_id": test_user_id,
            "native_lang": "ru",
            "target_lang": "cb"
        }
        
        response = session.get(f"{NGROK_URL}/api/spaced/next", params=params)
        log_with_time(f"📡 Статус ответа: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            word = data.get('word', 'N/A')
            word_id = data.get('word_id', 'N/A')
            log_with_time(f"✅ Получена карточка: {word} (ID: {word_id})")
            
            # 2. Тест предзагрузки
            log_with_time("\n2️⃣ Тест предзагрузки")
            
            params_preload = params.copy()
            params_preload["preload"] = "true"
            
            response_preload = session.get(f"{NGROK_URL}/api/spaced/next", params=params_preload)
            log_with_time(f"📡 Статус ответа предзагрузки: {response_preload.status_code}")
            
            if response_preload.status_code == 200:
                preload_data = response_preload.json()
                preload_word = preload_data.get('word', 'N/A')
                log_with_time(f"✅ Предзагружена карточка: {preload_word}")
                
                if preload_word != word:
                    log_with_time(f"✅ УСПЕХ: Предзагрузка вернула другое слово!")
                    log_with_time(f"   Обычная загрузка: {word}")
                    log_with_time(f"   Предзагрузка: {preload_word}")
                else:
                    log_with_time(f"❌ ПРОБЛЕМА: Предзагрузка вернула то же слово!")
                    log_with_time(f"   Обе загрузки вернули: {word}")
                    
            elif response_preload.status_code == 404:
                log_with_time("ℹ️ Предзагрузка не нашла карточек (это нормально)")
            else:
                log_with_time(f"❌ Ошибка предзагрузки: {response_preload.text}")
            
            # 3. Тест отправки ответа
            log_with_time("\n3️⃣ Тест отправки неправильного ответа")
            
            answer_data = {
                "user_id": test_user_id,
                "is_correct": False,
                "response_time": 2.5
            }
            
            response_answer = session.post(f"{NGROK_URL}/api/spaced/{word_id}/response", json=answer_data)
            log_with_time(f"📡 Статус ответа на ответ: {response_answer.status_code}")
            
            if response_answer.status_code == 200:
                answer_result = response_answer.json()
                interval_level = answer_result.get('interval_level', 'N/A')
                log_with_time(f"✅ Неправильный ответ обработан, interval_level: {interval_level}")
                
                # 4. Проверяем форсированную очередь
                log_with_time("\n4️⃣ Проверяем форсированную очередь")
                
                response_forced = session.get(f"{NGROK_URL}/api/spaced/next", params=params)
                
                if response_forced.status_code == 200:
                    forced_data = response_forced.json()
                    forced_word = forced_data.get('word', 'N/A')
                    is_forced = forced_data.get('is_forced_review', False)
                    
                    log_with_time(f"✅ Получена карточка: {forced_word}")
                    log_with_time(f"📋 is_forced_review: {is_forced}")
                    
                    if forced_word == word:
                        log_with_time(f"✅ УСПЕХ: Форсированная очередь работает!")
                    else:
                        log_with_time(f"❌ ПРОБЛЕМА: Форсированная очередь вернула другое слово!")
                        
                    # 5. Отвечаем правильно
                    log_with_time("\n5️⃣ Отвечаем правильно")
                    
                    answer_data_correct = {
                        "user_id": test_user_id,
                        "is_correct": True,
                        "response_time": 1.8
                    }
                    
                    response_correct = session.post(f"{NGROK_URL}/api/spaced/{word_id}/response", json=answer_data_correct)
                    
                    if response_correct.status_code == 200:
                        correct_result = response_correct.json()
                        new_interval = correct_result.get('interval_level', 'N/A')
                        log_with_time(f"✅ Правильный ответ обработан, interval_level: {new_interval}")
                        
                        # 6. КЛЮЧЕВАЯ ПРОВЕРКА: Следующая карточка
                        log_with_time("\n6️⃣ КЛЮЧЕВАЯ ПРОВЕРКА: Следующая карточка после правильного ответа")
                        
                        response_next = session.get(f"{NGROK_URL}/api/spaced/next", params=params)
                        
                        if response_next.status_code == 200:
                            next_data = response_next.json()
                            next_word = next_data.get('word', 'N/A')
                            
                            if next_word != word:
                                log_with_time(f"✅ УСПЕХ: Следующая карточка другая!")
                                log_with_time(f"   Предыдущая: {word}")
                                log_with_time(f"   Следующая: {next_word}")
                            else:
                                log_with_time(f"❌ ПРОБЛЕМА: Следующая карточка та же!")
                                log_with_time(f"   Обе карточки: {word}")
                                
                        elif response_next.status_code == 404:
                            log_with_time(f"✅ УСПЕХ: Нет доступных карточек (недавно отвеченное слово исключено)")
                        else:
                            log_with_time(f"❌ Ошибка получения следующей карточки: {response_next.text}")
                        
                        # 7. КЛЮЧЕВАЯ ПРОВЕРКА: Предзагрузка после правильного ответа
                        log_with_time("\n7️⃣ КЛЮЧЕВАЯ ПРОВЕРКА: Предзагрузка после правильного ответа")
                        
                        response_preload_final = session.get(f"{NGROK_URL}/api/spaced/next", params=params_preload)
                        
                        if response_preload_final.status_code == 200:
                            preload_final_data = response_preload_final.json()
                            preload_final_word = preload_final_data.get('word', 'N/A')
                            
                            if preload_final_word != word:
                                log_with_time(f"✅ УСПЕХ: Предзагрузка НЕ вернула недавно отвеченное слово!")
                                log_with_time(f"   Предыдущая: {word}")
                                log_with_time(f"   Предзагрузка: {preload_final_word}")
                            else:
                                log_with_time(f"❌ ПРОБЛЕМА: Предзагрузка вернула недавно отвеченное слово!")
                                log_with_time(f"   Обе: {word}")
                                
                        elif response_preload_final.status_code == 404:
                            log_with_time(f"✅ УСПЕХ: Предзагрузка не нашла карточек (исключение работает)")
                        else:
                            log_with_time(f"❌ Ошибка предзагрузки: {response_preload_final.text}")
                    else:
                        log_with_time(f"❌ Ошибка отправки правильного ответа: {response_correct.text}")
                else:
                    log_with_time(f"❌ Ошибка получения форсированной карточки: {response_forced.text}")
            else:
                log_with_time(f"❌ Ошибка отправки ответа: {response_answer.text}")
                
        elif response.status_code == 404:
            log_with_time("ℹ️ Нет доступных карточек для тестирования")
        else:
            log_with_time(f"❌ Ошибка API: {response.text}")
            
    except Exception as e:
        log_with_time(f"❌ Критическая ошибка: {e}")
    
    log_with_time("\n🏁 Тест завершен")

if __name__ == "__main__":
    test_api_connection()
