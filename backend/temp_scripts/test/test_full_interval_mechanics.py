#!/usr/bin/env python3
"""
Полный тест механики интервалов повторения.

Проверяем весь цикл:
1. Неправильный ответ → форсированная очередь
2. Правильный ответ → interval_level 0 (1 минута)
3. Правильный ответ → interval_level 1 (5 минут)
4. Правильный ответ → interval_level 2 (10 минут)
5. И так далее...

На каждом этапе проверяем, что слово НЕ появляется сразу после ответа.
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta
from bson import ObjectId

# Добавляем путь к корню проекта
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import Database

async def test_full_interval_mechanics():
    """Тестируем полную механику интервалов."""
    
    print("🧪 Полный тест механики интервалов")
    print("=" * 70)
    
    # Подключаемся к базе данных
    db_instance = Database()
    await db_instance.connect_to_db()
    
    # Импортируем сервис только после подключения к БД
    from app.services.spaced_repetition import spaced_repetition_service
    
    # Получаем коллекции
    words_db = db_instance.get_db('words')
    progress_collection = words_db.user_progress
    
    # Создаем тестового пользователя
    test_user_id = ObjectId()
    print(f"👤 Тестовый пользователь: {test_user_id}")
    
    # Очищаем прогресс тестового пользователя
    await progress_collection.delete_many({"user_id": test_user_id})
    
    # Интервалы из кода
    INTERVALS = [0.5, 5, 10, 60, 1440, 4320, 10080, 20160, 43200, 86400, 172800, 345600, 525600, 788400, 1051200, 1576800]
    
    try:
        # 1. Получаем новое слово
        print("\n1️⃣ Получаем новое слово")
        print("-" * 40)
        
        new_word = await spaced_repetition_service.get_next_word(test_user_id, target_lang="cb")
        
        if not new_word:
            print("❌ ОШИБКА: Не удалось получить новое слово!")
            return
            
        word_id = ObjectId(new_word.get('word_id'))
        word_text = new_word.get('word')
        
        print(f"✅ Получено новое слово: {word_text}")
        print(f"   interval_level: {new_word.get('interval_level', 'N/A')}")
        
        # 2. Отвечаем неправильно
        print("\n2️⃣ Отвечаем неправильно")
        print("-" * 40)
        
        result = await spaced_repetition_service.process_answer(
            user_id=test_user_id,
            word_id=word_id,
            is_correct=False
        )
        
        print(f"📊 После неправильного ответа:")
        print(f"   interval_level: {result.get('interval_level')}")
        print(f"   force_review: {result.get('force_review')}")
        
        # Проверяем, что слово в форсированной очереди
        forced_word = await spaced_repetition_service.get_next_word(test_user_id, target_lang="cb")
        if forced_word and forced_word.get('word') == word_text:
            print("✅ ПРАВИЛЬНО: Слово попало в форсированную очередь")
        else:
            print("❌ ОШИБКА: Слово НЕ попало в форсированную очередь!")
        
        # 3. Отвечаем правильно (переход в interval_level 0)
        print("\n3️⃣ Отвечаем правильно → interval_level 0")
        print("-" * 40)
        
        result = await spaced_repetition_service.process_answer(
            user_id=test_user_id,
            word_id=word_id,
            is_correct=True
        )
        
        print(f"📊 После правильного ответа:")
        print(f"   interval_level: {result.get('interval_level')}")
        print(f"   force_review: {result.get('force_review')}")
        print(f"   next_review: {result.get('next_review')}")
        
        expected_interval = INTERVALS[0]  # 30 секунд
        print(f"   Ожидаемый интервал: {expected_interval} минут")
        
        # Проверяем, что слово НЕ появляется сразу
        next_word = await spaced_repetition_service.get_next_word(test_user_id, target_lang="cb")
        if next_word and next_word.get('word') == word_text:
            print("❌ ОШИБКА: Слово появилось сразу после ответа!")
        else:
            print("✅ ПРАВИЛЬНО: Слово НЕ появилось сразу (исключено на 2 минуты)")
        
        # Теперь тестируем циклы интервалов
        current_level = 0
        
        for cycle in range(5):  # Тестируем первые 5 интервалов
            print(f"\n{4+cycle}️⃣ Цикл {cycle+1}: interval_level {current_level} → {current_level+1}")
            print("-" * 40)
            
            # Имитируем прошедшее время (интервал + буфер)
            interval_minutes = INTERVALS[current_level]
            past_time = datetime.utcnow() - timedelta(minutes=interval_minutes + 1)
            
            # Обновляем время в БД
            await progress_collection.update_one(
                {"user_id": test_user_id, "word_id": word_id},
                {
                    "$set": {
                        "last_reviewed": past_time,
                        "next_review": past_time + timedelta(minutes=interval_minutes)
                    }
                }
            )
            
            print(f"⏰ Имитируем прошедшее время: {interval_minutes + 1} минут")
            
            # Проверяем, что слово появляется в активной очереди
            active_word = await spaced_repetition_service.get_next_word(test_user_id, target_lang="cb")
            
            if active_word and active_word.get('word') == word_text:
                print(f"✅ ПРАВИЛЬНО: Слово '{word_text}' появилось в активной очереди")
                print(f"   interval_level: {active_word.get('interval_level')}")
            else:
                if active_word:
                    print(f"❌ ОШИБКА: Получено другое слово: {active_word.get('word')}")
                else:
                    print("❌ ОШИБКА: Слово НЕ появилось в активной очереди!")
                break
            
            # Отвечаем правильно
            result = await spaced_repetition_service.process_answer(
                user_id=test_user_id,
                word_id=word_id,
                is_correct=True
            )
            
            new_level = result.get('interval_level')
            next_interval = INTERVALS[new_level] if new_level < len(INTERVALS) else "MAX"
            
            print(f"📊 После правильного ответа:")
            print(f"   interval_level: {current_level} → {new_level}")
            print(f"   Следующий интервал: {next_interval} минут")
            print(f"   force_review: {result.get('force_review')}")
            
            # Проверяем, что слово НЕ появляется сразу после ответа
            immediate_word = await spaced_repetition_service.get_next_word(test_user_id, target_lang="cb")
            
            if immediate_word and immediate_word.get('word') == word_text:
                print("❌ ОШИБКА: Слово появилось сразу после правильного ответа!")
                print("   Это означает, что проблема все еще есть!")
                break
            else:
                if immediate_word:
                    print(f"✅ ПРАВИЛЬНО: Получено другое слово: {immediate_word.get('word')}")
                else:
                    print("✅ ПРАВИЛЬНО: Слов не найдено (все исключены)")
            
            current_level = new_level
            
            # Если достигли максимального уровня, выходим
            if current_level >= len(INTERVALS) - 1:
                print(f"🎯 Достигнут максимальный уровень: {current_level}")
                break
        
        # Финальная проверка состояния
        print(f"\n🏁 Финальная проверка")
        print("-" * 40)
        
        final_progress = await progress_collection.find_one({
            "user_id": test_user_id,
            "word_id": word_id
        })
        
        if final_progress:
            print(f"📋 Финальное состояние слова '{word_text}':")
            print(f"   interval_level: {final_progress.get('interval_level')}")
            print(f"   correct_answers: {final_progress.get('correct_answers')}")
            print(f"   incorrect_answers: {final_progress.get('incorrect_answers')}")
            print(f"   is_learned: {final_progress.get('is_learned')}")
            print(f"   force_review: {final_progress.get('force_review')}")
            
            if final_progress.get('interval_level') >= 4:
                print("🎉 ОТЛИЧНО: Слово прошло несколько интервалов успешно!")
            
    except Exception as e:
        print(f"❌ ОШИБКА в тесте: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Очищаем тестовые данные
        await progress_collection.delete_many({"user_id": test_user_id})
        print(f"\n🧹 Очищены тестовые данные для пользователя {test_user_id}")
    
    print("\n" + "=" * 70)
    print("🏁 Тест завершен")

if __name__ == "__main__":
    asyncio.run(test_full_interval_mechanics())
