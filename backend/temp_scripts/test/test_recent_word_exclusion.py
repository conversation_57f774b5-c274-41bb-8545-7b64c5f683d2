#!/usr/bin/env python3
"""
Тест исключения недавно отвеченных слов из активной очереди.
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta
from bson import ObjectId

# Добавляем путь к корню проекта
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import Database

async def test_recent_word_exclusion():
    """Тестируем исключение недавно отвеченных слов."""
    
    print("🧪 Тест исключения недавно отвеченных слов")
    print("=" * 60)
    
    # Подключаемся к базе данных
    db_instance = Database()
    await db_instance.connect_to_db()
    
    # Импортируем сервис только после подключения к БД
    from app.services.spaced_repetition import spaced_repetition_service
    
    # Получаем коллекции
    words_db = db_instance.get_db('words')
    progress_collection = words_db.user_progress
    
    # Создаем тестового пользователя
    test_user_id = ObjectId()
    print(f"👤 Тестовый пользователь: {test_user_id}")
    
    # Очищаем прогресс тестового пользователя
    await progress_collection.delete_many({"user_id": test_user_id})
    
    try:
        # 1. Получаем новое слово
        print("\n1️⃣ Получаем новое слово")
        print("-" * 30)
        
        new_word = await spaced_repetition_service.get_next_word(test_user_id, target_lang="cb")
        
        if not new_word:
            print("❌ ОШИБКА: Не удалось получить новое слово!")
            return
            
        word_id = ObjectId(new_word.get('word_id'))
        word_text = new_word.get('word')
        
        print(f"✅ Получено новое слово: {word_text}")
        
        # 2. Отвечаем неправильно (чтобы слово попало в форсированную очередь)
        print("\n2️⃣ Отвечаем неправильно")
        print("-" * 30)
        
        result = await spaced_repetition_service.process_answer(
            user_id=test_user_id,
            word_id=word_id,
            is_correct=False
        )
        
        print(f"📊 После неправильного ответа:")
        print(f"   interval_level: {result.get('interval_level')}")
        print(f"   force_review: {result.get('force_review')}")
        
        # 3. Отвечаем правильно (слово переходит в активную очередь с коротким интервалом)
        print("\n3️⃣ Отвечаем правильно")
        print("-" * 30)
        
        result = await spaced_repetition_service.process_answer(
            user_id=test_user_id,
            word_id=word_id,
            is_correct=True
        )
        
        print(f"📊 После правильного ответа:")
        print(f"   interval_level: {result.get('interval_level')}")
        print(f"   force_review: {result.get('force_review')}")
        print(f"   next_review: {result.get('next_review')}")
        
        # 4. Сразу же пытаемся получить активное слово (должно быть исключено)
        print("\n4️⃣ Пытаемся получить активное слово сразу после ответа")
        print("-" * 30)
        
        active_word = await spaced_repetition_service._get_next_active_word(test_user_id, target_lang="cb")
        
        if active_word:
            active_word_text = active_word.get('word')
            print(f"❌ ОШИБКА: Найдено активное слово: {active_word_text}")
            if active_word_text == word_text:
                print("❌ КРИТИЧЕСКАЯ ОШИБКА: Возвращено то же слово, на которое только что ответили!")
            else:
                print("ℹ️ Возвращено другое слово (это нормально)")
        else:
            print("✅ ПРАВИЛЬНО: Активных слов не найдено (недавно отвеченное слово исключено)")
        
        # 5. Ждем 2.5 минуты и проверяем снова
        print("\n5️⃣ Ждем 2.5 минуты и проверяем снова")
        print("-" * 30)
        print("⏳ Ждем 2.5 минуты...")

        # Имитируем прошедшее время, изменив last_reviewed в БД
        past_time = datetime.utcnow() - timedelta(minutes=2.5)
        await progress_collection.update_one(
            {"user_id": test_user_id, "word_id": word_id},
            {"$set": {"last_reviewed": past_time}}
        )
        
        print("✅ Время изменено (имитация прошедших 2.5 минут)")
        
        # Проверяем активную очередь снова
        active_word_after = await spaced_repetition_service._get_next_active_word(test_user_id, target_lang="cb")
        
        if active_word_after:
            active_word_text_after = active_word_after.get('word')
            print(f"✅ ПРАВИЛЬНО: Найдено активное слово: {active_word_text_after}")
            if active_word_text_after == word_text:
                print("✅ ПРАВИЛЬНО: Возвращено то же слово (время прошло, исключение снято)")
            else:
                print("ℹ️ Возвращено другое слово")
        else:
            print("ℹ️ Активных слов не найдено (возможно, интервал еще не наступил)")
        
        # 6. Проверяем полную логику get_next_word
        print("\n6️⃣ Проверяем полную логику get_next_word")
        print("-" * 30)
        
        # Сначала сбрасываем время обратно к недавнему
        recent_time = datetime.utcnow() - timedelta(seconds=5)
        await progress_collection.update_one(
            {"user_id": test_user_id, "word_id": word_id},
            {"$set": {"last_reviewed": recent_time}}
        )
        
        next_word = await spaced_repetition_service.get_next_word(test_user_id, target_lang="cb")
        
        if next_word:
            next_word_text = next_word.get('word')
            print(f"✅ Получено слово: {next_word_text}")
            if next_word_text == word_text:
                print("❌ ОШИБКА: get_next_word вернул недавно отвеченное слово!")
            else:
                print("✅ ПРАВИЛЬНО: get_next_word вернул другое слово")
        else:
            print("✅ ПРАВИЛЬНО: get_next_word не нашел слов (недавно отвеченное исключено)")
            
    except Exception as e:
        print(f"❌ ОШИБКА в тесте: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Очищаем тестовые данные
        await progress_collection.delete_many({"user_id": test_user_id})
        print(f"\n🧹 Очищены тестовые данные для пользователя {test_user_id}")
    
    print("\n" + "=" * 60)
    print("🏁 Тест завершен")

if __name__ == "__main__":
    asyncio.run(test_recent_word_exclusion())
