#!/usr/bin/env python3
"""
Тест исправления создания записей прогресса при предзагрузке.
"""

import asyncio
import sys
import os
from datetime import datetime
from bson import ObjectId

# Добавляем путь к корню проекта
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import Database

async def test_preload_progress_fix():
    """Тест исправления создания записей прогресса при предзагрузке."""
    
    print("🧪 ТЕСТ ИСПРАВЛЕНИЯ ПРЕДЗАГРУЗКИ И ПРОГРЕССА")
    print("=" * 60)
    
    # Подключаемся к базе данных
    db_instance = Database()
    await db_instance.connect_to_db()
    
    # Импортируем сервис
    from app.services.spaced_repetition import spaced_repetition_service
    
    # Получаем коллекции
    words_db = db_instance.get_db('words')
    progress_collection = words_db.user_progress
    
    # Создаем тестового пользователя
    user_id = ObjectId()
    print(f"👤 Тестовый пользователь: {user_id}")
    
    try:
        print("\n" + "="*50)
        print("ТЕСТ 1: Обычная загрузка создает прогресс")
        print("="*50)
        
        # 1. Получаем новое слово (обычная загрузка)
        word1 = await spaced_repetition_service.get_next_word(user_id, target_lang="cb", preload=False)
        if not word1:
            print("❌ Не удалось получить новое слово")
            return
            
        word1_text = word1.get('word')
        word1_id = ObjectId(word1.get('word_id'))
        
        print(f"✅ Получено новое слово: {word1_text}")
        
        # Проверяем, создалась ли запись прогресса
        progress1 = await progress_collection.find_one({"user_id": user_id, "word_id": word1_id})
        if progress1:
            print(f"✅ Запись прогресса создана: interval_level={progress1.get('interval_level')}")
        else:
            print("❌ Запись прогресса НЕ создана!")
            
        print("\n" + "="*50)
        print("ТЕСТ 2: Предзагрузка НЕ создает прогресс")
        print("="*50)
        
        # 2. Получаем новое слово (предзагрузка)
        word2 = await spaced_repetition_service.get_next_word(user_id, target_lang="cb", preload=True)
        if not word2:
            print("❌ Не удалось получить новое слово для предзагрузки")
            return
            
        word2_text = word2.get('word')
        word2_id = ObjectId(word2.get('word_id'))
        
        print(f"✅ Предзагружено новое слово: {word2_text}")
        
        # Проверяем, НЕ создалась ли запись прогресса
        progress2 = await progress_collection.find_one({"user_id": user_id, "word_id": word2_id})
        if progress2:
            print(f"❌ Запись прогресса создана при предзагрузке! interval_level={progress2.get('interval_level')}")
        else:
            print("✅ Запись прогресса НЕ создана при предзагрузке (правильно!)")
            
        print("\n" + "="*50)
        print("ТЕСТ 3: Ответ на предзагруженное слово")
        print("="*50)
        
        # 3. Отвечаем на предзагруженное слово
        result2 = await spaced_repetition_service.process_answer(user_id, word2_id, True)
        
        print(f"📊 Результат после ответа на предзагруженное слово:")
        print(f"   interval_level: {result2.get('interval_level')}")
        print(f"   is_learned: {result2.get('is_learned')}")
        print(f"   correct_answers: {result2.get('correct_answers')}")
        print(f"   incorrect_answers: {result2.get('incorrect_answers')}")
        
        # Проверяем результат
        if result2.get('interval_level') == 15 and result2.get('is_learned') == True:
            print("✅ УСПЕХ: Предзагруженное слово выучено с первого раза!")
        else:
            print("❌ ОШИБКА: Предзагруженное слово НЕ выучено с первого раза!")
            
        print("\n" + "="*50)
        print("ТЕСТ 4: Множественные предзагрузки")
        print("="*50)
        
        # 4. Тестируем несколько предзагрузок подряд
        preloaded_words = []
        for i in range(3):
            word = await spaced_repetition_service.get_next_word(user_id, target_lang="cb", preload=True)
            if not word:
                print(f"❌ Не удалось предзагрузить слово {i+1}")
                break
                
            word_text = word.get('word')
            word_id = ObjectId(word.get('word_id'))
            preloaded_words.append((word_text, word_id))
            
            print(f"🔄 Предзагружено слово {i+1}: {word_text}")
            
            # Проверяем, что прогресс НЕ создался
            progress = await progress_collection.find_one({"user_id": user_id, "word_id": word_id})
            if progress:
                print(f"   ❌ Прогресс создан при предзагрузке!")
            else:
                print(f"   ✅ Прогресс НЕ создан (правильно)")
                
        print("\n" + "="*50)
        print("ТЕСТ 5: Ответы на предзагруженные слова")
        print("="*50)
        
        # 5. Отвечаем на все предзагруженные слова
        learned_count = 0
        for i, (word_text, word_id) in enumerate(preloaded_words):
            print(f"\n🔄 Отвечаем на слово {i+1}: {word_text}")
            
            result = await spaced_repetition_service.process_answer(user_id, word_id, True)
            
            print(f"   interval_level: {result.get('interval_level')}")
            print(f"   is_learned: {result.get('is_learned')}")
            
            if result.get('interval_level') == 15 and result.get('is_learned') == True:
                print(f"   ✅ Слово {i+1} выучено правильно")
                learned_count += 1
            else:
                print(f"   ❌ Слово {i+1} НЕ выучено!")
                
        print(f"\n🎯 ИТОГО ВЫУЧЕННЫХ ПРЕДЗАГРУЖЕННЫХ СЛОВ: {learned_count} из {len(preloaded_words)}")
        
        if learned_count == len(preloaded_words):
            print("✅ УСПЕХ: Все предзагруженные слова выучены правильно!")
        else:
            print("❌ ОШИБКА: Не все предзагруженные слова выучены!")
            
        print("\n" + "="*50)
        print("ПРОВЕРКА БАЗЫ ДАННЫХ")
        print("="*50)
        
        # Проверяем все записи в базе данных
        all_progress = await progress_collection.find({"user_id": user_id}).to_list(None)
        print(f"\n📊 Всего записей прогресса: {len(all_progress)}")
        
        learned_count_total = 0
        for i, prog in enumerate(all_progress):
            word_id = prog.get('word_id')
            interval_level = prog.get('interval_level')
            is_learned = prog.get('is_learned')
            correct_answers = prog.get('correct_answers')
            incorrect_answers = prog.get('incorrect_answers')
            
            print(f"\n📋 Запись {i+1}:")
            print(f"   word_id: {word_id}")
            print(f"   interval_level: {interval_level}")
            print(f"   is_learned: {is_learned}")
            print(f"   correct_answers: {correct_answers}")
            print(f"   incorrect_answers: {incorrect_answers}")
            
            if is_learned:
                learned_count_total += 1
                
        print(f"\n🎯 ИТОГО ВЫУЧЕННЫХ СЛОВ В БД: {learned_count_total}")
        
        # Ожидаемый результат: 4 выученных слова (1 обычное + 3 предзагруженных)
        expected_learned = 4
        if learned_count_total == expected_learned:
            print(f"✅ УСПЕХ: Выучено {learned_count_total} слов (ожидалось {expected_learned})")
        else:
            print(f"❌ ОШИБКА: Выучено {learned_count_total} слов (ожидалось {expected_learned})")
            
    finally:
        # Очищаем тестовые данные
        await progress_collection.delete_many({"user_id": user_id})
        print(f"\n🧹 Очищены тестовые данные для пользователя {user_id}")
        
    print("\n🏁 Тест завершен")

if __name__ == "__main__":
    asyncio.run(test_preload_progress_fix())
