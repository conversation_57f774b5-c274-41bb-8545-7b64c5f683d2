#!/usr/bin/env python3
"""
Тест исправления немедленного изучения новых слов.
"""

import asyncio
import sys
import os
from datetime import datetime
from bson import ObjectId

# Добавляем путь к корню проекта
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import Database

async def test_immediate_learning_fix():
    """Тест исправления немедленного изучения."""
    
    print("🧪 ТЕСТ ИСПРАВЛЕНИЯ НЕМЕДЛЕННОГО ИЗУЧЕНИЯ")
    print("=" * 60)
    
    # Подключаемся к базе данных
    db_instance = Database()
    await db_instance.connect_to_db()
    
    # Импортируем сервис
    from app.services.spaced_repetition import spaced_repetition_service
    
    # Получаем коллекции
    words_db = db_instance.get_db('words')
    progress_collection = words_db.user_progress
    
    # Создаем тестового пользователя
    user_id = ObjectId()
    print(f"👤 Тестовый пользователь: {user_id}")
    
    try:
        print("\n" + "="*50)
        print("ТЕСТ 1: Новое слово с правильным ответом с первого раза")
        print("="*50)
        
        # 1. Получаем новое слово
        word1 = await spaced_repetition_service.get_next_word(user_id, target_lang="cb")
        if not word1:
            print("❌ Не удалось получить новое слово")
            return
            
        word1_text = word1.get('word')
        word1_id = ObjectId(word1.get('word_id'))
        
        print(f"✅ Получено новое слово: {word1_text}")
        print(f"📋 is_new: {word1.get('is_new')}")
        print(f"📋 interval_level: {word1.get('interval_level')}")
        
        # 2. Отвечаем правильно с первого раза
        result1 = await spaced_repetition_service.process_answer(user_id, word1_id, True)
        
        print(f"\n📊 Результат после правильного ответа:")
        print(f"   interval_level: {result1.get('interval_level')}")
        print(f"   is_learned: {result1.get('is_learned')}")
        print(f"   correct_answers: {result1.get('correct_answers')}")
        print(f"   incorrect_answers: {result1.get('incorrect_answers')}")
        
        # Проверяем результат
        if result1.get('interval_level') == 15 and result1.get('is_learned') == True:
            print("✅ УСПЕХ: Новое слово выучено с первого раза!")
        else:
            print("❌ ОШИБКА: Новое слово НЕ выучено с первого раза!")
            
        print("\n" + "="*50)
        print("ТЕСТ 2: Новое слово с неправильным, затем правильным ответом")
        print("="*50)
        
        # 3. Получаем второе новое слово
        word2 = await spaced_repetition_service.get_next_word(user_id, target_lang="cb")
        if not word2:
            print("❌ Не удалось получить второе новое слово")
            return
            
        word2_text = word2.get('word')
        word2_id = ObjectId(word2.get('word_id'))
        
        print(f"✅ Получено второе новое слово: {word2_text}")
        
        # 4. Отвечаем неправильно
        result2a = await spaced_repetition_service.process_answer(user_id, word2_id, False)
        
        print(f"\n📊 Результат после неправильного ответа:")
        print(f"   interval_level: {result2a.get('interval_level')}")
        print(f"   is_learned: {result2a.get('is_learned')}")
        print(f"   force_review: {result2a.get('force_review')}")
        
        # 5. Отвечаем правильно
        result2b = await spaced_repetition_service.process_answer(user_id, word2_id, True)
        
        print(f"\n📊 Результат после правильного ответа:")
        print(f"   interval_level: {result2b.get('interval_level')}")
        print(f"   is_learned: {result2b.get('is_learned')}")
        print(f"   correct_answers: {result2b.get('correct_answers')}")
        print(f"   incorrect_answers: {result2b.get('incorrect_answers')}")
        
        # Проверяем результат
        if result2b.get('interval_level') == 0 and result2b.get('is_learned') == False:
            print("✅ УСПЕХ: Слово с ошибкой НЕ выучено сразу (правильно)!")
        else:
            print("❌ ОШИБКА: Слово с ошибкой обработано неправильно!")
            
        print("\n" + "="*50)
        print("ТЕСТ 3: Множественные новые слова подряд")
        print("="*50)
        
        # 6. Тестируем несколько новых слов подряд
        for i in range(3):
            word = await spaced_repetition_service.get_next_word(user_id, target_lang="cb")
            if not word:
                print(f"❌ Не удалось получить слово {i+3}")
                break
                
            word_text = word.get('word')
            word_id = ObjectId(word.get('word_id'))
            
            print(f"\n🔄 Слово {i+3}: {word_text}")
            
            # Отвечаем правильно с первого раза
            result = await spaced_repetition_service.process_answer(user_id, word_id, True)
            
            print(f"   interval_level: {result.get('interval_level')}")
            print(f"   is_learned: {result.get('is_learned')}")
            
            if result.get('interval_level') == 15 and result.get('is_learned') == True:
                print(f"   ✅ Слово {i+3} выучено правильно")
            else:
                print(f"   ❌ Слово {i+3} НЕ выучено!")
                
        print("\n" + "="*50)
        print("ПРОВЕРКА БАЗЫ ДАННЫХ")
        print("="*50)
        
        # Проверяем все записи в базе данных
        all_progress = await progress_collection.find({"user_id": user_id}).to_list(None)
        print(f"\n📊 Всего записей прогресса: {len(all_progress)}")
        
        learned_count = 0
        for i, prog in enumerate(all_progress):
            word_id = prog.get('word_id')
            interval_level = prog.get('interval_level')
            is_learned = prog.get('is_learned')
            correct_answers = prog.get('correct_answers')
            incorrect_answers = prog.get('incorrect_answers')
            
            print(f"\n📋 Запись {i+1}:")
            print(f"   word_id: {word_id}")
            print(f"   interval_level: {interval_level}")
            print(f"   is_learned: {is_learned}")
            print(f"   correct_answers: {correct_answers}")
            print(f"   incorrect_answers: {incorrect_answers}")
            
            if is_learned:
                learned_count += 1
                
        print(f"\n🎯 ИТОГО ВЫУЧЕННЫХ СЛОВ: {learned_count}")
        
        # Ожидаемый результат: 4 выученных слова (все кроме второго)
        expected_learned = 4
        if learned_count == expected_learned:
            print(f"✅ УСПЕХ: Выучено {learned_count} слов (ожидалось {expected_learned})")
        else:
            print(f"❌ ОШИБКА: Выучено {learned_count} слов (ожидалось {expected_learned})")
            
    finally:
        # Очищаем тестовые данные
        await progress_collection.delete_many({"user_id": user_id})
        print(f"\n🧹 Очищены тестовые данные для пользователя {user_id}")
        
    print("\n🏁 Тест завершен")

if __name__ == "__main__":
    asyncio.run(test_immediate_learning_fix())
