#!/usr/bin/env python3
"""
Тест новой логики для новых слов - если слово отвечено правильно с первого раза,
оно должно сразу считаться выученным.
"""

import asyncio
import os
import sys
from pathlib import Path
from bson import ObjectId
from datetime import datetime
import time

# Добавляем путь к приложению
sys.path.append(str(Path(__file__).parent / "app"))

from app.services.spaced_repetition import SpacedRepetitionService
from app.database import Database

async def test_new_word_logic():
    """Тест логики для новых слов"""
    print("=== ТЕСТ НОВОЙ ЛОГИКИ ДЛЯ НОВЫХ СЛОВ ===")
    
    # Инициализация
    db_instance = Database()
    await db_instance.connect_to_db()
    service = SpacedRepetitionService(db_instance.get_db('words'))
    
    # Создаем тестового пользователя
    test_user_id = ObjectId()
    print(f"Тестовый пользователь: {test_user_id}")
    
    # Создаем тестовое слово с уникальным именем
    test_word_id = ObjectId()
    words_collection = await service.words_collection
    unique_word = f"test_new_word_{int(time.time())}"

    test_word = {
        "_id": test_word_id,
        "word": unique_word,
        "language": "en",
        "level": "A0",
        "concept_id": "test_concept",
        "examples": [{"sentence": f"This is a {unique_word}", "correct_answers": [unique_word]}],
        "ipa_pronunciation": "/test/",
        "part_of_speech": "noun"
    }

    # Удаляем тестовое слово если оно уже существует
    await words_collection.delete_one({"word": unique_word, "language": "en"})
    await words_collection.insert_one(test_word)
    print(f"Создано тестовое слово: {unique_word}")
    
    # Добавляем слово в прогресс пользователя (новое слово с interval_level = -1)
    success = await service._add_word_to_progress(test_user_id, test_word_id)
    if not success:
        print("❌ Не удалось добавить слово в прогресс")
        return False
    
    # Проверяем начальное состояние
    collection = await service.user_progress_collection
    initial_progress = await collection.find_one({
        "user_id": test_user_id,
        "word_id": test_word_id
    })
    
    print(f"Начальное состояние: interval_level={initial_progress.get('interval_level')}, is_learned={initial_progress.get('is_learned')}")
    
    if initial_progress.get('interval_level') != -1:
        print("❌ Новое слово должно иметь interval_level = -1")
        return False
    
    if initial_progress.get('is_learned') != False:
        print("❌ Новое слово должно иметь is_learned = False")
        return False
    
    print("✅ Начальное состояние корректно")
    
    # Отвечаем правильно на новое слово
    print("\n--- Отвечаем правильно на новое слово ---")
    result = await service.process_answer(test_user_id, test_word_id, True)
    
    print(f"Результат после правильного ответа:")
    print(f"  interval_level: {result.get('interval_level')}")
    print(f"  is_learned: {result.get('is_learned')}")
    print(f"  correct_answers: {result.get('correct_answers')}")
    
    # Проверяем, что слово стало выученным
    if result.get('is_learned') != True:
        print("❌ Новое слово с правильным ответом должно стать выученным (is_learned = True)")
        return False
    
    # Проверяем, что interval_level установлен на максимальный
    from app.services.spaced_repetition import MAX_INTERVAL_LEVEL
    if result.get('interval_level') != MAX_INTERVAL_LEVEL:
        print(f"❌ Новое слово с правильным ответом должно иметь interval_level = {MAX_INTERVAL_LEVEL}, получено: {result.get('interval_level')}")
        return False
    
    print("✅ Новое слово правильно помечено как выученное!")
    
    # Тестируем статистику
    print("\n--- Тестируем статистику ---")
    stats = await service.get_user_statistics(test_user_id, "en")
    print(f"Статистика: {stats}")
    
    if stats.get('learned_words') != 1:
        print(f"❌ Статистика должна показывать 1 выученное слово, получено: {stats.get('learned_words')}")
        return False
    
    print("✅ Статистика корректна!")
    
    # Очистка
    await collection.delete_one({"user_id": test_user_id, "word_id": test_word_id})
    await words_collection.delete_one({"_id": test_word_id})
    print("\n✅ Тест завершен успешно!")
    
    return True

async def test_existing_word_logic():
    """Тест логики для уже изучаемых слов (не должна измениться)"""
    print("\n=== ТЕСТ ЛОГИКИ ДЛЯ СУЩЕСТВУЮЩИХ СЛОВ ===")
    
    # Инициализация
    db_instance = Database()
    await db_instance.connect_to_db()
    service = SpacedRepetitionService(db_instance.get_db('words'))
    
    # Создаем тестового пользователя
    test_user_id = ObjectId()
    print(f"Тестовый пользователь: {test_user_id}")
    
    # Создаем тестовое слово с уникальным именем
    test_word_id = ObjectId()
    words_collection = await service.words_collection
    unique_word = f"test_existing_word_{int(time.time())}"

    test_word = {
        "_id": test_word_id,
        "word": unique_word,
        "language": "en",
        "level": "A0",
        "concept_id": "test_concept_2",
        "examples": [{"sentence": f"This is a {unique_word}", "correct_answers": [unique_word]}],
        "ipa_pronunciation": "/test/",
        "part_of_speech": "noun"
    }

    # Удаляем тестовое слово если оно уже существует
    await words_collection.delete_one({"word": unique_word, "language": "en"})
    await words_collection.insert_one(test_word)
    
    # Создаем прогресс для уже изучаемого слова (interval_level = 0)
    collection = await service.user_progress_collection
    existing_progress = {
        "user_id": test_user_id,
        "word_id": test_word_id,
        "interval_level": 0,  # Уже изучается
        "correct_answers": 1,
        "incorrect_answers": 0,
        "last_reviewed": datetime.utcnow(),
        "next_review": datetime.utcnow(),
        "is_learned": False,
        "force_review": False,
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow()
    }
    
    await collection.insert_one(existing_progress)
    print(f"Создано слово с interval_level = 0 (уже изучается)")
    
    # Отвечаем правильно на существующее слово
    result = await service.process_answer(test_user_id, test_word_id, True)
    
    print(f"Результат после правильного ответа:")
    print(f"  interval_level: {result.get('interval_level')}")
    print(f"  is_learned: {result.get('is_learned')}")
    
    # Проверяем, что слово НЕ стало сразу выученным
    if result.get('interval_level') != 1:
        print(f"❌ Существующее слово должно перейти с уровня 0 на уровень 1, получено: {result.get('interval_level')}")
        return False
    
    if result.get('is_learned') == True:
        print("❌ Существующее слово НЕ должно сразу становиться выученным")
        return False
    
    print("✅ Логика для существующих слов работает корректно!")
    
    # Очистка
    await collection.delete_one({"user_id": test_user_id, "word_id": test_word_id})
    await words_collection.delete_one({"_id": test_word_id})
    
    return True

async def test_reset_word_logic():
    """Тест логики для слов со сброшенным уровнем (НЕ должны становиться выученными)"""
    print("\n=== ТЕСТ ЛОГИКИ ДЛЯ СЛОВ СО СБРОШЕННЫМ УРОВНЕМ ===")

    # Инициализация
    db_instance = Database()
    await db_instance.connect_to_db()
    service = SpacedRepetitionService(db_instance.get_db('words'))

    # Создаем тестового пользователя
    test_user_id = ObjectId()
    print(f"Тестовый пользователь: {test_user_id}")

    # Создаем тестовое слово с уникальным именем
    test_word_id = ObjectId()
    words_collection = await service.words_collection
    unique_word = f"test_reset_word_{int(time.time())}"

    test_word = {
        "_id": test_word_id,
        "word": unique_word,
        "language": "en",
        "level": "A0",
        "concept_id": "test_concept_3",
        "examples": [{"sentence": f"This is a {unique_word}", "correct_answers": [unique_word]}],
        "ipa_pronunciation": "/test/",
        "part_of_speech": "noun"
    }

    # Удаляем тестовое слово если оно уже существует
    await words_collection.delete_one({"word": unique_word, "language": "en"})
    await words_collection.insert_one(test_word)

    # Создаем прогресс для слова, которое было сброшено до -1 после неправильных ответов
    collection = await service.user_progress_collection
    reset_progress = {
        "user_id": test_user_id,
        "word_id": test_word_id,
        "interval_level": -1,  # Сброшено до -1
        "correct_answers": 2,  # Уже были правильные ответы
        "incorrect_answers": 3,  # И неправильные ответы
        "last_reviewed": datetime.utcnow(),
        "next_review": datetime.utcnow(),
        "is_learned": False,
        "force_review": True,
        "last_answer_was_incorrect": True,  # Последний ответ был неправильный
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow()
    }

    await collection.insert_one(reset_progress)
    print(f"Создано слово со сброшенным interval_level = -1 (correct_answers=2, incorrect_answers=3)")

    # Отвечаем правильно на сброшенное слово
    result = await service.process_answer(test_user_id, test_word_id, True)

    print(f"Результат после правильного ответа:")
    print(f"  interval_level: {result.get('interval_level')}")
    print(f"  is_learned: {result.get('is_learned')}")
    print(f"  correct_answers: {result.get('correct_answers')}")
    print(f"  incorrect_answers: {result.get('incorrect_answers')}")

    # Проверяем, что слово НЕ стало сразу выученным
    if result.get('interval_level') != 0:
        print(f"❌ Сброшенное слово должно перейти с уровня -1 на уровень 0, получено: {result.get('interval_level')}")
        return False

    if result.get('is_learned') == True:
        print("❌ Сброшенное слово НЕ должно сразу становиться выученным")
        return False

    if result.get('correct_answers') != 3:
        print(f"❌ Количество правильных ответов должно увеличиться до 3, получено: {result.get('correct_answers')}")
        return False

    print("✅ Логика для сброшенных слов работает корректно!")

    # ДОПОЛНИТЕЛЬНАЯ ПРОВЕРКА: Тестируем is_new логику
    print("\n--- Проверяем is_new логику для API ---")

    # Проверяем логику определения is_new в API (копируем логику из роутера)
    # Для сброшенного слова: interval_level=-1, но correct_answers=3, incorrect_answers=3
    # Должно быть is_new = False
    api_is_new = (
        result.get("interval_level", -1) == -1 and
        result.get("correct_answers", 0) <= 1 and
        result.get("incorrect_answers", 0) == 0 and
        not result.get("is_learned", False)
    )

    print(f"API is_new логика для сброшенного слова: {api_is_new}")
    print(f"  interval_level: {result.get('interval_level')}")
    print(f"  correct_answers: {result.get('correct_answers')} (должно быть > 1)")
    print(f"  incorrect_answers: {result.get('incorrect_answers')} (должно быть > 0)")
    print(f"  is_learned: {result.get('is_learned')}")

    if api_is_new == True:
        print("❌ Сброшенное слово НЕ должно считаться новым в API")
        return False

    print("✅ API правильно определяет, что сброшенное слово не является новым!")

    # Очистка
    await collection.delete_one({"user_id": test_user_id, "word_id": test_word_id})
    await words_collection.delete_one({"_id": test_word_id})

    return True

async def main():
    """Основная функция тестирования"""
    try:
        # Тест новой логики
        success1 = await test_new_word_logic()

        # Тест существующей логики
        success2 = await test_existing_word_logic()

        # Тест логики для сброшенных слов
        success3 = await test_reset_word_logic()

        if success1 and success2 and success3:
            print("\n🎉 ВСЕ ТЕСТЫ ПРОШЛИ УСПЕШНО!")
            print("✅ Новые слова с правильным ответом сразу становятся выученными")
            print("✅ Существующие слова продолжают работать по старой логике")
            print("✅ Сброшенные слова НЕ становятся сразу выученными")
            print("✅ Статистика работает корректно")
        else:
            print("\n❌ НЕКОТОРЫЕ ТЕСТЫ НЕ ПРОШЛИ")

    except Exception as e:
        print(f"❌ Ошибка при выполнении тестов: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
