#!/usr/bin/env python3
"""
Тест полного потока работы с новым словом.

Проверяем:
1. Загрузка нового слова
2. Правильный ответ на новое слово
3. Проверка, что слово выучилось и не появляется снова
"""

import asyncio
import sys
import os
from datetime import datetime
from bson import ObjectId

# Добавляем путь к корню проекта
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import Database

async def test_new_word_flow():
    """Тестируем полный поток работы с новым словом."""
    
    print("🧪 Тест полного потока работы с новым словом")
    print("=" * 60)
    
    # Подключаемся к базе данных
    db_instance = Database()
    await db_instance.connect_to_db()
    
    # Импортируем сервис только после подключения к БД
    from app.services.spaced_repetition import spaced_repetition_service
    
    # Получаем коллекции
    words_db = db_instance.get_db('words')
    words_collection = words_db.words
    progress_collection = words_db.user_progress
    
    # Создаем тестового пользователя
    test_user_id = ObjectId()
    print(f"👤 Тестовый пользователь: {test_user_id}")
    
    # Очищаем прогресс тестового пользователя
    await progress_collection.delete_many({"user_id": test_user_id})
    
    try:
        # 1. Получаем новое слово
        print("\n1️⃣ Получаем новое слово")
        print("-" * 30)
        
        new_word = await spaced_repetition_service.get_next_word(test_user_id, target_lang="cb")
        
        if not new_word:
            print("❌ ОШИБКА: Не удалось получить новое слово!")
            return
            
        word_id = ObjectId(new_word.get('word_id'))
        word_text = new_word.get('word')
        
        print(f"✅ Получено новое слово: {word_text}")
        print(f"📊 is_new: {new_word.get('is_new')}")
        print(f"📊 is_forced_review: {new_word.get('is_forced_review')}")
        
        # Проверяем состояние в БД
        progress_before = await progress_collection.find_one({
            "user_id": test_user_id,
            "word_id": word_id
        })
        
        if progress_before:
            print(f"📋 В БД до ответа:")
            print(f"   interval_level: {progress_before.get('interval_level')}")
            print(f"   force_review: {progress_before.get('force_review')}")
            print(f"   is_learned: {progress_before.get('is_learned')}")
            print(f"   correct_answers: {progress_before.get('correct_answers')}")
            print(f"   incorrect_answers: {progress_before.get('incorrect_answers')}")
        
        # 2. Отправляем правильный ответ
        print("\n2️⃣ Отправляем правильный ответ")
        print("-" * 30)
        
        result = await spaced_repetition_service.process_answer(
            user_id=test_user_id,
            word_id=word_id,
            is_correct=True
        )
        
        print(f"📊 Результат process_answer:")
        print(f"   interval_level: {result.get('interval_level')}")
        print(f"   force_review: {result.get('force_review')}")
        print(f"   is_learned: {result.get('is_learned')}")
        print(f"   correct_answers: {result.get('correct_answers')}")
        print(f"   incorrect_answers: {result.get('incorrect_answers')}")
        
        # Проверяем состояние в БД после ответа
        progress_after = await progress_collection.find_one({
            "user_id": test_user_id,
            "word_id": word_id
        })
        
        if progress_after:
            print(f"📋 В БД после ответа:")
            print(f"   interval_level: {progress_after.get('interval_level')}")
            print(f"   force_review: {progress_after.get('force_review')}")
            print(f"   is_learned: {progress_after.get('is_learned')}")
            print(f"   correct_answers: {progress_after.get('correct_answers')}")
            print(f"   incorrect_answers: {progress_after.get('incorrect_answers')}")
            print(f"   next_review: {progress_after.get('next_review')}")
        
        # 3. Проверяем, что слово выучилось
        print("\n3️⃣ Проверяем, что слово выучилось")
        print("-" * 30)
        
        if progress_after.get('is_learned'):
            print("✅ ПРАВИЛЬНО: Слово помечено как выученное")
        else:
            print("❌ ОШИБКА: Слово НЕ помечено как выученное!")
        
        # 4. Пытаемся получить следующее слово
        print("\n4️⃣ Пытаемся получить следующее слово")
        print("-" * 30)
        
        next_word = await spaced_repetition_service.get_next_word(test_user_id, target_lang="cb")
        
        if next_word:
            next_word_id = next_word.get('word_id')
            next_word_text = next_word.get('word')
            
            print(f"✅ Получено следующее слово: {next_word_text}")
            print(f"📊 is_new: {next_word.get('is_new')}")
            print(f"📊 is_forced_review: {next_word.get('is_forced_review')}")
            
            # Проверяем, что это НЕ то же самое слово
            if next_word_id != new_word.get('word_id'):
                print("✅ ПРАВИЛЬНО: Получено другое слово (выученное не повторяется)")
            else:
                print("❌ ОШИБКА: Получено то же самое слово, которое должно быть выученным!")
        else:
            print("ℹ️ Следующее слово не найдено (возможно, все слова изучены)")
        
        # 5. Проверяем статистику
        print("\n5️⃣ Проверяем статистику")
        print("-" * 30)
        
        stats = await spaced_repetition_service.get_user_statistics(test_user_id, target_lang="cb")
        
        print(f"📊 Статистика:")
        print(f"   learned_words: {stats.get('learned_words')}")
        print(f"   total_words_in_progress: {stats.get('total_words_in_progress')}")
        print(f"   active_words: {stats.get('active_words')}")
        print(f"   forced_words: {stats.get('forced_words')}")
        
        if stats.get('learned_words') >= 1:
            print("✅ ПРАВИЛЬНО: Статистика показывает выученные слова")
        else:
            print("❌ ОШИБКА: Статистика НЕ показывает выученные слова!")
            
    except Exception as e:
        print(f"❌ ОШИБКА в тесте: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Очищаем тестовые данные
        await progress_collection.delete_many({"user_id": test_user_id})
        print(f"\n🧹 Очищены тестовые данные для пользователя {test_user_id}")
    
    print("\n" + "=" * 60)
    print("🏁 Тест завершен")

if __name__ == "__main__":
    asyncio.run(test_new_word_flow())
