#!/usr/bin/env python3
"""
Тест реальной проблемы с API - предзагрузка возвращает то же слово.
"""

import asyncio
import sys
import os
import requests
import time
from datetime import datetime

# Добавляем путь к корню проекта
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

API_BASE = "http://localhost:8000"

def log_with_time(message):
    """Логирование с временной меткой."""
    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] {message}")

async def test_real_api_issue():
    """Воспроизводим реальную проблему с API."""
    
    print("🧪 Тест реальной проблемы с API")
    print("=" * 60)
    
    # Используем того же пользователя из логов
    user_id = "3b5c0d62"
    
    try:
        # 1. Получаем карточку (как в логах)
        log_with_time("1️⃣ Получаем карточку")
        
        response1 = requests.get(f"{API_BASE}/api/spaced/next", params={
            "user_id": user_id,
            "native_lang": "ru", 
            "target_lang": "cb"
        })
        
        if response1.status_code != 200:
            print(f"❌ Ошибка API: {response1.status_code}")
            return
            
        card1 = response1.json()
        word1 = card1.get('word')
        card_id = card1.get('word_id')
        
        log_with_time(f"✅ Получена карточка: {word1}")
        log_with_time(f"   card_id: {card_id}")
        log_with_time(f"   interval_level: {card1.get('interval_level')}")
        
        # 2. Сразу же делаем предзагрузку (как во фронтенде)
        log_with_time("2️⃣ Предзагрузка (сразу после загрузки)")
        
        response2 = requests.get(f"{API_BASE}/api/spaced/next", params={
            "user_id": user_id,
            "native_lang": "ru", 
            "target_lang": "cb",
            "preload": "true"
        })
        
        if response2.status_code == 200:
            card2 = response2.json()
            word2 = card2.get('word')
            
            log_with_time(f"✅ Предзагружена карточка: {word2}")
            
            if word2 == word1:
                log_with_time("❌ ПРОБЛЕМА: Предзагрузка вернула то же слово!")
            else:
                log_with_time("✅ Предзагрузка вернула другое слово")
        else:
            log_with_time(f"❌ Ошибка предзагрузки: {response2.status_code}")
        
        # 3. Отвечаем на карточку (неправильно, как в сценарии)
        log_with_time("3️⃣ Отвечаем неправильно")
        
        response3 = requests.post(f"{API_BASE}/api/spaced/{card_id}/response", 
                                json={"is_correct": False})
        
        if response3.status_code == 200:
            result3 = response3.json()
            log_with_time(f"✅ Неправильный ответ обработан")
            log_with_time(f"   interval_level: {result3.get('interval_level')}")
            log_with_time(f"   force_review: {result3.get('force_review', 'N/A')}")
        else:
            log_with_time(f"❌ Ошибка ответа: {response3.status_code}")
            return
        
        # 4. Получаем следующую карточку (должно быть то же слово из форсированной очереди)
        log_with_time("4️⃣ Получаем следующую карточку (форсированная очередь)")
        
        response4 = requests.get(f"{API_BASE}/api/spaced/next", params={
            "user_id": user_id,
            "native_lang": "ru", 
            "target_lang": "cb"
        })
        
        if response4.status_code == 200:
            card4 = response4.json()
            word4 = card4.get('word')
            
            log_with_time(f"✅ Получена карточка: {word4}")
            
            if word4 == word1:
                log_with_time("✅ Правильно: То же слово из форсированной очереди")
            else:
                log_with_time("❌ ОШИБКА: Получено другое слово!")
        
        # 5. Отвечаем правильно
        log_with_time("5️⃣ Отвечаем правильно")
        
        response5 = requests.post(f"{API_BASE}/api/spaced/{card_id}/response", 
                                json={"is_correct": True})
        
        if response5.status_code == 200:
            result5 = response5.json()
            log_with_time(f"✅ Правильный ответ обработан")
            log_with_time(f"   interval_level: {result5.get('interval_level')}")
            log_with_time(f"   next_review: {result5.get('next_review')}")
            log_with_time(f"   force_review: {result5.get('force_review', 'N/A')}")
        else:
            log_with_time(f"❌ Ошибка ответа: {response5.status_code}")
            return
        
        # 6. КЛЮЧЕВАЯ ПРОВЕРКА: Получаем следующую карточку сразу после правильного ответа
        log_with_time("6️⃣ КЛЮЧЕВАЯ ПРОВЕРКА: Следующая карточка сразу после правильного ответа")
        
        response6 = requests.get(f"{API_BASE}/api/spaced/next", params={
            "user_id": user_id,
            "native_lang": "ru", 
            "target_lang": "cb"
        })
        
        if response6.status_code == 200:
            card6 = response6.json()
            word6 = card6.get('word')
            
            log_with_time(f"✅ Получена карточка: {word6}")
            
            if word6 == word1:
                log_with_time("❌ ПРОБЛЕМА: То же слово вернулось сразу после правильного ответа!")
            else:
                log_with_time("✅ Получено другое слово (исключение работает)")
        else:
            log_with_time(f"ℹ️ Нет доступных карточек: {response6.status_code}")
        
        # 7. КЛЮЧЕВАЯ ПРОВЕРКА: Предзагрузка сразу после правильного ответа
        log_with_time("7️⃣ КЛЮЧЕВАЯ ПРОВЕРКА: Предзагрузка сразу после правильного ответа")
        
        response7 = requests.get(f"{API_BASE}/api/spaced/next", params={
            "user_id": user_id,
            "native_lang": "ru", 
            "target_lang": "cb",
            "preload": "true"
        })
        
        if response7.status_code == 200:
            card7 = response7.json()
            word7 = card7.get('word')
            
            log_with_time(f"✅ Предзагружена карточка: {word7}")
            
            if word7 == word1:
                log_with_time("❌ ПРОБЛЕМА: Предзагрузка вернула то же слово!")
                log_with_time("   Это и есть причина дублирования карточек во фронтенде!")
            else:
                log_with_time("✅ Предзагрузка вернула другое слово")
        else:
            log_with_time(f"ℹ️ Предзагрузка не нашла карточек: {response7.status_code}")
        
        # 8. Ждем 2.5 минуты и проверяем снова
        log_with_time("8️⃣ Ждем 2.5 минуты и проверяем снова...")
        log_with_time("   (В реальном тесте можно пропустить ожидание)")
        
        # В реальном тесте можно раскомментировать:
        # time.sleep(150)  # 2.5 минуты
        
        # response8 = requests.get(f"{API_BASE}/api/spaced/next", params={
        #     "user_id": user_id,
        #     "native_lang": "ru", 
        #     "target_lang": "cb"
        # })
        
        # if response8.status_code == 200:
        #     card8 = response8.json()
        #     word8 = card8.get('word')
        #     log_with_time(f"✅ После 2.5 минут получена карточка: {word8}")
        # else:
        #     log_with_time(f"ℹ️ После 2.5 минут нет карточек: {response8.status_code}")
        
    except Exception as e:
        log_with_time(f"❌ Ошибка в тесте: {e}")
    
    print("\n" + "=" * 60)
    print("🏁 Тест завершен")
    print("📋 Проверьте логи бэкенда для подробной диагностики")

if __name__ == "__main__":
    asyncio.run(test_real_api_issue())
