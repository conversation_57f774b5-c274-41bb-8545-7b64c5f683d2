#!/usr/bin/env python3
"""
Тест для проверки проблемы с interval_level для новых слов.
Воспроизводит сценарий: новое слово + правильный ответ = должно стать interval_level=15 и is_learned=true
"""

import asyncio
import os
from datetime import datetime
from bson import ObjectId
from dotenv import load_dotenv
from motor.motor_asyncio import AsyncIOMotorClient

# Загружаем переменные окружения
load_dotenv()

async def test_new_word_interval():
    """Тестирует логику interval_level для новых слов."""
    
    # Подключаемся к MongoDB
    mongo_uri = os.getenv("MONGODB_URL")
    db_name = os.getenv("WORDS_DATABASE_NAME", "word_master")
    client = AsyncIOMotorClient(mongo_uri)
    db = client[db_name]
    
    try:
        # Импортируем сервис
        import sys
        sys.path.append('/home/<USER>/Memo/backend')
        from app.services.spaced_repetition import SpacedRepetitionService, MAX_INTERVAL_LEVEL
        from app.database import Database

        print(f"🔧 MAX_INTERVAL_LEVEL = {MAX_INTERVAL_LEVEL}")
        print(f"🔧 Ожидаемый interval_level для выученного слова: {MAX_INTERVAL_LEVEL}")

        # Инициализируем подключение к базе данных
        await Database.connect_to_db()

        # Создаем сервис
        service = SpacedRepetitionService()
        
        # Тестовые данные
        test_user_id = ObjectId("684c7900e9e41d7a3b5c0d62")  # Существующий пользователь
        
        # Создаем тестовое слово с уникальным именем
        import time
        unique_suffix = str(int(time.time() * 1000))  # Миллисекунды для уникальности
        test_word = {
            "_id": ObjectId(),
            "word": f"test_new_word_{unique_suffix}",
            "language": "en",
            "concept_id": f"test-concept-{unique_suffix}",
            "level": "A0",
            "translations": ["тестовое новое слово"],
            "examples": [],
            "tags": [],
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        # Добавляем слово в коллекцию words
        words_collection = await service.words_collection
        await words_collection.insert_one(test_word)
        test_word_id = test_word["_id"]
        
        print(f"\n1️⃣ Создано тестовое слово: {test_word['word']} (ID: {test_word_id})")
        
        # Добавляем слово в прогресс пользователя (как новое слово)
        success = await service._add_word_to_progress(test_user_id, test_word_id)
        if not success:
            print("❌ Не удалось добавить слово в прогресс")
            return False
            
        print("2️⃣ Слово добавлено в прогресс пользователя")
        
        # Проверяем начальное состояние
        progress_collection = await service.user_progress_collection
        initial_progress = await progress_collection.find_one({
            "user_id": test_user_id,
            "word_id": test_word_id
        })
        
        print(f"\n📊 НАЧАЛЬНОЕ СОСТОЯНИЕ:")
        print(f"   interval_level: {initial_progress.get('interval_level')}")
        print(f"   is_learned: {initial_progress.get('is_learned')}")
        print(f"   correct_answers: {initial_progress.get('correct_answers')}")
        print(f"   incorrect_answers: {initial_progress.get('incorrect_answers')}")
        
        # Проверяем, что это действительно новое слово
        if initial_progress.get('interval_level') != -1:
            print("❌ ОШИБКА: Новое слово должно иметь interval_level = -1")
            return False
            
        if initial_progress.get('correct_answers') != 0:
            print("❌ ОШИБКА: Новое слово должно иметь correct_answers = 0")
            return False
            
        print("✅ Подтверждено: это новое слово (interval_level = -1, correct_answers = 0)")
        
        # Отправляем правильный ответ
        print(f"\n3️⃣ Отправляем ПРАВИЛЬНЫЙ ответ на новое слово...")
        
        result = await service.process_answer(
            user_id=test_user_id,
            word_id=test_word_id,
            is_correct=True
        )
        
        print(f"\n📊 РЕЗУЛЬТАТ process_answer:")
        print(f"   interval_level: {result.get('interval_level')}")
        print(f"   is_learned: {result.get('is_learned')}")
        print(f"   correct_answers: {result.get('correct_answers')}")
        print(f"   incorrect_answers: {result.get('incorrect_answers')}")
        print(f"   next_review: {result.get('next_review')}")
        
        # Проверяем результат
        expected_interval = MAX_INTERVAL_LEVEL
        actual_interval = result.get('interval_level')
        actual_learned = result.get('is_learned')
        
        print(f"\n🔍 ПРОВЕРКА РЕЗУЛЬТАТА:")
        print(f"   Ожидаемый interval_level: {expected_interval}")
        print(f"   Фактический interval_level: {actual_interval}")
        print(f"   Ожидаемый is_learned: True")
        print(f"   Фактический is_learned: {actual_learned}")
        
        # Проверяем в базе данных
        final_progress = await progress_collection.find_one({
            "user_id": test_user_id,
            "word_id": test_word_id
        })
        
        print(f"\n📊 СОСТОЯНИЕ В БАЗЕ ДАННЫХ:")
        print(f"   interval_level: {final_progress.get('interval_level')}")
        print(f"   is_learned: {final_progress.get('is_learned')}")
        print(f"   correct_answers: {final_progress.get('correct_answers')}")
        print(f"   incorrect_answers: {final_progress.get('incorrect_answers')}")
        
        # Финальная проверка
        success = True
        
        if actual_interval != expected_interval:
            print(f"❌ ОШИБКА: interval_level должен быть {expected_interval}, получен {actual_interval}")
            success = False
            
        if not actual_learned:
            print(f"❌ ОШИБКА: is_learned должен быть True, получен {actual_learned}")
            success = False
            
        if final_progress.get('interval_level') != expected_interval:
            print(f"❌ ОШИБКА: В БД interval_level должен быть {expected_interval}, получен {final_progress.get('interval_level')}")
            success = False
            
        if not final_progress.get('is_learned'):
            print(f"❌ ОШИБКА: В БД is_learned должен быть True, получен {final_progress.get('is_learned')}")
            success = False
            
        if success:
            print("✅ ВСЕ ПРОВЕРКИ ПРОШЛИ УСПЕШНО!")
            print("✅ Новое слово правильно помечено как выученное с максимальным интервалом")
        else:
            print("❌ ОБНАРУЖЕНЫ ОШИБКИ В ЛОГИКЕ!")
            
        return success
        
    except Exception as e:
        print(f"❌ ОШИБКА: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Очищаем тестовые данные
        try:
            words_collection = await service.words_collection
            await words_collection.delete_one({"_id": test_word_id})
            
            progress_collection = await service.user_progress_collection
            await progress_collection.delete_one({
                "user_id": test_user_id,
                "word_id": test_word_id
            })
            print(f"\n🧹 Тестовые данные очищены")
        except:
            pass
            
        client.close()

if __name__ == "__main__":
    print("🧪 Тестирование interval_level для новых слов")
    print("=" * 50)
    
    result = asyncio.run(test_new_word_interval())
    
    if result:
        print("\n🎉 ТЕСТ ПРОЙДЕН!")
    else:
        print("\n💥 ТЕСТ НЕ ПРОЙДЕН!")
