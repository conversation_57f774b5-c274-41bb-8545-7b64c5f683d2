"""
Скрипт для добавления слов в прогресс пользователя.
"""

import asyncio
from bson import ObjectId
from datetime import datetime, timedelta
from dotenv import load_dotenv
import os

from motor.motor_asyncio import AsyncIOMotorClient

# Загружаем переменные окружения из .env файла
load_dotenv()

# Настройки
USER_EMAIL = "<EMAIL>"
WORDS_TO_ADD = 10  # Количество слов для добавления

async def add_words_to_user():
    """Добавляем слова в прогресс пользователя"""
    try:
        # Подключаемся к MongoDB
        client = AsyncIOMotorClient(os.getenv("MONGODB_URL"))
        db = client["word_master"]
        users_db = client["users_db"]
        
        # Находим пользователя
        user = await users_db.users.find_one({"email": USER_EMAIL})
        if not user:
            print(f"❌ Пользователь с email {USER_EMAIL} не найден")
            return
        
        user_id = user["_id"]
        print(f"Найден пользователь: {user['email']} (ID: {user_id})")
        
        # Получаем список слов, которые еще не добавлены пользователю
        pipeline = [
            {
                "$lookup": {
                    "from": "user_progress",
                    "let": {"word_id": "$_id"},
                    "pipeline": [
                        {
                            "$match": {
                                "$expr": {
                                    "$and": [
                                        {"$eq": ["$word_id", "$$word_id"]},
                                        {"$eq": ["$user_id", user_id]}
                                    ]
                                }
                            }
                        }
                    ],
                    "as": "progress"
                }
            },
            {"$match": {"progress": {"$size": 0}}},  # Только слова без прогресса
            {"$limit": WORDS_TO_ADD}
        ]
        
        words_cursor = db.words.aggregate(pipeline)
        words = await words_cursor.to_list(length=WORDS_TO_ADD)
        
        if not words:
            print("❌ Нет доступных слов для добавления")
            return
        
        print(f"\nДобавляем {len(words)} слов в прогресс пользователя:")
        
        # Создаем записи в user_progress
        now = datetime.utcnow()
        user_progress = []
        
        for word in words:
            progress = {
                "user_id": user_id,
                "word_id": word["_id"],
                "interval_level": 0,
                "correct_answers": 0,
                "incorrect_answers": 0,
                "last_reviewed": now,
                "next_review": now + timedelta(minutes=1),  # Следующее повторение через 1 минуту
                "is_learned": False,
                "force_review": False,
                "created_at": now,
                "updated_at": now
            }
            user_progress.append(progress)
            print(f"- {word.get('word')} (ID: {word['_id']})")
        
        # Вставляем записи в базу
        if user_progress:
            result = await db.user_progress.insert_many(user_progress)
            print(f"\n✅ Успешно добавлено {len(result.inserted_ids)} слов в прогресс пользователя")
        
    except Exception as e:
        print(f"❌ Ошибка: {str(e)}")
    finally:
        if 'client' in locals():
            client.close()

if __name__ == "__main__":
    asyncio.run(add_words_to_user())
