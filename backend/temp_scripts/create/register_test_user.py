import requests
import json

def register_test_user():
    url = "http://localhost:8001/api/register"
    
    test_user = {
        "email": "<EMAIL>",
        "password": "testpassword123",
        "username": "testuser"
    }
    
    try:
        print(f"Попытка регистрации пользователя: {test_user['email']}")
        response = requests.post(url, json=test_user)
        response.raise_for_status()
        
        print("✅ Пользователь успешно зарегистрирован!")
        print("Данные пользователя:")
        print(json.dumps(response.json(), indent=2, ensure_ascii=False))
        
        return True
    except requests.exceptions.HTTPError as e:
        if e.response.status_code == 400:
            print("❌ Ошибка: Пользователь с таким email уже существует")
            print("Попытка входа...")
            return login_test_user()
        else:
            print(f"❌ Ошибка при регистрации: {e}")
    except Exception as e:
        print(f"❌ Произошла ошибка: {e}")
    
    return False

def login_test_user():
    url = "http://localhost:8001/api/token"
    
    login_data = {
        "username": "<EMAIL>",
        "password": "testpassword123"
    }
    
    try:
        print(f"\nПопытка входа пользователя: {login_data['username']}")
        response = requests.post(
            url,
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        response.raise_for_status()
        
        token = response.json().get("access_token")
        if token:
            print("✅ Успешный вход!")
            print(f"Токен доступа: {token[:15]}...")
            
            # Получаем данные пользователя
            user_url = "http://localhost:8001/api/users/me"
            user_response = requests.get(
                user_url,
                headers={"Authorization": f"Bearer {token}"}
            )
            user_response.raise_for_status()
            
            print("\nДанные пользователя:")
            print(json.dumps(user_response.json(), indent=2, ensure_ascii=False))
            
            return True
        
    except requests.exceptions.HTTPError as e:
        print(f"❌ Ошибка при входе: {e}")
        if e.response.status_code == 401:
            print("Неверный email или пароль")
    except Exception as e:
        print(f"❌ Произошла ошибка: {e}")
    
    return False

if __name__ == "__main__":
    print("=== Тестирование регистрации и входа пользователя ===\n")
    
    # Сначала пробуем зарегистрировать
    if not register_test_user():
        print("\nНе удалось зарегистрировать/войти с тестовым пользователем")
    else:
        print("\n✅ Готово! Теперь вы можете использовать эти данные для входа в приложении:")
        print("Email: <EMAIL>")
        print("Пароль: testpassword123")
