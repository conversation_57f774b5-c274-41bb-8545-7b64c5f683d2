"""
Создание оптимизированных индексов для улучшения производительности запросов
"""
import asyncio
import os
from dotenv import load_dotenv
from motor.motor_asyncio import AsyncIOMotorClient

# Загружаем переменные окружения
load_dotenv()

async def create_optimized_indexes():
    """Создаем оптимизированные индексы для улучшения производительности"""
    try:
        # Подключаемся к MongoDB
        client = AsyncIOMotorClient(os.getenv("MONGODB_URL"))
        db = client.get_database("word_master")
        words_collection = db.words
        
        print("🔗 Подключение к MongoDB установлено")
        
        # Проверяем текущие индексы
        print(f"\n📋 Текущие индексы коллекции words:")
        words_indexes = await words_collection.index_information()
        for name, info in words_indexes.items():
            print(f"  - {name}: {info.get('key', [])}")
        
        # Создаем оптимизированный составной индекс для запросов с фильтрами
        print(f"\n🚀 Создание оптимизированного индекса...")
        
        try:
            # Составной индекс для быстрой фильтрации по языку, уровню и приоритету
            await words_collection.create_index(
                [("language", 1), ("level", 1), ("priority", 1)], 
                background=True,
                name="language_level_priority_optimized"
            )
            print("✅ Создан оптимизированный индекс: language + level + priority")
        except Exception as e:
            if "already exists" in str(e).lower():
                print("ℹ️  Индекс language + level + priority уже существует")
            else:
                print(f"⚠️  Ошибка при создании индекса: {e}")
        
        try:
            # Индекс для оптимизации lookup операций
            await words_collection.create_index(
                [("_id", 1), ("language", 1)], 
                background=True,
                name="id_language_lookup_optimized"
            )
            print("✅ Создан оптимизированный индекс: _id + language")
        except Exception as e:
            if "already exists" in str(e).lower():
                print("ℹ️  Индекс _id + language уже существует")
            else:
                print(f"⚠️  Ошибка при создании индекса: {e}")
        
        # Проверяем новые индексы
        print(f"\n📋 Индексы после оптимизации:")
        words_indexes = await words_collection.index_information()
        for name, info in words_indexes.items():
            print(f"  - {name}: {info.get('key', [])}")
        
        print(f"\n✅ Оптимизация индексов завершена")
        
    except Exception as e:
        print(f"❌ Ошибка: {e}")
    finally:
        client.close()

async def test_optimized_performance():
    """Тестируем производительность после оптимизации"""
    try:
        client = AsyncIOMotorClient(os.getenv("MONGODB_URL"))
        db = client.get_database("word_master")
        words_collection = db.words
        user_progress_collection = db.user_progress
        
        print(f"\n🧪 Тестирование производительности после оптимизации")
        
        test_user_id = ObjectId("684c7900e9e41d7a3b5c0d62")
        target_lang = "cb"
        
        import time
        from bson import ObjectId
        
        # Тест оптимизированной агрегации
        start_time = time.time()
        
        pipeline = [
            {
                "$match": {
                    "user_id": test_user_id,
                    "word_id": {"$exists": True}
                }
            },
            {
                "$lookup": {
                    "from": "words",
                    "localField": "word_id",
                    "foreignField": "_id",
                    "as": "word_info"
                }
            },
            {
                "$match": {
                    "word_info.language": target_lang.lower()
                }
            },
            {
                "$project": {
                    "word_id": 1,
                    "_id": 0
                }
            }
        ]
        
        cursor = user_progress_collection.aggregate(pipeline)
        learned_word_ids = [doc["word_id"] async for doc in cursor]
        
        prep_time = (time.time() - start_time) * 1000
        
        # Тест основного запроса с новыми индексами
        start_time = time.time()
        
        match_conditions = {
            "_id": {"$nin": learned_word_ids},
            "language": target_lang.lower(),
            "level": "A0",
            "priority": "ultra_core"
        }
        
        pipeline = [
            {"$match": match_conditions},
            {"$sample": {"size": 1}}
        ]
        
        cursor = words_collection.aggregate(pipeline)
        words = await cursor.to_list(length=1)
        
        query_time = (time.time() - start_time) * 1000
        total_time = prep_time + query_time
        
        print(f"   ⏱️  Оптимизированная подготовка: {prep_time:.2f}ms")
        print(f"   ⏱️  Запрос с новыми индексами: {query_time:.2f}ms")
        print(f"   ⏱️  Общее время: {total_time:.2f}ms")
        print(f"   📊 Исключено слов: {len(learned_word_ids)}")
        print(f"   ✅ Найдено слов: {len(words)}")
        if words:
            print(f"   📝 Слово: {words[0].get('word', 'N/A')}")
        
        print(f"\n🎯 Ожидаемое улучшение: ~2.5x быстрее (с 1034ms до ~400ms)")
        
    except Exception as e:
        print(f"❌ Ошибка в тестировании: {e}")
    finally:
        client.close()

if __name__ == "__main__":
    asyncio.run(create_optimized_indexes())
    print("\n" + "="*50)
    asyncio.run(test_optimized_performance())
