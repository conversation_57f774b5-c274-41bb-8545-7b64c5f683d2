#!/usr/bin/env python3
"""
Скрипт для создания файлов концептов для всех 146 слов A0
Извлекает данные из A0_word_list.md автоматически
"""

import json
import os
import re
from pathlib import Path

def parse_word_list():
    """Извлекает данные слов из A0_word_list.md"""
    
    # Читаем файл списка слов
    with open('../md/A0_word_list.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    words = []
    
    # Регулярное выражение для извлечения строк таблицы
    pattern = r'\| (\d+) \| ([^|]+) \| ([^|]+) \| [^|]+ \| ([^|]+) \| ([^|]+) \|'
    
    for match in re.finditer(pattern, content):
        num = int(match.group(1))
        ru_word = match.group(2).strip()
        en_word = match.group(3).strip()
        priority = match.group(4).strip()
        uuid = match.group(5).strip()
        
        # Определяем категорию и описание на основе слова
        category, concept_name, description_en, description_ru, semantic_field, usage_context = categorize_word(ru_word, en_word)
        
        words.append({
            "num": num,
            "ru": ru_word,
            "en": en_word,
            "priority": priority,
            "uuid": uuid,
            "category": category,
            "concept_name": concept_name,
            "description_en": description_en,
            "description_ru": description_ru,
            "semantic_field": semantic_field,
            "usage_context": usage_context
        })
    
    return words

def categorize_word(ru_word, en_word):
    """Определяет категорию и описание слова"""
    
    # Словарь категоризации
    categories = {
        # Местоимения
        "я": ("pronouns", "first_person_pronoun", "First person singular pronoun - refers to the speaker", "Местоимение первого лица единственного числа", "personal_identity", "basic_communication"),
        "ты": ("pronouns", "second_person_pronoun", "Second person singular pronoun - refers to the addressee", "Местоимение второго лица единственного числа", "personal_identity", "basic_communication"),
        "мы": ("pronouns", "first_person_plural", "First person plural pronoun - refers to speaker and others", "Местоимение первого лица множественного числа", "group_identity", "social_communication"),
        "вы": ("pronouns", "second_person_plural", "Second person plural pronoun - formal or plural addressee", "Местоимение второго лица множественного числа", "social_identity", "formal_communication"),
        "они": ("pronouns", "third_person_plural", "Third person plural pronoun - refers to multiple others", "Местоимение третьего лица множественного числа", "reference", "description"),
        
        # Ответы
        "да": ("responses", "positive_response", "Affirmative response, agreement, confirmation", "Утвердительный ответ, согласие", "communication", "basic_responses"),
        "нет": ("responses", "negative_response", "Negative response, disagreement, denial", "Отрицательный ответ, несогласие", "communication", "basic_responses"),
        
        # Вопросы
        "что": ("questions", "what_question", "Interrogative pronoun asking about things or concepts", "Вопросительное местоимение о предметах", "information_seeking", "basic_questions"),
        "где": ("questions", "location_question", "Interrogative adverb asking about location", "Вопросительное наречие о местоположении", "spatial_orientation", "location_inquiry"),
        "когда": ("questions", "time_question", "Interrogative adverb asking about time", "Вопросительное наречие о времени", "temporal_orientation", "time_inquiry"),
        "как": ("questions", "manner_question", "Interrogative adverb asking about manner or method", "Вопросительное наречие о способе", "process_inquiry", "method_inquiry"),
        "почему": ("questions", "reason_question", "Interrogative adverb asking about reason or cause", "Вопросительное наречие о причине", "causal_inquiry", "explanation_seeking"),
        "сколько": ("questions", "quantity_question", "Interrogative pronoun asking about quantity", "Вопросительное местоимение о количестве", "quantitative_inquiry", "counting"),
        "какой": ("questions", "quality_question", "Interrogative pronoun asking about quality or type", "Вопросительное местоимение о качестве", "descriptive_inquiry", "specification"),
        "зачем": ("questions", "purpose_question", "Interrogative adverb asking about purpose", "Вопросительное наречие о цели", "purpose_inquiry", "goal_seeking"),
        
        # Желания и потребности
        "хочу": ("desires", "basic_desire", "Expression of want, desire, or wish", "Выражение желания или потребности", "emotional_states", "expressing_needs"),
        
        # Базовые действия
        "есть": ("basic_actions", "eating_action", "Basic action of consuming food", "Базовое действие приема пищи", "basic_needs", "daily_life"),
        "пить": ("basic_actions", "drinking_action", "Basic action of consuming liquids", "Базовое действие употребления жидкости", "basic_needs", "daily_life"),
        "спать": ("basic_actions", "rest_action", "Basic biological need for rest and sleep", "Базовая потребность в отдыхе и сне", "basic_needs", "daily_routine"),
        "идти": ("movement", "basic_movement", "Basic action of moving from one place to another", "Базовое действие перемещения", "physical_actions", "daily_movement"),
        "видеть": ("senses", "visual_perception", "Basic sense of sight, visual perception", "Базовое чувство зрения", "sensory_experience", "perception"),
        "говорить": ("communication", "verbal_communication", "Basic action of verbal communication", "Базовое действие устного общения", "communication", "social_interaction"),
        "понимать": ("cognition", "comprehension", "Mental process of understanding", "Мыслительный процесс понимания", "cognitive_processes", "learning"),
        "знать": ("cognition", "knowledge", "Mental state of having information or awareness", "Ментальное состояние обладания знанием", "cognitive_processes", "information_processing"),
        "жить": ("existence", "living_state", "Basic state of being alive and existing", "Базовое состояние жизни и существования", "existence", "life_experience"),
        "делать": ("actions", "general_action", "General action of doing or performing", "Общее действие выполнения или совершения", "activity", "task_performance"),
        
        # Социальные взаимодействия
        "помощь": ("social_interaction", "help_request", "Request for assistance or support", "Просьба о помощи или поддержке", "social_needs", "emergency_situations"),
        "спасибо": ("politeness", "gratitude_expression", "Expression of gratitude and appreciation", "Выражение благодарности и признательности", "social_courtesy", "polite_interaction"),
        "извините": ("politeness", "apology_regret", "Expression of regret or apology", "Выражение сожаления или извинения", "social_courtesy", "conflict_resolution"),
        "пожалуйста": ("politeness", "polite_request", "Polite way to make requests", "Вежливый способ просьбы", "social_courtesy", "polite_interaction"),
        
        # Профессии и люди
        "врач": ("professions", "medical_professional", "Medical professional who treats illnesses", "Медицинский специалист", "healthcare", "medical_emergency"),
        "мама": ("family", "mother_figure", "Female parent, maternal figure", "Женщина-родитель, материнская фигура", "family_relations", "family_communication"),
        "папа": ("family", "father_figure", "Male parent, paternal figure", "Мужчина-родитель, отцовская фигура", "family_relations", "family_communication"),
        "друг": ("relationships", "friendship", "Person with whom one has friendship", "Человек, с которым дружеские отношения", "social_relations", "friendship"),
        "человек": ("people", "human_being", "Individual person, human being", "Отдельная личность, человеческое существо", "humanity", "social_context"),
        "ребенок": ("people", "child", "Young human being, offspring", "Молодой человек, потомство", "age_groups", "family_context"),
        
        # Базовые потребности
        "вода": ("basic_needs", "essential_liquid", "Essential liquid for survival", "Жизненно важная жидкость", "survival_needs", "basic_survival"),
        "хлеб": ("food", "staple_food", "Basic food item, bread", "Основной продукт питания, хлеб", "nutrition", "daily_meals"),
        "еда": ("food", "general_food", "General term for food and nourishment", "Общий термин для пищи и питания", "nutrition", "daily_meals"),
        "молоко": ("food", "dairy_product", "Basic dairy product, milk", "Основной молочный продукт", "nutrition", "beverages"),
        "мясо": ("food", "protein_source", "Animal protein source, meat", "Источник животного белка", "nutrition", "main_dishes"),
        "рис": ("food", "grain_staple", "Basic grain food, rice", "Основная зерновая пища", "nutrition", "staple_foods"),
        "чай": ("beverages", "hot_beverage", "Popular hot beverage, tea", "Популярный горячий напиток", "beverages", "social_drinking"),
        "кофе": ("beverages", "caffeinated_drink", "Popular caffeinated beverage, coffee", "Популярный кофеиновый напиток", "beverages", "daily_routine"),
    }
    
    # Если слово есть в словаре, используем его данные
    if ru_word in categories:
        return categories[ru_word]
    
    # Иначе определяем категорию по английскому слову или общим правилам
    if en_word in ["big", "small", "new", "old", "good", "bad", "hot", "cold", "fast", "slow", "easy", "heavy", "expensive", "cheap", "correct", "important"]:
        return ("adjectives", f"{en_word}_quality", f"Descriptive quality: {en_word}", f"Описательное качество: {ru_word}", "descriptive_qualities", "description")
    elif en_word in ["here", "there", "far", "close", "left", "right", "straight", "up", "down"]:
        return ("location", f"{en_word}_position", f"Spatial position or direction: {en_word}", f"Пространственное положение: {ru_word}", "spatial_orientation", "navigation")
    elif en_word in ["now", "today", "tomorrow", "yesterday", "morning", "afternoon", "evening", "later", "soon", "slowly"]:
        return ("time", f"{en_word}_time", f"Time reference: {en_word}", f"Временная ссылка: {ru_word}", "temporal_reference", "time_expression")
    elif en_word in ["one", "two", "three", "ten", "hundred", "thousand", "many", "few", "all", "nothing"]:
        return ("numbers", f"{en_word}_quantity", f"Numerical or quantity concept: {en_word}", f"Числовое понятие: {ru_word}", "quantitative_concepts", "counting")
    elif en_word in ["white", "black", "red", "blue"]:
        return ("colors", f"{en_word}_color", f"Basic color: {en_word}", f"Основной цвет: {ru_word}", "visual_properties", "color_identification")
    else:
        return ("general", "basic_concept", f"Basic concept: {en_word}", f"Базовое понятие: {ru_word}", "general_knowledge", "basic_communication")

def create_concept_file(word_data):
    """Создает файл концепта для одного слова"""
    
    # Определяем номер в приоритете
    if word_data["priority"] == "ultra_core":
        priority_num = f"{word_data['num']:02d}"
    elif word_data["priority"] == "core":
        priority_num = f"{word_data['num'] - 15:02d}"
    else:  # extended
        priority_num = f"{word_data['num'] - 50:02d}"
    
    # Формируем имя файла
    filename = f"{word_data['num']:04d}_A0_{word_data['priority']}_{priority_num}_concept.json"
    
    # Создаем структуру концепта
    concept = {
        "concept_id": word_data["uuid"],
        "level": "A0",
        "priority": word_data["priority"],
        "category": word_data["category"],
        "concept_name": word_data["concept_name"],
        "description": {
            "en": word_data["description_en"],
            "ru": word_data["description_ru"]
        },
        "semantic_field": word_data["semantic_field"],
        "usage_context": word_data["usage_context"],
        "examples": {
            "en": f"I want {word_data['en']}, I need {word_data['en']}",
            "ru": f"Я хочу {word_data['ru']}, Мне нужно {word_data['ru']}"
        },
        "translation_notes": {
            "general": f"Use the most common and basic form of '{word_data['en']}' in each language",
            "specific": {
                "asian_languages": "Focus on natural expression that works across different grammatical structures",
                "european_languages": "Use nominative case for the target word when possible"
            }
        },
        "difficulty_level": "beginner",
        "frequency": "high",
        "cultural_notes": "Universal concept across all cultures",
        "related_concepts": [],
        "antonyms": [],
        "created_date": "2024-06-26",
        "last_updated": "2024-06-26"
    }
    
    return filename, concept

def main():
    """Основная функция создания всех файлов концептов"""
    
    print("🔍 Извлекаем данные из A0_word_list.md...")
    words = parse_word_list()
    print(f"📊 Найдено {len(words)} слов")
    
    concepts_dir = Path("data/concepts/A0")
    concepts_dir.mkdir(parents=True, exist_ok=True)
    
    created_files = []
    
    for word_data in words:
        filename, concept = create_concept_file(word_data)
        filepath = concepts_dir / filename
        
        # Записываем файл
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(concept, f, ensure_ascii=False, indent=2)
        
        created_files.append(filename)
        print(f"✅ Создан: {filename} ({word_data['ru']}/{word_data['en']})")
    
    print(f"\n🎉 Создано {len(created_files)} файлов концептов!")
    print(f"📁 Папка: {concepts_dir.absolute()}")

if __name__ == "__main__":
    main()
