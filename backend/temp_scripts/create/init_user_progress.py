import os
from pymongo import MongoClient, ASCENDING, IndexModel
from dotenv import load_dotenv
from pathlib import Path

def init_user_progress_collection():
    """Инициализирует коллекцию user_progress с нужными индексами"""
    # Загружаем переменные окружения
    env_path = Path(__file__).parent / '.env'
    if env_path.exists():
        load_dotenv(dotenv_path=env_path)
    
    mongo_uri = os.getenv("MONGODB_URL")
    words_db_name = os.getenv("WORDS_DATABASE_NAME", "word_master")
    
    if not mongo_uri:
        print("Ошибка: Не задана переменная MONGODB_URL в .env файле")
        return
    
    try:
        # Подключаемся к MongoDB
        client = MongoClient(mongo_uri, serverSelectionTimeoutMS=5000)
        db = client[words_db_name]
        
        # Проверяем, существует ли коллекция
        if 'user_progress' in db.list_collection_names():
            print("Коллекция 'user_progress' уже существует")
            user_progress = db['user_progress']
        else:
            # Создаем коллекцию
            user_progress = db.create_collection('user_progress')
            print("Создана коллекция 'user_progress'")
        
        # Создаем индексы
        indexes = [
            # Составной индекс для поиска по user_id и word_id
            IndexModel([("user_id", ASCENDING), ("word_id", ASCENDING)], unique=True),
            # Индекс для поиска слов, которые нужно повторить
            IndexModel([("user_id", ASCENDING), ("next_review", ASCENDING)]),
            # Индекс для поиска выученных слов
            IndexModel([("user_id", ASCENDING), ("is_learned", ASCENDING)])
        ]
        
        # Получаем текущие индексы
        current_indexes = user_progress.index_information()
        print("\nТекущие индексы коллекции 'user_progress':")
        for name, index in current_indexes.items():
            print(f"- {name}: {index['key']}")
        
        # Добавляем недостающие индексы
        for index in indexes:
            # Получаем ключи индекса
            index_keys = index.document['key'].items()
            # Генерируем имя индекса
            index_name = '_'.join([f"{k}_{v}" for k, v in index_keys])
            
            # Проверяем, существует ли индекс с таким же набором полей
            index_exists = False
            for existing_index in current_indexes.values():
                # Преобразуем ключи в список кортежей для сравнения
                existing_keys = [(k, v) for k, v in existing_index['key']]
                new_keys = [(k, v) for k, v in index_keys]
                
                if set(existing_keys) == set(new_keys):
                    index_exists = True
                    print(f"Индекс уже существует: {index_name} - {existing_index['key']}")
                    break
            
            if not index_exists:
                try:
                    user_progress.create_indexes([index])
                    print(f"✅ Добавлен индекс: {index_name} - {index.document['key']}")
                except Exception as e:
                    print(f"❌ Ошибка при создании индекса {index_name}: {e}")
        
        print("\nТекущие индексы коллекции 'user_progress':")
        for name, index in user_progress.index_information().items():
            print(f"- {name}: {index['key']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при инициализации коллекции 'user_progress': {e}")
        return False
    finally:
        if 'client' in locals():
            client.close()

if __name__ == "__main__":
    print("Инициализация коллекции 'user_progress'...")
    success = init_user_progress_collection()
    if success:
        print("✅ Инициализация завершена успешно")
    else:
        print("❌ Ошибка при инициализации")
