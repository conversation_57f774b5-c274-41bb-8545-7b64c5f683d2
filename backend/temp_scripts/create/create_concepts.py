#!/usr/bin/env python3
"""
Скрипт для создания файлов концептов для всех 146 слов A0
"""

import json
import os
from pathlib import Path

# Данные слов из A0_word_list.md
words_data = [
    # ULTRA-CORE (1-15)
    {"num": 1, "ru": "я", "en": "I", "priority": "ultra_core", "uuid": "550e8400-e29b-41d4-a716-************", "category": "pronouns", "concept_name": "first_person_pronoun", "description_en": "First person singular pronoun - refers to the speaker/writer themselves", "description_ru": "Местоимение первого лица единственного числа - обозначает говорящего", "semantic_field": "personal_identity", "usage_context": "basic_communication"},
    
    {"num": 2, "ru": "ты", "en": "you", "priority": "ultra_core", "uuid": "550e8400-e29b-41d4-a716-************", "category": "pronouns", "concept_name": "second_person_pronoun", "description_en": "Second person singular pronoun - refers to the person being addressed", "description_ru": "Местоимение второго лица единственного числа - обозначает собеседника", "semantic_field": "personal_identity", "usage_context": "basic_communication"},
    
    {"num": 3, "ru": "да", "en": "yes", "priority": "ultra_core", "uuid": "550e8400-e29b-41d4-a716-************", "category": "responses", "concept_name": "positive_response", "description_en": "Affirmative response, agreement, confirmation", "description_ru": "Утвердительный ответ, согласие, подтверждение", "semantic_field": "communication", "usage_context": "basic_responses"},
    
    {"num": 4, "ru": "нет", "en": "no", "priority": "ultra_core", "uuid": "550e8400-e29b-41d4-a716-************", "category": "responses", "concept_name": "negative_response", "description_en": "Negative response, disagreement, denial", "description_ru": "Отрицательный ответ, несогласие, отрицание", "semantic_field": "communication", "usage_context": "basic_responses"},
    
    {"num": 5, "ru": "что", "en": "what", "priority": "ultra_core", "uuid": "550e8400-e29b-41d4-a716-************", "category": "questions", "concept_name": "what_question", "description_en": "Interrogative pronoun asking about things, objects, or concepts", "description_ru": "Вопросительное местоимение для вопросов о предметах, объектах или понятиях", "semantic_field": "information_seeking", "usage_context": "basic_questions"},
    
    {"num": 6, "ru": "где", "en": "where", "priority": "ultra_core", "uuid": "550e8400-e29b-41d4-a716-************", "category": "questions", "concept_name": "location_question", "description_en": "Interrogative adverb asking about location or place", "description_ru": "Вопросительное наречие для вопросов о местоположении или месте", "semantic_field": "spatial_orientation", "usage_context": "location_inquiry"},
    
    {"num": 7, "ru": "хочу", "en": "want", "priority": "ultra_core", "uuid": "550e8400-e29b-41d4-a716-************", "category": "desires", "concept_name": "basic_desire", "description_en": "Expression of want, desire, or wish for something", "description_ru": "Выражение желания, потребности или стремления к чему-либо", "semantic_field": "emotional_states", "usage_context": "expressing_needs"},
    
    {"num": 8, "ru": "есть", "en": "eat", "priority": "ultra_core", "uuid": "550e8400-e29b-41d4-a716-************", "category": "basic_actions", "concept_name": "eating_action", "description_en": "Basic action of consuming food, expressing desire to eat", "description_ru": "Базовое действие приема пищи, выражение желания есть", "semantic_field": "basic_needs", "usage_context": "daily_life"},
    
    {"num": 9, "ru": "пить", "en": "drink", "priority": "ultra_core", "uuid": "550e8400-e29b-41d4-a716-************", "category": "basic_actions", "concept_name": "drinking_action", "description_en": "Basic action of consuming liquids, expressing thirst", "description_ru": "Базовое действие употребления жидкости, выражение жажды", "semantic_field": "basic_needs", "usage_context": "daily_life"},
    
    {"num": 10, "ru": "помощь", "en": "help", "priority": "ultra_core", "uuid": "550e8400-e29b-41d4-a716-************", "category": "social_interaction", "concept_name": "help_request", "description_en": "Request for assistance, support, or aid from others", "description_ru": "Просьба о помощи, поддержке или содействии от других людей", "semantic_field": "social_needs", "usage_context": "emergency_situations"},
    
    {"num": 11, "ru": "спасибо", "en": "thank you", "priority": "ultra_core", "uuid": "550e8400-e29b-41d4-a716-************", "category": "politeness", "concept_name": "gratitude_expression", "description_en": "Expression of gratitude, appreciation, or thanks", "description_ru": "Выражение благодарности, признательности или спасибо", "semantic_field": "social_courtesy", "usage_context": "polite_interaction"},
    
    {"num": 12, "ru": "извините", "en": "sorry", "priority": "ultra_core", "uuid": "550e8400-e29b-41d4-a716-************", "category": "politeness", "concept_name": "apology_regret", "description_en": "Expression of regret, apology, or sorrow for mistake or wrongdoing", "description_ru": "Выражение сожаления, извинения или раскаяния за ошибку или проступок", "semantic_field": "social_courtesy", "usage_context": "conflict_resolution"},
    
    {"num": 13, "ru": "врач", "en": "doctor", "priority": "ultra_core", "uuid": "550e8400-e29b-41d4-a716-************", "category": "professions", "concept_name": "medical_professional", "description_en": "Medical professional who treats illnesses and injuries", "description_ru": "Медицинский специалист, который лечит болезни и травмы", "semantic_field": "healthcare", "usage_context": "medical_emergency"},
    
    {"num": 14, "ru": "вода", "en": "water", "priority": "ultra_core", "uuid": "550e8400-e29b-41d4-a716-************", "category": "basic_needs", "concept_name": "essential_liquid", "description_en": "Essential liquid for survival, basic human need", "description_ru": "Жизненно важная жидкость для выживания, базовая потребность человека", "semantic_field": "survival_needs", "usage_context": "basic_survival"},
    
    {"num": 15, "ru": "это", "en": "this", "priority": "ultra_core", "uuid": "550e8400-e29b-41d4-a716-************", "category": "demonstratives", "concept_name": "proximal_demonstrative", "description_en": "Demonstrative pronoun indicating something close to the speaker", "description_ru": "Указательное местоимение, обозначающее предмет близко к говорящему", "semantic_field": "spatial_reference", "usage_context": "object_identification"},

    # CORE (16-50)
    {"num": 16, "ru": "идти", "en": "go", "priority": "core", "uuid": "550e8400-e29b-41d4-a716-************", "category": "movement", "concept_name": "basic_movement", "description_en": "Basic action of moving from one place to another", "description_ru": "Базовое действие перемещения из одного места в другое", "semantic_field": "physical_actions", "usage_context": "daily_movement"},

    {"num": 17, "ru": "спать", "en": "sleep", "priority": "core", "uuid": "550e8400-e29b-41d4-a716-************", "category": "basic_actions", "concept_name": "rest_action", "description_en": "Basic biological need for rest and sleep", "description_ru": "Базовая биологическая потребность в отдыхе и сне", "semantic_field": "basic_needs", "usage_context": "daily_routine"},

    {"num": 18, "ru": "видеть", "en": "see", "priority": "core", "uuid": "550e8400-e29b-41d4-a716-************", "category": "senses", "concept_name": "visual_perception", "description_en": "Basic sense of sight, visual perception", "description_ru": "Базовое чувство зрения, зрительное восприятие", "semantic_field": "sensory_experience", "usage_context": "perception"},

    {"num": 19, "ru": "говорить", "en": "speak", "priority": "core", "uuid": "550e8400-e29b-41d4-a716-************", "category": "communication", "concept_name": "verbal_communication", "description_en": "Basic action of verbal communication and speech", "description_ru": "Базовое действие устного общения и речи", "semantic_field": "communication", "usage_context": "social_interaction"},

    {"num": 20, "ru": "понимать", "en": "understand", "priority": "core", "uuid": "550e8400-e29b-41d4-a716-************", "category": "cognition", "concept_name": "comprehension", "description_en": "Mental process of understanding and comprehension", "description_ru": "Мыслительный процесс понимания и осмысления", "semantic_field": "cognitive_processes", "usage_context": "learning"},
]

def create_concept_file(word_data):
    """Создает файл концепта для одного слова"""
    
    # Определяем номер в приоритете
    if word_data["priority"] == "ultra_core":
        priority_num = f"{word_data['num']:02d}"
    elif word_data["priority"] == "core":
        priority_num = f"{word_data['num'] - 15:02d}"  # core начинается с 16
    else:  # extended
        priority_num = f"{word_data['num'] - 50:02d}"  # extended начинается с 51
    
    # Формируем имя файла
    filename = f"{word_data['num']:04d}_A0_{word_data['priority']}_{priority_num}_concept.json"
    
    # Создаем структуру концепта
    concept = {
        "concept_id": word_data["uuid"],
        "level": "A0",
        "priority": word_data["priority"],
        "category": word_data["category"],
        "concept_name": word_data["concept_name"],
        "description": {
            "en": word_data["description_en"],
            "ru": word_data["description_ru"]
        },
        "semantic_field": word_data["semantic_field"],
        "usage_context": word_data["usage_context"],
        "examples": {
            "en": f"I want {word_data['en']}, I need {word_data['en']}",
            "ru": f"Я хочу {word_data['ru']}, Мне нужно {word_data['ru']}"
        },
        "translation_notes": {
            "general": f"Use the most common and basic form of '{word_data['en']}' in each language",
            "specific": {
                "asian_languages": "Focus on natural expression that works across different grammatical structures",
                "european_languages": "Use nominative case for the target word when possible"
            }
        },
        "difficulty_level": "beginner",
        "frequency": "high",
        "cultural_notes": "Universal concept across all cultures",
        "related_concepts": [],
        "antonyms": [],
        "created_date": "2024-06-26",
        "last_updated": "2024-06-26"
    }
    
    return filename, concept

def main():
    """Основная функция создания всех файлов концептов"""
    
    concepts_dir = Path("A0")
    concepts_dir.mkdir(exist_ok=True)
    
    created_files = []
    
    for word_data in words_data:
        filename, concept = create_concept_file(word_data)
        filepath = concepts_dir / filename
        
        # Записываем файл
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(concept, f, ensure_ascii=False, indent=2)
        
        created_files.append(filename)
        print(f"✅ Создан: {filename}")
    
    print(f"\n🎉 Создано {len(created_files)} файлов концептов!")
    print(f"📁 Папка: {concepts_dir.absolute()}")

if __name__ == "__main__":
    main()
