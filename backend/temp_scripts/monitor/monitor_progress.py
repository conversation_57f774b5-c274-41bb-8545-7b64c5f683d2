#!/usr/bin/env python3
"""
Мониторинг изменений в коллекции user_progress в реальном времени.
Запустите этот скрипт и затем отвечайте на новые слова в приложении.
"""

import asyncio
import os
from datetime import datetime
from bson import ObjectId
from dotenv import load_dotenv
from motor.motor_asyncio import AsyncIOMotorClient

# Загружаем переменные окружения
load_dotenv()

async def monitor_user_progress():
    """Мониторит изменения в коллекции user_progress."""
    
    # Подключаемся к MongoDB
    mongo_uri = os.getenv("MONGODB_URL")
    db_name = os.getenv("WORDS_DATABASE_NAME", "word_master")
    client = AsyncIOMotorClient(mongo_uri)
    db = client[db_name]
    
    try:
        progress_collection = db.user_progress
        words_collection = db.words
        
        print("🔍 Мониторинг коллекции user_progress")
        print("=" * 60)
        print("Теперь отвечайте на новые слова в приложении...")
        print("Нажмите Ctrl+C для остановки")
        print()
        
        # Получаем начальное состояние
        initial_count = await progress_collection.count_documents({})
        print(f"📊 Начальное количество документов: {initial_count}")
        
        # Показываем текущие записи
        print("\n📋 Текущие записи:")
        async for doc in progress_collection.find().sort("updated_at", -1):
            word_info = await words_collection.find_one({"_id": doc["word_id"]})
            word_text = word_info.get("word", "Unknown") if word_info else "Unknown"
            
            print(f"  - {word_text}: interval_level={doc.get('interval_level')}, "
                  f"is_learned={doc.get('is_learned')}, "
                  f"correct={doc.get('correct_answers', 0)}, "
                  f"incorrect={doc.get('incorrect_answers', 0)}")
        
        print("\n🔄 Ожидание изменений...")
        
        # Мониторим изменения
        last_check = datetime.utcnow()
        
        while True:
            await asyncio.sleep(2)  # Проверяем каждые 2 секунды
            
            # Ищем новые или обновленные записи
            new_or_updated = await progress_collection.find({
                "updated_at": {"$gte": last_check}
            }).sort("updated_at", -1).to_list(None)
            
            if new_or_updated:
                print(f"\n⚡ ОБНАРУЖЕНЫ ИЗМЕНЕНИЯ ({datetime.now().strftime('%H:%M:%S')}):")
                print("-" * 40)
                
                for doc in new_or_updated:
                    # Получаем информацию о слове
                    word_info = await words_collection.find_one({"_id": doc["word_id"]})
                    word_text = word_info.get("word", "Unknown") if word_info else "Unknown"
                    
                    print(f"📝 Слово: {word_text}")
                    print(f"   interval_level: {doc.get('interval_level')}")
                    print(f"   is_learned: {doc.get('is_learned')}")
                    print(f"   correct_answers: {doc.get('correct_answers', 0)}")
                    print(f"   incorrect_answers: {doc.get('incorrect_answers', 0)}")
                    print(f"   last_reviewed: {doc.get('last_reviewed')}")
                    print(f"   next_review: {doc.get('next_review')}")
                    print(f"   force_review: {doc.get('force_review')}")
                    print(f"   created_at: {doc.get('created_at')}")
                    print(f"   updated_at: {doc.get('updated_at')}")
                    
                    # Анализируем, является ли это новым словом
                    if (doc.get('interval_level') == -1 and 
                        doc.get('correct_answers', 0) == 0 and 
                        doc.get('incorrect_answers', 0) == 0):
                        print("   🆕 ЭТО НОВОЕ СЛОВО!")
                    
                    # Проверяем, стало ли слово выученным
                    if doc.get('is_learned') and doc.get('interval_level') == 15:
                        print("   🎉 СЛОВО ВЫУЧЕНО! (interval_level = 15)")
                    elif doc.get('is_learned'):
                        print(f"   ⚠️  СЛОВО ПОМЕЧЕНО КАК ВЫУЧЕННОЕ, НО interval_level = {doc.get('interval_level')} (не 15!)")
                    
                    # Проверяем логику для новых слов
                    if (doc.get('correct_answers', 0) == 1 and 
                        doc.get('incorrect_answers', 0) == 0 and
                        doc.get('interval_level') != 15):
                        print("   ❌ ПРОБЛЕМА: Новое слово с правильным ответом должно иметь interval_level = 15!")
                    
                    print()
                
                last_check = datetime.utcnow()
            
            # Показываем общую статистику каждые 10 секунд
            if datetime.utcnow().second % 10 == 0:
                current_count = await progress_collection.count_documents({})
                learned_count = await progress_collection.count_documents({"is_learned": True})
                max_level_count = await progress_collection.count_documents({"interval_level": 15})
                
                print(f"📊 Статистика: всего={current_count}, выучено={learned_count}, уровень_15={max_level_count}")
                
    except KeyboardInterrupt:
        print("\n👋 Мониторинг остановлен")
    except Exception as e:
        print(f"❌ ОШИБКА: {e}")
        import traceback
        traceback.print_exc()
    finally:
        client.close()

if __name__ == "__main__":
    print("🚀 Запуск мониторинга user_progress...")
    asyncio.run(monitor_user_progress())
