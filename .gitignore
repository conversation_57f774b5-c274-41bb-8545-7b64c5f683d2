# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
backend/venv*
venv/
ENV/

# Node
frontend/node_modules/
frontend/.expo/
npm-debug.log*
yarn-debug.log*
yarn-error.log*


# Environment variables
.env
.env.local
.env.production
.env.test
# исключаем .env.example из игнора
!.env.example

# IDE
.idea/
.vscode/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db
